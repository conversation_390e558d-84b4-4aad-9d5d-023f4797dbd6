    private static void setFinalStatic(Class<?> clazz, String fieldName, Object newValue) {
        try {
            java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);

            // 使用VarHandle替代直接访问modifiers字段
            java.lang.invoke.MethodHandles.Lookup lookup = java.lang.invoke.MethodHandles.lookup();
            java.lang.invoke.VarHandle modifiersHandle = lookup.findVarHandle(java.lang.reflect.Field.class, "modifiers", int.class);
            modifiersHandle.set(field, (int)modifiersHandle.get(field) & ~java.lang.reflect.Modifier.FINAL);

            field.set(null, newValue);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }