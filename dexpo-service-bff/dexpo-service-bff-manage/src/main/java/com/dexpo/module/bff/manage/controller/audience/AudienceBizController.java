package com.dexpo.module.bff.manage.controller.audience;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.audience.api.AudienceBizApi;
import com.dexpo.module.audience.api.dto.audience.AudienceBoardDTO;
import com.dexpo.module.audience.api.dto.audience.AudiencePageQueryDTO;
import com.dexpo.module.audience.api.vo.audience.AudienceBoardVO;
import com.dexpo.module.audience.api.vo.audience.AudiencePageListVO;
import com.dexpo.module.audience.api.vo.audience.AudienceRegisterExhibitionVO;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/biz/audience")
@AllArgsConstructor
public class AudienceBizController {

    private final AudienceBizApi audienceBizApi;

    /**
     * 分页获取媒体列表
     */
    @PostMapping("/page")
    @Operation(summary = "分页获取观众分页列表")
    CommonResult<PageResult<AudiencePageListVO>> getMediaPageList(@Valid @RequestBody AudiencePageQueryDTO queryDTO) {
        return audienceBizApi.getAudiencePageList(queryDTO);
    }


    @PostMapping("/activeAudience")
    @Operation(summary = "普通观众用户激活")
    CommonResult<Boolean> activeAudience(@Valid @NotEmpty @RequestBody List<Long> ids) {
        return audienceBizApi.activeAudience(ids);
    }

    @GetMapping("/registerDetail")
    @Operation(summary = "获取观众注册展会信息详情")
    CommonResult<AudienceRegisterExhibitionVO> getAudienceDetail(@Valid @NotNull @RequestParam("id") Long id) {
        return audienceBizApi.getAudienceDetail(id);
    }

    @PostMapping("/board")
    @Operation(summary = "观众看板数据")
    CommonResult<AudienceBoardVO> getAudienceBoard(@Valid @RequestBody AudienceBoardDTO queryDTO){
        return audienceBizApi.getAudienceBoard(queryDTO);
    }

}
