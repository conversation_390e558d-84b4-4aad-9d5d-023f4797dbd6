package com.dexpo.module.bff.manage.controller.base;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.base.api.basic.OrganizationApi;
import com.dexpo.module.base.api.basic.dto.UserPageQueryDTO;
import com.dexpo.module.base.api.basic.vo.ManageOrganizationVO;
import com.dexpo.module.base.api.basic.vo.SysOrganizationVO;
import com.dexpo.module.base.api.basic.vo.UserPageVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "组织相关")
@RestController
@RequestMapping("/common/organization")
@RequiredArgsConstructor
public class OrganizationController {

    private final OrganizationApi organizationApi;

    /**
     * 获得项目组织列表
     */
    @GetMapping("/getOrganizationList")
    @Operation(summary = "获得项目组织列表")
    CommonResult<List<SysOrganizationVO>> getOrganizationList() {
        return organizationApi.getOrganizationList();
    }


    /**
     * 获得管理组织列表
     */
    @GetMapping("/getManageOrganizationList")
    @Operation(summary = "获得管理组织列表")
    CommonResult<List<ManageOrganizationVO>> getManageOrganizationList() {
        return organizationApi.getManageOrganizationList();
    }

    @PostMapping("/getUserPage")
    @Operation(summary = "获得主办方用户列表")
    CommonResult<PageResult<UserPageVO>> getUserPage(@Valid @RequestBody UserPageQueryDTO req) {
        return organizationApi.getUserPage(req);
    }
}
