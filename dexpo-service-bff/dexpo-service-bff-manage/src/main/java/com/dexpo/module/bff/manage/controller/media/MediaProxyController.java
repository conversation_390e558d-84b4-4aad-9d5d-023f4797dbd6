package com.dexpo.module.bff.manage.controller.media;

import com.dexpo.framework.common.exception.enums.ErrorCodeEnums;
import com.dexpo.framework.common.exception.enums.MemberServiceErrorCodeEnum;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.framework.security.core.LoginUser;
import com.dexpo.framework.security.core.util.SecurityFrameworkUtils;
import com.dexpo.module.integration.api.flie.FileApi;
import com.dexpo.module.integration.utils.FileUtils;
import com.dexpo.module.log.api.SysUploadDownloadRecordApi;
import com.dexpo.module.log.api.dto.SysUploadDownloadRecordDTO;
import com.dexpo.module.log.api.vo.SysUploadDownloadRecordVO;
import com.dexpo.module.member.api.MediaProxyApi;
import com.dexpo.module.member.api.dto.media.MediaProxyRecordDTO;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Objects;

@Slf4j
@RestController
@Validated
public class MediaProxyController {


    @Resource
    private MediaProxyApi mediaProxyApi;


    @Resource
    private FileApi fileApi;

    @Resource
    private SysUploadDownloadRecordApi sysUploadDownloadRecordApi;

    @PostMapping(value = "/media/uploadFile", consumes = "multipart/form-data")
    @Operation(summary = "媒体批量注册-上传文件")
    public CommonResult<Boolean> uploadFile(@RequestParam("file") MultipartFile file) {

        if (!"application/zip".equals(file.getContentType()) || !Objects.requireNonNull(file.getOriginalFilename()).endsWith(".zip")) {
            throw MemberServiceErrorCodeEnum.MEDIA_PROXY_UPLOAD_TYPE_ZIP.getServiceException();
        }

        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        String originalFilename = file.getOriginalFilename();
        // 上传文件
        String path = FileUtils.getFilePath(loginUser.getMemberCode());
        CommonResult<String> result = null;
        try {
            result = fileApi.uploadFileByBytes(originalFilename, path, file.getBytes());
        } catch (IOException e) {
            log.error("上传文件失败：{}", e.getMessage());
            throw ErrorCodeEnums.BFF_USER_FILE_UPLOAD_FAIL.getServiceException();
        }

        try {
            MediaProxyRecordDTO mediaProxyRecordDTO = new MediaProxyRecordDTO();
            mediaProxyRecordDTO.setFileName(file.getOriginalFilename());
            mediaProxyRecordDTO.setFileUrl(result.getData());
            mediaProxyApi.registrationByFile(mediaProxyRecordDTO);
        } catch (Exception e) {
            log.info(ErrorCodeEnums.BFF_USER_FILE_UPLOAD_FAIL.getMsg(), e);
            throw ErrorCodeEnums.BFF_USER_FILE_UPLOAD_FAIL.getServiceException();
        }

        return CommonResult.success(true);
    }

    @PostMapping(value = "/media/queryMemberMediaProxyRecordPage")
    @Operation(summary = "根据展会分页查询上传批量代注册的记录")
    CommonResult<PageResult<SysUploadDownloadRecordVO>> queryMemberMediaProxyRecordPage(@RequestBody
                                                                                        SysUploadDownloadRecordDTO sysUploadDownloadRecordDTO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        sysUploadDownloadRecordDTO.setUserId(loginUser.getId());
        return CommonResult.success(sysUploadDownloadRecordApi.page(sysUploadDownloadRecordDTO));
    }

}
