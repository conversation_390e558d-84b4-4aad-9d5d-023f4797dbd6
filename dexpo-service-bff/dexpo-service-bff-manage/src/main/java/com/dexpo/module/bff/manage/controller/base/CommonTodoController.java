package com.dexpo.module.bff.manage.controller.base;

import com.dexpo.framework.common.pojo.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.base.api.basic.CommonTodoApi;
import com.dexpo.module.base.api.basic.dto.CommenTodoPageQueryDTO;
import com.dexpo.module.base.api.basic.vo.CommonTodoVO;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/common/todo")
@RequiredArgsConstructor
public class CommonTodoController {

    private final CommonTodoApi commonTodoApi;

    @Operation(summary = "分页获取待办事项")
    @PostMapping("/page")
    public CommonResult<PageResult<CommonTodoVO>> getPage(@RequestBody @Valid CommenTodoPageQueryDTO req) {
        return commonTodoApi.getPage(req);
    }
}
