package com.dexpo.module.bff.manage.controller.base;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.base.api.basic.BasicValuesetApi;
import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/common/valueset")
public class BasicValuesetController {

    @Autowired
    private BasicValuesetApi basicValuesetApi;

    /**
     * 根据值集代码列表获取值集信息
     *
     * @param valuesetCodes codes
     * @return values
     */
    @PostMapping("/getValueSetOptionList")
    CommonResult<List<BasicValuesetInfoVO>> getValuesetListByCodes(@Valid @RequestBody List<String> valuesetCodes) {
        return basicValuesetApi.getValuesetListByCodes(valuesetCodes);
    }
}