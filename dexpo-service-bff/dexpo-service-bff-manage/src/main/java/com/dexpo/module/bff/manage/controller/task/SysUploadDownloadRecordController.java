package com.dexpo.module.bff.manage.controller.task;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.log.api.SysUploadDownloadRecordApi;
import com.dexpo.module.log.api.dto.SysUploadDownloadRecordDTO;
import com.dexpo.module.log.api.vo.SysUploadDownloadRecordVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "导入导出记录")
@RestController
@RequiredArgsConstructor
@RequestMapping("/sysUploadDownloadRecord")
public class SysUploadDownloadRecordController {

    private final SysUploadDownloadRecordApi sysUploadDownloadRecordApi;

    @PostMapping("/page")
    public CommonResult<PageResult<SysUploadDownloadRecordVO>> page(@Valid @RequestBody SysUploadDownloadRecordDTO dto) {
        PageResult<SysUploadDownloadRecordVO> page = sysUploadDownloadRecordApi.page(dto);
        return CommonResult.success(page);
    }
}
