package com.dexpo.module.bff.manage.controller.media;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.member.api.MediaApi;
import com.dexpo.module.member.api.MemberMediaBizApi;
import com.dexpo.module.member.api.dto.media.MediaAuditDTO;
import com.dexpo.module.member.api.dto.media.MediaRegisterInfoQueryDTO;
import com.dexpo.module.member.api.dto.media.MediaSyncDataDTO;
import com.dexpo.module.member.api.vo.media.MediaRegisterInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@Validated
public class MediaBizController {

    @Resource
    private MediaApi mediaApi;

    @Resource
    private  MemberMediaBizApi memberMediaBizApi;


    private static final String PREFIX = "/member";

    @PostMapping(PREFIX+"/mediaMember/audit")
    @Operation(summary = "媒体信息审核")
    CommonResult<List<Long>> audit(@Valid @RequestBody MediaAuditDTO mediaAuditDTO){
        return mediaApi.audit(mediaAuditDTO);
    }


    @PostMapping(PREFIX+"/mediaMember/detail")
    @Operation(summary = "获取媒体注册信息接口")
    CommonResult<MediaRegisterInfoVO> getMediaRegisterInfo(@Valid @RequestBody MediaRegisterInfoQueryDTO infoQueryDTO){
        return memberMediaBizApi.getMediaRegisterInfo(infoQueryDTO);
    }


    @PostMapping(PREFIX+"/mediaMember/syncApprovedData")
    @Operation(summary = "同步数据接口")
    CommonResult<List<Long>> syncApprovedData(@Valid @RequestBody MediaSyncDataDTO syncDataDTO){
        return mediaApi.syncApprovedData(syncDataDTO);
    }


}

