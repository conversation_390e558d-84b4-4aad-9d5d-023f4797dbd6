# 基础镜像
FROM ciif-acr-registry.cn-shanghai.cr.aliyuncs.com/dev/eclipse-temurin:21-jdk-jammy

# 挂载目录
VOLUME /home/<USER>

# 创建目录
RUN mkdir -p /home/<USER>/dexpo-service-bff-manage

# 指定路径
WORKDIR /home/<USER>/dexpo-service-bff-manage

# 复制jar文件到路径
COPY target/dexpo-service-bff-manage.jar /home/<USER>/dexpo-service-bff-manage/dexpo-service-bff-manage.jar

ENV TZ=Asia/Shanghai

EXPOSE 48086
# 启动
CMD java -jar dexpo-service-bff-manage.jar
