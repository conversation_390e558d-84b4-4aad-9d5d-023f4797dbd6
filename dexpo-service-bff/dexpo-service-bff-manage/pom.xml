<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.dexpo</groupId>
		<artifactId>dexpo-framework-starter-parent</artifactId>
		<version>1.0.0-SNAPSHOT</version>
		<relativePath>../../dexpo-framework/dexpo-framework-starter-parent/pom.xml</relativePath>
	</parent>
	<artifactId>dexpo-service-bff-manage</artifactId>

	<packaging>jar</packaging>
	<name>dexpo-service-bff-manage</name>

    <properties>
        <docker.deploy.version>${project.version}</docker.deploy.version>
    </properties>
    
	<dependencies>
		<dependency>
			<groupId>com.dexpo</groupId>
			<artifactId>dexpo-framework-starter-common</artifactId>
		</dependency>

		<dependency>
			<groupId>com.dexpo</groupId>
			<artifactId>dexpo-service-base-api</artifactId>
		</dependency>

		<dependency>
			<groupId>com.dexpo</groupId>
			<artifactId>dexpo-service-member-api</artifactId>
		</dependency>

		<dependency>
			<groupId>com.dexpo</groupId>
			<artifactId>dexpo-service-integration-api</artifactId>
			<version>${revision}</version>
		</dependency>

		<dependency>
			<groupId>com.dexpo</groupId>
			<artifactId>dexpo-service-exhibition-api</artifactId>
			<version>${revision}</version>
		</dependency>

		<dependency>
			<groupId>com.dexpo</groupId>
			<artifactId>dexpo-service-audience-api</artifactId>
			<version>${revision}</version>
		</dependency>

		<dependency>
			<groupId>com.dexpo</groupId>
			<artifactId>dexpo-service-log-api</artifactId>
			<version>${revision}</version>
		</dependency>

		<!-- RPC 远程调用相关 -->
		<dependency>
			<groupId>com.dexpo</groupId>
			<artifactId>dexpo-framework-starter-rpc</artifactId>
		</dependency>

		<!-- Registry 注册中心相关 -->
		<dependency>
			<groupId>com.dexpo</groupId>
			<artifactId>dexpo-framework-starter-kubernetes</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.dexpo</groupId>
			<artifactId>dexpo-framework-starter-cache</artifactId>
		</dependency>

		<dependency>
			<groupId>com.dexpo</groupId>
			<artifactId>dexpo-framework-starter-security</artifactId>
		</dependency>

		<!-- Web 相关 -->
		<dependency>
			<groupId>io.swagger.core.v3</groupId> <!-- 接口文档：使用最新版本的 Swagger 模型 -->
			<artifactId>swagger-annotations</artifactId>
			<scope>provided</scope>
		</dependency>

		<!-- 参数校验 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<scope>provided</scope>
		</dependency>
	</dependencies>

	<build>
		<!-- 设置构建的 jar 包名 -->
		<finalName>${project.artifactId}</finalName>
		<plugins>
			<!-- 打包 -->
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${spring.boot.version}</version>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>21</source>
					<target>21</target>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
