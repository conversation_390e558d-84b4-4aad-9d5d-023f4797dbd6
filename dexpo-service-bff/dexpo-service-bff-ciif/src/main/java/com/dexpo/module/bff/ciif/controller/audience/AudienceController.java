package com.dexpo.module.bff.ciif.controller.audience;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.audience.api.AudienceApi;
import com.dexpo.module.audience.api.dto.audience.AudienceLoginDTO;
import com.dexpo.module.audience.api.vo.audience.AudienceLoginInfoVO;
import com.dexpo.module.member.api.dto.media.MediaMemberLoginDTO;
import com.dexpo.module.member.api.vo.LoginInfoResVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: <PERSON><PERSON><PERSON>
 * @CreateTime: 2025-06-23
 * @Description:
 */

@RestController
@Validated
@Tag(name = "观众接口")
public class AudienceController {

    private static final String PREFIX = "/audience";

    @Resource
    private AudienceApi audienceApi;

    @PostMapping(PREFIX + "/login")
    @Operation(summary = "观众用户登录")
    public CommonResult<AudienceLoginInfoVO> audienceLogin(@Valid @RequestBody AudienceLoginDTO loginDTO) {
        return audienceApi.audienceLogin(loginDTO);
    }
}
