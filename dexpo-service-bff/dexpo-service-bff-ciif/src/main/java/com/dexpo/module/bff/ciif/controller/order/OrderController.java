package com.dexpo.module.bff.ciif.controller.order;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.integration.api.wechatpay.WXNotifyMsgDTO;
import com.dexpo.module.integration.api.wechatpay.WXNotifyResponseDTO;
import com.dexpo.module.integration.api.wechatpay.WechatPayApi;
import com.dexpo.module.integration.api.wechatpay.WechatPayDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@Validated
@Slf4j
@Tag(name = "企业信息")
public class OrderController {

    private static final String PREFIX = "/wechat";

    @Resource
    private WechatPayApi wechatPayApi;

    @PostMapping(PREFIX + "/pay")
    @Operation(summary = "用户下单")
    public CommonResult<Map<String, String>> pay(@Valid @RequestBody WechatPayDTO wechatPayDTO) {
        return wechatPayApi.handlePay(wechatPayDTO);
    }

    @PostMapping(PREFIX + "/pay/payNotify")
    @Operation(summary = "用户下单回调")
    public WXNotifyResponseDTO payNotify(@RequestBody WXNotifyMsgDTO msg) {
        log.info("payNotify msg={}", msg);
        WXNotifyResponseDTO wxNotifyResponseDTO = new WXNotifyResponseDTO();
        try {
            wechatPayApi.payCallBack(msg);
        } catch (Exception e) {
            log.info("payNotify msg={}", msg);
            wxNotifyResponseDTO.setCode("FAIL");
            wxNotifyResponseDTO.setMessage("失败");
            return wxNotifyResponseDTO;
        }
        return wxNotifyResponseDTO;
    }

    @PostMapping("/pay/payNotify")
    @Operation(summary = "用户下单回调")
    public WXNotifyResponseDTO payNotifyTemp(@RequestBody WXNotifyMsgDTO msg) {
        log.info("payNotify msg={}", msg);
        WXNotifyResponseDTO wxNotifyResponseDTO = new WXNotifyResponseDTO();
        try {
            wechatPayApi.payCallBack(msg);
        } catch (Exception e) {
            log.info("payNotify msg={}", msg);
            wxNotifyResponseDTO.setCode("FAIL");
            wxNotifyResponseDTO.setMessage("失败");
            return wxNotifyResponseDTO;
        }
        return wxNotifyResponseDTO;
    }

    @PostMapping(PREFIX + "/pay/refundNotify")
    @Operation(summary = "用户退款回调")
    public void refundNotify(@RequestBody String msg) {
        log.info("refundNotify msg={}", msg);
    }
}
