package com.dexpo.module.bff.ciif.controller.exhibition;

import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.exhibition.api.ExhibitionApi;
import com.dexpo.module.exhibition.api.dto.ExhibitionTagCodeQueryDTO;
import com.dexpo.module.exhibition.api.vo.ExhibitionVO;
import com.dexpo.module.exhibition.cache.ExhibitionCacheQueryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Validated
@Tag(name = "展会信息")
public class ExhibitionController {

    private static final String PREFIX = "/exhibition/info";

    @Resource
    private ExhibitionApi exhibitionApi;

    @Resource
    private RedisService redisService;

    @PostMapping(PREFIX + "/getExhibition")
    @Operation(summary = "媒体入口获取展会信息")
    public CommonResult<ExhibitionVO> getExhibition(@Valid @RequestBody ExhibitionTagCodeQueryDTO req) {
        ExhibitionCacheQueryService queryService = new ExhibitionCacheQueryService( exhibitionApi, redisService);
        ExhibitionVO exhibition = queryService.getExhibitionByTagCode(req.getExhibitionTagCode());
        return CommonResult.success(exhibition);
    }
}
