package com.dexpo.module.bff.ciif.controller.wchart;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.integration.api.wechatpay.WechatOpenidDTO;
import com.dexpo.module.integration.api.wechatpay.WechatPayApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Validated
@Tag(name = "企业信息")
public class WechartController {

    private static final String PREFIX = "/wechat";

    @Resource
    private WechatPayApi wechatPayApi;

    @PostMapping(PREFIX + "/getOpenId")
    @Operation(summary = "用户通过code获取openid")
    public CommonResult<WechatOpenidDTO> getOpenId(@Valid @RequestBody WechatOpenidDTO wechatOpenidDTO) {
        log.info("wechatOpenidDTO={}", wechatOpenidDTO);
        return wechatPayApi.getOpenId(wechatOpenidDTO);
    }
}
