package com.dexpo.module.bff.ciif.controller.basic;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.base.api.basic.BasicRegionApi;
import com.dexpo.module.base.api.basic.dto.BasicRegionDTO;
import com.dexpo.module.base.api.basic.vo.BasicRegionVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Validated
@Tag(name = "行政区域信息")
public class BasicRegionController {

    private static final String PREFIX = "/common/region";

    @Resource
    private BasicRegionApi basicRegionApi;

    @PostMapping(PREFIX + "/getRegionList")
    @Operation(summary = "获取行政区域信息")
    public CommonResult<List<BasicRegionVO>> getRegionList(@Valid @RequestBody BasicRegionDTO regionDTO) {
        return basicRegionApi.getRegionList(regionDTO);
    }
}
