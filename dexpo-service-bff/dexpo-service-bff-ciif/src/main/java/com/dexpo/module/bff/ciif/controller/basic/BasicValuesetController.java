package com.dexpo.module.bff.ciif.controller.basic;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.base.api.basic.BasicValuesetApi;
import com.dexpo.module.base.api.basic.dto.ExhibitionTagValuesetDTO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Validated
@Tag(name = "值集信息")
public class BasicValuesetController {

    private static final String PREFIX = "/common/valueset";

    @Resource
    private BasicValuesetApi basicValuesetApi;

    @PostMapping(PREFIX + "/getValueSetOptionList")
    @Operation(summary = "根据值集代码列表获取值集信息")
    public CommonResult<List<BasicValuesetInfoVO>> getValuesetListByCodes(@Valid @RequestBody List<String> valuesetCodes) {
        return basicValuesetApi.getValuesetListByCodes(valuesetCodes);
    }

    @PostMapping(PREFIX + "/getTagValueSetOptionList")
    @Operation(summary = "根据值集代码列表获取展会值集信息")
    public CommonResult<List<BasicValuesetInfoVO>> getExhibitionValuesetListByCodes(
            @Valid @RequestBody ExhibitionTagValuesetDTO request) {
        return basicValuesetApi.getExhibitionValuesetListByCodes(request);
    }
} 