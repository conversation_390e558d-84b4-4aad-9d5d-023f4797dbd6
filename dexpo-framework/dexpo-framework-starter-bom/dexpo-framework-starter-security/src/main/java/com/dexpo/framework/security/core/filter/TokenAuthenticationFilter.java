package com.dexpo.framework.security.core.filter;

import cn.hutool.core.text.CharSequenceUtil;
import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.util.json.JsonUtils;
import com.dexpo.framework.common.util.servlet.ServletUtils;
import com.dexpo.framework.security.config.SecurityProperties;
import com.dexpo.framework.security.core.LoginUser;
import com.dexpo.framework.security.core.service.TokenService;
import com.dexpo.framework.security.core.util.SecurityFrameworkUtils;
import com.dexpo.framework.web.web.core.handler.GlobalExceptionHandler;
import com.dexpo.framework.web.web.core.util.WebFrameworkUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Collections;
import java.util.List;

/**
 * Token 过滤器，验证 token 的有效性
 * 验证通过后，获得 {@link LoginUser} 信息，并加入到 Spring Security 上下文
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Order(-1)
public class TokenAuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private GlobalExceptionHandler globalExceptionHandler;

    @Resource
    private TokenService tokenService;

    /**
     * HTTP 请求时，访问令牌的请求 Header
     */
    private String tokenHeader = "Authorization";

    /**
     * mock 模式的开关
     */
    private Boolean mockEnable = false;
    /**
     * mock 模式的密钥
     * 一定要配置密钥，保证安全性
     */
    private String mockSecret = "test";

    /**
     * 免登录的 URL 列表
     */
    private List<String> permitAllUrls = Collections.emptyList();

    /**
     * PasswordEncoder 加密复杂度，越高开销越大
     */
    private Integer passwordEncoderLength = 4;

    @Override
    @SuppressWarnings("NullableProblems")
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        log.debug("TokenAuthenticationFilter doFilterInternal");
        // 情况一，基于 header[login-user] 获得用户，例如说来自 Gateway 或者其它服务透传
        LoginUser loginUser = buildLoginUserByHeader(request);
        log.debug("TokenAuthenticationFilter doFilterInternal LoginUser = {}", JsonUtils.toJsonString(loginUser));
        // 情况二，基于 Token 获得用户
        // 注意，这里主要满足直接使用 Nginx 直接转发到 Spring Cloud 服务的场景。
        if (loginUser == null) {
            log.debug("TokenAuthenticationFilter doFilterInternal LoginUser is null, then build user By Token ");
            String token = SecurityFrameworkUtils.obtainAuthorization(request, tokenHeader);
            if (CharSequenceUtil.isNotEmpty(token)) {
                Integer userType = WebFrameworkUtils.getLoginUserType(request);
                try {
                    // 1.1 基于 token 构建登录用户
                    loginUser = buildLoginUserByToken(token, userType);
                    log.debug("TokenAuthenticationFilter doFilterInternal build LoginUser success loginUser={}", JsonUtils.toJsonString(loginUser));
                    // 1.2 模拟 Login 功能，方便日常开发调试
                    if (loginUser == null) {
                        log.debug("TokenAuthenticationFilter doFilterInternal mock LoginUser");
                        loginUser = mockLoginUser(request, token, userType);
                    }
                } catch (Throwable ex) {
                    CommonResult<?> result = globalExceptionHandler.allExceptionHandler(request, ex);
                    ServletUtils.writeJSON(response, result);
                    return;
                }
            }
        }

        // 设置当前用户
        if (loginUser != null) {
            log.debug("TokenAuthenticationFilter doFilterInternal  SecurityFrameworkUtils.setLoginUser  ={}", JsonUtils.toJsonString(loginUser));
            SecurityFrameworkUtils.setLoginUser(loginUser, request);
        }
        // 继续过滤链
        chain.doFilter(request, response);
    }

    private LoginUser buildLoginUserByToken(String token, Integer userType) {
        try {
            // 校验访问令牌
            LoginUser accessToken = tokenService.getUserInfoByToke(token);
            if (accessToken == null) {
                return null;
            }

            // 构建登录用户
            return new LoginUser().setId(accessToken.getId())
                    .setUserName(accessToken.getUserName())
                    .setMemberCode(accessToken.getMemberCode())
                    .setMemberMobile(accessToken.getMemberMobile())
                    .setMemberEmail(accessToken.getMemberEmail())
                    .setUserType(accessToken.getUserType())
                    .setScopes(accessToken.getScopes());
        } catch (ServiceException serviceException) {
            // 校验 Token 不通过时，考虑到一些接口是无需登录的，所以直接返回 null 即可
            return null;
        }
    }

    /**
     * 模拟登录用户，方便日常开发调试
     * <p>
     * 注意，在线上环境下，一定要关闭该功能！！！
     *
     * @param request  请求
     * @param token    模拟的 token，格式为 {@link SecurityProperties#getMockSecret()} + 用户编号
     * @param userType 用户类型
     * @return 模拟的 LoginUser
     */
    private LoginUser mockLoginUser(HttpServletRequest request, String token, Integer userType) {
        if (Boolean.FALSE.equals(mockEnable)) {
            return null;
        }
        // 必须以 mockSecret 开头
        if (!token.startsWith(mockSecret)) {
            return null;
        }
        // 构建模拟用户
        Long userId = Long.valueOf(token.substring(mockSecret.length()));
        return new LoginUser().setId(userId).setUserType(userType);
    }

    private LoginUser buildLoginUserByHeader(HttpServletRequest request) throws UnsupportedEncodingException {
        String loginUserStr = request.getHeader(SecurityFrameworkUtils.LOGIN_USER_HEADER);
        if (CharSequenceUtil.isNotEmpty(loginUserStr)) {
            String decode = URLDecoder.decode(loginUserStr, "UTF-8");
            log.info("buildLoginUserByHeader loginUserStr:{} encode:{}", loginUserStr, decode);
            return JsonUtils.parseObject(decode, LoginUser.class);
        }
        return null;
    }

}
