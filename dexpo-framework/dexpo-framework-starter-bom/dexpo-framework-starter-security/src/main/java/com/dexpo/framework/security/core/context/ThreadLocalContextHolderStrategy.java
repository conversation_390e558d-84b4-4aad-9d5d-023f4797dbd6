package com.dexpo.framework.security.core.context;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.dexpo.framework.security.core.LoginUser;
import org.springframework.util.Assert;

/**
 * 基于 TransmittableThreadLocal 实现的 Security Context 持有者策略
 * 目的是，避免 @Async 等异步执行时，原生 ThreadLocal 的丢失问题
 *
 * <AUTHOR>
 */
public class ThreadLocalContextHolderStrategy {

    /**
     * 使用 TransmittableThreadLocal 作为上下文
     */
    private static final ThreadLocal<LoginUser> CONTEXT_HOLDER = new TransmittableThreadLocal<>();

    public static void clearContext() {
        CONTEXT_HOLDER.remove();
    }

    public static LoginUser getContext() {
        LoginUser ctx = CONTEXT_HOLDER.get();
        if (ctx == null) {
            ctx = createEmptyContext();
            CONTEXT_HOLDER.set(ctx);
        }
        return ctx;
    }

    public static void setContext(LoginUser context) {
        Assert.notNull(context, "Only non-null SecurityContext instances are permitted");
        CONTEXT_HOLDER.set(context);
    }

    public static LoginUser createEmptyContext() {
        return new LoginUser();
    }

}
