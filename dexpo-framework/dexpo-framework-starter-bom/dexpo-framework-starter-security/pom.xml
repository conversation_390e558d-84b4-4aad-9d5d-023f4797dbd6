<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.dexpo</groupId>
        <artifactId>dexpo-framework-starter-bom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>dexpo-framework-starter-security</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>用户的认证、权限的校验</description>

    <dependencies>
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-cache</artifactId>
        </dependency>

        <!-- Spring 核心 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-web</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <!-- spring boot 配置所需依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-security</artifactId>-->
<!--        </dependency>-->

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>33.4.8-jre</version>
        </dependency>
        <!-- 需要使用它，进行 Token 的校验 -->
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-biz-data-permission-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

</project>
