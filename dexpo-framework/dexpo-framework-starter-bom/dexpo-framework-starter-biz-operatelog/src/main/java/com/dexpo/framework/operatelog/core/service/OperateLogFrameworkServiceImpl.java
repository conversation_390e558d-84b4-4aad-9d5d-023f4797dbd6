package com.dexpo.framework.operatelog.core.service;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;

/**
 * 操作日志 Framework Service 实现类
 *
 * 基于 {@link OperateLogApi} 远程服务，记录操作日志
 *
 * <AUTHOR>
 */


@Slf4j

public class OperateLogFrameworkServiceImpl implements OperateLogFrameworkService {

//    @Resource
//    ProducerTool producerTool;
    
    @Override
    @Async
    public void createOperateLog(OperateLog operateLog) {
        String logstr = JSONUtil.toJsonPrettyStr(operateLog);
        log.info("operationlog is {}",logstr);


        //发送kafka消息实现操作日志的写入
        log.debug("创建OperateLog,后续改造kafka消息发送...");

        //producerTool.sendMsg(OperateLogUtils.OPERATIONLOG_TOPIC,"",operateLog);
    }

}
