package com.dexpo.framework.mybatis.core.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import com.dexpo.framework.web.web.core.util.WebFrameworkUtils;
import org.apache.ibatis.reflection.MetaObject;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 通用参数填充实现类
 * 如果没有显式的对通用参数进行赋值，这里会对通用参数进行填充、赋值
 *
 * <AUTHOR>
 */
public class DefaultDBFieldHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        if (metaObject != null && metaObject.getOriginalObject() instanceof BaseDO baseDO) {

            LocalDateTime current = LocalDateTime.now();
            // 创建时间为空，则以当前时间为插入时间
            if (Objects.isNull(baseDO.getCreateTime())) {
                baseDO.setCreateTime(current);
            }
            // 更新时间为空，则以当前时间为更新时间
            if (Objects.isNull(baseDO.getUpdateTime())) {
                baseDO.setUpdateTime(current);
            }

            //获取当前用户ID
            addUserId(baseDO);

            addUserName(baseDO);
            //设置默认未删除
            baseDO.setDelFlg(false);
        }
    }

    private void addUserId(BaseDO baseDO) {
        Long userId = WebFrameworkUtils.getLoginUserId();
        // 当前登录用户不为空，创建人ID为空，则当前登录用户ID为创建人ID
        if (Objects.nonNull(userId) && Objects.isNull(baseDO.getCreateUser())) {
            baseDO.setCreateUser(userId);
        }
        // 当前登录用户不为空，更新人ID为空，则当前登录用户ID为更新人ID
        if (Objects.nonNull(userId) && Objects.isNull(baseDO.getUpdateUser())) {
            baseDO.setUpdateUser(userId);
        }
    }

    private void addUserName(BaseDO baseDO) {
        //获取当前登录用户名
        String userName = WebFrameworkUtils.getLoginUserName();
        // 当前登录用户不为空，创建人为空，则当前登录用户为创建人
        if (Objects.nonNull(userName) && Objects.isNull(baseDO.getCreateUserName())) {
            baseDO.setCreateUserName(userName);
        }
        // 当前登录用户不为空，更新人为空，则当前登录用户为更新人
        if (Objects.nonNull(userName) && Objects.isNull(baseDO.getUpdateUserName())) {
            baseDO.setUpdateUserName(userName);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        // 更新时间为空，则以当前时间为更新时间
        Object modifyTime = getFieldValByName("updateTime", metaObject);
        if (Objects.isNull(modifyTime)) {
            setFieldValByName("updateTime", LocalDateTime.now(), metaObject);
        }

        // 当前登录用户不为空，更新人为空，则当前登录用户为更新人
        Object modifierId = getFieldValByName("updateUser", metaObject);
        Long updateUser = WebFrameworkUtils.getLoginUserId();
        if (Objects.nonNull(updateUser) && Objects.isNull(modifierId)) {
            setFieldValByName("updateUser", updateUser, metaObject);
        }

        Object modifier = getFieldValByName("updateUserName", metaObject);
        String userName = WebFrameworkUtils.getLoginUserName();
        if (Objects.nonNull(userName) && Objects.isNull(modifier)) {
            setFieldValByName("updateUserName", userName, metaObject);
        }
    }
}
