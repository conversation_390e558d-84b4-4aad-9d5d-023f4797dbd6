package com.dexpo.framework.cache.redis.entity.exhibition;

import lombok.Data;

/**
 * 展会tag信息缓存对象
 */
@Data
public class ExhibitionTagInfoCache {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 展会tag名称
     */
    private String exhibitionTagName;

    /**
     * 展会tag编码
     */
    private String exhibitionTagCode;

    /**
     * 展会taglogo
     */
    private String exhibitionTagLogo;

    /**
     * 展会tag等级
     */
    private Integer exhibitionTagLevel;

    /**
     * 父展会tag编码
     */
    private String parentExhibitionTagCode;
} 