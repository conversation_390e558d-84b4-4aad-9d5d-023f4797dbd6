package com.dexpo.framework.cache.redis.constant;

/**
 * exhibition-service redis key
 */
public class ExhibitionRedisKey extends ICacheKey {
    private ExhibitionRedisKey(){

    }


    /**
     * exhibition schedule cache key
     */
    public static final String EXHIBITION_INFO_SCHEDULE_BY_CODE = "exhibition:info:schedule:code";


    /**
     * 展会协议信息缓存 - 按展会编码
     */
    public static final String EXHIBITION_AGREEMENT_ALL = "exhibition:agreement:all";


    /**
     * 协议内容缓存 英文
     * 参数：id
     */
    public static final String EXHIBITION_AGREEMENT_CONTENT_EN = "exhibition:agreement:content:en";

    /**
     * 协议内容缓存 中文
     * 参数：id
     */
    public static final String EXHIBITION_AGREEMENT_CONTENT_CN = "exhibition:agreement:content:cn";

    /**
     * 展会信息缓存全量
     */
    public static final String EXHIBITION_INFO_ALL = "exhibition:info:all";


    /**
     * 展会信息缓存 根据id 缓存
     */
    public static final String EXHIBITION_INFO_BY_ID = "exhibition:info:id";

    /**
     * 展会信息缓存 根据code 缓存
     */
    public static final String EXHIBITION_INFO_BY_CODE = "exhibition:info:code";

    /**
     * 展会信息缓存 直接提供外部接口的缓存 BFF层使用
     */
    public static final String EXHIBITION_INFO_VO_BY_TAGCODE = "exhibition:info:vo:tagcode";

    /**
     * 展会信息缓存 直接提供外部接口的缓存 BFF层使用 by id
     */
    public static final String EXHIBITION_INFO_VO_BY_ID = "exhibition:info:vo:id";


    /**
     * 展会tag信息 全缓存
     */
    public static final String EXHIBITION_TAG_INFO_ALL = "exhibition:tag:info:all";

}
