package com.dexpo.framework.cache.redis.entity.exhibition;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * 展会注册时间安排缓存对象
 */
@Data
public class ExhibitionInfoCache {

    /**
     * id
     */
    private Long id;

    /**
     * 展会CODE
     */
    private String exhibitionCode;

    /**
     * 展会名称-中文
     */
    private String exhibitionNameCn;

    /**
     * 展会名称-英文
     */
    private String exhibitionNameEn;

    /**
     * 年份：如 2025
     */
    private Integer exhibitionYear;

    /**
     * 展会届数：如 1
     */
    private Integer exhibitionSession;

    /**
     * 展会年份和届数组成的唯一key，如2025_1
     */
    private String exhibitionSessionKey;

    /**
     * 展会标签
     */
    private String exhibitionTagCode;

    /**
     * 展会状态
     */
    private String exhibitionStatus;

    /**
     * 展会LOGO
     */
    private String exhibitionLogo;

    /**
     * 展会开始时间
     */
    private LocalDateTime exhibitionBeginTime;

    /**
     * 展会结束时间
     */
    private LocalDateTime exhibitionEndTime;
}