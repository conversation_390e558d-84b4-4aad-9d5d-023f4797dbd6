package com.dexpo.framework.cache.redis.operate.exhibition;

import java.util.List;

import org.springframework.stereotype.Component;

import com.dexpo.framework.cache.redis.constant.ExhibitionRedisKey;
import com.dexpo.framework.cache.redis.constant.ICacheKey;
import com.dexpo.framework.cache.redis.entity.exhibition.ExhibitionInfoCache;
import com.dexpo.framework.cache.redis.service.RedisService;

import lombok.RequiredArgsConstructor;

/**
 * base service 验证码code 操作
 */
@Component
@RequiredArgsConstructor
public class ExhibitionInfoCacheOpt {

    private final RedisService redisService;

    /**
     * @return true- valid success，false- valid failed
     */
    public List<ExhibitionInfoCache> getExhibitionInfoCache() {

        return redisService.getCacheObject(ExhibitionRedisKey.EXHIBITION_INFO_ALL);
    }

    public ExhibitionInfoCache getExhibitionInfoCacheById(Long exhibitionId) {

        String key = ICacheKey.generateKey(ExhibitionRedisKey.EXHIBITION_INFO_BY_ID,
                String.valueOf(exhibitionId));
        return redisService.getCacheObject(key);
    }
}
