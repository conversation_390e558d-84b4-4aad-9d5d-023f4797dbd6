package com.dexpo.framework.cache.redis.entity.exhibition;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 展会注册时间安排缓存对象
 */
@Data
public class ExhibitionRegisterScheduleCache {

    private Long id;
    private String exhibitionCode;
    private String exhibitionTagCode;
    private String memberType;
    private LocalDateTime registerBeginTime;
    private LocalDateTime registerEndTime;
} 