package com.dexpo.framework.cache.redis.operate.member;


import com.dexpo.framework.cache.redis.constant.MemberRedisKey;
import com.dexpo.framework.cache.redis.constant.ICacheKey;
import com.dexpo.framework.cache.redis.entity.audience.AudienceBaseInfoCache;
import com.dexpo.framework.cache.redis.entity.MemberBaseInfoCache;
import com.dexpo.framework.cache.redis.entity.sponsor.SponsorProfileCache;
import com.dexpo.framework.cache.redis.service.RedisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 用户缓存信息相关操作类
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class MemberBaseInfoOpt {

    private final RedisService redisService;

    public void setMemberBaseInfoCache(MemberBaseInfoCache baseInfo,String loginTool) {
        //将新用户写到redis中
        String userInfoKey  = ICacheKey.generateKey(MemberRedisKey.MEMBER_MEDIA_INFO,loginTool);
        log.info("MemberBaseInfoOpt setMemberBaseInfoCache add new user to redis key={},value={}",userInfoKey,baseInfo);
        redisService.setCacheObject(userInfoKey, baseInfo);
    }

    public MemberBaseInfoCache getMemberBaseInfoCache(String loginTool) {
        String userInfoKey  = ICacheKey.generateKey(MemberRedisKey.MEMBER_MEDIA_INFO,loginTool);
        MemberBaseInfoCache baseInfoCache = redisService.getCacheObject(userInfoKey);
        log.info("MemberBaseInfoOpt getMemberBaseInfoCache get user from redis key={},value={}",userInfoKey,baseInfoCache);
        return baseInfoCache;
    }


    public void setAudienceBaseInfoCache(AudienceBaseInfoCache baseInfo, String loginTool) {
        //将新用户写到redis中
        String userInfoKey  = ICacheKey.generateKey(MemberRedisKey.MEMBER_AUDIENCE_INFO,loginTool);
        log.info("AudienceBaseInfoOpt setAudienceBaseInfoCache add new user to redis key={},value={}",userInfoKey,baseInfo);
        redisService.setCacheObject(userInfoKey, baseInfo);
    }

    public AudienceBaseInfoCache getAudienceBaseInfoCache(String loginTool) {
        String userInfoKey  = ICacheKey.generateKey(MemberRedisKey.MEMBER_AUDIENCE_INFO,loginTool);
        AudienceBaseInfoCache baseInfoCache = redisService.getCacheObject(userInfoKey);
        log.info("AudienceBaseInfoOpt getAudienceBaseInfoCache get user from redis key={},value={}",userInfoKey,baseInfoCache);
        return baseInfoCache;
    }


    /**
     * 获取 运营人员登录的缓存
     *
     * @param id id
     */
    public SponsorProfileCache sponsorProfile(Long id) {
        String key = ICacheKey.generateKey(MemberRedisKey.MEMBER_SPONSOR_USER_INFO, String.valueOf(id));
        return redisService.getCacheObject(key);
    }
}
