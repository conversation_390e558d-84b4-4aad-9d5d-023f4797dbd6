package com.dexpo.framework.cache.redis.constant;


/**
 * Member-Service redis key
 */
public class MemberRedisKey extends  ICacheKey{

    private MemberRedisKey(){

    }

    /**
     * token缓存的key 每次登陆时缓存 服务名:业务:命名
     */
    public static final String MEMBER_LOGIN_TOKEN = "member:login:token";

    /**
     * 媒体用户信息缓存的key
     */
    public static final String MEMBER_MEDIA_INFO = "member:media:info";

    /**
     * 观众用户缓存key
     */
    public static final String MEMBER_AUDIENCE_INFO = "member:audience:info";

    /**
     * 后台管理用户
     * sponsor user profile
     */
    public static final String MEMBER_SPONSOR_USER_INFO = "member:sponsor:profile";
}
