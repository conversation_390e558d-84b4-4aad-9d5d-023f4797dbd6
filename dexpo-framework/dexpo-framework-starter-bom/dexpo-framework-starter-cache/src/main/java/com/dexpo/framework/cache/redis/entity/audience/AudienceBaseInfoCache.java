package com.dexpo.framework.cache.redis.entity.audience;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * 会员基本信息数据对象
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AudienceBaseInfoCache {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 观众编码
     */
    private String audienceCode;

    /**
     * 观众姓名
     */
    private String audienceName;

    /**
     * 观众英文名
     */
    private String audienceFirstName;

    /**
     * 观众英文姓
     */
    private String audienceLastName;

    /**
     * 观众性别
     */
    private String audienceGender;

    /**
     * 观众手机号
     */
    private String audienceMobile;

    /**
     * 观众邮箱
     */
    private String audienceEmail;

    /**
     * 证件类型
     */
    private String idCategory;

    /**
     * 证件号码
     */
    private String idNumber;

    /**
     * 国家CODE
     */
    private String countryCode;

    /**
     * 国家名称-中文
     */
    private String countryNameCn;

    /**
     * 国家名称-英文
     */
    private String countryNameEn;

    /**
     * 居住地所在省CODE
     */
    private String currentHomeProvinceCode;

    /**
     * 居住地所在省名称
     */
    private String currentHomeProvinceName;

    /**
     * 居住地所在市CODE
     */
    private String currentHomeCityCode;

    /**
     * 居住地所在市名称
     */
    private String currentHomeCityName;

    /**
     * 居住地所在区CODE
     */
    private String currentHomeDistrictCode;

    /**
     * 居住地所在区名称
     */
    private String currentHomeDistrictName;

    /**
     * 居住地详细地址
     */
    private String currentHomeDetailAddress;

    /**
     * 单位名称
     */
    private String enterpriseName;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 职位名称
     */
    private String positionName;




   
} 