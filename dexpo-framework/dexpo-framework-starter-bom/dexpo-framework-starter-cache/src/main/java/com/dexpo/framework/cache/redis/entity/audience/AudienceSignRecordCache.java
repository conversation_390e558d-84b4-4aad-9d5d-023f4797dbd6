package com.dexpo.framework.cache.redis.entity.audience;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AudienceSignRecordCache {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 观众ID
     */
    private Long audienceId;
    
    /**
     * 协议ID
     */
    private Long agreementId;
    
    /**
     * 签署时间
     */
    private LocalDateTime signTime;
    
    /**
     * 是否有效
     */
    private Boolean isEffect;
    
    
    
    




}
