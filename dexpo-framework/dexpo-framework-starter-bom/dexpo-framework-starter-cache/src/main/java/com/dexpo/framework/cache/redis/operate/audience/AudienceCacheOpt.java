package com.dexpo.framework.cache.redis.operate.audience;

import com.dexpo.framework.cache.redis.constant.AudienceRedisKey;
import com.dexpo.framework.cache.redis.constant.ICacheKey;
import com.dexpo.framework.cache.redis.entity.audience.AudienceParticipateRecordCache;
import com.dexpo.framework.cache.redis.service.RedisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: Maojie
 * @CreateTime: 2025-06-24
 * @Description: 观众相关缓存操作类
 */

@Component
@RequiredArgsConstructor
@Slf4j
public class AudienceCacheOpt {

    private final RedisService redisService;


    public void setAudienceParticipateRecordCache(AudienceParticipateRecordCache audienceParticipateRecordCache){
        String key = ICacheKey.generateKey(AudienceRedisKey.AUDIENCE_PARTICIPATE_RECORD_INFO, audienceParticipateRecordCache.getAudienceId().toString(),audienceParticipateRecordCache.getExhibitionId().toString());
        redisService.setCacheObject(key,audienceParticipateRecordCache);
        log.info("AudienceCacheOpt setAudienceParticipateRecordCache add record to redis key={},value={}",key);
    }

    public AudienceParticipateRecordCache getAudienceParticipateRecordCache(Long audienceId, Long exhibitionId){
        String key = ICacheKey.generateKey(AudienceRedisKey.AUDIENCE_PARTICIPATE_RECORD_INFO, audienceId.toString(),exhibitionId.toString());
        log.info("AudienceCacheOpt setAudienceParticipateRecordCache get record from redis key={}",key);
        return redisService.getCacheObject(key);
    }

    public void setAudienceSignRecordCache(Long audienceId, Long agreementId){
        String key = ICacheKey.generateKey(AudienceRedisKey.AUDIENCE_AGREEMENT_SIGN_INFO, audienceId.toString(),agreementId.toString());
        log.info("AudienceCacheOpt setAudienceSignRecordCache set record to redis key={},value={}",key);
        redisService.setCacheObject(key, audienceId);
    }

    public Long getAudienceSignRecordCache(Long audienceId, Long agreementId){
        String key = ICacheKey.generateKey(AudienceRedisKey.AUDIENCE_AGREEMENT_SIGN_INFO, audienceId.toString(),agreementId.toString());
        log.info("AudienceCacheOpt getAudienceSignRecordCache get record from redis key={}",key);
        return redisService.getCacheObject(key);
    }


}
