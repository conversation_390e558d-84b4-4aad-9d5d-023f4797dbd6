package com.dexpo.framework.cache.redis.constant;

public abstract class ICacheKey {

    ICacheKey() {
    }

    /**
     * 分割符号
     */
    private static final String SPLIT = ":";


    /**
     * 组装key
     *
     * @param key   base key
     * @param value value
     * @return full key
     */
    public static String generateKey(String key, String value) {
        return key + SPLIT + value;
    }




    /**
     * 组装key
     *
     * @param key   base key
     * @param values value
     * @return full key
     */
    public static String generateKey(String key, String... values) {
        return key + SPLIT + String.join(SPLIT, values);
    }
}
