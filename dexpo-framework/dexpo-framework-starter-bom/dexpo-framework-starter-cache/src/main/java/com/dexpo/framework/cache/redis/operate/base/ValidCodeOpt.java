package com.dexpo.framework.cache.redis.operate.base;

import com.dexpo.framework.cache.redis.constant.BasicRedisKey;
import com.dexpo.framework.cache.redis.constant.ICacheKey;
import com.dexpo.framework.cache.redis.service.RedisService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * base service 验证码code 操作
 */
@Component
@RequiredArgsConstructor
public class ValidCodeOpt {

    private final RedisService redisService;


    /**
     * 验证码 验证逻辑
     * 一次性，验证通过后会删除cache
     *
     * @param text mobile or email
     * @param code valid cdoe
     * @return true- valid success，false-  valid failed
     */
    public boolean validCode(String text, String code) {
        String key = ICacheKey.generateKey(BasicRedisKey.BASIC_LOGIN_VALID_KEY, text);
        String cacheCode = redisService.getCacheObject(key);
        if (cacheCode == null || !Objects.equals(cacheCode, code)) {
            return false;
        }
        redisService.deleteObject(key);
        return true;

    }

}
