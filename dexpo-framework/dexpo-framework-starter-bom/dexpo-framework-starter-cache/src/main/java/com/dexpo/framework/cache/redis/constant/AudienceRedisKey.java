package com.dexpo.framework.cache.redis.constant;

/**
 * @Author: <PERSON><PERSON><PERSON>
 * @CreateTime: 2025-06-24
 * @Description: 观众基本缓存key 服务名:业务:命名
 */
public class AudienceRedisKey {

    private AudienceRedisKey(){
    }


    /**
     * 观众签署过的隐私协议缓存
     */
    public static final String AUDIENCE_AGREEMENT_SIGN_INFO = "audience:agreement:sign:info";

    /**
     * 观众参展记录缓存
     */
    public static final String AUDIENCE_PARTICIPATE_RECORD_INFO = "audience:participate:record:info";

}
