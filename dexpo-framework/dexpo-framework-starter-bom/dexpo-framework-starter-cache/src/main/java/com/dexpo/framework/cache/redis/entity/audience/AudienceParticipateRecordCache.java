package com.dexpo.framework.cache.redis.entity.audience;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AudienceParticipateRecordCache {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 观众ID
     */
    private Long audienceId;

    /**
     * 观众类型
     */
    private String audienceType;

    /**
     * 展会ID
     */
    private Long exhibitionId;

    /**
     * 唯一编码
     */
    private String participateCode;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 参展状态 值集VS_AUDIENCE_PARTICIPATE_STATUS
     */
    private String participateStatus;

    /**
     * 参展状态 名称
     */
    private String participateStatusName;

    /**
     * 注册系统
     */
    private String registerSystem;

    /**
     * 注册方式
     */
    private String registerMethod;

    /**
     * 注册来源
     */
    private String registerSource;

    /**
     * 注册时语言环境
     */
    private String registerLanguage;





}
