package com.dexpo.framework.cache.redis.util;

import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.exception.enums.ErrorCodeEnums;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Redis分布式锁工具类
 */
@Component
@Slf4j
public class RedisReentrantLockUtil {

    public static final String MESSAGE_PREFIX_STRING = "获取分布式锁被中断: ";

    /**
     * 尝试获取分布式锁。
     *
     * @param rLock 分布式锁对象，实现ReentrantLock接口。
     * @return boolean 如果成功获取锁返回true，否则返回false。
     * @throws RuntimeException 如果线程在等待锁时被中断，抛出此运行时异常。
     */
    public boolean tryLock(RLock rLock) {
        try {
            return tryLock(rLock, 10, 30, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error(ErrorCodeEnums.LOCK_GET_FAILED.getMsg(), e);
            throw new ServiceException(ErrorCodeEnums.LOCK_GET_FAILED.getCode(), ErrorCodeEnums.LOCK_GET_FAILED.getMsg());
        }
    }

    /**
     * 尝试获取分布式锁。
     *
     * @param rLock    分布式锁对象，实现ReentrantLock接口。
     * @param waitTime 获取锁的最大等待时间。
     * @param unit     等待时间的单位。
     * @return 如果成功获取锁返回true，否则返回false。
     * @throws RuntimeException 如果在尝试获取锁的过程中发生异常。
     */
    public boolean tryLock(RLock rLock, int waitTime, TimeUnit unit) {
        try {
            return rLock.tryLock(waitTime, unit);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new ServiceException(ErrorCodeEnums.LOCK_GET_FAILED.getCode(), MESSAGE_PREFIX_STRING + e.getMessage());
        } catch (Exception e) {
            log.error(ErrorCodeEnums.LOCK_GET_FAILED.getMsg(), e);
            throw new ServiceException(ErrorCodeEnums.LOCK_GET_FAILED.getCode(), ErrorCodeEnums.LOCK_GET_FAILED.getMsg());
        } finally {
            rLock.unlock();
        }
    }

    /**
     * 尝试获取分布式锁。
     *
     * @param rLock     分布式锁对象，实现ReentrantLock接口。
     * @param waitTime  获取锁的最大等待时间。
     * @param leaseTime 锁的持有时间。
     * @param unit      时间单位。
     * @return 如果成功获取锁返回true，否则返回false。
     * @throws RuntimeException 如果尝试获取锁时发生异常。
     */
    public boolean tryLock(RLock rLock, int waitTime, int leaseTime, TimeUnit unit) {
        try {
            return rLock.tryLock(waitTime, leaseTime, unit);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new ServiceException(ErrorCodeEnums.LOCK_GET_FAILED.getCode(), MESSAGE_PREFIX_STRING + e.getMessage());
        } catch (Exception e) {
            log.error(ErrorCodeEnums.LOCK_GET_FAILED.getMsg(), e);
            throw new ServiceException(ErrorCodeEnums.LOCK_GET_FAILED.getCode(), ErrorCodeEnums.LOCK_GET_FAILED.getMsg());
        } finally {
            rLock.unlock();
        }
    }

    /**
     * 尝试释放一个锁，并提供重试机制。
     *
     * @param lock       需要被释放的锁，类型为RLock，是可重入锁的接口。
     * @param retryCount 重试次数，表示如果第一次释放锁失败，将尝试再次释放的次数。
     * @return 返回一个布尔值，如果锁成功释放，则返回true；否则返回false。
     */
    public boolean unlock(RLock lock, int retryCount) {
        return releaseLockWithRetry(lock, retryCount);
    }

    /**
     * 尝试释放一个锁，并提供重试机制。
     *
     * @param lock       需要被释放的锁，类型为RLock，是可重入锁的接口。
     * @param retryCount 重试次数，表示如果第一次释放锁失败，将尝试再次释放的次数。
     * @return 返回一个布尔值，如果锁成功释放，则返回true；否则返回false。
     */
    private boolean releaseLockWithRetry(RLock lock, int retryCount) {
        if (retryCount <= 0) {
            return false;
        }
        try {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                return true;
            }
            Thread.sleep(100); // 简单延时后重试
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new ServiceException(ErrorCodeEnums.LOCK_GET_FAILED.getCode(), MESSAGE_PREFIX_STRING + e.getMessage());
        } catch (Exception e) {
            log.error("释放锁第:{}重试异常：", retryCount, e);
        }
        return releaseLockWithRetry(lock, retryCount - 1);
    }

}
