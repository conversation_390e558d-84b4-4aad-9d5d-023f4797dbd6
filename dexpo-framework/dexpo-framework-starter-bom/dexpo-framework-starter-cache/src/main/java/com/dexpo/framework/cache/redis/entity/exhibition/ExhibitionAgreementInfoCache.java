package com.dexpo.framework.cache.redis.entity.exhibition;

import lombok.Data;

import java.io.Serializable;

/**
 * 展会协议信息缓存实体
 */
@Data
public class ExhibitionAgreementInfoCache implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 展会编码
     */
    private String exhibitionCode;

    /**
     * 协议名称-中文
     */
    private String agreementNameCn;

    /**
     * 协议名称-英文
     */
    private String agreementNameEn;

    /**
     * 协议类型：值集VS_AGREEMENT_TYPE
     */
    private String agreementType;

    /**
     * 协议版本号
     */
    private Integer agreementVersion;


    /**
     * userType
     */
    private String memberType;


} 