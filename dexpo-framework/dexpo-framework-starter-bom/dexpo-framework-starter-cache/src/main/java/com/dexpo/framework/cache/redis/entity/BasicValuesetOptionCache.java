package com.dexpo.framework.cache.redis.entity;

import lombok.Data;

/**
 * 值集项 option cache
 */
@Data
public class BasicValuesetOptionCache {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 值集代码
     */
    private String valuesetCode;

    /**
     * 值集项代码
     */
    private String optionCode;

    /**
     * 值集项描述-中文
     */
    private String optionDescriptionCn;

    /**
     * 值集项描述-英文
     */
    private String optionDescriptionEn;

    /**
     * 顺序
     */
    private Integer optionOrder;

    /**
     * 父值集项ID
     */
    private String parentCode;
}
