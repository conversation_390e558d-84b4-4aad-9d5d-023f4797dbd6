package com.dexpo.framework.file.core.client.db;

import cn.hutool.extra.spring.SpringUtil;
import com.dexpo.framework.file.core.client.AbstractFileClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;

/**
 * 基于 DB 存储的文件客户端的配置类
 *
 * <AUTHOR>
 */
public class DBFileClient extends AbstractFileClient<DBFileClientConfigNew> {


    private static final Logger log = LoggerFactory.getLogger(DBFileClient.class);
    private DBFileContentFrameworkDAO dao;

    public DBFileClient(Long id, DBFileClientConfigNew config) {
        super(id, config);
    }

    @Override
    protected void doInit() {
        log.info("init DBFileClient");
    }

    @Override
    public String upload(byte[] content, String path, String type) {
        getDao().insert(getId(), path, content);
        // 拼接返回路径
        return super.formatFileUrl(config.getDomain(), path);
    }

    @Override
    public void delete(String path) {
        getDao().delete(getId(), path);
    }

    @Override
    public byte[] getContent(String path) {
        return getDao().selectContent(getId(), path);
    }

    @Override
    public List<Object> getListObjects(String prefix) {
        return Collections.emptyList();
    }

    private DBFileContentFrameworkDAO getDao() {
        // 延迟获取，因为 SpringUtil 初始化太慢
        if (dao == null) {
            dao = SpringUtil.getBean(DBFileContentFrameworkDAO.class);
        }
        return dao;
    }

}
