package com.dexpo.framework.file.core.client;

import com.dexpo.framework.file.core.enums.FileStorageEnum;

public interface FileClientFactory {

    /**
     * 获得文件客户端
     *
     * @param configId 配置编号
     * @return 文件客户端
     */
    FileClient getFileClient(Long configId);

    /**
     * 获得文件客户端
     *
     * @param code 配置编码
     * @return 文件客户端
     */
    FileClient getFileClient(String code);

    /**
     * 创建文件客户端
     *
     * @param configId 配置编号
     * @param storage 存储器的枚举 {@link FileStorageEnum}
     * @param config 文件配置
     */
    <T extends FileClientConfig> void createOrUpdateFileClient(Long configId, Integer storage, T config,String code);

}
