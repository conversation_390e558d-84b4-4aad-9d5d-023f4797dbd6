package com.dexpo.framework.file.core.client;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ReflectUtil;
import com.dexpo.framework.file.core.enums.FileStorageEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 文件客户端的工厂实现类
 *
 * <AUTHOR>
 */
@Slf4j
public class FileClientFactoryImpl implements FileClientFactory {

    /**
     * 文件客户端 Map
     * key：配置编号
     */
    private final ConcurrentMap<String, AbstractFileClient<?>> clients = new ConcurrentHashMap<>();
    @Override
    public FileClient getFileClient(Long configId) {
        AbstractFileClient<?> client = clients.get(configId.toString());
        if (client == null) {
            log.error("[getFileClient][配置编号({}) 找不到客户端]", configId);
        }
        return client;
    }

    @Override
    public FileClient getFileClient(String code) {
        AbstractFileClient<?> client = clients.get(code);
        if (client == null) {
            log.error("[getFileClient][配置编码({}) 找不到客户端]", code);
        }
        return client;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T extends FileClientConfig> void createOrUpdateFileClient(Long configId, Integer storage, T config,String code) {
        AbstractFileClient<T> client =  (AbstractFileClient<T>) clients.get(code);
        if (client == null) {
            client = this.createFileClient(configId, storage, config);
            client.init();
            clients.put(code, client);
        } else {
            client.refresh(config);
        }
    }

    @SuppressWarnings("unchecked")
    private <T extends FileClientConfig> AbstractFileClient<T> createFileClient(
            Long configId, Integer storage, T config) {
        FileStorageEnum storageEnum = FileStorageEnum.getByStorage(storage);
        Assert.notNull(storageEnum, String.format("文件配置(%s) 为空", storageEnum));
        // 创建客户端
        return (AbstractFileClient<T>) ReflectUtil.newInstance(storageEnum.getClientClass(),configId, config);
    }

}
