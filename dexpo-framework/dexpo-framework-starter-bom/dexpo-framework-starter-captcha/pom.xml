<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.dexpo</groupId>
        <artifactId>dexpo-framework-starter-bom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>dexpo-framework-starter-captcha</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>验证码拓展
        1. 基于 aj-captcha 实现滑块验证码，文档：https://ajcaptcha.beliefteam.cn/captcha-doc/
    </description>

    <dependencies>
        <!-- Spring 核心 -->
        <dependency>
            <groupId>com.xingyuv</groupId>
            <artifactId>spring-boot-starter-captcha-plus</artifactId>
        </dependency>
        <!-- Spring 核心 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-cache</artifactId>
        </dependency>

    </dependencies>

</project>
