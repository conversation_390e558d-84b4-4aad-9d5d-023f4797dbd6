package com.dexpo.system.api.token.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "RPC 服务 - OAuth2 访问令牌的信息 Response DTO")
@Data
@Accessors(chain = true)
public class DexpoAccessTokenRespDTO implements Serializable {

    @Schema(description = "访问令牌")
    private String accessToken;

    @Schema(description = "用户编号")
    private Long userId;

    @Schema(description = "用户类型，参见 UserTypeEnum 枚举", example = "1" )
    private Integer userType;

    @Schema(description = "过期时间")
    private LocalDateTime expiresTime;

}
