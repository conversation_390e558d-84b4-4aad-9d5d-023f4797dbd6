package com.dexpo.system.api.token.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Schema(description = "RPC 服务 - OAuth2 访问令牌的校验 Response DTO")
@Data
public class DexpoAccessTokenCheckRespDTO implements Serializable {

    @Schema(description = "用户编号")
    private Long userId;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "用户类型，参见 UserTypeEnum 枚举", example = "1")
    private Integer userType;

    @Schema(description = "授权范围的数组", example = "user_info")
    private List<String> scopes;

}
