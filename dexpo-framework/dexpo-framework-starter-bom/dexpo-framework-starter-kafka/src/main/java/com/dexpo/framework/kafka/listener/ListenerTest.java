package com.dexpo.framework.kafka.listener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@Slf4j
//@Component
public class ListenerTest {
    public ListenerTest(){
        log.info("ListenerTest initialized");
    }

    //    此处只是示例，在实际业务程序中，groupId  需要修改为自己业务相关的 groupid标识
    @KafkaListener(topics = "test-topic", groupId = "xxx_my_group", properties = "max.poll.records:100")
    public void onMessage(String message)
    {

        if (log.isInfoEnabled()) {
            log.info("收到消息：" + message);
        }

    }
}
