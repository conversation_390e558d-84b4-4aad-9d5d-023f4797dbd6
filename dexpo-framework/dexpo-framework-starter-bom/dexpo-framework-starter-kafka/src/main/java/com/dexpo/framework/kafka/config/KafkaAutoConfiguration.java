package com.dexpo.framework.kafka.config;

import com.dexpo.framework.kafka.producer.ProducerTool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

/**
 * 消息队列配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration
@Configuration
@Slf4j
public class KafkaAutoConfiguration {


    final
    KafkaTemplate<String,String> kafkaTemplate;

    public KafkaAutoConfiguration(KafkaTemplate<String, String> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }

    @Component
    public class ListenerTest {
        public ListenerTest(){
            log.info("ListenerTest initialized");
        }

        //    此处只是示例，在实际业务程序中，groupId  需要修改为自己业务相关的 groupid标识
        @KafkaListener(topics = "test-topic", groupId = "xxx_my_group", properties = "max.poll.records:100")
        public void onMessage(String message)
        {

            if (log.isInfoEnabled()) {
                log.info("收到消息：" + message);
            }

        }
    }

    @Bean
    public ProducerTool getProducerTool()
    {
        return new ProducerTool(kafkaTemplate);
    }


}
