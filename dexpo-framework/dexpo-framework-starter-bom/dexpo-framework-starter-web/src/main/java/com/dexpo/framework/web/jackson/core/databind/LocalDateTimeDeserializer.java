package com.dexpo.framework.web.jackson.core.databind;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * LocalDateTime反序列化规则
 * <p>
 * 支持毫秒级时间戳和字符串格式的时间反序列化为LocalDateTime
 * 字符串格式支持：yyyy-MM-dd HH:mm:ss
 */
public class LocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {

    public static final LocalDateTimeDeserializer INSTANCE = new LocalDateTimeDeserializer();
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getValueAsString();
        
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        
        // 尝试解析为字符串格式的时间
        try {
            return LocalDateTime.parse(value, FORMATTER);
        } catch (DateTimeParseException e) {
            // 如果字符串解析失败，尝试解析为时间戳
            try {
                long timestamp = Long.parseLong(value);
                return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
            } catch (NumberFormatException ex) {
                throw new IOException("无法解析时间格式: " + value + "，支持格式: yyyy-MM-dd HH:mm:ss 或时间戳", e);
            }
        }
    }
}
