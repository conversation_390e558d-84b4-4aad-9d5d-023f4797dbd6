package com.dexpo.framework.web.apilog.core.service;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;

/**
 * API 错误日志 Framework Service 实现类
 *
 * 基于 kafka消息，记录错误日志
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
public class ApiErrorLogFrameworkServiceImpl implements ApiErrorLogFrameworkService {



    @Override
    @Async
    public void createApiErrorLog(ApiErrorLog apiErrorLog) {
        // 发送kafka消息实现API日志的记录
        log.debug("发送API日志被调用，TODO改造为kafka消息");
    }

}
