package com.dexpo.framework.web.apilog.config;

import com.dexpo.framework.web.apilog.core.filter.ApiAccessLogFilter;
import com.dexpo.framework.web.apilog.core.service.ApiAccessLogFrameworkService;
import com.dexpo.framework.web.apilog.core.service.ApiAccessLogFrameworkServiceImpl;
import com.dexpo.framework.web.apilog.core.service.ApiErrorLogFrameworkService;
import com.dexpo.framework.web.apilog.core.service.ApiErrorLogFrameworkServiceImpl;
import com.dexpo.framework.common.enums.WebFilterOrderEnum;
import com.dexpo.framework.web.web.config.WebAutoConfiguration;
import com.dexpo.framework.web.web.config.WebProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;

import jakarta.servlet.Filter;

/**
 * <AUTHOR>
 */
@Slf4j
@AutoConfiguration(after = WebAutoConfiguration.class)
public class ApiLogAutoConfiguration {

    @Bean
    public ApiAccessLogFrameworkService apiAccessLogFrameworkService() {
        return new ApiAccessLogFrameworkServiceImpl();
    }

    @Bean
    public ApiErrorLogFrameworkService apiErrorLogFrameworkService() {
        return new ApiErrorLogFrameworkServiceImpl();
    }

    /**
     * 创建 ApiAccessLogFilter Bean，记录 API 请求日志
     */
    @Bean
    @ConditionalOnProperty(prefix = "dexpo.access-log", value = "enable", matchIfMissing = true) // 允许使用 dtt.access-log.enable=false 禁用访问日志
    public FilterRegistrationBean<ApiAccessLogFilter> apiAccessLogFilter(WebProperties webProperties,
                                                                         @Value("${spring.application.name}") String applicationName,
                                                                         ApiAccessLogFrameworkService apiAccessLogFrameworkService) {
        log.debug("apiAccessLogFilter init,dexpo.access-log.enable=true");
        ApiAccessLogFilter filter = new ApiAccessLogFilter(webProperties, applicationName, apiAccessLogFrameworkService);
        return createFilterBean(filter, WebFilterOrderEnum.API_ACCESS_LOG_FILTER.getCode());
    }

    private static <T extends Filter> FilterRegistrationBean<T> createFilterBean(T filter, Integer order) {
        FilterRegistrationBean<T> bean = new FilterRegistrationBean<>(filter);
        bean.setOrder(order);
        return bean;
    }

}
