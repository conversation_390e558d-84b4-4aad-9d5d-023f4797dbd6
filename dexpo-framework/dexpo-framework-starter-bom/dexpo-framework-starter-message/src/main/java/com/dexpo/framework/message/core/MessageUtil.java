package com.dexpo.framework.message.core;

import com.dexpo.framework.common.util.validation.ValidationUtils;
import com.dexpo.framework.message.enums.MessageTypeEnum;

/**
 * 消息类型工具
 */
public class MessageUtil {

    private MessageUtil(){
        throw new IllegalStateException("Utility class");
    }
    /**
     * 获取消息类型
     *
     * @param receiver 消息接收人
     * @return messageType
     */
    public static MessageTypeEnum getMessageType(String receiver) {
        if (ValidationUtils.isEmail(receiver)) {
            return MessageTypeEnum.EMAIL;
        }
        if (ValidationUtils.isMobile(receiver)) {
            return MessageTypeEnum.SMS;
        }
        return null;
    }

}
