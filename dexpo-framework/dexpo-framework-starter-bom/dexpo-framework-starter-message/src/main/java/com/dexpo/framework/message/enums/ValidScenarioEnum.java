package com.dexpo.framework.message.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 验证码的场景
 */
@Getter
public enum ValidScenarioEnum {

    /**
     * 媒体注册登录
     */
    MEDIA_LOGIN("MEDIA_LOGIN", "MEDIA_LOGIN_TEMPLATE", "媒体注册登录"),

    /**
     * 运营人员登录
     */
    SPONSOR_LOGIN("SPONSOR_LOGIN", "SPONSOR_LOGIN_TEMPLATE", "运营人员登录"),

    /**
     * 媒体审核通过
     */
    MEDIA_AUDIT_PASS("MEDIA_AUDIT_PASS", "MEDIA_AUDIT_PASS_TEMPLATE", "媒体审核通过"),

    /**
     * 媒体审核驳回
     */
    MEDIA_AUDIT_REJECT("MEDIA_AUDIT_REJECT", "MEDIA_AUDIT_REJECT_TEMPLATE", "媒体审核驳回"),

    /**
     * 媒体代注册
     */
    MEDIA_PROXY_REGISTER("MEDIA_PROXY_REGISTER", "MEDIA_PROXY_REGISTER_TEMPLATE", "媒体代注册"),

    /**
     * 观众代注册
     */
    AUDIENCE_PROXY_REGISTER("AUDIENCE_PROXY_REGISTER", "AUDIENCE_PROXY_REGISTER_TEMPLATE", "观众代注册");


    /**
     * 场景code
     */
    private String code;

    /**
     * 模板编号
     */
    private String template;

    /**
     * 描述
     */
    private String desc;

    ValidScenarioEnum(String code, String template, String desc) {
        this.code = code;
        this.template = template;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     */
    public static ValidScenarioEnum getByCode(String code) {
        return Arrays.stream(ValidScenarioEnum.values())
                .filter(item -> item.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
