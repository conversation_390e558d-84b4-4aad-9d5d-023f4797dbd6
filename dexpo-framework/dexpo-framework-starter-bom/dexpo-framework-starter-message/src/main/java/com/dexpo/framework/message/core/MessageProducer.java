package com.dexpo.framework.message.core;

import com.alibaba.fastjson2.JSON;
import com.dexpo.framework.cache.redis.constant.BasicRedisKey;
import com.dexpo.framework.cache.redis.constant.ICacheKey;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.exception.enums.IntegrationServiceErrorCodeEnum;
import com.dexpo.framework.message.MessageConstant;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class MessageProducer {

    @Resource
    private StreamBridge streamBridge;

    @Resource
    private RedisService redisService;

    /**
     * 消息发送
     *
     * @param content
     */
    public void smsChannel(SmsMessageDTO content) {
        try {
            log.info("smsChannel: {}", JSON.toJSONString(content));
            streamBridge.send("smsChannel-out-0", content);
        } catch (Exception e) {
            log.error("smsChannel send error:{}", JSON.toJSONString(content));
            throw new ServiceException(IntegrationServiceErrorCodeEnum.VALID_CODE_SEND_LIMIT);
        }
    }

    /**
     * 邮箱消息发送
     *
     * @param content
     */
    public void emailChannel(EmailMessageDTO content) {
        try {
            log.info("emailChannel: {}", JSON.toJSONString(content));
            streamBridge.send("emailChannel-out-0", content);
        } catch (Exception e) {
            log.error("emailChannel send error:{}", JSON.toJSONString(content));
            throw new ServiceException(IntegrationServiceErrorCodeEnum.VALID_CODE_SEND_LIMIT);
        }
    }


    /**
     * 发送消息的前置检查
     *
     * @param mobile 手机号码
     */
    public void smsSendCheck(String mobile) {
        String limitKey = ICacheKey.generateKey(BasicRedisKey.BASIC_LOGIN_SMS_LIMIT_KEY, mobile);
        boolean limitCheck = redisService.setNx(limitKey, 1, MessageConstant.DEFAULT_LOGIN_CODE_LIMIT_TIME, TimeUnit.SECONDS);
        if (!limitCheck) {
            throw new ServiceException(IntegrationServiceErrorCodeEnum.VALID_CODE_SEND_LIMIT);
        }
    }


}
