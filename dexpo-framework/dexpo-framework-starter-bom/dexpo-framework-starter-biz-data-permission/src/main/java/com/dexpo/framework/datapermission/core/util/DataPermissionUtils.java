package com.dexpo.framework.datapermission.core.util;

import com.dexpo.framework.datapermission.core.annotation.DataPermission;
import com.dexpo.framework.datapermission.core.aop.DataPermissionContextHolder;
import lombok.SneakyThrows;

/**
 * 数据权限 Util
 *
 * <AUTHOR>
 */
public class DataPermissionUtils {
    private DataPermissionUtils() {
        throw new IllegalStateException("Utility class");
    }

    private static DataPermission dataPermissionDisable;

    @DataPermission(enable = false)
    @SneakyThrows
    private static DataPermission getDisableDataPermissionDisable() {
        if (dataPermissionDisable == null) {
            dataPermissionDisable = DataPermissionUtils.class
                    .getDeclaredMethod("getDisableDataPermissionDisable")
                    .getAnnotation(DataPermission.class);
        }
        return dataPermissionDisable;
    }

    /**
     * 忽略数据权限，执行对应的逻辑
     *
     * @param runnable 逻辑
     */
    public static void executeIgnore(Runnable runnable) {
        DataPermission dataPermission = getDisableDataPermissionDisable();
        DataPermissionContextHolder.add(dataPermission);
        try {
            // 执行 runnable
            runnable.run();
        } finally {
            DataPermissionContextHolder.remove();
        }
    }

}
