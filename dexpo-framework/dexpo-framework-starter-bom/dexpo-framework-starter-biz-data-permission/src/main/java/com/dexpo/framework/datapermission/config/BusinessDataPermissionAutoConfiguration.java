package com.dexpo.framework.datapermission.config;

import cn.hutool.extra.spring.SpringUtil;
import com.dexpo.framework.datapermission.core.rule.business.BusinessDataPermissionRule;
import com.dexpo.framework.datapermission.core.rule.business.BusinessDataPermissionRuleCustomizer;
import com.dexpo.system.api.permission.PermissionApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;

import java.util.List;

/**
 * 基于业务线的数据权限 AutoConfiguration
 *
 * <AUTHOR>
 */
@Slf4j
@AutoConfiguration
//@ConditionalOnClass(LoginUser.class)
@ConditionalOnBean(value = {PermissionApi.class, BusinessDataPermissionRuleCustomizer.class})
public class BusinessDataPermissionAutoConfiguration {


    @Bean
    public BusinessDataPermissionRule businessDataPermissionRule(PermissionApi permissionApi,
                                                                 List<BusinessDataPermissionRuleCustomizer> customizers) {
        log.info("初始化bean：BusinessDataPermissionRule");
        // Cloud 专属逻辑：优先使用本地的 PermissionApi 实现类，而不是 Feign 调用
        // 原因：在创建租户时，租户还没创建好，导致 Feign 调用获取数据权限时，报“租户不存在”的错误
        try {
            PermissionApi permissionApiImpl = SpringUtil.getBean("permissionApiImpl", PermissionApi.class);
            if (permissionApiImpl != null) {
                permissionApi = permissionApiImpl;
            }
        } catch (Exception ignored) {
            log.error("businessDataPermissionRule {}", ignored.getMessage());
        }
        // 创建 DeptDataPermissionRule 对象
        BusinessDataPermissionRule rule = new BusinessDataPermissionRule(permissionApi);
        log.info("BusinessDataPermissionRule bean instance:" + rule);
        // 补全表配置
        customizers.forEach(customizer -> customizer.customize(rule));
        return rule;
    }

}
