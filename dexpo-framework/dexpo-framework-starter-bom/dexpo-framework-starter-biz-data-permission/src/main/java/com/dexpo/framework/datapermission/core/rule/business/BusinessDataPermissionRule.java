package com.dexpo.framework.datapermission.core.rule.business;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.dexpo.framework.common.enums.ValueSetUserTypeEnum;
import com.dexpo.framework.common.util.collection.CollectionUtils;
import com.dexpo.framework.common.util.json.JsonUtils;
import com.dexpo.framework.datapermission.core.rule.DataPermissionRule;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import com.dexpo.framework.mybatis.core.util.MyBatisUtils;
import com.dexpo.framework.security.core.LoginUser;
import com.dexpo.framework.security.core.util.SecurityFrameworkUtils;
import com.dexpo.system.api.permission.PermissionApi;
import com.dexpo.system.api.permission.dto.BusinessDataPermissionRespDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.*;
import net.sf.jsqlparser.expression.operators.conditional.OrExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import net.sf.jsqlparser.expression.operators.relational.ParenthesedExpressionList;
import org.springframework.boot.autoconfigure.AutoConfiguration;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 基于业务线的 {@link DataPermissionRule} 数据权限规则实现
 *
 * 注意，使用 BusinessDataPermissionRule 时，需要保证表中有 business_code 业务线编号的字段，可自定义。
 *
 * <AUTHOR>
 */
@AutoConfiguration
@AllArgsConstructor
@Slf4j
public class BusinessDataPermissionRule implements DataPermissionRule {

    /**
     * LoginUser 的 Context 缓存 Key
     */
    protected static final String CONTEXT_KEY = BusinessDataPermissionRule.class.getSimpleName();
    /**
     * 业务线编码字段
     */
    private static final String BUSINESS_COLUMN_NAME = "business_code";
    /**
     * 创建人username字段
     */
    private static final String USER_COLUMN_NAME = "created_by";

    static final Expression EXPRESSION_NULL = new NullValue();

    /**
     * system权限API
     */
    private final PermissionApi permissionApi;

    /**
     * 基于业务线的表字段配置
     * 一般情况下，每个表的业务线编号字段是 business_code，通过该配置自定义。
     *
     * key：表名
     * value：字段名
     */
    private final Map<String, String> businessColumns = new HashMap<>();
    /**
     * 基于用户的表字段配置
     * 一般情况下，每个表的业务线编号字段是 business_code，通过该配置自定义。
     *
     * key：表名
     * value：字段名
     */
    private final Map<String, String> userColumns = new HashMap<>();
    /**
     * 所有表名，是 {@link #businessColumns} 和 {@link #userColumns} 的合集
     */
    private final Set<String> tableNames = new HashSet<>();

    @Override
    public Set<String> getTableNames() {
        return tableNames;
    }

    @Override
    public Expression getExpression(String tableName, Alias tableAlias) {
        // 只有有登陆用户的情况下，才进行数据权限的处理
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser == null) {
            return null;
        }
        // 只有管理员类型的用户，才进行数据权限的处理
        if (ObjectUtil.notEqual(loginUser.getUserType(), ValueSetUserTypeEnum.SPONSOR.getCode())) {
            return null;
        }

        // 获得数据权限
        BusinessDataPermissionRespDTO businessDataPermission = loginUser.getContext(CONTEXT_KEY, BusinessDataPermissionRespDTO.class);
        // 从上下文中拿不到，则调用逻辑进行获取
        if (businessDataPermission == null) {
            businessDataPermission = permissionApi.getBusinessDataPermission(loginUser.getId()).getData();
            if (businessDataPermission == null) {
                log.error("[getExpression][LoginUser({}) 获取数据权限为 null]", JsonUtils.toJsonString(loginUser));
                throw new NullPointerException(String.format("LoginUser(%d) Table(%s/%s) 未返回数据权限",
                        loginUser.getId(), tableName, tableAlias.getName()));
            }
            // 添加到上下文中，避免重复计算
            loginUser.setContext(CONTEXT_KEY, businessDataPermission);
        }

        // 情况一，如果是 ALL 可查看全部，则无需拼接条件
        if (Boolean.TRUE.equals(businessDataPermission.getAll())) {
            return null;
        }

        // 情况二，即不能查看业务线，又不能查看自己，则说明 100% 无权限
        if (CollUtil.isEmpty(businessDataPermission.getBusinessCodesSet())
            && Boolean.FALSE.equals(businessDataPermission.getSelf())) {
            // WHERE null = null，可以保证返回的数据为空
            return new EqualsTo(null, null);
        }

        // 情况三，拼接 BusinessCode 和 User 的条件，最后组合
        Expression businessExpression = buildBusinessExpression(tableName,tableAlias, businessDataPermission.getBusinessCodesSet());
        Expression userExpression = buildUserExpression(tableName, tableAlias, businessDataPermission.getSelf(), loginUser.getId());
        if (businessExpression == null && userExpression == null) {
            // 获得不到条件的时候，暂时不抛出异常，而是不返回数据
            log.warn("[getExpression][LoginUser({}) Table({}/{}) DeptDataPermission({}) 构建的条件为空]",
                    JsonUtils.toJsonString(loginUser), tableName, tableAlias, JsonUtils.toJsonString(businessDataPermission));
            return EXPRESSION_NULL;
        }
        if (businessExpression == null) {
            return userExpression;
        }
        if (userExpression == null) {
            return businessExpression;
        }
        // 目前，如果有指定业务线 + 可查看自己，采用 OR 条件。即，WHERE (business_code IN ? OR created_by = ?)
        return new ParenthesedExpressionList<>(new OrExpression(businessExpression, userExpression));
    }

    private Expression buildBusinessExpression(String tableName, Alias tableAlias, Set<String> businessCodes) {
        // 如果不存在配置，则无需作为条件
        String columnName = businessColumns.get(tableName);
        if (CharSequenceUtil.isEmpty(columnName)) {
            return null;
        }
        // 如果为空，则无条件
        if (CollUtil.isEmpty(businessCodes)) {
            return null;
        }
        // 拼接条件
        return new InExpression(MyBatisUtils.buildColumn(tableName, tableAlias, columnName),
                new ExpressionList<>(CollectionUtils.convertList(businessCodes, StringValue::new)));
    }


    private Expression buildUserExpression(String tableName, Alias tableAlias, Boolean self, Long userId) {
        // 如果不查看自己，则无需作为条件
        if (Boolean.FALSE.equals(self)) {
            return null;
        }
        String columnName = userColumns.get(tableName);
        if (CharSequenceUtil.isEmpty(columnName)) {
            return null;
        }
        // 拼接条件
        return new EqualsTo(MyBatisUtils.buildColumn(tableName, tableAlias, columnName), new LongValue(userId));
    }

    // ==================== 添加配置 ====================

    public void addBusinessColumn(Class<? extends BaseDO> entityClass) {
        addBusinessColumn(entityClass, BUSINESS_COLUMN_NAME);
    }

    public void addBusinessColumn(Class<? extends BaseDO> entityClass, String columnName) {
        String tableName = TableInfoHelper.getTableInfo(entityClass).getTableName();
        addBusinessColumn(tableName, columnName);
    }

    public void addBusinessColumn(String tableName, String columnName) {
        businessColumns.put(tableName, columnName);
        tableNames.add(tableName);
    }

    public void addUserColumn(Class<? extends BaseDO> entityClass) {
        addUserColumn(entityClass, USER_COLUMN_NAME);
    }

    public void addUserColumn(Class<? extends BaseDO> entityClass, String columnName) {
        String tableName = TableInfoHelper.getTableInfo(entityClass).getTableName();
        addUserColumn(tableName, columnName);
    }

    public void addUserColumn(String tableName, String columnName) {
        userColumns.put(tableName, columnName);
        tableNames.add(tableName);
    }

}
