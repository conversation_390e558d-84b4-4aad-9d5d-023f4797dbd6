package com.dexpo.framework.datapermission.core.rule.business;

/**
 * {@link BusinessDataPermissionRule} 的自定义配置接口
 *
 * <AUTHOR>
 */
@FunctionalInterface
public interface BusinessDataPermissionRuleCustomizer {

    /**
     * 自定义该权限规则
     * 1. 调用 {@link BusinessDataPermissionRule#addBusinessColumn(Class, String)} 方法，配置基于 business_code 的过滤规则
     * 2. 调用 {@link BusinessDataPermissionRule#addUserColumn(Class, String)} 方法，配置基于 created_by 的过滤规则
     *
     * @param rule 权限规则
     */
    void customize(BusinessDataPermissionRule rule);

}
