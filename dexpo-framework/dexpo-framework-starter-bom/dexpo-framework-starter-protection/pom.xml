<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.dexpo</groupId>
        <artifactId>dexpo-framework-starter-bom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>dexpo-framework-starter-protection</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>服务保证，提供分布式锁、幂等、限流、熔断等等功能</description>


    <dependencies>
        <!-- DB 相关 -->
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-cache</artifactId>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.resilience4j</groupId>
            <artifactId>resilience4j-spring-boot2</artifactId>
        </dependency>
    </dependencies>

</project>
