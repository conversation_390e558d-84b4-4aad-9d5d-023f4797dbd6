package com.dexpo.framework.encrypt.core.util.outsign;


import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

@Slf4j
public class SignatureUtil {

    // 指定签名算法
    private static final String HMAC_SHA256 = "HmacSHA256";

    /**
     * 生成 HMAC-SHA256 签名
     *
     * @param params 请求参数 key-value 形式
     * @param secret 秘钥（客户端和服务端约定）
     * @return 签名 hex 字符串
     */
    public static String generateSignature(Map<String, String> params, String secret) throws NoSuchAlgorithmException, InvalidKeyException {
        // 1. 参数按 key 字典序排序
        Map<String, String> sortedMap = new TreeMap<>(params);

        // 2. 拼接 key=value&key=value 格式字符串
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedMap.entrySet()) {
            if (sb.length() > 0) {
                sb.append("&");
            }
            sb.append(entry.getKey()).append("=").append(entry.getValue());
        }

        String signStr = sb.toString();

        // 3. 使用 HMAC-SHA256 加密
        Mac mac = Mac.getInstance(HMAC_SHA256);
        SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
        mac.init(secretKey);
        byte[] signatureBytes = mac.doFinal(signStr.getBytes(StandardCharsets.UTF_8));

        // 4. 转换为十六进制字符串
        return bytesToHex(signatureBytes);
    }

    /**
     * 将字节数组转为十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1)
                hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }

    public static void main(String[] args) throws Exception {
        // 请求参数
        Map<String, String> params = new HashMap<>();
        params.put("timestamp", Instant.now().getEpochSecond() + "");
        params.put("nonce", NonceUtil.createNonce(32));
        params.put("appKey", "appKey");

        // 秘钥
        String appSecret = "appSecret";

        // 生成签名
        String signature = generateSignature(params, appSecret);

        log.info("签名字符串: " + buildSignString(params));
        log.info("签名结果: " + signature);
    }

    // 辅助方法：构建签名原始字符串（用于调试）
    private static String buildSignString(Map<String, String> params) {
        Map<String, String> sortedMap = new TreeMap<>(params);
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedMap.entrySet()) {
            if (sb.length() > 0) {
                sb.append("&");
            }
            sb.append(entry.getKey()).append("=").append(entry.getValue());
        }
        return sb.toString();
    }

}
