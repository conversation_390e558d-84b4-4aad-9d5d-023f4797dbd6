package com.dexpo.framework.encrypt.core.util.outsign;

import lombok.extern.slf4j.Slf4j;

import java.util.Base64;

@Slf4j
public class AesGcmUtil {

    private AesGcmUtil( ) {}

    public static String convertToPEMFormat(String type, byte[] keyBytes) {
        String encoded = Base64.getEncoder().encodeToString(keyBytes);
        StringBuilder pem = new StringBuilder();
        pem.append("-----BEGIN ").append(type).append("-----\n");

        // 每行 64 字符换行
        for (int i = 0; i < encoded.length(); i += 64) {
            int end = Math.min(i + 64, encoded.length());
            pem.append(encoded.substring(i, end)).append("\n");
        }

        pem.append("-----END ").append(type).append("-----");
        return pem.toString();
    }


}
