package com.dexpo.framework.encrypt.core.util.outsign;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

@Slf4j
public class EncryptUtil {

    private static final String ALGORITHM = "RSA/ECB/OAEPWithSHA-256AndMGF1Padding";

    private EncryptUtil() {}


    /**
     * 使用公钥加密数据
     */
    @SneakyThrows
    public static String encrypt(String publicKeyPEM, String plainText) {
        RSAPublicKey publicKey = readPublicKeyFromPEM(publicKeyPEM);
        Cipher cipher = Cipher.getInstance(ALGORITHM, "BC");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes());
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    /**
     * 公钥
     */
    @SneakyThrows
    public static RSAPublicKey readPublicKeyFromPEM(String publicKeyPEM) {
        byte[] pkcsBytes = Base64.getDecoder().decode(publicKeyPEM);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(pkcsBytes);
        KeyFactory kf = KeyFactory.getInstance("RSA");
        return (RSAPublicKey) kf.generatePublic(keySpec);
    }
}
