package com.dexpo.framework.encrypt.core.util.outsign;

import lombok.extern.slf4j.Slf4j;

import java.security.SecureRandom;
import java.util.Base64;

@Slf4j
public class SecretKeyGenerator {
    private static final SecureRandom secureRandom = new SecureRandom();
    /**
     * 生成指定长度的随机字节数组，并转换为 Base64 编码字符串
     *
     * @param length 字节数（建议 16~64 字节）
     * @return Base64 格式的秘钥
     */
    public static String generateBase64SecretKey(int length) {
        byte[] randomBytes = new byte[length];
        secureRandom.nextBytes(randomBytes);
        return Base64.getEncoder().encodeToString(randomBytes);
    }

    /**
     * 生成默认长度（32 字节）的 Base64 秘钥
     */
    public static String generateBase64SecretKey() {
        return generateBase64SecretKey(32); // 32 bytes = 256 bits
    }

    /**
     * 生成十六进制格式的秘钥（可选）
     */
    public static String generateHexSecretKey(int length) {
        byte[] randomBytes = new byte[length];
        secureRandom.nextBytes(randomBytes);
        StringBuilder sb = new StringBuilder();
        for (byte b : randomBytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    // ————————————————
    // 🔍 示例主函数
    // ————————————————
    public static void main(String[] args) {
        log.info("Base64 Secret Key: " + generateBase64SecretKey());
        log.info("Hex Secret Key: " + generateHexSecretKey(32));
        log.info("Hex Secret Key: " + generateHexSecretKey(16));
    }

}
