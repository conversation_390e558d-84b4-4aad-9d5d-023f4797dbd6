package com.dexpo.framework.encrypt.core.util.sha;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

@Slf4j
public class SHAUtils {

    private SHAUtils() {
        throw new IllegalStateException("Utility class");
    }
    /**
     * 利用 java 原生的摘要实现 SHA256 加密
     *
     * @param str 加密后的报文
     * @return
     */
    public static String getSHA256StrJava(String str) {
        MessageDigest messageDigest;
        String encodeStr = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(str.getBytes(StandardCharsets.UTF_8));
            encodeStr = byte2Hex(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            log.error(e.getMessage());
        }
        return encodeStr;
    }

    /**
     * 将 byte 转为 16 进制
     *
     * @param bytes
     * @return
     */
    private static String byte2Hex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        String temp;
        for (byte aByte : bytes) {
            temp = Integer.toHexString(aByte & 0xFF);
            if (temp.length() == 1) {
                //1 得到一位的进行补 0 操作
                sb.append("0");
            }
            sb.append(temp);
        }
        return sb.toString();
    }

}
