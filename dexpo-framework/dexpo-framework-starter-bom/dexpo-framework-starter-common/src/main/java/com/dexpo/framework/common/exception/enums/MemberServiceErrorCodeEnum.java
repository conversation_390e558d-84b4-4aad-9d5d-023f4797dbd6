package com.dexpo.framework.common.exception.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * member service error enum
 * 模块 service-member 错误码区间 [1-002-000-000 ~ 1-003-000-000)
 */
@AllArgsConstructor
@Getter
public enum MemberServiceErrorCodeEnum implements ErrorCodeEnum{

    MEMBER_SPONSOR_USER_NOT_EXIST(1002002001, "用户不存在",
            "用户不存在，请检查输入信息是否有误", "用户不存在"),

    MEMBER_SPONSOR_USER_DISABLE(1002002002, "用户已禁用，请联系管理员"
            , "用户已禁用，请联系管理员", "用户禁用 不能登录"),

    MEMBER_SPONSOR_LOGIN_VALID_ERROR(1002002003, "验证码错误/已过期，请重新输入"
            , "OTP is incorrect/has expired", "验证码错误/已过期"),

    MEMBER_INVALID_CONTACT(1002002004, "请输入正确手机号/邮箱"
            , "Please enter valid phone number/E-mail address", "联系方式格式错误"),
    MEMBER_EXCEL_HAS_ERROR(1002002005, "excel解析异常"
            , "excel has error!", "excel解析失败"),
    MEMBER_REGISTER_STATUS_ERROR(1002002006, "媒体信息注册状态错误"
            , "Media register status error", "媒体注册信息中的注册状态错误"),
    AUTH_LOGIN_IDENTITY_AUTHENTICATION_FAILED(1002002007, "实名认证失败！请输入正确的姓名/身份证号码！"
            , "Real-name authentication failed! Please enter the correct name/ID number.", "实名认证失败！请输入正确的姓名/身份证号码！"),

    EMAIL_OR_MOBILE_CHECK_ERROR(1002002008, "请输入正确手机号/邮箱!",
                                        "Please enter valid phone number/E-mail address", "邮箱或短信验证"),
    RECALL_FAIL_NOT_FOUND_RECORD(1002002009, "撤回失败,未找到用户的展会关联信息!",
            "Recall failed,no exhibition information related to the user was found", "撤回失败,未找到用户的展会关联信息"),
    RECALL_FAIL_STATUS_ERROR(**********, "撤回失败,该单据提交状态不为待审核!",
            "Recall failed,the submission status is not pending review", "撤回失败,该单据提交状态不为待审核!"),
    MEDIA_NEWSMAN_NUM_NOT_FOUND(**********, "记者证号不能为空!",
            "Media newsman NO. should not be empty", "记者证号不能为空!"),
    MEMBER_EMAIL_EXIST(**********, "该邮箱已注册",
            "The provided E-mail is already associated with an existing account", "该邮箱已注册"),
    MEMBER_MOBILE_EXIST(**********, "该手机号已注册",
            "The provided phone number is already associated with an existing account", "该手机号已注册"),
    MEDIA_PROXY_UPLOAD_TYPE_ZIP(**********, "请上传符合规则的附件",
            "Please upload attachments that comply with the rules", "上传文件仅支持.zip格式"),
    MEDIA_AUDIT_FAILED(**********, "审核失败,请重新选择",
            "Audit failed. Please reselect", "审核失败,请重新选择"),
    MOBILE_CANNOT_EMPTY(**********, "手机号不能为空",
            "Mobile can not empty", "手机号不能为空"),
    MEMBER_MOBILE_OR_EMAIL_EXIST(**********, "该手机号或邮箱已注册",
            "The provided Mobile/E-mail is already associated with an existing account", "该手机号或邮箱已注册"),
    MEMBER_SPONSOR_STATUS_INVALID(**********, "主办方状态参数无效",
            "Invalid sponsor status parameter", "主办方状态参数无效，只允许VO_SPONSOR_STATUS_1或VO_SPONSOR_STATUS_2"),
    MEMBER_SPONSOR_NAME_INVALID(**********, "姓名格式不正确，仅支持中文、字母、'·'，且不超过20字符",
            "Invalid sponsor name format", "姓名格式不正确，仅支持中文、字母、'·'，且不超过20字符"),
    MEMBER_SPONSOR_MOBILE_INVALID(**********, "手机号格式不正确，需为11位国内手机号",
            "Invalid mobile format", "手机号格式不正确，需为11位国内手机号"),
    MEMBER_SPONSOR_EMAIL_INVALID(**********, "邮箱格式不正确",
            "Invalid email format", "邮箱格式不正确"),
    MEMBER_SPONSOR_MANAGE_ORGANIZATION_CODE_INVALID(**********, "管理组织编码不正确",
            "Invalid manage organization code format", "管理组织编码不正确")
    ;


    private final Integer code;
    private final String msg;
    private final String msgEn;
    private final String desc;

}
