package com.dexpo.framework.common.exception;

import com.dexpo.framework.common.exception.enums.ErrorCodeEnum;
import com.dexpo.framework.common.exception.enums.GlobalErrorCodeConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 服务器异常 Exception
 */
@Data
@EqualsAndHashCode(callSuper = true)
public final class ServerException extends RuntimeException {

    /**
     * 全局错误码
     *
     * @see GlobalErrorCodeConstants
     */
    private Integer code;
    /**
     * 错误提示
     */
    private String message;

    /**
     * 错误提示-英文
     */
    private String messageEn;

    /**
     * 空构造方法，避免反序列化问题
     */
    public ServerException() {
    }

    public ServerException(ErrorCode errorCode) {
        this.code = errorCode.getCode();
        this.message = errorCode.getMsg();
        this.messageEn = errorCode.getMsgEn();
    }

    public ServerException(ErrorCodeEnum errorCodeEnum) {
        this.code = errorCodeEnum.getCode();
        this.message = errorCodeEnum.getMsg();
        this.messageEn = errorCodeEnum.getMsgEn();
    }

    public ServerException(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public ServerException(Integer code, String message, String messageEn) {
        this.code = code;
        this.message = message;
        this.messageEn = messageEn;
    }

    public Integer getCode() {
        return code;
    }

    public ServerException setCode(Integer code) {
        this.code = code;
        return this;
    }

    public String getMessageEn() {
        return messageEn;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public ServerException setMessage(String message) {
        this.message = message;
        return this;
    }

    public ServerException setMessageEn(String messageEn) {
        this.messageEn = messageEn;
        return this;
    }

    public ServerException setMessage(String message, String messageEn) {
        this.message = message;
        this.messageEn = messageEn;
        return this;
    }

}
