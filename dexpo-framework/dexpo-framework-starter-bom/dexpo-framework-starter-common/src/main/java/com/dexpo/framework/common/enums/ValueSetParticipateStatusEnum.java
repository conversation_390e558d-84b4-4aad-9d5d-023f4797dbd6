package com.dexpo.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ValueSetParticipateStatusEnum {

    /** 
     * 未购票
     */
    NOT_BOUGHT("VS_AUDIENCE_PARTICIPATE_STATUS", "VO_AUDIENCE_PARTICIPATE_STATUS_1", "未购票", ""),

    /**
     * 未激活
     */
    NOT_ACTIVATED("VS_AUDIENCE_PARTICIPATE_STATUS", "VO_AUDIENCE_PARTICIPATE_STATUS_2", "未激活", ""),

    /**
     * 已激活
     */
    ACTIVATED("VS_AUDIENCE_PARTICIPATE_STATUS", "VO_AUDIENCE_PARTICIPATE_STATUS_3", "已激活", ""),

    /**
     * 已入场
     */
    ENTERED("VS_AUDIENCE_PARTICIPATE_STATUS", "VO_AUDIENCE_PARTICIPATE_STATUS_4", "已入场", ""),

    ;

    /**
     * 值集编码
     * 用于标识该枚举所属的值集
     */
    private String valuesetCode;

    /**
     * 选项编码
     * 用于标识该枚举在值集中的具体选项
     */
    private final String optionCode;

    /**
     * 中文描述
     * 用于展示给中文用户
     */
    private final String description;

    /**
     * 英文描述
     * 用于展示给英文用户
     */
    private final String descriptionEn;

}
