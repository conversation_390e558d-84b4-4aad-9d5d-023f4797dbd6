package com.dexpo.framework.common.enums;


import lombok.Getter;

@Getter
public enum RegisterSubmitTypeEnum {

    DRAFT("DRAFT", "草稿"),
    SUBMIT("SUBMIT", "提交"),
    PROXY_REGISTER("PROXY_REGISTER", "代注册");

    RegisterSubmitTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }


    /**
     * 枚举项编码
     */
    private final String code;

    /**
     * 中文描述
     */
    private final String description;
}
