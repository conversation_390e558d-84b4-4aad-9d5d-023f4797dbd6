package com.dexpo.framework.common.exception.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * base service error enum
 * 模块 service-base 错误码区间 [1-001-000-000 ~ 1-002-000-000)
 */
@AllArgsConstructor
@Getter
public enum BaseServiceErrorCodeEnum implements ErrorCodeEnum{

//    MEMBER_SPONSOR_USER_NOT_EXIST(1002002001, "用户不存在，请检查输入信息是否有误",
//            "用户不存在，请检查输入信息是否有误", "用户不存在"),
//
//    MEMBER_SPONSOR_USER_DISABLE(1002002002, "用户已禁用，请联系管理员"
//            , "用户已禁用，请联系管理员", "用户禁用 不能登录"),

    PARAMETER_CANNOT_BE_ALL_EMPTY(1001002003, "参数不能全部为空"
            , "Parameters cannot be all empty", "参数验证失败"),
    ATTACHMENT_NOT_FIND(1001002004, "文件未找到"
            , "attachment not find", "获取模板文件时未找到对应文件")
    ;

    private final Integer code;
    private final String msg;
    private final String msgEn;
    private final String desc;

}
