package com.dexpo.framework.common.enums;

import lombok.Getter;

@Getter
public enum AudienceTypeEnum {

    INDIVIDUAL("VS_AUDIENCE_TYPE", "VO_AUDIENCE_TYPE_1", "个人观众", ""),
    GROUP("VS_AUDIENCE_TYPE", "VO_AUDIENCE_TYPE_2", "团体观众", "");

    /**
     * 值集分类编码
     */
    private final String valuesetCode;

    /**
     * 枚举项编码
     */
    private final String code;

    /**
     * 中文描述
     */
    private final String descriptionCN;

    /**
     * 英文描述
     */
    private final String descriptionEN;

    AudienceTypeEnum(String valuesetCode, String code, String descriptionCN, String descriptionEN) {
        this.valuesetCode = valuesetCode;
        this.code = code;
        this.descriptionCN = descriptionCN;
        this.descriptionEN = descriptionEN;
    }

    /**
     * 根据枚举编码获取枚举实例
     * @param code VO_AUDIENCE_TYPE_1 格式的编码
     * @return 匹配的枚举实例，未找到返回null
     */
    public static AudienceTypeEnum getByCode(String code) {
        for (AudienceTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }
} 