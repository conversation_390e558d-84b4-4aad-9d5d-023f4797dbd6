package com.dexpo.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ValueSetTodoStatusEnum {

    /**
     * 待处理
     */
    TODO(ValueSetTodoStatusEnum.VALUE_SET_CODE, "VO_TODO_STATUS_1", "待处理", ""),

    /**
     * 已处理
     */
    DONE(ValueSetTodoStatusEnum.VALUE_SET_CODE, "VO_TODO_STATUS_2", "已处理", ""),

    /**
     * 已处理
     */
    CANCEL(ValueSetTodoStatusEnum.VALUE_SET_CODE, "VO_TODO_STATUS_3", "已取消", "");

    ;

    /**
     * 值集编码
     * 用于标识该枚举所属的值集
     */
    private String valuesetCode;

    /**
     * 选项编码
     * 用于标识该枚举在值集中的具体选项
     */
    private final String optionCode;

    /**
     * 中文描述
     * 用于展示给中文用户
     */
    private final String description;

    /**
     * 英文描述
     * 用于展示给英文用户
     */
    private final String descriptionEn;

    public static final String VALUE_SET_CODE = "VS_TODO_STATUS";

}
