package com.dexpo.framework.common.util.code;

import java.security.SecureRandom;
import java.util.Random;

/**
 * SMS verification code generator utility class
 * Generates 6-digit numeric verification codes
 */
public class CodeGenerator {

    private static final int CODE_LENGTH = 6;
    private static final String NUMBERS = "0123456789";
    private static final Random RANDOM = new SecureRandom();

    private CodeGenerator() {
        // Private constructor to prevent instantiation
    }

    /**
     * Generate a 6-digit numeric verification code
     *
     * @return 6-digit verification code as String
     */
    public static String generate() {
        StringBuilder code = new StringBuilder(CODE_LENGTH);
        for (int i = 0; i < CODE_LENGTH; i++) {
            int index = RANDOM.nextInt(NUMBERS.length());
            code.append(NUMBERS.charAt(index));
        }
        return code.toString();
    }

} 