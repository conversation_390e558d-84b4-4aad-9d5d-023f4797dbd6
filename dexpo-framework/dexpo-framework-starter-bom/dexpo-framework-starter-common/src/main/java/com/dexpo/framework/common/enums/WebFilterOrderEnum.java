package com.dexpo.framework.common.enums;

import lombok.Getter;

/**
 * Web 过滤器顺序的枚举类，保证过滤器按照符合我们的预期
 *
 * 考虑到每个 starter 都需要用到该工具类，所以放到 common 模块下的 enum 包下
 *
 * <AUTHOR>
 */
@Getter
public enum WebFilterOrderEnum {

    CORS_FILTER(Integer.MIN_VALUE),
    TRACE_FILTER(CORS_FILTER.getCode() + 1),
    ENV_TAG_FILTER(TRACE_FILTER.getCode() + 1),
    REQUEST_BODY_CACHE_FILTER(Integer.MIN_VALUE + 500),
    ORDERED_REQUEST_CONTEXT_FILTER(-105),
    TENANT_CONTEXT_FILTER(-104), // 需要保证在 ApiAccessLogFilter 前面
    API_ACCESS_LOG_FILTER(-103), // 需要保证在 RequestBodyCacheFilter 后面
    XSS_FILTER(-102),  // 需要保证在 RequestBodyCacheFilter 后面
    SPRING_SECURITY_FILTER(-100), // Spring Security Filter 默认为 -100
    TENANT_SECURITY_FILTER(-99), // 需要保证在 Spring Security 过滤器后面
    FLOWABLE_FILTER(-98), // 需要保证在 Spring Security 过滤后面
    DEMO_FILTER(Integer.MAX_VALUE);

    private final int code;

    WebFilterOrderEnum(int code) {
        this.code = code;
    }

}