package com.dexpo.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 接口枚举
 * 用于定义系统中不同类型的接口
 * <p>
 * 值集编码: VS_INTERFACE
 * 值集说明: 接口值集
 */
@Getter
@AllArgsConstructor
public enum ValueSetInterfaceEnum {

    /**
     * 媒体注册信息存储接口
     * 值集编码: VS_INTERFACE
     * 选项编码: VO_INTERFACE_SC_DW_001
     * 说明: 媒体注册信息存储接口
     */
    VO_INTERFACE_SC_DW_001("VS_INTERFACE", "VO_INTERFACE_SC_DW_001", "工博会(上海)_媒体注册信息存储接口",
            "Media Registration Information Storage Interface"),

    /**
     * 观众注册信息存储接口
     * 值集编码: VS_INTERFACE
     * 选项编码: VO_INTERFACE_SC_DW_002
     * 服务中台 -> 数仓
     * 服务中台实时向数据仓库同步观众用户注册信息，分为用户登录成功（未购票）、注册成功（点立即购票）、购票激活成功（已支付）三个阶段
     * 说明: 观众注册信息存储接口
     */
    VO_INTERFACE_SC_DW_002("VS_INTERFACE", "VO_INTERFACE_SC_DW_002", "工博会(上海)_观众注册信息存储接口",
            "Audience Registration Information Storage Interface"),

    /**
     * 观众用户同步接口
     * 值集编码: VS_INTERFACE
     * 选项编码: VO_INTERFACE_SC_DW_003
     * 说明: 观众用户同步接口
     * 数仓 -> 服务中台
     * 数仓将毅朝通道注册的观众用户数据实时同步至服务中台，购票激活成功一个阶段
     */
    VO_INTERFACE_SC_DW_003("VS_INTERFACE", "VO_INTERFACE_SC_DW_003", "工博会(上海)_观众用户同步接口",
            "Audience User Synchronization Interface"),

    /**
     * 实名认证接口
     * 值集编码: VS_INTERFACE
     * 选项编码: VO_INTERFACE_SC_AL_001
     * 说明: 实名认证接口
     */
    REAL_NAME_AUTH("VS_INTERFACE", "VO_INTERFACE_SC_AL_001", "工博会(上海)_实名认证接口", "Real Name Authentication Interface"),

    /**
     * 微信支付接口
     * 值集编码: VS_INTERFACE
     * 选项编码: VO_INTERFACE_SC_WC_001
     * 说明: 微信支付接口
     */
    WECHAT_PAY("VS_INTERFACE", "VO_INTERFACE_SC_WC_001", "工博会(上海)_微信支付接口", "WeChat Payment Interface"),

    /**
     * 微信退款接口
     * 值集编码: VS_INTERFACE
     * 选项编码: VO_INTERFACE_SC_WC_002
     * 说明: 微信退款接口
     */
    WECHAT_REFUND("VS_INTERFACE", "VO_INTERFACE_SC_WC_002", "工博会(上海)_微信退款接口", "WeChat Refund Interface");

    ;

    /**
     * 值集编码
     * 用于标识该枚举所属的值集
     */
    private String valuesetCode;

    /**
     * 选项编码
     * 用于标识该枚举在值集中的具体选项
     */
    private final String optionCode;

    /**
     * 中文描述
     * 用于展示给中文用户
     */
    private final String description;

    /**
     * 英文描述
     * 用于展示给英文用户
     */
    private final String descriptionEn;

    /**
     * 根据选项编码获取对应的枚举实例
     *
     * @param code 选项编码
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static ValueSetInterfaceEnum getByCode(String code) {
        for (ValueSetInterfaceEnum type : values()) {
            if (type.getOptionCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}