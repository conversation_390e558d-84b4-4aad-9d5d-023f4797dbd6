package com.dexpo.framework.common.enums;

import lombok.Getter;

@Getter
public enum GenderEnum {

    MALE(
            "VS_GENDER",
            "VO_GENDER_1",
            "男士",
            "Male"
    ),
    FEMALE(
            "VS_GENDER",
            "VO_GENDER_2",
            "女士",
            "Female"
    );

    /**
     * 值集分类编码
     */
    private final String valuesetCode;

    /**
     * 枚举项编码
     */
    private final String code;

    /**
     * 中文描述
     */
    private final String descriptionCN;

    /**
     * 英文描述
     */
    private final String descriptionEN;

    GenderEnum(String valuesetCode, String code, String descriptionCN, String descriptionEN) {
        this.valuesetCode = valuesetCode;
        this.code = code;
        this.descriptionCN = descriptionCN;
        this.descriptionEN = descriptionEN;
    }

    /**
     * 根据枚举编码获取枚举实例
     * @param code VO_GENDER_1 格式的编码
     * @return 匹配的枚举实例，未找到返回null
     */
    public static GenderEnum getByCode(String code) {
        for (GenderEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据中文获取枚举实例
     *
     * @param descriptionCN 中文
     * @return 匹配的枚举实例，未找到返回null
     */
    public static GenderEnum getByDescriptionCN(String descriptionCN) {
        for (GenderEnum type : values()) {
            if (type.descriptionCN.equals(descriptionCN)) {
                return type;
            }
        }
        return null;
    }
} 