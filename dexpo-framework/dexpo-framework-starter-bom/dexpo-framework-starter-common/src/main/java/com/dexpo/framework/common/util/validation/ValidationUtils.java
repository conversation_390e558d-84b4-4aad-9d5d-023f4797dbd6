package com.dexpo.framework.common.util.validation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import org.springframework.util.StringUtils;

import java.util.Set;
import java.util.regex.Pattern;

/**
 * 校验工具类
 *
 * <AUTHOR>
 */
public class ValidationUtils {

    private ValidationUtils(){}
    private static final Pattern PATTERN_MOBILE = Pattern.compile("^(?:(?:\\+|00)86)?1(?:(?:3[\\d])|(?:4[0,1,4-9])|(?:5[0-3,5-9])|(?:6[2,5-7])|(?:7[0-8])|(?:8[\\d])|(?:9[0-3,5-9]))\\d{8}$");

    private static final Pattern PATTERN_URL = Pattern.compile("^(https?|ftp|file)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]");

    private static final Pattern PATTERN_XML_NCNAME = Pattern.compile("[a-zA-Z_][\\-_.0-9_a-zA-Z$]*");

    private static final Pattern PATTERN_EMAIL = Pattern.compile(
            "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
    );

    // 身份证号码长度
    private static final int ID_CARD_LENGTH = 18;
    
    // 身份证号码正则表达式
    private static final Pattern PATTERN_ID_CARD = Pattern.compile("^[1-9]\\d{5}(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]$");
    
    // 加权因子
    private static final int[] WEIGHT_FACTOR = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
    
    // 校验码
    private static final char[] CHECK_CODE = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};

    public static boolean isEmail(String email) {
        return StringUtils.hasText(email) && PATTERN_EMAIL.matcher(email).matches();
    }

    public static boolean isMobile(String mobile) {
        return StringUtils.hasText(mobile)
                && PATTERN_MOBILE.matcher(mobile).matches();
    }

    public static boolean isURL(String url) {
        return StringUtils.hasText(url)
                && PATTERN_URL.matcher(url).matches();
    }

    public static boolean isXmlNCName(String str) {
        return StringUtils.hasText(str)
                && PATTERN_XML_NCNAME.matcher(str).matches();
    }

    public static void validate(Object object, Class<?>... groups) {
        Validator validator = Validation.buildDefaultValidatorFactory().getValidator();
        Assert.notNull(validator);
        validate(validator, object, groups);
    }

    public static void validate(Validator validator, Object object, Class<?>... groups) {
        Set<ConstraintViolation<Object>> constraintViolations = validator.validate(object, groups);
        if (CollUtil.isNotEmpty(constraintViolations)) {
            throw new ConstraintViolationException(constraintViolations);
        }
    }

    /**
     * 验证身份证号码
     * @param idCard 身份证号码
     * @return 是否有效
     */
    public static boolean isValidIdCard(String idCard) {
        if (!StringUtils.hasText(idCard) || idCard.length() != ID_CARD_LENGTH) {
            return false;
        }

        // 基本格式验证
        if (!PATTERN_ID_CARD.matcher(idCard).matches()) {
            return false;
        }

        // 验证出生日期
        String birthDate = idCard.substring(6, 14);
        try {
            int year = Integer.parseInt(birthDate.substring(0, 4));
            int month = Integer.parseInt(birthDate.substring(4, 6));
            int day = Integer.parseInt(birthDate.substring(6, 8));

            if (checkYearMonthDay(year, month, day)) return false;

            // 验证月份天数
            if (checkMonth(month, year, day)) return false;
        } catch (NumberFormatException e) {
            return false;
        }

        // 验证校验码
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            sum += (idCard.charAt(i) - '0') * WEIGHT_FACTOR[i];
        }
        char checkCode = CHECK_CODE[sum % 11];
        return Character.toUpperCase(idCard.charAt(17)) == checkCode;
    }

    private static boolean checkMonth(int month, int year, int day) {
        if (month == 2) {
            boolean isLeapYear = (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
            if (isLeapYear && day > 29) {
                return true;
            }
            if (!isLeapYear && day > 28) {
                return true;
            }
        } else if ((month == 4 || month == 6 || month == 9 || month == 11) && day > 30) {
            return true;
        }
        return false;
    }

    private static boolean checkYearMonthDay(int year, int month, int day) {
        if (year < 1900 || year > 2100) {
            return true;
        }
        if (month < 1 || month > 12) {
            return true;
        }
        return day < 1 || day > 31;
    }

}
