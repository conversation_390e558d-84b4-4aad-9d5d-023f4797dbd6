package com.dexpo.framework.common.exception;

import com.dexpo.framework.common.exception.enums.ErrorCodeEnum;
import com.dexpo.framework.common.exception.enums.ServiceErrorCodeRange;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务逻辑异常 Exception
 */
@Data
@EqualsAndHashCode(callSuper = true)
public final class ServiceException extends RuntimeException {

    /**
     * 业务错误码
     *
     * @see ServiceErrorCodeRange
     */
    private Integer code;
    /**
     * 错误提示-中文
     */
    private String message;

    /**
     * 错误提示-英文
     */
    private String messageEn;

    /**
     * 空构造方法，避免反序列化问题
     */
    public ServiceException() {
    }

    public ServiceException(ErrorCode errorCode) {
        this.code = errorCode.getCode();
        this.message = errorCode.getMsg();
        this.messageEn = errorCode.getMsgEn();
    }

    public ServiceException(ErrorCodeEnum errorCodeEnum) {
        this.code = errorCodeEnum.getCode();
        this.message = errorCodeEnum.getMsg();
        this.messageEn = errorCodeEnum.getMsgEn();
    }

    public ServiceException(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public ServiceException(Integer code, String message, String messageEn) {
        this.code = code;
        this.message = message;
        this.messageEn = messageEn;
    }

    public Integer getCode() {
        return code;
    }

    public ServiceException setCode(Integer code) {
        this.code = code;
        return this;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public String getMessageEn() {
        return messageEn;
    }

    public ServiceException setMessage(String message) {
        this.message = message;
        return this;
    }

    public ServiceException setMessageEn(String messageEn) {
        this.messageEn = messageEn;
        return this;
    }

    public ServiceException setMessage(String message, String messageEn) {
        this.message = message;
        this.messageEn = messageEn;
        return this;
    }

}
