package com.dexpo.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户类型枚举
 * 用于定义系统中不同类型的用户角色
 * <p>
 * 值集编码: VS_ACTION_USER_TYPE
 * 值集说明: 用户类型值集
 */
@Getter
@AllArgsConstructor
public enum ValueSetUserTypeEnum {

    /**
     * 主办方用户
     * 值集编码: VS_ACTION_USER_TYPE
     * 选项编码: VO_ACTION_USER_TYPE_1
     * 说明: 具有展会主办方权限的用户
     */
    SPONSOR("VS_ACTION_USER_TYPE", "VO_ACTION_USER_TYPE_1"
            , "主办方用户", "Sponsor User"),

    /**
     * 媒体用户
     * 值集编码: VS_ACTION_USER_TYPE
     * 选项编码: VO_ACTION_USER_TYPE_2
     * 说明: 具有媒体采访和报道权限的用户
     */
    MEDIA("VS_ACTION_USER_TYPE", "VO_ACTION_USER_TYPE_2"
            , "媒体用户", "Media User"),

    /**
     * 观众用户
     * 值集编码: VS_ACTION_USER_TYPE
     * 选项编码: VO_ACTION_USER_TYPE_3
     * 说明: 具有展会参观权限的普通用户
     */
    AUDIENCE("VS_ACTION_USER_TYPE", "VO_ACTION_USER_TYPE_3"
            , "观众用户", "Audience User");

    /**
     * 值集编码
     * 用于标识该枚举所属的值集
     */
    private String valuesetCode;

    /**
     * 选项编码
     * 用于标识该枚举在值集中的具体选项
     */
    private final String optionCode;

    /**
     * 中文描述
     * 用于展示给中文用户
     */
    private final String description;

    /**
     * 英文描述
     * 用于展示给英文用户
     */
    private final String descriptionEn;

    /**
     * 根据选项编码获取对应的枚举实例
     *
     * @param code 选项编码
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static ValueSetUserTypeEnum getByCode(String code) {
        for (ValueSetUserTypeEnum type : values()) {
            if (type.getOptionCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 