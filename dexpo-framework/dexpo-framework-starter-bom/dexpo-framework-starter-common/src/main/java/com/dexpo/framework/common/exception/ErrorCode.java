package com.dexpo.framework.common.exception;

import com.dexpo.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.dexpo.framework.common.exception.enums.ServiceErrorCodeRange;
import lombok.Data;

/**
 * 错误码对象
 *
 * 全局错误码，占用 [0, 999], 参见 {@link GlobalErrorCodeConstants}
 * 业务异常错误码，占用 [1 000 000 000, +∞)，参见 {@link ServiceErrorCodeRange}
 *
 *
 */
@Data
public class ErrorCode {

    /**
     * 错误码
     */
    private final Integer code;
    /**
     * 错误提示
     */
    private final String msg;

    /**
     * 错误提示
     */
    private final String msgEn;

    public ErrorCode(Integer code, String message, String msgEn) {
        this.code = code;
        this.msg = message;
        this.msgEn = msgEn;
    }

    public ErrorCode(Integer code, String message) {
        this.code = code;
        this.msg = message;
        this.msgEn = null;
    }


}
