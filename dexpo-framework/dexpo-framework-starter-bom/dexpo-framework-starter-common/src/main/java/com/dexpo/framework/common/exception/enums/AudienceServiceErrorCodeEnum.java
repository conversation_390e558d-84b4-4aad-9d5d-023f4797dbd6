package com.dexpo.framework.common.exception.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * audience service error enum
 * 模块 audience-member 错误码区间 [1-009-000-000 ~ 1-010-000-000)
 */
@AllArgsConstructor
@Getter
public enum AudienceServiceErrorCodeEnum implements ErrorCodeEnum{

    AUDIENCE_NOT_EXIST(1009000001, "观众信息数据不存在",
            "观众信息数据不存在", "观众信息数据不存在"),


    AUDIENCE_PARTICIPATE_STATUS_ERROR(1009000001, "观众参展状态错误，请刷新重试",
            "观众参展状态错误，请刷新重试", "观众参展状态错误"),
    AUDIENCE_LOGIN_VALID_ERROR(1009000003, "验证码错误/已过期，请重新输入"
            , "OTP is incorrect/has expired", "验证码错误/已过期"),

    ;


    private final Integer code;
    private final String msg;
    private final String msgEn;
    private final String desc;

}
