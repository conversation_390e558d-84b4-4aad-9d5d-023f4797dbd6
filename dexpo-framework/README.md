## 🐯 简介

> 有任何问题，请联系管理员。
>
> 😜 欢迎各位的使用，以及提出宝贵的意见！

* Java 后端
* 管理后台的电脑端：Vue3
* 管理后台的移动端：采用 [uni-app] 方案，一份代码多终端适配，同时支持 APP、小程序、H5！
* 数据库可使用 MySQL、Oracle、PostgreSQL、SQL Server、MariaDB、国产达梦 DM、TiDB 等，基于 MyBatis Plus、Redis + Redisson 操作
* 权限认证使用 Spring Security & Token & Redis，支持多终端、多种用户的认证系统，支持 SSO 单点登录
* 支持加载动态权限菜单，按钮级别权限控制，Redis 缓存提升性能
* 支持 SaaS 多租户系统，可自定义每个租户的权限，提供透明化的多租户底层封装
* 高效率开发，使用代码生成器可以一键生成 Java、Vue 前后端代码、SQL 脚本、接口文档，支持单表、树表、主子表



### 系统功能

| 功能    | 描述                              |
|-------|---------------------------------|
| 用户管理  | 用户是系统操作者，该功能主要完成系统用户配置          |
| 在线用户  | 当前系统中活跃用户状态监控，支持手动踢下线           |
| 角色管理  | 角色菜单权限分配、设置角色按机构进行数据范围权限划分      |
| 菜单管理  | 配置系统菜单、操作权限、按钮权限标识等，本地缓存提供性能    |
| 部门管理  | 配置系统组织机构（公司、部门、小组），树结构展现支持数据权限  |
| 岗位管理  | 配置系统用户所属担任职务                    |
| 租户管理  | 配置系统租户，支持 SaaS 场景下的多租户功能        |
| 租户套餐  | 配置租户套餐，自定每个租户的菜单、操作、按钮的权限       |
| 字典管理  | 对系统中经常使用的一些较为固定的数据进行维护          |
| 操作日志  | 系统正常操作日志记录和查询，集成 Swagger 生成日志内容 |
| 登录日志  | 系统登录日志记录查询，包含登录异常               |
| 错误码管理 | 系统所有错误码的管理，可在线修改错误提示，无需重启服务     |
| 应用管理  | 管理 SSO 单点登录的应用，支持多种 OAuth2 授权方式 |
| 地区管理  | 展示省份、城市、区镇等城市信息，支持 IP 对应城市      |


### 基础设施

| 功能        | 描述                                           |
|-----------|----------------------------------------------|
| 代码生成      | 前后端代码的生成（Java、Vue、SQL、单元测试），支持 CRUD 下载       |
| 系统接口      | 基于 Swagger 自动生成相关的 RESTful API 接口文档          |
| 表单构建      | 拖动表单元素生成相应的 HTML 代码，支持导出 JSON、Vue 文件         |
| 配置管理      | 对系统动态配置常用参数，支持 SpringBoot 加载                 |
| MySQL 监控  | 监视当前系统数据库连接池状态，可进行分析SQL找出系统性能瓶颈              |
| Redis 监控  | 监控 Redis 数据库的使用情况，使用的 Redis Key 管理           |
| 消息队列      | 基于 Redis 实现消息队列，Stream 提供集群消费，Pub/Sub 提供广播消费 |
| Java 监控   | 基于 Spring Boot Admin 实现 Java 应用的监控           |
| 服务保障      | 基于 Redis 实现分布式锁、幂等、限流功能，满足高并发场景              |

## 🐨 技术栈

### 微服务

| 项目                             | 说明                |
|--------------------------------|-------------------|
| `dtt-framework`                | Maven 依赖版本管理和基础功能 |
| `dtt-framework-starter-bom`    | Java 框架拓展         |
| `dtt-framework-starter-parent` | springboot服务统一父pom |
| `dtt-system-service`           | 框架基础系统服务，包含系统基础权限、角色、日志管理等常用功能 |
| `dtt-gateway-service`          | gateway网关         | 
| `dtt-gateway-service`          | gateway网关         | 

[dtt-framework-starter-bom](dtt-framework-starter-bom)目录结构说明

| 项目                                                                                                                        | 说明                                                                   |
|---------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------|
| [dtt-dependencies](dtt-dependencies)                                                                                      | Maven 依赖版本管理                                                         |
| [dtt-framework-starter-parent](dtt-framework-starter-parent)                                                              | springboot服务统一父pom                                                   |
| [dtt-framework-starter-bom](dtt-framework-starter-bom)                                                                    | 技术组件包                                                                |  
| [dtt-framework-starter-biz-data-permission](dtt-framework-starter-bom%2Fdtt-framework-starter-biz-data-permission)        | 数据权限                                                                 |
| [dtt-framework-starter-biz-forgeRock](dtt-framework-starter-bom%2Fdtt-framework-starter-biz-forgeRock)                    | 加密解密token等相关（TODO）                                                   |
| [dtt-framework-starter-biz-operatelog](dtt-framework-starter-bom%2Fdtt-framework-starter-biz-operatelog)                  | 操作日志                                                                 |
| [dtt-framework-starter-biz-tenant](dtt-framework-starter-bom%2Fdtt-framework-starter-biz-tenant)                          | 多租户                                                                  |
| [dtt-framework-starter-captcha](dtt-framework-starter-bom%2Fdtt-framework-starter-captcha)                                | 验证码拓展                                                                | 
| [dtt-framework-starter-common](dtt-framework-starter-bom%2Fdtt-framework-starter-common)                                  | 基础通用包，定义基础 pojo 类、枚举、工具类等等                                           | 
| [dtt-framework-starter-desensitize](dtt-framework-starter-bom%2Fdtt-framework-starter-desensitize)                        | 脱敏组件                                                                 | 
| [dtt-framework-starter-excel](dtt-framework-starter-bom%2Fdtt-framework-starter-excel)                                    | 基于 EasyExcel 实现 Excel 相关的操作                                          | 
| [dtt-framework-starter-file](dtt-framework-starter-bom%2Fdtt-framework-starter-file)                                      | 文件客户端，支持多种存储器（file、ftp、sftp、db、s3）                                   | 
| [dtt-framework-starter-ip](dtt-framework-starter-bom%2Fdtt-framework-starter-ip)                                          | 地区 & IP 库                  | 
| [dtt-framework-starter-job](dtt-framework-starter-bom%2Fdtt-framework-starter-job)                                        | 任务拓展，基于 XXL-Job 实现                                                   | 
| [dtt-framework-starter-kafka](dtt-framework-starter-bom%2Fdtt-framework-starter-kafka)                                    | kafka消息队列                                                            | 
| [dtt-framework-starter-monitor](dtt-framework-starter-bom%2Fdtt-framework-starter-monitor)                                | 服务监控，提供链路追踪、日志服务、指标收集等等功能                                            | 
| [dtt-framework-starter-mq](dtt-framework-starter-bom%2Fdtt-framework-starter-mq)                                          | 消息队列: 1.基于 Spring Cloud Stream 实现异步消息  2. 基于 Spring Cloud Bus 实现事件总线 | 
| [dtt-framework-starter-mybatis](dtt-framework-starter-bom%2Fdtt-framework-starter-mybatis)                                | 数据库连接池、多数据源、事务、MyBatis 拓展                                            | 
| [dtt-framework-starter-protection](dtt-framework-starter-bom%2Fdtt-framework-starter-protection)                          | 服务保证，提供分布式锁、幂等、限流、熔断等等功能                                             | 
| [dtt-framework-starter-cache](dtt-framework-starter-bom%2Fdtt-framework-starter-cache)                                    | Redis 封装拓展                                                           | 
| [dtt-framework-starter-rpc](dtt-framework-starter-bom%2Fdtt-framework-starter-rpc)                                        | OpenFeign：提供 RESTful API 的调用                                         | 
| [dtt-framework-starter-security](dtt-framework-starter-bom%2Fdtt-framework-starter-security)                              | 用户的认证、权限的校验                                                          | 
| [dtt-framework-starter-social](dtt-framework-starter-bom%2Fdtt-framework-starter-social)                                  | social                                                               | 
| [dtt-framework-starter-test](dtt-framework-starter-bom%2Fdtt-framework-starter-test)                                      | 测试组件，用于单元测试、集成测试                                                     | 
| [dtt-framework-starter-web](dtt-framework-starter-bom%2Fdtt-framework-starter-web)                                        | springboot web                                                       | 



 # 后端手册
[dtt-framework-starter-common](dtt-framework-starter-bom%2Fdtt-framework-starter-common)
定义了基础的对象、枚举、异常等通用类和基础工具类。

package说明：

---|core-核心类

---|enums-枚举

---|exception-异常相关

---|pojo-通用对象

---|util-工具类

---|validation-校验相关
### CommonResult
  统一返回对象 

## 异常处理
统一响应
后端提供 RESTful API 给前端时，需要响应前端 API 调用是否成功：

如果成功，成功的数据是什么。后续，前端会将数据渲染到页面上
如果失败，失败的原因是什么。一般，前端会将原因弹出提示给用户
因此，需要有统一响应，而不能是每个接口定义自己的风格。一般来说，统一响应返回信息如下：

成功时，返回成功的状态码 + 数据
失败时，返回失败的状态码 + 错误提示
在标准的 RESTful API 的定义，是推荐使用 HTTP 响应状态码作为状态码。一般来说，我们实践很少这么去做，主要原因如下：

业务返回的错误状态码很多，HTTP 响应状态码无法很好的映射。例如说，活动还未开始、订单已取消等等
学习成本高，开发者对 HTTP 响应状态码不是很了解。例如说，可能只知道 200、403、404、500 几种常见的



### @ControllerAdvice
在 Spring MVC 中，通过 @ControllerAdvice + @ExceptionHandler 注解，声明将指定类型的异常，转换成对应的 CommonResult 响应。实现的代码，可见 GlobalExceptionHandler类

### Filter 的异常
在请求被 Spring MVC 处理之前，是先经过 Filter 处理的，此时发生异常时，是无法通过 @ExceptionHandler 注解来处理的。只能通过 try catch 的方式来实现

### 业务异常
在 Service 发生业务异常时，如果进行返回呢？例如说，用户名已经存在，商品库存不足等。常用的方案选择，主要有两种：

方案一，使用 CommonResult 统一响应结果，里面有错误码和错误提示，然后进行 return 返回
方案二，使用 ServiceException 统一业务异常，里面有错误码和错误提示，然后进行 throw 抛出
选择方案一 CommonResult 会存在两个问题：

因为 Spring @Transactional 声明式事务，是基于异常进行回滚的，如果使用 CommonResult 返回，则事务回滚会非常麻烦
当调用别的方法时，如果别人返回的是 CommonResult 对象，还需要不断的进行判断，写起来挺麻烦的
因此，项目采用方案二 ServiceException 异常。

### ServiceException
定义 ServiceException 异常类，继承 RuntimeException 异常类（非受检），用于定义业务异常。

### ServiceExceptionUtil
在 Service 需抛出业务异常时，通过调用 ServiceExceptionUtil 的 #exception(ErrorCode errorCode, Object... params) 方法来构建 ServiceException 异常，然后使用 throw 进行抛出
### 错误码-ErrorCode
exception.com.dexpo.framework.common.ErrorCode
### 系统错误码-GlobalErrorCodeConstants
全局的系统错误码，使用 0-999 错误码段，和 HTTP 响应状态码对应。虽然说，HTTP 响应状态码作为业务使用表达能力偏弱，但是使用在系统层面还是非常不错的
### 业务错误码-ServiceErrorCodeRange
模块的业务错误码，按照模块分配错误码的区间，避免模块之间的错误码冲突。
① 业务错误码一共 10 位，分成 4 段，在 ServiceErrorCodeRange 分配.
② 每个业务模块，定义自己的 ErrorCodeConstants 错误码枚举类。

## 参数校验

项目使用 Hibernate Validator框架，对 RESTful API 接口进行参数的校验，以保证最终数据入库的正确性。例如说，用户注册时，会校验手机格式的正确性，密码非弱密码。

如果参数校验不通过，会抛出 ConstraintViolationException 异常，被全局的异常处理捕获，返回“请求参数不正确”的响应。示例如下：


     {
      "code": 400,
      "data": null,
      "msg": "请求参数不正确:密码不能为空"
     }

### 参数校验注解
Validator 内置了 20+ 个参数校验注解，整理成常用与不常用的注解。
1.1 常用注解


| 注解                                                                                          | 功能               | 
|---------------------------------------------------------------------------------------------|------------------|
|@NotBlank	| 只能用于字符串不为 null ，并且字符串 #trim() 以后 length 要大于 0 |
|@NotEmpty	|集合对象的元素不为 0 ，即集合不为空，也可以用于字符串不为 null|
|@NotNull|不能为 null|
|@Pattern(value)|被注释的元素必须符合指定的正则表达式|
|@Max(value)|该字段的值只能小于或等于该值|
|@Min(value)|该字段的值只能大于或等于该值|
|@Range(min=, max=)|检被注释的元素必须在合适的范围内|
|@Size(max, min)|检查该字段的 size 是否在 min 和 max 之间，可以是字符串、数组、集合、Map 等|
|@Length(max, min)	|被注释的字符串的大小必须在指定的范围内。|
|@AssertFalse|被注释的元素必须为 true|
|@AssertTrue|被注释的元素必须为 false|
|@Email|被注释的元素必须是电子邮箱地址|
|@URL(protocol=,host=,port=,regexp=,flags=)|被注释的字符串必须是一个有效的 URL|

1.2不常用注解

| 注解                                                                                    | 功能               | 
|---------------------------------------------------------------------------------------|------------------|
| @Null	                                                                                |必须为 null|
| @DecimalMax(value|被注释的元素必须是一个数字，其值必须小于等于指定的最大值                                               |
| @DecimalMin(value)|被注释的元素必须是一个数字，其值必须大于等于指定的最小值                                             |
| @Digits(integer, fraction)|被注释的元素必须是一个数字，其值必须在可接受的范围内                                       |
| @Positive|判断正数                                                                              |
| @PositiveOrZero|判断正数或 0                                                                     |
| @Negative|判断负数                                                                              |
| @NegativeOrZero|判断负数或 0                                                                    |
| @Future|被注释的元素必须是一个将来的日期                                                                  |
| @FutureOrPresent	|判断日期是否是将来或现在日期                                                          |
| @Past|检查该字段的日期是在过去                                                                       |
| @PastOrPresent|判断日期是否是过去或现在日期                                                           |
| @SafeHtml|判断提交的 HTML 是否安全。例如说，不能包含 JavaScript 脚本等等                                     |

2. 参数校验使用

只需要三步，即可开启参数校验的功能。

〇 第零步，引入参数校验的 spring-boot-starter-validation 依赖。一般不需要做，项目默认已经引入。
① 第一步，在需要参数校验的类上，添加 @Validated 注解，例如说 Controller、Service 类。代码如下：

    // Controller 示例
    @Validated
    public class AuthController {}
    
    // Service 示例，一般放在实现类上
    @Service
    @Validated
    public class AdminAuthServiceImpl implements AdminAuthService {}
② 第二步（情况一）如果方法的参数是 Bean 类型，则在方法参数上添加 @Valid 注解，并在 Bean 类上添加参数校验的注解。代码如下：

     // Controller 示例
     @Validated
     public class AuthController {
     
         @PostMapping("/login")
         public CommonResult<AuthLoginRespVO> login(@RequestBody @Valid AuthLoginReqVO reqVO) {}
     
     }
     // Service 示例，一般放在接口上
     public interface AdminAuthService {
     
         String login(@Valid AuthLoginReqVO reqVO, String userIp, String userAgent);
     }
     
     // Bean 类的示例。一般建议添加参数注解到属性上。原因：采用 Lombok 后，很少使用 getter 方法
     public class AuthLoginReqVO {
     
         @NotEmpty(message = "登录账号不能为空")
         @Length(min = 4, max = 16, message = "账号长度为 4-16 位")
         @Pattern(regexp = "^[A-Za-z0-9]+$", message = "账号格式为数字以及字母")
         private String username;
     
         @NotEmpty(message = "密码不能为空")
         @Length(min = 4, max = 16, message = "密码长度为 4-16 位")
         private String password;
     
     }
② 第二步（情况二）如果方法的参数是普通类型，则在方法参数上直接添加参数校验的注解。代码如下：

     // Controller 示例
     @Validated
     public class DictDataController {
     
         @GetMapping(value = "/get")
         public CommonResult<DictDataRespVO> getDictData(@RequestParam("id") @NotNull(message = "编号不能为空") Long id) {}
     
     }
     
     // Service 示例，一般放在接口上
     public interface DictDataService {
     
         DictDataDO getDictData(@NotNull(message = "编号不能为空") Long id);
     
     }

③ 启动项目，模拟调用 RESTful API 接口，少填写几个参数，看看参数校验是否生效。

3. 自定义注解

如果 Validator 内置的参数校验注解不满足需求时，我们也可以自定义参数校验的注解。

在项目的[dtt-framework-starter-common](dtt-framework-starter-bom%2Fdtt-framework-starter-common)的 validation 包下，就自定义了多个参数校验的注解，以 @Mobile 注解来举例，它提供了手机格式的校验。

① 第一步，新建 @Mobile 注解，并设置自定义校验器为 MobileValidator类。代码如下：

    @Target({
    ElementType.METHOD,
    ElementType.FIELD,
    ElementType.ANNOTATION_TYPE,
    ElementType.CONSTRUCTOR,
    ElementType.PARAMETER,
    ElementType.TYPE_USE
    })
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    @Constraint(
    validatedBy = MobileValidator.class // 设置校验器
    )
    public @interface Mobile {
    
        String message() default "手机号格式不正确";
    
        Class<?>[] groups() default {};
    
        Class<? extends Payload>[] payload() default {};
    
    }
② 第二步，新建 MobileValidator校验器。代码如下：

    public class MobileValidator implements ConstraintValidator<Mobile, String> {
    
        @Override
        public void initialize(Mobile annotation) {
        }
    
        @Override
        public boolean isValid(String value, ConstraintValidatorContext context) {
            // 如果手机号为空，默认不校验，即校验通过
            if (StrUtil.isEmpty(value)) {
                return true;
            }
            // 校验手机
            return ValidationUtils.isMobile(value);
        }
    
    }
③ 第三步，在需要手机格式校验的参数上添加 @Mobile 注解。示例代码如下：

    public class AppAuthLoginReqVO {
    
        @NotEmpty(message = "手机号不能为空")
        @Mobile // <=== here
        private String mobile;
    
    }

## 分页实现
基于 MyBatis Plus 分页功能，二次封装

分页参数: [PageParam.java](dtt-framework-starter-bom%2Fdtt-framework-starter-common%2Fsrc%2Fmain%2Fjava%2Fcom%2Fdtt%2Fedp%2Fframework%2Fcommon%2Fpojo%2FPageParam.java)

分页结果: [PageResult.java](dtt-framework-starter-bom%2Fdtt-framework-starter-common%2Fsrc%2Fmain%2Fjava%2Fcom%2Fdtt%2Fedp%2Fframework%2Fcommon%2Fpojo%2FPageResult.java)


针对 MyBatis Plus 分页查询的二次分装，在 [BaseMapperX.java](dtt-framework-starter-bom%2Fdtt-framework-starter-mybatis%2Fsrc%2Fmain%2Fjava%2Fcom%2Fdtt%2Fedp%2Fframework%2Fmybatis%2Fcore%2Fmapper%2FBaseMapperX.java) 中实现，主要是将 MyBatis 的分页结果 IPage，转换成项目的分页结果 PageResult。


## VO 对象转换、数据翻译
1. 对象转换
   对象转换，指的是 A 类型对象，转换成 B 类型对象。例如说，我们有一个 UserDO 类型对象，需要转换成 UserVO 或者 UserDTO 类型对象。

市面上有很多的对象转换工具，例如说 MapStruct、Dozer、各种 BeanUtils、BeanCopier 等等。目前我们提供了 MapStruct、BeanUtils 两种解决方案。

相比来说，MapStruct 性能会略好于 BeanUtils，但是相比数据库操作带来的耗时来说，基本可以忽略不计。因此，一般情况下，建议使用 BeanUtils 即可。

1.1 MapStruct
项目使用 MapStruct实现 VO、DO、DTO 等对象之间的转换。
1.2 BeanUtils
项目提供了 BeanUtils 类，它是基于 Hutool 的 BeanUtil 封装一层。

2. 数据翻译

数据翻译，指的是将 A 类型对象的某个字段，“翻译”成 B 类型对象的某个字段。例如说，我们有一个 UserVO 的 deptId 字段，读取对应 DeptDO 的 name 字段，最终设置到 UserVO 的 deptName 字段。
一般来说，目前有两种方案：
方案一：数据库 SQL 联表查询，可见 《MyBatis 联表&分页查询》 文档
方案二：数据库多次单表查询，然后在 Java 代码中进行数据拼接（翻译）。其实就是「1.2 BeanUtils」的“复杂场景”。

项目里，大多数采用“方案二”，因为这样可以减少数据库的压力，避免 SQL 过于复杂，也方便后续维护。



 

## SaaS 多租户【字段隔离】[dtt-framework-starter-biz-tenant](dtt-framework-starter-bom%2Fdtt-framework-starter-biz-tenant)
实现透明化的多租户能力，针对 Web、Security、DB、Redis、AOP、Job、MQ、Async 等多个层面进行封装。

引入[dtt-framework-starter-biz-tenant](dtt-framework-starter-bom%2Fdtt-framework-starter-biz-tenant)

            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dtt-framework-starter-biz-tenant</artifactId>
                <version>${revision}</version>
            </dependency>

配置

    dtt:
     tenant:
       enable: true  # 开启多租户
       ignoreUrls:    #需要忽略多租户的请求,数组
       ignoreTables：  #需要忽略多租户的表

详情请参考类com.dexpo.framework.tenant.config.TenantProperties

COLUMN 模式，基于 MyBatis Plus 自带的多租户功能实现。

核心：每次对数据库操作时，它会自动拼接 WHERE tenant_id = ? 条件来进行租户的过滤，并且基本支持所有的 SQL 场景。

如下是具体方式：

① 需要开启多租户的表，必须添加 tenant_id 字段。例如说 system_users、system_role 等表。
并且该表对应的 DO 需要使用到 tenantId 属性时，建议继承 TenantBaseDO 类。

② 无需开启多租户的表，需要添加表名到 dtt.tenant.ignore-tables 配置项目。
如果不配置的话，MyBatis Plus 会自动拼接 WHERE tenant_id = ? 条件，导致报 tenant_id 字段不存在的错误。

默认情况下，前端的每个请求 Header 必须带上 tenant-id,值为租户编号,t_system_tenant 表的主键编号
如果不带该请求头，会报“租户的请求未传递，请进行排查”错误提示。


## Excel 导入导出[dtt-framework-starter-excel](dtt-framework-starter-bom%2Fdtt-framework-starter-excel)
[ExcelUtils.java](dtt-framework-starter-bom%2Fdtt-framework-starter-excel%2Fsrc%2Fmain%2Fjava%2Fcom%2Fdtt%2Fedp%2Fframework%2Fexcel%2Fcore%2Futil%2FExcelUtils.java)提供简单的write和read方法


## MyBatis 数据库[dtt-framework-starter-mybatis](dtt-framework-starter-bom%2Fdtt-framework-starter-mybatis)
基于 MyBatis Plus 实现数据库的操作。

BaseDO是所有数据库实体的父类，用于规范数据库table的基础字段规范。

1.1 主键编号

id 主键编号，推荐使用 Long 型自增，原因是：

   自增，保证数据库是按顺序写入，性能更加优秀。

   Long 型，避免未来业务增长，超过 Int 范围。

对应的 SQL 字段如下：

      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
项目的 id 默认采用数据库自增的策略，如果希望使用 Snowflake 雪花算法，可以修改 application.yaml 配置文件，将配置项 mybatis-plus.global-config.db-config.id-type 修改为 ASSIGN_ID。


1.2 逻辑删除
所有表通过 deleted 字段来实现逻辑删除，值为 0 表示未删除，值为 1 表示已删除，可见 application.yaml 配置文件的 logic-delete-value 和 logic-not-delete-value 配置项。

① 所有 SELECT 查询，都会自动拼接 WHERE deleted = 0 查询条件，过滤已经删除的记录。如果被删除的记录，只能通过在 XML 或者 @SELECT 来手写 SQL 语句。


②建立唯一索引时，需要额外增加 delete_time 字段，添加到唯一索引字段中，避免唯一索引冲突。例如说，system_users 使用 username 作为唯一索引：

未添加前：先逻辑删除了一条 username = test 的记录，然后又插入了一条 username = test 的记录时，会报索引冲突的异常。

已添加后：先逻辑删除了一条 username = test 的记录并更新 delete_time 为当前时间，然后又插入一条 username = test 并且 delete_time 为 0 的记录，不会导致唯一索引冲突。

1.3 自动填充

[DefaultDBFieldHandler.java](dtt-framework-starter-bom%2Fdtt-framework-starter-mybatis%2Fsrc%2Fmain%2Fjava%2Fcom%2Fdtt%2Fedp%2Fframework%2Fmybatis%2Fcore%2Fhandler%2FDefaultDBFieldHandler.java)基于 MyBatis 自动填充机制，实现 BaseDO 通用字段的自动设置。

1.4 “复杂”字段类型

MyBatis Plus 提供 TypeHandler 字段类型处理器，用于 JavaType 与 JdbcType 之间的转换.

2 字段加密
[EncryptTypeHandler.java](dtt-framework-starter-bom%2Fdtt-framework-starter-mybatis%2Fsrc%2Fmain%2Fjava%2Fcom%2Fdtt%2Fedp%2Fframework%2Fmybatis%2Fcore%2Ftype%2FEncryptTypeHandler.java)EncryptTypeHandler，基于 Hutool AES实现字段的解密与解密。

例如说，数据源配置 的 password 密码需要实现加密存储，则只需要在该字段上添加 EncryptTypeHandler 处理器。
      
      @TableField(typeHandler = EncryptTypeHandler.class) // ② 添加 EncryptTypeHandler 处理器

在 application.yaml 配置文件中，可使用 mybatis-plus.encryptor.password 设置加密密钥。

字段加密后，只允许使用精准匹配，无法使用模糊匹配。


## Redis 缓存[dtt-framework-starter-cache](dtt-framework-starter-bom%2Fdtt-framework-starter-cache)
使用 Redis 实现缓存的功能，它有 2 种使用方式：

编程式缓存：基于 Spring Data Redis 框架的 RedisTemplate 操作模板

声明式缓存：基于 Spring Cache 框架的 @Cacheable 等等注解

1. 编程式缓存


由于 Redisson 提供了分布式锁、队列、限流等特性，所以使用它作为 Spring Data Redis 的客户端。

1.1 Spring Data Redis 配置

① 在 application-local.yaml 配置文件中，通过 spring.redis 配置项，设置 Redis 的配置。

② 在 RedisAutoConfiguration配置类，设置使用 JSON 序列化 value 值。

## 消息队列

### 消息队列（Redis）[dtt-framework-starter-mq](dtt-framework-starter-bom%2Fdtt-framework-starter-mq) 
基于 Redis 实现分布式消息队列：

使用 Stream 特性，提供【集群】消费的能力。

使用 Pub/Sub 特性，提供【广播】消费的能力。



1.集群消费基于 Redis Stream 实现：

实现 AbstractRedisStreamMessage 抽象类，定义【集群】消息。

使用 RedisMQTemplate 的 #send(message) 方法，发送消息。

实现 AbstractRedisStreamMessageListener 接口，消费消息。

最终使用 RedisMQAutoConfiguration 配置类，扫描所有的 AbstractRedisStreamMessageListener 监听器，初始化对应的消费者。

2.广播消费

广播消费基于 Redis Pub/Sub 实现：

实现 AbstractChannelMessage 抽象类，定义【广播】消息。
使用 RedisMQTemplate 的 #send(message) 方法，发送消息。
实现 AbstractRedisChannelMessageListener 接口，消费消息。
最终使用  RedisMQAutoConfiguration 配置类，扫描所有的 AbstractRedisChannelMessageListener 监听器，初始化对应的消费者。
## 任务JOB[dtt-framework-starter-job](dtt-framework-starter-bom%2Fdtt-framework-starter-job)
提供xxl-job的集成。

Async 配置:

在 AsyncAutoConfiguration 配置类，设置使用 TransmittableThreadLocal ，解决异步执行时上下文传递的问题。

项目使用到 ThreadLocal 的地方，建议都使用 TransmittableThreadLocal 进行替换。

 
 


## 消息队列（Kafka）[dtt-framework-starter-kafka](dtt-framework-starter-bom%2Fdtt-framework-starter-kafka)
提供kafka消息队列的集成和配置。

## 幂等性（防重复提交）[dtt-framework-starter-protection](dtt-framework-starter-bom%2Fdtt-framework-starter-protection)
提供声明式的幂等特性，可防止重复请求。

它的实现原理非常简单，针对相同参数的方法，一段时间内，有且仅能执行一次。执行流程如下：

① 在方法执行前，根据参数对应的 Key 查询是否存在：

   如果存在，说明正在执行中，则进行报错。
   如果不在，则计算参数对应的 Key，存储到 Redis 中，并设置过期时间，即标记正在执行中。
   默认参数的 Redis Key 的计算规则由 DefaultIdempotentKeyResolver实现，使用 MD5(方法名 + 方法参数)，避免 Redis Key 过长。

② 方法执行完成，不会主动删除参数对应的 Key。

如果希望会主动删除 Key，可以使用 《开发指南 —— 分布式锁》 提供的 @Lock 来实现幂等性。
从本质上来说，idempotent 包提供的幂等特性，本质上也是基于 Redis 实现的分布式锁。

③ 如果方法执行时间较长，超过 Key 的过期时间，则 Redis 会自动删除对应的 Key。因此，需要大概评估下，避免方法的执行时间超过过期时间。

④ 如果方法执行发生 Exception 异常时，默认会删除 Key，避免下次请求无法正常执行

### @Idempotent 注解
@Idempotent注解，声明在方法上，表示该方法需要开启幂等性。
① 对应的 AOP 切面是 IdempotentAspect类
② 对应的 Redis Key 的前缀是 idempotent:%s，可见 IdempotentRedisDAO 类

#### 使用示例
① 在 pom.xml 文件中，引入 dtt-framework-starter-protection 依赖。

         <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dtt-framework-starter-protection</artifactId>
         </dependency>
② 在该 API 接口的对应方法上，添加 @Idempotent 注解。

      @GetMapping("/test")
      @Idempotent(timeout = 10, message = "重复请求，请稍后重试")
      public CommonResult<T> getTestDemo(@RequestParam("id") Long id) {
         // ... 省略代码
      }

## 单元测试[dtt-framework-starter-test](dtt-framework-starter-bom%2Fdtt-framework-starter-test)

项目使用 Junit5 + Mockito 实现单元测试，提升代码质量、重复测试效率、部署可靠性等。


1.1 快速测试的基类

测试组件提供了 4 种单元测试的基类，通过继承它们，可以快速的构建单元测试的环境。

| 基类    | 作用                              |
|-------|---------------------------------|
|BaseMockitoUnitTest|纯 Mockito 的单元测试|
|BaseDbUnitTest|使用内嵌的 H2 数据库的单元测试|
|BaseRedisUnitTest|使用内嵌的 Redis 缓存的单元测试|
|BaseDbAndRedisUnitTest	|使用内嵌的 H2 数据库 + Redis 缓存的单元测试|

1.2 测试工具类

① RandomUtils 基于 podam开源项目，实现 Bean 对象的随机生成。

② AssertUtils封装 Junit 的 Assert 断言，实现 Bean 对象的断言，支持忽略部分属性。

## 工具类 Util

Hutool
项目使用 Hutool作为主工具库。Hutool 是国产的一个 Java 工具包，它可以帮助我们简化每一行代码，减少每一个方法，让 Java 语言也可以“甜甜的”。

[dtt-framework-starter-common](dtt-framework-starter-bom%2Fdtt-framework-starter-common)模块的 util[util](dtt-framework-starter-bom%2Fdtt-framework-starter-common%2Fsrc%2Fmain%2Fjava%2Fcom%2Fdtt%2Fedp%2Fframework%2Fcommon%2Futil) 包作为辅工具库，以 Utils 结尾，补充 Hutool 缺少的工具能力。

| 作用    | Hutool                              | dtt Utils                            |
|-------|---------------------------------|--------------------------------|
| 数组工具|ArrayUtil|ArrayUtils|
| ⭐ 集合工具|CollUtil|CollectionUtils|
| ⭐ Map 工具|MapUtil|MapUtils|
| Set 工具| |SetUtils|
| List 工具|ListUtil| |
| 文件工具|FileUtil FileTypeUtil|FileUtils|
| 压缩工具|ZipUtil|IoUtils|
| IO 工具|ZipUtil| |
| Resource 工具|ResourceUtil| |
| JSON 工具| |JsonUtils|
| 数字工具|NumberUtil|NumberUtils|
| 对象工具|ObjectUtil|ObjectUtils|
| 唯一 ID 工具|IdUtil| |
| ⭐ 字符串工具|StrUtil|StrUtils|
| 时间工具|DateUtil|DateUtils|
| 反射工具|ReflectUtil| |
| 异常工具|ExceptionUtil| |
| 随机工具|RandomUtil|RandomUtils|
| URL 工具|URLUtil|HttpUtils|
| Servlet 工具| |ServletUtils|
| Spring 工具|SpringUtil| SpringAopUtils SpringExpressionUtils|
| 分页工具| |PageUtils|
| 校验工具|ValidationUtil|ValidationUtils|
| 断言工具|Assert|AssertUtils|

 

## 新建服务
### 新建一个简单的服务
springboot + mybatis 结构
pom.xml
pom继承dtt-framework-starter-parent

    <parent>
        <groupId>com.dexpo</groupId>
        <artifactId>dtt-framework-starter-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

引入dtt-framework-starter-web和dtt-framework-starter-mybatis相关依赖

     <dependencies>
       <!-- web 相关 -->
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dtt-framework-starter-web</artifactId>
        </dependency> 
        <!-- DB 相关 -->
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dtt-framework-starter-mybatis</artifactId>
        </dependency>
    </dependencies>

application.yml

    spring:
      application:
        name: dtt-test
      # 数据源配置项
      autoconfigure:
        exclude:
          - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 排除 Druid 的自动配置，使用 dynamic-datasource-spring-boot-starter 配置多数据源
      datasource:
        druid: # Druid 【监控】相关的全局配置
          web-stat-filter:
            enabled: true
          stat-view-servlet:
            enabled: true
            allow: # 设置白名单，不填则允许所有访问
            url-pattern: /druid/*
            login-username: # 控制台管理用户名和密码
            login-password:
          filter:
            stat:
              enabled: true
              log-slow-sql: true # 慢 SQL 记录
              slow-sql-millis: 100
              merge-sql: true
            wall:
              config:
                multi-statement-allow: true
        dynamic: # 多数据源配置
          druid: # Druid 【连接池】相关的全局配置
            initial-size: 5 # 初始连接数
            min-idle: 10 # 最小连接池数量
            max-active: 20 # 最大连接池数量
            max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
            time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
            min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
            max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
            validation-query: SELECT 1 FROM DUAL # 配置检测连接是否有效
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
          primary: master
          datasource:
            master:
              name: dtt_system
              url: ***************************/${spring.datasource.dynamic.datasource.master.name}?allowMultiQueries=true&useUnicode=true&useSSL=false&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&autoReconnect=true&nullCatalogMeansCurrent=true # MySQL Connector/J 8.X 连接的示例
              driver-class-name: com.mysql.jdbc.Driver
              username: root
              password: 123456 
    
    dtt:
      info:
        version: 1.0.0
        base-package: com.dexpo    
      web:
        admin-ui:
          url: http://127.0.0.1:48081/admin-api # Admin 管理后台 UI 的地址
    
    logging:
      config: classpath:logback-spring.xml

## 引入其它功能
根据实际业务需要，选择性引入对应的maven依赖









