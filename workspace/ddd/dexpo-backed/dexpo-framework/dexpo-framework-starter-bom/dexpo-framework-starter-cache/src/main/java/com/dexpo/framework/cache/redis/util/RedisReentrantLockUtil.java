/**
     * 尝试获取分布式锁。
     *
     * @param rLock 分布式锁对象，实现ReentrantLock接口。
     * @param waitTime 获取锁的最大等待时间。
     * @param unit 等待时间的单位。
     * @return 如果成功获取锁返回true，否则返回false。
     * @throws RuntimeException 如果在尝试获取锁的过程中发生异常。
     */
    public boolean tryLock(RLock rLock, int waitTime, TimeUnit unit) {
        try {
            boolean isLocked = rLock.tryLock(waitTime, unit);
            if (!isLocked && rLock.isHeldByCurrentThread()) {
                // 删除原因：当 isLocked 为 false 时，线程并未持有锁，无需释放。
            }
            return isLocked;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
