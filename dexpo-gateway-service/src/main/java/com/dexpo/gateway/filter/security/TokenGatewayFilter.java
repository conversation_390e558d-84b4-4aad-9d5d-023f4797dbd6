package com.dexpo.gateway.filter.security;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.dexpo.framework.cache.redis.constant.BasicRedisKey;
import com.dexpo.framework.cache.redis.constant.ICacheKey;
import com.dexpo.framework.cache.redis.entity.base.SysPermissionInterfaceRelationCache;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.framework.common.enums.ValueSetUserTypeEnum;
import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.exception.enums.ErrorCodeEnums;
import com.dexpo.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.dexpo.framework.security.core.LoginUser;
import com.dexpo.framework.security.core.service.TokenService;
import com.dexpo.gateway.util.ResponseDecoratorUtils;
import com.dexpo.gateway.util.SecurityFrameworkUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Token 过滤器，验证 token 的有效性
 * 1. 验证通过时，将 userId、userType、tenantId 通过 Header 转发给服务
 * 2. 验证不通过，还是会转发给服务。因为，接口是否需要登录的校验，还是交给服务自身处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RefreshScope
public class TokenGatewayFilter implements GlobalFilter, Ordered {

    // 本地权限缓存
    private static Map<String, SysPermissionInterfaceRelationCache> PERMISSION_FUNCTION_LOCAL_CACHE = new ConcurrentHashMap<>();
    @Value("${dexpo.gateway.skipPermission:false}")
    private Boolean SKIP;

    @Value("${dexpo.gateway.uriWhitelist}")
    private List<String> whiteListApis;

    @Value("${dexpo.gateway.notManagePrefixUrl}")
    private List<String> notManagePrefixUrl;
    @Resource
    private RedisService redisService;
    @Value("${dexpo.gateway.localCache}")
    private Boolean localCache;
    @Value("${dexpo.gateway.localCacheTtl}")
    private Long localCacheTtl;

    /**
     * 空的 LoginUser 的结果
     *
     * 用于解决如下问题：
     * 1. {@link #getLoginUser(String)} 返回 Mono.empty() 时，会导致后续的 flatMap 无法进行处理的问题。
     */
    private static final LoginUser LOGIN_USER_EMPTY = new LoginUser();

    @Resource
    private TokenService tokenService;

    @Override
    public Mono<Void> filter(final ServerWebExchange exchange, GatewayFilterChain chain) {
        // 移除 login-user 的请求头，避免伪造模拟
        SecurityFrameworkUtils.removeLoginUser(exchange);

        // 公共接口
        ServerHttpRequest request = exchange.getRequest();

        // 获取url
        String requestUri = request.getPath().pathWithinApplication().value();
        log.info("requestUri : 【{}】",requestUri);

        // 过滤白名单接口
        log.info("whiteListApis : 【{}】", whiteListApis);
        if(getWhiteListApis().contains(requestUri)){
            return buildServerHttpResponseDecorator(exchange,chain);
        }

        // 校验请求头中是否包含token
        String token = SecurityFrameworkUtils.obtainAuthorization(exchange);
        if (CharSequenceUtil.isEmpty(token)) {
            throw ErrorCodeEnums.GATEWAY_NOT_FIND_TOKEN.getServiceException();
        }

        // 根据token查询用户基本信息
        LoginUser userInfoByToke = tokenService.getUserInfoByToke(token);
        if (userInfoByToke == LOGIN_USER_EMPTY) {
            throw ErrorCodeEnums.GATEWAY_NOT_FIND_TOKEN.getServiceException();
        }

        // 鉴权开关
        if (SKIP) {
            return skipByUser(exchange, chain);
        }

        // 非内部用户
        ValueSetUserTypeEnum byCode = ValueSetUserTypeEnum.getByCode(userInfoByToke.getUserType());
        if (byCode == null) {
            throw ErrorCodeEnums.GATEWAY_NOT_FIND_TOKEN.getServiceException();
        }

        // 外部用户，如果访问的是ciif的就云效登录
//        if (!byCode.equals(ValueSetUserTypeEnum.SPONSOR)) {
//            for (String prefixUrl : notManagePrefixUrl){
//                if(requestUri.startsWith(prefixUrl)){
//                    return chain.filter(exchange);
//                }
//            }
//            throw new ServiceException(GlobalErrorCodeConstants.FORBIDDEN.getCode(),
//                    GlobalErrorCodeConstants.FORBIDDEN.getMsg(), GlobalErrorCodeConstants.FORBIDDEN.getMsgEn()) ;
//        }

        // 功能权限校验
        if (!checkPermissionFunction(requestUri, userInfoByToke)) {
            throw new ServiceException(GlobalErrorCodeConstants.FORBIDDEN.getCode(),
                    GlobalErrorCodeConstants.FORBIDDEN.getMsg(), GlobalErrorCodeConstants.FORBIDDEN.getMsgEn());
        }

        return getVoidMono(exchange, chain, token);
    }

    private Mono<Void> skipByUser(ServerWebExchange exchange, GatewayFilterChain chain) {
        String token = SecurityFrameworkUtils.obtainAuthorization(exchange);
        LoginUser localUser = tokenService.getUserInfoByToke(token);

        tokenService.refreshAccessToken(token);

        // 2.1 有用户，则设置登录用户
        SecurityFrameworkUtils.setLoginUser(exchange, localUser);
        // 2.2 将 user 并设置到 login-user 的请求头，使用 json 存储值
        log.info("localUser {}", localUser);
        ServerWebExchange newExchange = exchange.mutate()
                .request(builder -> SecurityFrameworkUtils.setLoginUserHeader(builder, localUser)).build();

        return buildServerHttpResponseDecorator(newExchange, chain);
    }

    @NotNull
    private Mono<Void> getVoidMono(ServerWebExchange exchange, GatewayFilterChain chain, String token) {
        return getLoginUser(token).defaultIfEmpty(LOGIN_USER_EMPTY).flatMap(user -> {
            // 1. 无用户，直接 filter 继续请求
            if (user == LOGIN_USER_EMPTY) {
                return chain.filter(exchange);
            }

            // 2.1 有用户，则设置登录用户
            SecurityFrameworkUtils.setLoginUser(exchange, user);
            // 2.2 将 user 并设置到 login-user 的请求头，使用 json 存储值
            ServerWebExchange newExchange = exchange.mutate()
                    .request(builder -> SecurityFrameworkUtils.setLoginUserHeader(builder, user)).build();
            return chain.filter(newExchange);
        });
    }

    private Mono<LoginUser> getLoginUser(String token) {
        // 从缓存中，获取 LoginUser
        LoginUser tokenUser = tokenService.getUserInfoByToke(token);
        if (tokenUser != null) {
            return Mono.just(tokenUser);
        }

        return Mono.empty();
    }

    private Mono<Void> buildServerHttpResponseDecorator(ServerWebExchange exchange, GatewayFilterChain chain){
        return chain.filter(exchange.mutate()
                .response(ResponseDecoratorUtils.addHttpHeaders(exchange.getResponse()))
                .build());
    }

    private Set<String> getWhiteListApis(){
        Set<String> whiteListApiSet = new HashSet<>();
        whiteListApiSet.addAll(whiteListApis);
        return whiteListApiSet;
    }

    @Override
    public int getOrder() {
        // 和 Spring Security Filter 的顺序对齐
        return -100;
    }

    private boolean checkPermissionFunction(String requestUri, LoginUser loginUser) {
        SysPermissionInterfaceRelationCache permissionFunctionCache = getCacheSysPermissionInterfaceRelationCache(requestUri);
        if (permissionFunctionCache == null) {
            return false;
        }
        if (Boolean.TRUE.equals(permissionFunctionCache.getIsPublic())) {
            return true;
        }
        return loginUser.getScopes().contains(permissionFunctionCache.getPermissionCode());
    }

    private SysPermissionInterfaceRelationCache getCacheSysPermissionInterfaceRelationCache(String requestUri) {
        if (localCache) {
            SysPermissionInterfaceRelationCache cache = PERMISSION_FUNCTION_LOCAL_CACHE.get(requestUri);
            if (ObjectUtil.isNotNull(cache) && cache.getLocalTtl() > System.currentTimeMillis()) {
                return cache;
            }
        }
        String urlKey = ICacheKey.generateKey(BasicRedisKey.BASIC_LOGIN_VALID_KEY, requestUri);
        SysPermissionInterfaceRelationCache permission = redisService.getCacheObject(urlKey);
        if (ObjectUtil.isNotNull(permission)) {
            permission.setLocalTtl(System.currentTimeMillis() + localCacheTtl);
            PERMISSION_FUNCTION_LOCAL_CACHE.put(requestUri, permission);
            return permission;
        }
        return null;
    }
}
