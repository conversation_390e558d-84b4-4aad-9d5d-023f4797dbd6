package com.dexpo.gateway.util;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;

/**
 * eds 处理响应头
 * <AUTHOR>
 */
public class ResponseDecoratorUtils {

    public static ServerHttpResponseDecorator addHttpHeaders( ServerHttpResponse response){

        getResponse(response);

        return new ServerHttpResponseDecorator(response);
    }

    public static void getResponse(ServerHttpResponse response) {
        HttpHeaders headers = response.getHeaders();
        for (String header : HttpEdsHeadersConstant.HTTP_EDS_HEADER_LIST) {
            if(headers.containsKey(header)){
                response.getHeaders().remove(header);
            }
        }

        response.getHeaders().add(HttpEdsHeadersConstant.X_CONTENT_TYPE_OPTIONS
                , HttpEdsHeadersConstant.X_CONTENT_TYPE_OPTIONS_VALUE);
        response.getHeaders().add(HttpEdsHeadersConstant.X_FRAME_OPTIONS
                , HttpEdsHeadersConstant.X_FRAME_OPTIONS_VALUE);
        response.getHeaders().add(HttpEdsHeadersConstant.CACHE_CONTROL
                , HttpEdsHeadersConstant.CACHE_CONTROL_VALUE);
        response.getHeaders().add(HttpEdsHeadersConstant.CONTENT_SECURITY_POLICY,
                HttpEdsHeadersConstant.CONTENT_SECURITY_POLICY_VALUE);
        response.getHeaders().add(HttpEdsHeadersConstant.FEATURE_POLICY,
                HttpEdsHeadersConstant.FEATURE_POLICY_VALUE);
        response.getHeaders().add(HttpEdsHeadersConstant.REFERRER_POLICY,
                HttpEdsHeadersConstant.REFERRER_POLICY_VALUE);
    }
}
