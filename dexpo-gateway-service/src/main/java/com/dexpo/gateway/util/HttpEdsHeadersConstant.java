package com.dexpo.gateway.util;

import java.util.Arrays;
import java.util.List;

/**
 * eds 自定义请求头
 * <AUTHOR>
 */
public class HttpEdsHeadersConstant {

    public static final String X_CONTENT_TYPE_OPTIONS = "X-Content-Type-Options";
    public static final String X_CONTENT_TYPE_OPTIONS_VALUE = "nosniff";
    public static final String X_FRAME_OPTIONS = "X-Frame-Options";
    public static final String X_FRAME_OPTIONS_VALUE = "DENY";
    public static final String CACHE_CONTROL = "Cache-Control";
    public static final String CACHE_CONTROL_VALUE = "no-store";
    public static final String CONTENT_TYPE = "Content-type";
    public static final String CONTENT_TYPE_VALUE = "application/json";

    public static final String CONTENT_SECURITY_POLICY = "Content-Security-Policy";
    public static final String CONTENT_SECURITY_POLICY_VALUE = "default-src none";
    public static final String FEATURE_POLICY = "Feature-Policy";
    public static final String FEATURE_POLICY_VALUE = "none";
    public static final String REFERRER_POLICY = "Referrer-Policy";
    public static final String REFERRER_POLICY_VALUE = "no-referrer";

    public static final List<String> HTTP_EDS_HEADER_LIST = Arrays.asList(X_CONTENT_TYPE_OPTIONS,
            X_FRAME_OPTIONS, CACHE_CONTROL, CONTENT_TYPE, CONTENT_SECURITY_POLICY,FEATURE_POLICY,REFERRER_POLICY
            );


}
