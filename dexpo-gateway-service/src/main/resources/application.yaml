spring:
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。

  cloud:
    kubernetes:
      discovery:
        # 让所有命名空间服务都可以发现服务
        namespaces:
          - ${ENV}
        # 发现未标记为“就绪”的服务端地址
        include-not-ready-addresses: true
        # ExternalName类型服务的列表  DiscoveryClient::getInstances 返回该列表 ServiceInstance::getMetadata
        include-external-name-services: true
        enabled: true
    # Spring Cloud Gateway 配置项，对应 GatewayProperties 类
    gateway:
      x-forwarded:
        prefix-enabled: false # 避免 Swagger 重复带上额外的 /admin-api/system 前缀
      routes:
        # dexpo-service-bff-manage 服务
        - id: dexpo-service-bff-manage # 路由的编号
          uri: lb:http://dexpo-service-bff-manage
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/manage/**
        # dexpo-service-bff-ciif 服务
        - id: dexpo-service-bff-ciif # 路由的编号
          uri: lb:http://dexpo-service-bff-ciif
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/ciif/**

knife4j:
  # 聚合 Swagger 文档，参考 https://doc.xiaominfo.com/docs/action/springcloud-gateway 文档
  gateway:
    enabled: true
    routes:
      - name: dexpo-service-bff-manage
        service-name: dexpo-service-bff-manage
        url: /admin-api/product/v3/api-docs
      - name: dexpo-service-bff-ciif
        service-name: dexpo-service-bff-ciif
        url: /admin-api/order/v3/api-docs


management:
  endpoints:
    web:
      base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
      exposure:
        include: gateway # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。
  endpoint:
    health:
      show-details: always
    # 重点在这里,默认为false,所以没有gateway相关端点,打开后就可以访问
    gateway:
      enabled: true

---
dexpo:
  info:
    version: 1.0.0
    base-package: com.dexpo.gateway
  gateway:
    skipPermission: true
    uriWhitelist: /ciif/member/mediaMember/login,
      /ciif/exhibition/info/getExhibition,
      /ciif/common/valid/getCode,/ciif/exhibition/agreement/getAgreementInfo,
      /ciif/exhibition/agreement/getAgreementContent,
      /ciif/member/mediaMember/login,/manage/sponsor/loginValidCode,/manage/sponsor/login,
      /ciif/audience/login

    notManagePrefixUrl: /ciif
    localCache: true
    localCacheTtl: 60000