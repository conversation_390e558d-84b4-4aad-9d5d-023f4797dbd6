spring:
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。

  cloud:
    # Spring Cloud Gateway 配置项，对应 GatewayProperties 类
    gateway:
      # 路由配置项，对应 RouteDefinition 数组
      routes:
#        - id: openapi
#          uri: http://localhost:${server.port}
#          predicates:
#            - Path=/v3/api-docs/**
#          filters:
#            - RewritePath=/v3/api-docs/(?<path>.*), /$\{path}/v3/api-docs
        ## dtt-system-api 服务
        - id: dtt-system-admin-api # 路由的编号
          uri: http://dtt-system-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/admin-api/system/**
          filters:
              - RewritePath=/admin-api/system/v3/api-docs(?<segment>/?.*), /v3/api-docs$\{segment}
              - RewritePath=/admin-api/system/webjars(?<segment>/?.*), /webjars$\{segment}
              - RewritePath=/admin-api/system/doc.(?<segment>/?.*), /doc.$\{segment}
#          filters:
#            - StripPrefix=1
        - id: dtt-system-app-api # 路由的编号
          uri: http://dtt-system-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/app-api/system/**
          filters:
              - RewritePath=/app-api/system/v3/api-docs, /v3/api-docs
#              - RewritePath=/app-api/system/(?<segment>/?.*), /app-api/$\{segment}
        ## dtt-infra-api 服务
        - id: dtt-infra-admin-api # 路由的编号
          uri: http://dtt-system-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/admin-api/infra/**
          filters:
              - RewritePath=/admin-api/infra/v3/api-docs, /v3/api-docs
#          filters:
#            - StripPrefix=1
        - id: dtt-infra-app-api # 路由的编号
          uri: http://dtt-system-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/app-api/infra/**
          filters:
              - RewritePath=/app-api/infra/v3/api-docs, /v3/api-docs
        ## dtt-rpc-api 服务
        - id: dtt-system-rpc-api # 路由的编号
          uri: http://dtt-system-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/rpc-api/system/**
          filters:
              - RewritePath=/rpc-api/system/v3/api-docs, /v3/api-docs
#          filters:
#            - StripPrefix=1

        - id: dtt-product-admin-api # 路由的编号
          uri: http://dtt-product-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/admin-api/product/**
          filters:
              - RewritePath=/admin-api/product/v3/api-docs(?<segment>/?.*), /v3/api-docs$\{segment}
              - RewritePath=/admin-api/product/webjars(?<segment>/?.*), /webjars$\{segment}
              - RewritePath=/admin-api/product/doc.(?<segment>/?.*), /doc.$\{segment}
              - RewritePath=/admin-api/product/(?<segment>/?.*), /admin-api/$\{segment}

#          filters:
#              - StripPrefix=2
#          filters:
#            - StripPrefix=1
        - id: dtt-product-app-api # 路由的编号
          uri: http://dtt-product-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/app-api/product/**
          filters:
              - RewritePath=/app-api/product/(?<segment>/?.*), /app-api/$\{segment}
#          filters:
#              - RewritePath=/app-api/product/v3/api-docs, /v3/api-docs
#          filters:
#              - StripPrefix=2
#          filters:
#            - StripPrefix=1

        ## dtt-product-rpc-api 服务
        - id: dtt-product-rpc-api # 路由的编号
          uri: http://dtt-product-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/rpc-api/product/**
          filters:
            - RewritePath=/rpc-api/product/v3/api-docs, /v3/api-docs

        - id: dtt-subscription-admin-api # 路由的编号
          uri: http://dtt-subscription-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/admin-api/subscription/**
          filters:
              - RewritePath=/admin-api/subscription/v3/api-docs(?<segment>/?.*), /v3/api-docs$\{segment}
              - RewritePath=/admin-api/subscription/webjars(?<segment>/?.*), /webjars$\{segment}
              - RewritePath=/admin-api/subscription/doc.(?<segment>/?.*), /doc.$\{segment}
              - RewritePath=/admin-api/subscription/(?<segment>/?.*), /admin-api/$\{segment}
#          filters:
#              - RewritePath=/admin-api/subscription/v3/api-docs, /v3/api-docs
#          filters:
#              - StripPrefix=2
#          filters:
#            - StripPrefix=1
        - id: dtt-subscription-app-api # 路由的编号
          uri: http://dtt-subscription-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/app-api/subscription/**
          filters:
              - RewritePath=/app-api/subscription/(?<segment>/?.*), /app-api/$\{segment}
#          filters:
#              - RewritePath=/app-api/subscription/v3/api-docs, /v3/api-docs
#          filters:
#              - StripPrefix=2
#          filters:
#            - StripPrefix=1

        ## dtt-subscription-rpc-api 服务
        - id: dtt-subscription-rpc-api # 路由的编号
          uri: http://dtt-subscription-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/rpc-api/subscription/**
          filters:
            - RewritePath=/rpc-api/subscription/v3/api-docs, /v3/api-docs

## order route
        - id: dtt-order-admin-api # 路由的编号
          uri: http://dtt-order-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/admin-api/order/**
          filters:
              - RewritePath=/admin-api/order/v3/api-docs(?<segment>/?.*), /v3/api-docs$\{segment}
              - RewritePath=/admin-api/order/webjars(?<segment>/?.*), /webjars$\{segment}
              - RewritePath=/admin-api/order/doc.(?<segment>/?.*), /doc.$\{segment}
              - RewritePath=/admin-api/order/(?<segment>/?.*), /admin-api/$\{segment}
        - id: dtt-order-app-api # 路由的编号
          uri: http://dtt-order-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/app-api/order/**
          filters:
              - RewritePath=/app-api/order/(?<segment>/?.*), /app-api/$\{segment}

        ## dtt-order-rpc-api 服务
        - id: dtt-order-rpc-api # 路由的编号
          uri: http://dtt-order-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/rpc-api/order/**
          filters:
            - RewritePath=/rpc-api/order/v3/api-docs, /v3/api-docs

## consumer route
        - id: dtt-consumer-admin-api # 路由的编号
          uri: http://dtt-consumer-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/admin-api/consumer/**
          filters:
              - RewritePath=/admin-api/consumer/v3/api-docs(?<segment>/?.*), /v3/api-docs$\{segment}
              - RewritePath=/admin-api/consumer/webjars(?<segment>/?.*), /webjars$\{segment}
              - RewritePath=/admin-api/consumer/doc.(?<segment>/?.*), /doc.$\{segment}
              - RewritePath=/admin-api/consumer/(?<segment>/?.*), /admin-api/$\{segment}
        - id: dtt-consumer-app-api # 路由的编号
          uri: http://dtt-consumer-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/app-api/consumer/**
          filters:
              - RewritePath=/app-api/consumer/(?<segment>/?.*), /app-api/$\{segment}
## consumerbff route
        - id: dtt-consumerbff-admin-api # 路由的编号
          uri: http://dtt-consumerbff-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/admin-api/consumerbff/**
          filters:
              - RewritePath=/admin-api/consumerbff/v3/api-docs(?<segment>/?.*), /v3/api-docs$\{segment}
              - RewritePath=/admin-api/consumerbff/webjars(?<segment>/?.*), /webjars$\{segment}
              - RewritePath=/admin-api/consumerbff/doc.(?<segment>/?.*), /doc.$\{segment}
              - RewritePath=/admin-api/consumerbff/(?<segment>/?.*), /admin-api/$\{segment}
        - id: dtt-consumerbff-app-api # 路由的编号
          uri: http://dtt-consumerbff-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/app-api/consumerbff/**
          filters:
              - RewritePath=/app-api/consumerbff/(?<segment>/?.*), /app-api/$\{segment}
## payment route
        - id: dtt-payment-admin-api # 路由的编号
          uri: http://dtt-payment-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/admin-api/payment/**
          filters:
              - RewritePath=/admin-api/payment/v3/api-docs(?<segment>/?.*), /v3/api-docs$\{segment}
              - RewritePath=/admin-api/payment/webjars(?<segment>/?.*), /webjars$\{segment}
              - RewritePath=/admin-api/payment/doc.(?<segment>/?.*), /doc.$\{segment}
              - RewritePath=/admin-api/payment/(?<segment>/?.*), /admin-api/$\{segment}
        - id: dtt-payment-app-api # 路由的编号
          uri: http://dtt-payment-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/app-api/payment/**
          filters:
              - RewritePath=/app-api/payment/(?<segment>/?.*), /app-api/$\{segment}
## message route
        - id: dtt-message-admin-api # 路由的编号
          uri: http://dtt-message-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/admin-api/message/**
          filters:
              - RewritePath=/admin-api/message/v3/api-docs(?<segment>/?.*), /v3/api-docs$\{segment}
              - RewritePath=/admin-api/message/webjars(?<segment>/?.*), /webjars$\{segment}
              - RewritePath=/admin-api/message/doc.(?<segment>/?.*), /doc.$\{segment}
              - RewritePath=/admin-api/message/(?<segment>/?.*), /admin-api/$\{segment}
        - id: dtt-message-app-api # 路由的编号
          uri: http://dtt-message-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/app-api/message/**
          filters:
              - RewritePath=/app-api/message/(?<segment>/?.*), /app-api/$\{segment}
## integration route
        - id: dtt-integration-admin-api # 路由的编号
          uri: http://dtt-integration-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/admin-api/integration/**
          filters:
              - RewritePath=/admin-api/integration/v3/api-docs(?<segment>/?.*), /v3/api-docs$\{segment}
              - RewritePath=/admin-api/integration/webjars(?<segment>/?.*), /webjars$\{segment}
              - RewritePath=/admin-api/integration/doc.(?<segment>/?.*), /doc.$\{segment}
              - RewritePath=/admin-api/integration/(?<segment>/?.*), /admin-api/$\{segment}
        - id: dtt-integration-app-api # 路由的编号
          uri: http://dtt-integration-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/app-api/integration/**
          filters:
              - RewritePath=/app-api/integration/(?<segment>/?.*), /app-api/$\{segment}
## notification route
        - id: dtt-notification-admin-api # 路由的编号
          uri: http://dtt-notification-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/admin-api/notification/**
          filters:
              - RewritePath=/admin-api/notification/v3/api-docs(?<segment>/?.*), /v3/api-docs$\{segment}
              - RewritePath=/admin-api/notification/webjars(?<segment>/?.*), /webjars$\{segment}
              - RewritePath=/admin-api/notification/doc.(?<segment>/?.*), /doc.$\{segment}
              - RewritePath=/admin-api/notification/(?<segment>/?.*), /admin-api/$\{segment}
        - id: dtt-notification-app-api # 路由的编号
          uri: http://dtt-notification-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/app-api/notification/**
          filters:
              - RewritePath=/app-api/notification/(?<segment>/?.*), /app-api/$\{segment}

## report route
        - id: dtt-report-admin-api # 路由的编号
          uri: http://dtt-report-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/admin-api/report/**
          filters:
              - RewritePath=/admin-api/report/v3/api-docs(?<segment>/?.*), /v3/api-docs$\{segment}
              - RewritePath=/admin-api/report/webjars(?<segment>/?.*), /webjars$\{segment}
              - RewritePath=/admin-api/report/doc.(?<segment>/?.*), /doc.$\{segment}
              - RewritePath=/admin-api/report/(?<segment>/?.*), /admin-api/$\{segment}
        - id: dtt-report-app-api # 路由的编号
          uri: http://dtt-report-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/app-api/report/**
          filters:
              - RewritePath=/app-api/report/(?<segment>/?.*), /app-api/$\{segment}


## saml route
        - id: saml-sso-project # 路由的编号
          uri: http://saml-sso-project:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/saml/**
          filters:
              - RewritePath=/saml/v3/api-docs(?<segment>/?.*), /v3/api-docs$\{segment}
              - RewritePath=/saml/webjars(?<segment>/?.*), /webjars$\{segment}
              - RewritePath=/saml/doc.(?<segment>/?.*), /doc.$\{segment}
              - RewritePath=/saml/(?<segment>/?.*), /saml/$\{segment}



        - id: dtt-adminer # 路由的编号
          uri: http://adminer80:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/adminer80/**
          filters:
              - StripPrefix=1
        - id: dtt-xxl-job # 路由的编号
          uri: http://dtt-schedule-service:80
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
              - Path=/xxl-job-admin/**
#          filters:
#              - StripPrefix=1

      x-forwarded:
        prefix-enabled: false # 避免 Swagger 重复带上额外的 /admin-api/system 前缀

jwt:
  secret: FSLFJDSOfsfsfdsf453453

#springdoc:
#  enable-native-support: true
#  api-docs:
#    groups:
#      enabled: true
#    enabled: true
#  group-configs:
#    - group: service-1
#      paths-to-match:
#        - /admin-api/system/**
#      display-name: Service 1
#  swagger-ui:
#    config-url: /v3/api-docs/swagger-config
#    url: /v3/api-docs
#    urls:
#      - url: /admin-api/system/v3/api-docs
#        name: Service 1