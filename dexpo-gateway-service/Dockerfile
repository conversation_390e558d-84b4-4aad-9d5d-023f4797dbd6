# 基础镜像
FROM ciif-acr-registry.cn-shanghai.cr.aliyuncs.com/dev/eclipse-temurin:21-jdk-jammy

# 挂载目录
VOLUME /home/<USER>

# 创建目录
RUN mkdir -p /home/<USER>/dexpo-gateway-service

# 指定路径
WORKDIR /home/<USER>/dexpo-gateway-service

# 复制jar文件到路径
COPY target/dexpo-gateway-service.jar /home/<USER>/dexpo-gateway-service/dexpo-gateway-service.jar

ENV TZ=Asia/Shanghai

EXPOSE 48088
# 启动
CMD java -jar dexpo-gateway-service.jar
