apiVersion: v1
kind: ConfigMap
metadata:
  name: crm-consumer-center-service-config
  labels:
    app: crm-consumer-center-service
data:
  AA_APP_ConfirmedAPP: "APP, ConfirmedAPP"
  AA_APP_FlagshipAPP: "APP, FlagshipAPP"
  AA_WebSite_COM: "WebSite, .COM"
  APP: "100000008, APP, ConfirmedAPP, , adidasconfirmedapp"
  AliPay: "100000009, MemberHub, AliPay, , Alipay"
  ECOM: "100008003, WebSite, .COM, , Ecommerce官网"
  ECP_CHANNEL: "100000013"
  FAYA: "100000001, FRS, FAYA, , 法雅"
  JD: "100000015, JD, JD-FlagshipStore, JD, 京东"
  JoyRUN: "100008000, Social, JoyRun, , Joyrun"
  MEM_ALS_FO: "100000000, ALS, FO, , 门店POS"
  MEM_ALS_OR: "100000000, ALS, OR, , 门店POS"
  MEM_APP_ConfirmedAPP: "100000030, APP, ConfirmedAPP, ,adidasconfirmedapp"
  MEM_APP_FlagshipAPP: "100000019, APP, FlagshipAPP, , APP"
  MEM_FRS_FAYA: "100000023, FRS, FAYA, , 北京法雅FAYA"
  MEM_FRS_Really: "100000022, FRS, Really, ,上海锐力Really"
  MEM_FRS_SCLP: "100000026, FRS, SCLP, , 四川领跑SCLP"
  MEM_FRS_SSCY: "100000027, FRS, SSCY, , 盛世长运SSCY"
  MEM_FRS_XMYT: "100000024, FRS, XMYT, , 厦门育泰XMYT"
  MEM_FRS_YY: "100000018, FRS, YY, , YY"
  MEM_JD_FlagshipStore: "100000003, JD, JD-FlagshipStore, , JD"
  MEM_MemberHub_AdiClub: "100000005, MemberHub, AdiClub, , Wechat"
  MEM_MemberHub_AliPay: "100000027, MemberHub, AliPay, , Alipay"
  MEM_MemberHub_EWFO: "100000033, MemberHub, EWFO, , 企业微信"
  MEM_MemberHub_EWOR: "100000033, MemberHub, EWOR, , 企业微信"
  MEM_ORWS: "100000037, Wechat, ORWS, , ORWS"
  MEM_PDD_FlagshipStore: "100000035, PDD, PDD-FlagshipStore, PDD, 拼多多"
  MEM_Social_Joyrun: "100000012, Social, JoyRun, , Joyrun"
  MEM_Tiktok_Adidas: "100000034, Tiktok, Tiktok-Adidas, , 抖音"
  MEM_Tiktok_FlagshipStore: "100000034, Tiktok, Tiktok-FlagshipStore, DOUYINFSS, 抖音"
  MEM_Tiktok_KIDS: "100000034, Tiktok, Tiktok-Kids, DOUYINKIDSFSS, 抖音"
  MEM_Tiktok_Original: "100000034, Tiktok, Tiktok-Original, , 抖音"
  MEM_Tiktok_WOMEN: "100000034, Tiktok, Tiktok-Women, DOUYINWOMENFSS, 抖音"
  MEM_Tmall_FlagshipStore: "100000002, Tmall, TMall-FlagshipStore, Tmall, 天猫"
  MEM_Tmall_KIDS: "100000002, Tmall, TMall-Kids, TMKIDS, 天猫"
  MEM_Tmall_NEO: "100000002, Tmall, TMall-Neo, TmallNeo, 天猫"
  MEM_Tmall_Original: "100000002, Tmall, TMall-Original, TMORI, 天猫"
  MEM_Tmall_Outlet: "100000002, Tmall, TMall-Outlet, TMFB, 天猫"
  MEM_Tmall_Terrex: "100000002, Tmall, TMall-Terrex, TMallterrex, 天猫"
  MEM_WebSite_COM: "100000004, WebSite, .COM, ECOM, Ecommerce官网"
  MEM_Wechat_WMS: "100000021, Wechat, WMS, , Wechat Miniprogram Store"
  MemberHub_AdiClub: "100000010, MemberHub, AdiClub, WECHAT, Wechat"
  ORWS: "100000019, Wechat, ORWS, , ORWS"
  PDD: "100000018, PDD, PDD-Offical, PDD, 拼多多"
  PDD_ACCESS_TOKEN: "f80089d021694da0b81521a047e159ac84240e15"
  PDD_CLIENT_ID: "c13a0f59f49641e1a3e9be1f3c543789"
  PDD_CLIENT_SECRET: "7c3c517ece421a0f8be66b00dc02663d557e0a68"
  PDD_SYNC_URI: "pdd.membership.sync"
  PDD_TARGET_CLIENTID: "ef58afc707f946809037c7a1e7c4040a"
  PDD_URL: "https://ark-api.pinduoduo.com/ark/router"
  QQ: "100000012, Social, QQMusic, , QQ"
  REALLY: "100000000, FRS, Really, , 锐力"
  SCLP: "100000004, FRS, SCLP, , 四川领跑"
  SSCY: "100000005, FRS, SSCY, , 盛世长运"
  TIKTOK_APP_KEY: "6891524019653215757"
  TIKTOK_APP_SECRET: "65716311-e29b-4188-924f-3746cbbcc784"
  TIKTOK_URL: "https://openapi-fxg.jinritemai.com"
  TMALL_APP_KEY: "34283600"
  TMALL_SECRET: "72fe0274434bfa4c39224b2640610714"
  TMALL_URL: "http://gw.api.taobao.com/router/rest"
  Tiktok_Adidas: "100000017, Tiktok, Tiktok-Adidas, , 抖音"
  Tiktok_FlagshipStore: "100000017, Tiktok, Tiktok-FlagshipStore, DOUYINFSS, 抖音"
  Tiktok_KIDS: "100000017, Tiktok, Tiktok-Kids, DOUYINKIDSFSS, 抖音"
  Tiktok_Original: "100000017, Tiktok, Tiktok-Original, , 抖音"
  Tiktok_WOMEN: "100000017, Tiktok, Tiktok-Women, DOUYINWOMENFSS, 抖音"
  Tmall_FlagshipStore: "100008002, Tmall, TMall-FlagshipStore, Tmall, 天猫"
  Tmall_KIDS: "100008002, Tmall, TMall-Kids, TMKIDS, 天猫"
  Tmall_NEO: "00008002, Tmall, TMall-Neo, TmallNeo, 天猫"
  Tmall_Original: "100008002, Tmall, TMall-Original, TMORI, 天猫"
  Tmall_Outlet: "100008002, Tmall, TMall-Outlet, TMFB, 天猫"
  Tmall_Terrex: "100008002, Tmall, TMall-Terrex, TMallterrex, 天猫"
  V1_KAFKA_ENABLE: "1"
  WECHAT: "100000010, Wechat, WMS, , 微信"
  XMYT: "100000002, FRS, XMYT, , 厦门育泰"
  YY: "100008001, FRS, YY, , 胜道体育"
  accumulated_consumption_amount_cycle: "12"
  aurora_addr_reader: "aurora-prod.cluster-ro-cfnwbdk0oyi6.rds.cn-north-1.amazonaws.com.cn:3306"
  aurora_addr_write: "aurora-prod.cluster-cfnwbdk0oyi6.rds.cn-north-1.amazonaws.com.cn:3306"
  birthday_change_limit: "1"
  channel_als: "ALS"
  channel_app: "APP"
  channel_frs: "FRS"
  channel_jd: "JD"
  channel_memberhub: "MemberHub"
  channel_pdd: "PDD"
  channel_social: "Social"
  channel_tiktok: "Tiktok"
  channel_tmall: "Tmall"
  channel_website: "WebSite"
  channel_wechat: "Wechat"
  cloud_front_domain: "https://consumer-hub.static.adidas.com.cn/"
  communication_addr: "http://crm-communication-service:80"
  consumer_addr: "http://crm-consumer-center-service:80"
  consumer_common_cache_consumer_area_info_expire_time: "86400"
  consumer_common_cache_consumer_aws_key_expire_time: "86400"
  consumer_common_cache_consumer_campaign_info_expire_time: "86400"
  consumer_common_cache_consumer_info_expire_time: "86400"
  consumer_common_cache_consumer_member_info_expire_time: "86400"
  consumer_common_cache_consumer_personal_info_expire_time: "86400"
  consumer_common_cache_consumer_store_info_expire_time: "600"
  consumer_common_cache_merkting_consumer_expire_time: "86400"
  consumer_common_consumer_area_info_cache_key: "consumer:area-info"
  consumer_common_consumer_aws_key_cache_key: "consumer:aws-key"
  consumer_common_consumer_campaign_info_cache_key: "consumer:campaign-info"
  consumer_common_consumer_expire_page_size: "1000"
  consumer_common_consumer_info_code_cache_key: "consumer:consumer-info:code"
  consumer_common_consumer_info_md5_cache_key: "consumer:consumer-info:md5"
  consumer_common_consumer_liveness_page_size: "3000"
  consumer_common_consumer_member_list_consumer_code_cache_key: "consumer:consumer-member-list:consumer-code"
  consumer_common_consumer_member_tier_page_size: "5000"
  consumer_common_consumer_name_lenth: "64"
  consumer_common_consumer_personal_consumer_code_cache_key: "consumer:consumer-personal:consumer-code"
  consumer_common_consumer_store_info_cache_key: "consumer:store-info"
  consumer_common_default_page_num: "1"
  consumer_common_final_member_level_end_time: "2099-12-30"
  consumer_common_fourteen: "14"
  consumer_common_generate_consumer_code_prefix: "002"
  consumer_common_generate_consumer_digit: '%09d'
  consumer_common_int_one: "1"
  consumer_common_int_zero: "0"
  consumer_common_level_demotion_remind_prefix: 'consumer-center:level:demotion:remind:'
  consumer_common_member_level_total_amount_defalut: "0"
  consumer_common_merkting_consumer_cache_key: "consumer:marketig-consumer-list:marketing-code"
  consumer_common_merkting_consumer_dto_cache_key: "consumer:marketig-consumer-list:marketing-code"
  consumer_common_next_export_time: "7"
  consumer_common_page_size: "500"
  consumer_common_points_account_code_count: "consumer:consumerCode:code:count"
  consumer_common_post_code_regex: ^[1-9]\d{5}$
  consumer_common_project_name: "consumer-center"
  consumer_common_region_cache_key: 'masterdata:region:'
  consumer_common_register_consumer_redis_lock: "consumer:register:lock"
  consumer_common_register_consumer_redis_lock_out_time: "10"
  consumer_common_sign_off_restriction_limit_type_cache_key: 'consumer:signOff-restriction:limitType:'
  consumer_common_sign_off_restriction_limit_type_expire_time: "86400"
  consumer_common_six: "6"
  consumer_common_twelve: "12"
  consumer_common_twenty_four: "24"
  coupon_addr: "http://crm-coupon-center-service:80"
  demotion_level_one_level: "1000"
  demotion_level_three_level: "8000"
  demotion_level_two_level: "4000"
  kafka_addr: "kafka1.pro.beijing.internal.kaas.adidas.com.cn:9094,kafka2.pro.beijing.internal.kaas.adidas.com.cn:9094,kafka3.pro.beijing.internal.kaas.adidas.com.cn:9094,kafka4.pro.beijing.internal.kaas.adidas.com.cn:9094,kafka5.pro.beijing.internal.kaas.adidas.com.cn:9094,kafka6.pro.beijing.internal.kaas.adidas.com.cn:9094"
  kafka_batch_consume_num: "2000"
  kafka_v1_addr: "kafka1.pro.pivotal.cn.kaas.adidas.com.cn:9093,kafka2.pro.pivotal.cn.kaas.adidas.com.cn:9093,kafka3.pro.pivotal.cn.kaas.adidas.com.cn:9093"
  key_store_location_v2: "file:/home/<USER>/consumerhub/consumer_hub.consumer_hub_CN.kaas.3stripes.net.jks"
  level_amount_bronze_amount: "4000"
  level_amount_ginzal_amount: "8000"
  level_amount_new_standard_amount: "1000"
  liveness_process_update_enliven: "-6,2147483647,-1,0"
  liveness_process_update_enliven_to_lost: "-**********,-24,0,3"
  liveness_process_update_enliven_to_silent: "-12,-6,0,2"
  liveness_process_update_enliven_to_sleep: "-24,-12,0,1"
  liveness_process_update_lost: "-**********,-24,-1,3"
  liveness_process_update_silent: "-12,-6,-1,2"
  liveness_process_update_silent_to_lost: "-**********,-24,2,3"
  liveness_process_update_silent_to_sleep: "-24,-12,2,1"
  liveness_process_update_sleep: "-24,-12,-1,1"
  liveness_process_update_sleep_to_lost: "-**********,-24,1,3"
  log_environment: "prd"
  log_print: "false"
  log_version: "1.0.0"
  masterdata_addr: "http://crm-master-data-service:80"
  member_addr: "http://crm-member-center-service:80"
  member_level_change_type_demotion: "demotion"
  member_level_change_type_upgrade: "upgrade"
  member_type_adi_club_member: "003,adiClub会员,"
  member_type_call_center: "008,来电客户Call Center,"
  member_type_kids_club: "006,kidsClub会员,"
  member_type_medical_personnel: "005,医护,MEDICAL"
  member_type_non_member: "001,游客(非会员),"
  member_type_staff: "004,员工,"
  member_type_visitor: "002,访客,"
  member_type_women_club_member: "007,womenClub会员,"
  multipart_max_size: "1MB"
  order_addr: "http://crm-order-center-service:80"
  points_addr: "http://crm-points-center-service:80"
  pre_upgrade_day: "3"
  redis_addr: "redis://prd-redis.vti70w.clustercfg.cnn1.cache.amazonaws.com.cn:6379"
  s3_bucket_name: "crm-consumer-prd-profile"
  sql_show: "false"
  ssl_key_store_location_v1: "file:/home/<USER>/consumerhub/gca_fd_crm.crm.kaas.3stripes.net.jks"
  time_query_range: "15"
  tmall_session_kids: "610112701d3ac8ff2dcc59f46df1e373cc019f54b90a0294085990900"
  tmall_session_neo: "6100f267854412d56f852d48daf7d12f7119d626e39b25b2211842372764"
  tmall_session_official: "6100f004137cbe946b55f3b6bb9358a6c8188a5442acdb0446338500"
  tmall_session_outlet: "61019231022ea7c1eaf1274c289effe1c2656f0489c63463900242952"
  tmall_session_terrex: "6102122bc092c4d97889c47243ea4f19cb9d8d2c371a6122212834572628"
  trust_store_location_v1: "file:/home/<USER>/consumerhub/kafka.truststore.jks"
  trust_store_location_v2: "file:/home/<USER>/consumerhub/client.truststore.jks"
  upgrade_level_money_bronze_to_ginzal_level: "4000"
  upgrade_level_money_bronze_to_gold_level: "8000"
  upgrade_level_money_ginzal_to_gold_level: "8000"
  upgrade_level_money_new_standard_to_bronze_level: "1000"
  upgrade_level_money_new_standard_to_ginza_level: "4000"
  upgrade_level_money_new_standard_to_gold_level: "8000"
  java_opts: "-Xms3072m -Xmx7168m -XX:MaxGCPauseMillis=200"
  AUDIT_HUB_APP_KEY: "CRM-CMS-APIKEY-PRD"
  AUDIT_HUB_APP_ID: "crm-cms-prd"
  AUDIT_HUB_CALL_BACK_URL: "https://consumer-hub-gca-prd.api.adidas.com.cn/consumercenter/v1/consumer/valid/callback"
  AUDIT_HUB_URL: "https://audithub.adidas.com.cn/api/cmscenter/contentauditing"
  PICTURE_URL: "https://crm-consumer-prd-profile.s3.cn-north-1.amazonaws.com.cn/member-avatar/default-avatar.png"
  spring_documentation_enabled: "false"
  guide_consumer_code: "5"
