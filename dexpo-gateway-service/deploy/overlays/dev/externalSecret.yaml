apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  annotations:
    #强制同步secretManager的时间戳，可配置
    force-sync: "1661306061"
  name: dexpo-gateway-service-externalsecret
  #k8s的namespace，需要检查一下
#  namespace: jlr-dtt-system-dev
#  namespace: external-secrets
spec:
#  data:
#    - secretKey: db_user
#      remoteRef:
#        key: test/jlr_dtt
#        property: db_user
  dataFrom:
  - extract:
      conversionStrategy: Default
      #对应aws上创建的secret manager的name
      key: dev/jlr_dtt
  refreshInterval: 1h
  #关联secretStore配置
  secretStoreRef:
    kind: SecretStrore
    name: secret-store
  target:
    creationPolicy: Orphan
    deletionPolicy: Retain
    name: dexpo-gateway-service-externalsecret