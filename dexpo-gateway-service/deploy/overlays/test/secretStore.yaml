apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: secret-store
##  namespace: external-secrets
spec:
  provider:
    aws:
      service: SecretsManager
      region: cn-northwest-1
      role: arn:aws-cn:iam::304256661659:role/R-DEV-ECP-DEVELOPER
      auth:
        secretRef:
          #需要在aws的控制台创建AKSK信息后并在k8s里手动创建secret实例并配置到data数据里，这样secretStore就能引用secret里面的数据access-key和secret-access-key
          accessKeyIDSecretRef:
            name: dtt-gateway-service-secret
            key: access-key
          secretAccessKeySecretRef:
            name: dtt-gateway-service-secret
            key: secret-access-key