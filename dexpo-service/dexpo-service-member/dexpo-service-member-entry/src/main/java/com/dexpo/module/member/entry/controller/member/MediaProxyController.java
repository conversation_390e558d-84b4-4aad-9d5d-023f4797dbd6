package com.dexpo.module.member.entry.controller.member;

import com.dexpo.framework.common.exception.enums.MemberServiceErrorCodeEnum;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.member.api.MediaProxyApi;
import com.dexpo.module.member.api.dto.media.MediaProxyActionDTO;
import com.dexpo.module.member.app.service.MediaProxyRegistrationAppService;
import com.dexpo.module.member.domain.repository.MemberMediaProxyRecordRepository;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * 媒体批量操作
 */
@RestController
@AllArgsConstructor
@Slf4j
public class MediaProxyController implements MediaProxyApi {

    @Resource
    private MediaProxyRegistrationAppService mediaProxyRegistrationAppService;

    @Resource
    private MemberMediaProxyRecordRepository memberMediaProxyRecordRepository;

    @Override
    public CommonResult<Boolean> registrationByFile(Long memberMediaProxyRecordId, Long exhibitionId,
                                                    String fileName, byte[] content) {
        MediaProxyActionDTO dto = new MediaProxyActionDTO();
        try {
            dto = mediaProxyRegistrationAppService.importMediaList(memberMediaProxyRecordId,
                    exhibitionId, content, fileName);
            if (dto.isHasError()) {
                throw MemberServiceErrorCodeEnum.MEMBER_EXCEL_HAS_ERROR.getServiceException();
            }
            // 更新上传记录
            memberMediaProxyRecordRepository.saveSuccess(memberMediaProxyRecordId, dto.getFileUrl());
        } catch (Exception e) {
            log.info("mediaProxyRegistrationByFile:--{}", e.getMessage());
            memberMediaProxyRecordRepository.saveCheckErrorInfo(memberMediaProxyRecordId,
                    dto.getErrorInfo().toString(), dto.getFileUrl());
        }
        return CommonResult.success(true);
    }
}
