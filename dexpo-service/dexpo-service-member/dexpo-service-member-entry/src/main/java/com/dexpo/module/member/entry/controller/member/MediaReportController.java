package com.dexpo.module.member.entry.controller.member;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.member.api.MediaReportApi;
import com.dexpo.module.member.api.dto.media.MediaReportQueryDTO;
import com.dexpo.module.member.api.vo.media.MediaReportVO;
import com.dexpo.module.member.app.service.MediaReportAppService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 媒体报表接口
 */
@RestController
@RequiredArgsConstructor
public class MediaReportController implements MediaReportApi {

    private final MediaReportAppService mediaReportAppService;


    @Override
    public CommonResult<MediaReportVO> getMediaReport(@Valid @RequestBody MediaReportQueryDTO queryDTO) {
        MediaReportVO mediaReport = mediaReportAppService.getMediaReport(queryDTO);
        return CommonResult.success(mediaReport);
    }

}
