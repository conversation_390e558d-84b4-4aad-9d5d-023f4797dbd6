package com.dexpo.module.member.entry.controller.member;

import cn.hutool.json.JSONUtil;
import com.dexpo.module.member.api.dto.media.MediaProxyRecordDTO;
import com.dexpo.module.member.app.service.MediaProxyAppService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.doNothing;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest
@ContextConfiguration(classes = MediaProxyController.class)
public class MediaProxyControllerTest {
    @Autowired
    private MockMvc mockMvc;
    @MockitoBean
    private MediaProxyAppService mediaProxyAppService;

    private static final String PREFIX = "/mediaProxy";

//    @Test
//    void testQueryMemberMediaProxyRecordPage() throws Exception {
//        MediaProxyRecordDTO dto = new MediaProxyRecordDTO();
//        PageResult<MediaProxyRecordVO> pageResult = new PageResult<>();
//        when(mediaProxyRegistrationAppService.queryMemberMediaProxyRecordPage(dto)).thenReturn(pageResult);
//        mockMvc.perform(post(PREFIX + "/queryMemberMediaProxyRecordPage"))
//                .andExpect(status().isOk());
//    }

    @Test
    void testRegistrationByFile() throws Exception {
        MediaProxyRecordDTO dto = new MediaProxyRecordDTO();
//        when(mediaProxyRegistrationAppService.importMediaList(any(), any())).thenReturn(dto);
        doNothing().when(mediaProxyAppService).registrationByFile(dto);
        mockMvc.perform(post(PREFIX + "/registrationByFile")
                        .content(JSONUtil.toJsonStr(dto))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(true));
    }
} 