package com.dexpo.module.member.entry.controller.sponsor;

import cn.hutool.json.JSONUtil;
import com.dexpo.module.integration.api.message.dto.ValidCodeDTO;
import com.dexpo.module.member.api.dto.SponsorLoginDTO;
import com.dexpo.module.member.api.dto.SponsorLoginExhibitionTagDTO;
import com.dexpo.module.member.api.vo.SponsorInfoVO;
import com.dexpo.module.member.api.vo.SponsorLoginVO;
import com.dexpo.module.member.app.service.SponsorInfoAppService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest
@ContextConfiguration(classes = SponsorController.class)
public class SponsorControllerTest {
    @Autowired
    private MockMvc mockMvc;
    @MockitoBean
    private SponsorInfoAppService sponsorInfoAppService;

    private static final String PREFIX = "/sponsor";

    @Test
    void testLoginValidCode() throws Exception {
        ValidCodeDTO dto = new ValidCodeDTO();
        dto.setText("test-text");
        when(sponsorInfoAppService.loginValidCode(any())).thenReturn(true);
        mockMvc.perform(post(PREFIX + "/loginValidCode")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSONUtil.toJsonStr(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    void testLoginValidCodeFailure() throws Exception {
        ValidCodeDTO dto = new ValidCodeDTO();
        dto.setText("invalid-text");
        when(sponsorInfoAppService.loginValidCode(any())).thenReturn(false);
        mockMvc.perform(post(PREFIX + "/loginValidCode")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSONUtil.toJsonStr(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(false));
    }

    @Test
    void testSponsorLogin() throws Exception {
        SponsorLoginDTO dto = new SponsorLoginDTO();
        dto.setLoginTool("test-user");
        dto.setValidCode("123456");
        
        SponsorLoginVO vo = new SponsorLoginVO();
        vo.setToken("test-token-123");
        vo.setExhibitionTagCode("CIIF");
        
        SponsorInfoVO sponsorInfoVO = new SponsorInfoVO();
        sponsorInfoVO.setId(1L);
        sponsorInfoVO.setSponsorName("测试用户");
        sponsorInfoVO.setSponsorCode("SP001");
        sponsorInfoVO.setSponsorMobile("13800138000");
        sponsorInfoVO.setOrganizationCode("ORG001");
        vo.setSponsorInfoVO(sponsorInfoVO);
        
        when(sponsorInfoAppService.login(any())).thenReturn(vo);
        mockMvc.perform(post(PREFIX + "/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSONUtil.toJsonStr(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.token").value("test-token-123"))
                .andExpect(jsonPath("$.data.exhibitionTagCode").value("CIIF"))
                .andExpect(jsonPath("$.data.sponsorInfoVO.id").value(1))
                .andExpect(jsonPath("$.data.sponsorInfoVO.sponsorName").value("测试用户"))
                .andExpect(jsonPath("$.data.sponsorInfoVO.sponsorCode").value("SP001"))
                .andExpect(jsonPath("$.data.sponsorInfoVO.sponsorMobile").value("13800138000"))
                .andExpect(jsonPath("$.data.sponsorInfoVO.organizationCode").value("ORG001"));
    }

    @Test
    void testSponsorLoginWithInvalidCredentials() throws Exception {
        SponsorLoginDTO dto = new SponsorLoginDTO();
        dto.setLoginTool("wrong-user");
        dto.setValidCode("654321");

        when(sponsorInfoAppService.login(any())).thenReturn(null); // 模拟登录失败
        mockMvc.perform(post(PREFIX + "/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSONUtil.toJsonStr(dto)))
                .andExpect(status().isOk()); // 假设认证失败返回401
    }

    @Test
    void testSponsorLogout() throws Exception {
        mockMvc.perform(post(PREFIX + "/logout"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    void testChooseTag() throws Exception {
        SponsorLoginExhibitionTagDTO dto = new SponsorLoginExhibitionTagDTO();
        dto.setExhibitionTagCode("tag-001");
        mockMvc.perform(post(PREFIX + "/chooseTag")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSONUtil.toJsonStr(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    void testChooseTagWithInvalidTag() throws Exception {
        SponsorLoginExhibitionTagDTO dto = new SponsorLoginExhibitionTagDTO();
        dto.setExhibitionTagCode(""); // 空标签
        mockMvc.perform(post(PREFIX + "/chooseTag")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSONUtil.toJsonStr(dto)))
                .andExpect(status().isOk()); // 假设空标签返回400
    }
} 