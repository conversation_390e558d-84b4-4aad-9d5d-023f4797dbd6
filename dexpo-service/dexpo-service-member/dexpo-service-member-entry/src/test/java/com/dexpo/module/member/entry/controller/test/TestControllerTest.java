package com.dexpo.module.member.entry.controller.test;

import com.dexpo.framework.cache.redis.service.RedisService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest
@ContextConfiguration(classes = TestController.class)
public class TestControllerTest {
    @Autowired
    private MockMvc mockMvc;
    @MockitoBean
    private RedisService redisService;

    @Test
    void testHeather() throws Exception {
        doNothing().when(redisService).setCacheObject("test", "test01");
        when(redisService.getCacheObject("test")).thenReturn("test01");
        mockMvc.perform(get("/heather"))
                .andExpect(status().isOk())
                .andExpect(content().string("success"));
    }
} 