package com.dexpo.module.member.entry.controller;

import cn.hutool.json.JSONUtil;
import com.dexpo.module.member.api.dto.EnterpriseInfoQueryDTO;
import com.dexpo.module.member.api.vo.EnterpriseInfoVO;
import com.dexpo.module.member.app.service.EnterpriseInfoAppService;
import com.dexpo.module.member.constant.TestCommonConstant;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest
@ContextConfiguration(classes = EnterpriseController.class)
public class EnterpriseControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockitoBean
    private EnterpriseInfoAppService enterpriseInfoAppService;

    @Test
    void testGetEnterpriseInfoList() throws Exception {
        EnterpriseInfoQueryDTO param = new EnterpriseInfoQueryDTO();

        EnterpriseInfoVO data = new EnterpriseInfoVO();
        data.setId(1234L);
        when(enterpriseInfoAppService.getEnterpriseInfoList(any())).thenReturn(Collections.singletonList(data));

        mockMvc.perform(post("/enterprise/getEnterpriseInfoList").content(JSONUtil.toJsonStr(param))
                        .contentType(TestCommonConstant.CONTENT_TYPE_APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data[0].id").value("1234"));

    }
}
