package com.dexpo.module.member.domain.service.impl;

import com.dexpo.module.member.domain.repository.MemberParticipateRecordRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;

/**
 * 观众会员领域服务测试类
 */
@ExtendWith(MockitoExtension.class)
public class AudienceMemberDomainServiceImplTest {

    @Mock
    private MemberParticipateRecordRepository memberParticipateRecordRepository;

    @InjectMocks
    private AudienceMemberDomainServiceImpl audienceMemberDomainService;

    @Test
    void testActive_Success() {
        // Given
        List<Long> ids = Arrays.asList(1L, 2L, 3L);

        // When
        Boolean result = audienceMemberDomainService.active(ids);

        // Then
        assertTrue(result);
        verify(memberParticipateRecordRepository).active(ids);
    }

    @Test
    void testActive_SingleId() {
        // Given
        List<Long> ids = Collections.singletonList(1L);

        // When
        Boolean result = audienceMemberDomainService.active(ids);

        // Then
        assertTrue(result);
        verify(memberParticipateRecordRepository).active(ids);
    }

    @Test
    void testActive_MultipleIds() {
        // Given
        List<Long> ids = Arrays.asList(1L, 2L, 3L, 4L, 5L);

        // When
        Boolean result = audienceMemberDomainService.active(ids);

        // Then
        assertTrue(result);
        verify(memberParticipateRecordRepository).active(ids);
    }

    @Test
    void testActive_EmptyList() {
        // Given
        List<Long> ids = Collections.emptyList();

        // When
        Boolean result = audienceMemberDomainService.active(ids);

        // Then
        assertTrue(result);
        verify(memberParticipateRecordRepository).active(ids);
    }

    @Test
    void testActive_NullList() {
        // Given
        List<Long> ids = null;

        // When
        Boolean result = audienceMemberDomainService.active(ids);

        // Then
        assertTrue(result);
        verify(memberParticipateRecordRepository).active(ids);
    }

    @Test
    void testActive_LargeIds() {
        // Given
        List<Long> ids = Arrays.asList(1000L, 2000L, 3000L, 4000L, 5000L);

        // When
        Boolean result = audienceMemberDomainService.active(ids);

        // Then
        assertTrue(result);
        verify(memberParticipateRecordRepository).active(ids);
    }

    @Test
    void testActive_ZeroIds() {
        // Given
        List<Long> ids = Arrays.asList(0L, 1L, 2L);

        // When
        Boolean result = audienceMemberDomainService.active(ids);

        // Then
        assertTrue(result);
        verify(memberParticipateRecordRepository).active(ids);
    }

    @Test
    void testActive_NegativeIds() {
        // Given
        List<Long> ids = Arrays.asList(-1L, -2L, -3L);

        // When
        Boolean result = audienceMemberDomainService.active(ids);

        // Then
        assertTrue(result);
        verify(memberParticipateRecordRepository).active(ids);
    }

    @Test
    void testActive_MixedIds() {
        // Given
        List<Long> ids = Arrays.asList(1L, -2L, 0L, 100L, -50L);

        // When
        Boolean result = audienceMemberDomainService.active(ids);

        // Then
        assertTrue(result);
        verify(memberParticipateRecordRepository).active(ids);
    }

    @Test
    void testActive_OneId() {
        // Given
        List<Long> ids = Arrays.asList(1L);

        // When
        Boolean result = audienceMemberDomainService.active(ids);

        // Then
        assertTrue(result);
        verify(memberParticipateRecordRepository).active(ids);
    }

    @Test
    void testActive_TwoIds() {
        // Given
        List<Long> ids = Arrays.asList(1L, 2L);

        // When
        Boolean result = audienceMemberDomainService.active(ids);

        // Then
        assertTrue(result);
        verify(memberParticipateRecordRepository).active(ids);
    }

    @Test
    void testActive_MaxValueIds() {
        // Given
        List<Long> ids = Arrays.asList(Long.MAX_VALUE, Long.MIN_VALUE);

        // When
        Boolean result = audienceMemberDomainService.active(ids);

        // Then
        assertTrue(result);
        verify(memberParticipateRecordRepository).active(ids);
    }

    @Test
    void testActive_SequentialIds() {
        // Given
        List<Long> ids = Arrays.asList(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L, 9L, 10L);

        // When
        Boolean result = audienceMemberDomainService.active(ids);

        // Then
        assertTrue(result);
        verify(memberParticipateRecordRepository).active(ids);
    }

    @Test
    void testActive_DuplicateIds() {
        // Given
        List<Long> ids = Arrays.asList(1L, 1L, 2L, 2L, 3L);

        // When
        Boolean result = audienceMemberDomainService.active(ids);

        // Then
        assertTrue(result);
        verify(memberParticipateRecordRepository).active(ids);
    }
} 