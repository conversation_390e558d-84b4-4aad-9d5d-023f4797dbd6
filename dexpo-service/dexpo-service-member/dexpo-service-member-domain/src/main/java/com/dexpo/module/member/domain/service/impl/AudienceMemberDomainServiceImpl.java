package com.dexpo.module.member.domain.service.impl;

import com.dexpo.module.member.domain.repository.MemberParticipateRecordRepository;
import com.dexpo.module.member.domain.service.AudienceMemberDomainService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class AudienceMemberDomainServiceImpl implements AudienceMemberDomainService {

    private final MemberParticipateRecordRepository memberParticipateRecordRepository;


    @Override
    public Boolean active(List<Long> ids) {
        // 需要支付逻辑校验 数据权限控制
        memberParticipateRecordRepository.active(ids);
        return true;
    }

}
