package com.dexpo.module.member.domain.model.member;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户信息审核记录数据对象
 */
@Data
public class MemberApproveRecord {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 媒体用户ID
     */
    private Long memberId;
    
    /**
     * 展会ID
     */
    private Long exhibitionId;
    
    /**
     * 审批人ID
     */
    private Long approverId;
    
    /**
     * 审批人姓名
     */
    private String approverName;
    
    /**
     * 审批时间
     */
    private LocalDateTime approveTime;
    
    /**
     * 审批结果：值集VS_APPROVE_RESULT
     */
    private String approveResult;
    
    /**
     * 审批拒绝原因：值集VS_APPROVE_REJECTD_REASON
     */
    private String approveRejectReason;
    
    /**
     * 审批拒绝备注
     */
    private String approveRejectRemark;
} 