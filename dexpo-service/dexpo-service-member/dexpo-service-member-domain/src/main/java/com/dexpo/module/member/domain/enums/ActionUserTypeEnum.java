package com.dexpo.module.member.domain.enums;

import lombok.Getter;

@Getter
public enum ActionUserTypeEnum {


    SPONSOR_USER(
            EnumConstants.VS_ACTION_USER_TYPE,
            "VO_ACTION_USER_TYPE_1",
            "主办方用户",
            "Sponsor User"
    ),
    MEDIA_USER(
            EnumConstants.VS_ACTION_USER_TYPE,
            "VO_ACTION_USER_TYPE_2",
            "媒体用户",
            "Media User"
    ),
    AUDIENCE_USER(
            EnumConstants.VS_ACTION_USER_TYPE,
            "VO_ACTION_USER_TYPE_3",
            "观众用户",
            "Audience User"
    );

    /**
     * 值集分类编码
     */
    private final String valuesetCode;

    /**
     * 枚举项编码
     */
    private final String code;

    /**
     * 中文描述
     */
    private final String descriptionCN;

    /**
     * 英文描述
     */
    private final String descriptionEN;

    ActionUserTypeEnum(String valuesetCode, String code, String descriptionCN, String descriptionEN) {
        this.valuesetCode = valuesetCode;
        this.code = code;
        this.descriptionCN = descriptionCN;
        this.descriptionEN = descriptionEN;
    }

    /**
     * 根据枚举编码获取枚举实例
     * @param code VO_ACTION_USER_TYPE_1 格式的编码
     * @return 匹配的枚举实例，未找到返回null
     */
    public static ActionUserTypeEnum getByCode(String code) {
        for (ActionUserTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }
} 