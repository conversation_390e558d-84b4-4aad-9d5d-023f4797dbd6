package com.dexpo.module.member.domain.model.member.message;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "数据仓库同步对象")
public class MemberDataWarehouseSyncMessage {
    
    @Schema(description = "基础信息-会员编码")
    private String memberCode;
    
    @Schema(description = "基础信息-姓名")
    private String memberName;
    
    @Schema(description = "基础信息-名")
    private String memberFirstName;
    
    @Schema(description = "基础信息-姓")
    private String memberLastName;
    
    @Schema(description = "基础信息-性别")
    private String memberGender;
    
    @Schema(description = "基础信息-生日")
    private String memberBirthDay;
    
    @Schema(description = "基础信息-手机号码")
    private String memberMobile;
    
    @Schema(description = "基础信息-邮箱")
    private String memberEmail;
    
    @Schema(description = "基础信息-证件类型")
    private String idCategory;
    
    @Schema(description = "基础信息-证件号码")
    private String idNumber;
    
    @Schema(description = "基础信息-国籍编码")
    private String countryCode;
    
    @Schema(description = "基础信息-国籍名称")
    private String countryName;
    
    @Schema(description = "领证信息-领证方式")
    private String certificateCollectionMethod;
    
    @Schema(description = "领证信息-收件人姓名")
    private String recipientName;
    
    @Schema(description = "领证信息-收件人名")
    private String recipientFirstName;
    
    @Schema(description = "领证信息-收件人姓")
    private String recipientLastName;
    
    @Schema(description = "领证信息-收件人手机号")
    private String recipientMobile;
    
    @Schema(description = "领证信息-收件人所在省编码")
    private String recipientProvinceCode;
    
    @Schema(description = "领证信息-收件人所在省名称")
    private String recipientProvinceName;
    
    @Schema(description = "领证信息-收件人所在市编码")
    private String recipientCityCode;
    
    @Schema(description = "领证信息-收件人所在市名称")
    private String recipientCityName;
    
    @Schema(description = "领证信息-收件人所在区编码")
    private String recipientDistrictCode;
    
    @Schema(description = "领证信息-收件人所在区名称")
    private String recipientDistrictName;
    
    @Schema(description = "领证信息-收件人详细地址")
    private String recipientAddress;
    
    @Schema(description = "记者信息-媒体企业名称")
    private String enterpriseName;
    
    @Schema(description = "记者信息-记者证号")
    private String mediaNewsmanNo;
    
    @Schema(description = "记者信息-媒体类型编码")
    private String mediaTypeCode;
    
    @Schema(description = "记者信息-媒体职位分类编码")
    private String mediaPositionCategoryCode;
    
    @Schema(description = "记者信息-媒体职位编码")
    private String mediaPositionCode;
    
    @Schema(description = "记者信息-是否申请直播")
    private String isApplyLiveStream;
    
    @Schema(description = "记者信息-证件照地址")
    private String attachmentHeadPhoto;
    
    @Schema(description = "参展信息-用户类型")
    private String memberType;
    
    @Schema(description = "参展信息-展会编码")
    private String exhibitionCode;
    
    @Schema(description = "参展信息-展会中文名称")
    private String exhibitionNameCn;
    
    @Schema(description = "参展信息-展会英文名称")
    private String exhibitionNameEn;
    
    @Schema(description = "参展信息-展会届数")
    private String exhibitionSessionKey;
    
    @Schema(description = "参展信息-展会标签")
    private String exhibitionTagCode;
    
    @Schema(description = "参展信息-注册时间")
    private String registerTime;
    
    @Schema(description = "参展信息-注册状态")
    private String registerStatus;
    
    @Schema(description = "参展信息-注册系统")
    private String registerSystem;
    
    @Schema(description = "参展信息-注册方式")
    private String registerMethod;
    
    @Schema(description = "参展信息-注册来源")
    private String registerSource;
}
