package com.dexpo.module.member.domain.model.member;

import cn.hutool.core.lang.UUID;
import lombok.Data;

import java.time.LocalDate;

/**
 * 会员基本信息数据对象
 */
@Data
public class MemberBaseInfo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会员编码（系统自动生成）
     */
    private String memberCode;

    /**
     * 姓名
     */
    private String memberName;

    /**
     * 英文名
     */
    private String memberFirstName;

    /**
     * 英文姓
     */
    private String memberLastName;

    /**
     * 性别：值集VS_GENDER
     */
    private String memberGender;

    /**
     * 生日
     */
    private LocalDate memberBirthDay;

    /**
     * 手机号
     */
    private String memberMobile;

    /**
     * 邮箱
     */
    private String memberEmail;

    /**
     * 证件类型：值集VS_ID_CATEGORY
     */
    private String idCategory;

    /**
     * 证件号码
     */
    private String idNumber;

    /**
     * 国家CODE
     */
    private String countryCode;

    /**
     * 国家名称中文
     */
    private String countryNameCn;

    /**
     * 国家名称英文
     */
    private String countryNameEn;

    /**
     * 居住地所在省CODE
     */
    private String currentHomeProvinceCode;

    /**
     * 居住地所在省名称
     */
    private String currentHomeProvinceName;

    /**
     * 居住地所在市CODE
     */
    private String currentHomeCityCode;

    /**
     * 居住地所在市名称
     */
    private String currentHomeCityName;

    /**
     * 居住地所在区CODE
     */
    private String currentHomeDistrictCode;

    /**
     * 居住地所在区名称
     */
    private String currentHomeDistrictName;

    /**
     * 居住地详细地址
     */
    private String currentHomeDetailAddress;

    public static MemberBaseInfo initMemberBaseInfo() {
        MemberBaseInfo baseInfo = new MemberBaseInfo();
        baseInfo.setMemberCode(UUID.fastUUID().toString());
        return baseInfo;
    }
} 