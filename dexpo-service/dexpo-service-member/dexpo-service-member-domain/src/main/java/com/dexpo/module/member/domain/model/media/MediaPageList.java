package com.dexpo.module.member.domain.model.media;

import java.time.LocalDateTime;

import lombok.Data;

@Data
public class MediaPageList {

    /**
     * 参与记录ID
     */
    private Long memberParticipateRecordId;

    /**
     * 展会ID
     */
    private Long exhibitionId;

    /**
     * 用户id
     */
    private Long memberId;

    /**
     * 姓名
     */
    private String memberName;

    /**
     * 手机号
     */
    private String memberMobile;

    /**
     * 邮箱
     */
    private String memberEmail;

    /**
     * 所属地域CODE
     */
    private String enterpriseLocationCode;

    /**
     * 所属地域名称
     */
    private String enterpriseLocationNameCn;

    /**
     * 所属地域名称(英文)
     */
    private String enterpriseLocationNameEn;

    /**
     * 媒体类型 media_type_code
     */
    private String mediaTypeCode;

    /**
     * 注册状态
     */
    private String registerStatus;

    /**
     * 注册来源 值集VS_REGISTER_SOURCE
     */
    private String registerSource;



    /**
     * 媒体权限类型
     * 
     */
    private String mediaPermissionType;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;
}
