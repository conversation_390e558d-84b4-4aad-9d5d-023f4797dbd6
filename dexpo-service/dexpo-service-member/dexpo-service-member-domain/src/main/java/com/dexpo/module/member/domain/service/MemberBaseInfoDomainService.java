package com.dexpo.module.member.domain.service;

import com.dexpo.module.member.domain.model.member.MemberBaseInfo;

/**
 * 会员基本信息 Service 接口
 */
public interface MemberBaseInfoDomainService {


    /**
     * 更新会员基本信息
     *
     * @param memberBaseInfo 会员基本信息
     */
    void updateMemberBaseInfo(MemberBaseInfo memberBaseInfo);

    /**
     * 删除会员基本信息
     *
     * @param id 编号
     */
    void deleteMemberBaseInfo(Long id);

    /**
     * 获得会员基本信息
     *
     * @param id 编号
     * @return 会员基本信息
     */
    MemberBaseInfo getMemberBaseInfo(Long id);

    /**
     * 获得会员基本信息
     * @param mobileOrEmail
     * @return
     */
    MemberBaseInfo getMemberBaseInfoByMobileOrEmail(String mobileOrEmail);

} 