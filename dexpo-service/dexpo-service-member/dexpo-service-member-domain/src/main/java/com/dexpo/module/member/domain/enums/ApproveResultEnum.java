package com.dexpo.module.member.domain.enums;

import lombok.Getter;

@Getter
public enum ApproveResultEnum {

    APPROVED(
            "VS_APPROVE_RESULT",
            "VO_APPROVE_RESULT_1",
            "审核通过",
            "Approved"
    ),
    REJECTED(
            "VS_APPROVE_RESULT",
            "VO_APPROVE_RESULT_2",
            "审核驳回",
            "Rejected"
    );

    /**
     * 值集分类编码
     */
    private final String valuesetCode;

    /**
     * 枚举项编码
     */
    private final String code;

    /**
     * 中文描述
     */
    private final String descriptionCN;

    /**
     * 英文描述
     */
    private final String descriptionEN;

    ApproveResultEnum(String valuesetCode, String code, String descriptionCN, String descriptionEN) {
        this.valuesetCode = valuesetCode;
        this.code = code;
        this.descriptionCN = descriptionCN;
        this.descriptionEN = descriptionEN;
    }

    /**
     * 根据枚举编码获取枚举实例
     * @param code VO_APPROVE_RESULT_1 格式的编码
     * @return 匹配的枚举实例，未找到返回null
     */
    public static ApproveResultEnum getByCode(String code) {
        for (ApproveResultEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }
} 