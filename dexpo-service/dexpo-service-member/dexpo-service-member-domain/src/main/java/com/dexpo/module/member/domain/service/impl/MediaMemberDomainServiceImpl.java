package com.dexpo.module.member.domain.service.impl;

import com.dexpo.module.member.domain.model.enterprise.EnterpriseInfo;
import com.dexpo.module.member.domain.model.member.*;
import com.dexpo.module.member.domain.repository.*;
import com.dexpo.module.member.domain.service.MediaMemberDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class MediaMemberDomainServiceImpl implements MediaMemberDomainService {

    private final MemberSignRecordRepository memberSignRecordRepository;
    private final EnterpriseInfoRepository enterpriseInfoRepository;
    private final MemberBaseInfoRepository memberBaseInfoRepository;
    private final MemberRecipientInfoRepository memberRecipientInfoRepository;
    private final MemberReporterInfoRepository memberReporterInfoRepository;
    private final MemberParticipateRecordRepository memberParticipateRecordRepository;


    @Override
    public MemberSignRecord queryMemberSignRecordByMemberIdAndAgreementId(Long memberId, Long agreementId) {
                    return memberSignRecordRepository.queryByMemberIdAndAgreementId(memberId,
                    agreementId);
    }

    @Override
    public void addMemberSignRecord(MemberSignRecord recordDO) {
        memberSignRecordRepository.add(recordDO);
    }

    @Override
    public MemberBaseInfo queryMemberBaseInfoByMemberId(Long memberId) {
        return memberBaseInfoRepository.getById(memberId);
    }

    @Override
    public EnterpriseInfo getEnterpriseInfoById(Long enterpriseId) {
        return enterpriseInfoRepository.getById(enterpriseId);
    }

    @Override
    public MemberRecipientInfo queryMemberRecipientInfoByMemberId(Long memberId) {
        return memberRecipientInfoRepository.queryByMemberId(memberId);
    }

    @Override
    public MemberReporterInfo queryMemberReporterInfoByMemberId(Long memberId,Long exhibitionId) {
        MemberReporterInfo memberReporterInfo = memberReporterInfoRepository.queryByMemberId(memberId);
            if(memberReporterInfo !=null && !exhibitionId.equals(memberReporterInfo.getExhibitionId())){//id不同说明是以往的数据 需要清空主键使其新增
                memberReporterInfo.setId(null);

        }
        return memberReporterInfo;
    }

    @Override
    public MemberReporterInfo queryMemberReporterInfoByMemberIdAndExhibitionId(Long memberId, Long exhibitionId) {
        return memberReporterInfoRepository.queryByMemberIdAndExhibitionId(memberId, exhibitionId);
    }

    @Override
    public void addOrModifyMemberBaseInfo(MemberBaseInfo baseInfoDO) {
        memberBaseInfoRepository.addOrModify(baseInfoDO);
    }

    @Override
    public MemberParticipateRecord queryMemberParticipateRecordByUserIdAndExhibitionId(Long memberId, Long exhibitionId) {
        return memberParticipateRecordRepository
                .queryByUserIdAndExhibitionId(memberId, exhibitionId);
    }

    @Override
    public void modifyMemberParticipateRecordById(MemberParticipateRecord recordDO) {
        memberParticipateRecordRepository.modifyById(recordDO);
    }

    @Override
    public void addOrModifyMemberRecipientInfo(MemberRecipientInfo recipientInfoDO) {
        memberRecipientInfoRepository.addOrModify(recipientInfoDO);
    }

    @Override
    public void addOrModifyMemberReporterInfo(MemberReporterInfo reporterInfoDO) {
        memberReporterInfoRepository.addOrModify(reporterInfoDO);
    }

    @Override
    public EnterpriseInfo queryMediaEnterpriseByNameAndLocationCode(String enterpriseName, String enterpriseLocationCode) {
        return enterpriseInfoRepository.queryMediaEnterpriseByNameAndLocationCode(
                enterpriseName, enterpriseLocationCode);
    }

    @Override
    public void addOrModifyEnterpriseInfo(EnterpriseInfo enterpriseInfo) {
        enterpriseInfoRepository.addOrModify(enterpriseInfo);
    }

}
