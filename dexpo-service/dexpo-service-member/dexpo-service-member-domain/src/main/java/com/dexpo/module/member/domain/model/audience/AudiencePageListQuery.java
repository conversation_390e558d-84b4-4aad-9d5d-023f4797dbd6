package com.dexpo.module.member.domain.model.audience;

import com.dexpo.framework.common.pojo.PageParam;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class AudiencePageListQuery extends PageParam {


    /**
     * 姓名
     */
    private String memberName;

    /**
     * 手机号
     */
    private String memberMobile;

    /**
     * 是否国内，true-国内，false-境外
     */
    private Boolean isDomestic;

    /**
     * 激活 未激活
     */
    private Boolean isActive;

    /**
     * 会展ID
     */
    private List<Long> exhibitionIds;


    /**
     * 注册通道
     */
    private String registerSystem;

    /**
     * 观众类型
     */
    private String audienceType;


    /**
     * 注册时间开始
     */
    private LocalDateTime registerTimeStart;

    /**
     * 注册时间结束
     */
    private LocalDateTime registerTimeEnd;
}
