package com.dexpo.module.member.domain.repository;


import com.dexpo.module.member.domain.model.SponsorInfo;
import com.dexpo.module.member.domain.model.member.SponsorAllInfo;

import java.util.List;

public interface SponsorInfoRepository {

    SponsorInfo getSponsorUser(String loginTool);

    List<SponsorAllInfo> sponsorPageInfo(SponsorAllInfo req);

    Boolean updateStatus(SponsorAllInfo req);

    Boolean updateSponsor(SponsorAllInfo convertReq);
}
