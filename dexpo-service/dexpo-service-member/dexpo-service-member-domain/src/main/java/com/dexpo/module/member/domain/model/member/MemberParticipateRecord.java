package com.dexpo.module.member.domain.model.member;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户参展记录数据对象
 */
@Data
public class MemberParticipateRecord {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long memberId;

    /**
     * 记录编码
     */
    private String participateCode;

    /**
     * 展会ID
     */
    private Long exhibitionId;

    /**
     * 用户类型：值集VS_ACTION_USER_TYPE
     */
    private String memberType;

    /**
     * 注册时语言环境：值集VS_LANGUAGE
     */
    private String registerLanguage;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;
    
    /**
     * 注册状态：值集VS_REGISTER_STATUS
     */
    private String registerStatus;
    
    /**
     * 注册系统：值集VS_REGISTER_SYSTEM
     */
    private String registerSystem;
    
    /**
     * 注册方式：值集VS_REGISTER_METHOD
     */
    private String registerMethod;
    
    /**
     * 注册来源：值集VS_REGISTER_SOURCE
     */
    private String registerSource;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建者，目前使用 SysUser 的 id 编号
     *
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String createUserName;

    private Long createUser;
    /**
     * 更新者，目前使用 SysUser 的 id 编号
     *
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String updateUserName;

    private Long updateUser;
    /**
     * 是否删除
     */
    private Boolean delFlg;
} 