package com.dexpo.module.member.domain.enums;

import lombok.Getter;

@Getter
public enum MediaPositionTypeEnum {

    NEWS_GATHERING_EDITING(
            EnumConstants.VS_MEDIA_CATEGORY,
            "VO_MEDIA_CATEGORY_1",
            "新闻采编类",
            "News gathering and editing category"
    ),
    RADIO_TELEVISION(
            EnumConstants.VS_MEDIA_CATEGORY,
            "VO_MEDIA_CATEGORY_2",
            "广播电视类",
            "Radio and television category"
    ),
    NEW_MEDIA_OPERATIONS(
            EnumConstants.VS_MEDIA_CATEGORY,
            "VO_MEDIA_CATEGORY_3",
            "新媒体与运营类",
            "New Media and Operations"
    ),
    ADMINISTRATIVE_MANAGEMENT(
            EnumConstants.VS_MEDIA_CATEGORY,
            "VO_MEDIA_CATEGORY_4",
            "行政管理类",
            "Administrative management category"
    ),
    TECHNICAL(
            EnumConstants.VS_MEDIA_CATEGORY,
            "VO_MEDIA_CATEGORY_5",
            "技术类",
            "Technical category"
    ),
    OTHER(
            EnumConstants.VS_MEDIA_CATEGORY,
            "VO_MEDIA_CATEGORY_6",
            "其他",
            "Other"
    );

    /**
     * 值集分类编码
     */
    private final String valuesetCode;

    /**
     * 枚举项编码
     */
    private final String code;

    /**
     * 中文描述
     */
    private final String descriptionCN;

    /**
     * 英文描述
     */
    private final String descriptionEN;

    MediaPositionTypeEnum(String valuesetCode, String code, String descriptionCN, String descriptionEN) {
        this.valuesetCode = valuesetCode;
        this.code = code;
        this.descriptionCN = descriptionCN;
        this.descriptionEN = descriptionEN;
    }

    /**
     * 根据枚举编码获取枚举实例
     *
     * @param code VO_MEDIA_CATEGORY_1 格式的编码
     * @return 匹配的枚举实例，未找到返回null
     */
    public static MediaPositionTypeEnum getByCode(String code) {
        for (MediaPositionTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据枚举编码获取枚举实例
     *
     * @param descriptionCN descriptionCN
     * @return 匹配的枚举实例，未找到返回null
     */
    public static MediaPositionTypeEnum getByDescriptionCN(String descriptionCN) {
        for (MediaPositionTypeEnum type : values()) {
            if (type.descriptionCN.equals(descriptionCN)) {
                return type;
            }
        }
        return null;
    }
} 