package com.dexpo.module.member.domain.model.member;

import lombok.Data;

/**
 * 媒体用户拓展信息数据对象
 */
@Data
public class MemberReporterInfo {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 会员ID
     */
    private Long memberId;
    
    /**
     * 企业ID
     */
    private Long enterpriseId;
    
    /**
     * 展会ID
     */
    private Long exhibitionId;
    
    /**
     * 记者证号
     */
    private String mediaNewsmanNo;
    
    /**
     * 媒体类型：值集VS_MEDIA_TYPE
     */
    private String mediaTypeCode;

    /**
     * 媒体类型名称
     */
    private String mediaTypeNameCn;

    /**
     * 媒体类型英文名称
     */
    private String mediaTypeNameEn;

    /**
     * 媒体职位分类code：值集VS_MEDIA_POSITION
     */
    private String mediaPositionCategoryCode;

    /**
     * 媒体职位分类名称
     */
    private String mediaPositionCategoryNameCn;

    /**
     * 媒体职位分类英文名称
     */
    private String mediaPositionCategoryNameEn;

    /**
     * 其他媒体职位
     */
    private String otherMediaPosition;

    /**
     * 媒体职位：值集VS_MEDIA_POSITION
     */
    private String mediaPositionCode;
    
    /**
     * 媒体职位名称
     */
    private String mediaPositionNameCn;

    /**
     * 媒体职位英文名称
     */
    private String mediaPositionNameEn;

    /**
     * 媒体权限类型编码: 值集VS_MEDIA_PERMISSION_TYPE
     */
    private String mediaPermissionType;

    /**
     * 是否申请直播：0否 1是
     */
    private Boolean isApplyLiveStream;
    
    /**
     * 头像照
     */
    private String attachmentHeadPhoto;
    
    /**
     * 其他说明文件，多个文件用逗号隔开
     */
    private String attachmentOtherDescribe;
} 