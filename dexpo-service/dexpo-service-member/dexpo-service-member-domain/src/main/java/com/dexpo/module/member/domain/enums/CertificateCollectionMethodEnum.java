package com.dexpo.module.member.domain.enums;


/**
 * 证书领取方式枚举
 */
public enum CertificateCollectionMethodEnum {

    MAIL(
            "VS_CERTIFICATE_COLLECTION_METHOD",
            "VO_CERTIFICATE_COLLECTION_METHOD_1",
            "邮寄",
            "Delivery"
    ),
    SELF_PICKUP(
            "VS_CERTIFICATE_COLLECTION_METHOD",
            "VO_CERTIFICATE_COLLECTION_METHOD_2",
            "自取",
            "In-person Pickup"
    );

    /**
     * 值集分类编码
     */
    private final String valuesetCode;

    /**
     * 枚举项编码
     */
    private final String code;

    /**
     * 中文描述
     */
    private final String descriptionCN;

    /**
     * 英文描述
     */
    private final String descriptionEN;

    CertificateCollectionMethodEnum(String valuesetCode, String code, String descriptionCN, String descriptionEN) {
        this.valuesetCode = valuesetCode;
        this.code = code;
        this.descriptionCN = descriptionCN;
        this.descriptionEN = descriptionEN;
    }

    /**
     * 根据枚举编码获取枚举实例
     * @param code VO_CERTIFICATE_COLLECTION_METHOD_1 格式的编码
     * @return 匹配的枚举实例，未找到返回null
     */
    public static CertificateCollectionMethodEnum getByCode(String code) {
        for (CertificateCollectionMethodEnum method : values()) {
            if (method.code.equals(code)) {
                return method;
            }
        }
        return null;
    }

    /**
     * 根据descriptionCN获取枚举实例
     *
     * @param descriptionCN 中文
     * @return 匹配的枚举实例，未找到返回null
     */
    public static CertificateCollectionMethodEnum getByDescriptionCN(String descriptionCN) {
        for (CertificateCollectionMethodEnum method : values()) {
            if (method.descriptionCN.equals(descriptionCN)) {
                return method;
            }
        }
        return null;
    }

    // Getter 方法
    public String getValuesetCode() {
        return valuesetCode;
    }

    public String getCode() {
        return code;
    }

    public String getDescriptionCN() {
        return descriptionCN;
    }

    public String getDescriptionEN() {
        return descriptionEN;
    }
}
