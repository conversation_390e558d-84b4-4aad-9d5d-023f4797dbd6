package com.dexpo.module.member.domain.model.member;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户签署协议记录数据对象
 */
@Data
public class MemberSignRecord {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 协议ID
     */
    private Long agreementId;

    /**
     * 签署时间
     */
    private LocalDateTime signTime;

    /**
     * 是否有效
     */
    private Boolean isEffect;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private Long createUser;

} 