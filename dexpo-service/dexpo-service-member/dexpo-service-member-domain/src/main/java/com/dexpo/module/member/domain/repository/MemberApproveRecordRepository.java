package com.dexpo.module.member.domain.repository;

import com.dexpo.module.member.domain.model.member.MemberApproveRecord;
import com.dexpo.module.member.domain.model.member.message.MemberDataWarehouseSyncMessage;

import java.util.List;

public interface MemberApproveRecordRepository {
    void addBatch(List<MemberApproveRecord> memberApproveRecords);

    List<MemberDataWarehouseSyncMessage> selectDataWarehouseSyncMessageList(List<Long> ids, String code, Long exhibitionId);
}
