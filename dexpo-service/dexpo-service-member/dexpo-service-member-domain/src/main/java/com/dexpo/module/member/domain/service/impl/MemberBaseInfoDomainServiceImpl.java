package com.dexpo.module.member.domain.service.impl;

import com.dexpo.module.member.domain.model.member.MemberBaseInfo;
import com.dexpo.module.member.domain.repository.MemberBaseInfoRepository;
import com.dexpo.module.member.domain.service.MemberBaseInfoDomainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 会员基本信息 Service 实现类
 */
@Service
@Validated
@Slf4j
public class MemberBaseInfoDomainServiceImpl implements MemberBaseInfoDomainService {


    @Resource
    private MemberBaseInfoRepository memberBaseInfoRepository;


    @Override
    public void updateMemberBaseInfo(MemberBaseInfo memberBaseInfo) {
        memberBaseInfoRepository.updateMemberById(memberBaseInfo);
    }

    @Override
    public void deleteMemberBaseInfo(Long id) {
        memberBaseInfoRepository.deleteMemberById(id);
    }

    @Override
    public MemberBaseInfo getMemberBaseInfo(Long id) {
        return memberBaseInfoRepository.findById(id);
    }

    @Override
    public MemberBaseInfo getMemberBaseInfoByMobileOrEmail(String mobileOrEmail) {
        return memberBaseInfoRepository.getMemberBaseInfoByMobileOrEmail(mobileOrEmail);
    }


} 