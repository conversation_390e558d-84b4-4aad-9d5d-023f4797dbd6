package com.dexpo.module.member.domain.service;


import com.dexpo.module.member.domain.model.enterprise.EnterpriseInfo;
import com.dexpo.module.member.domain.model.member.*;

/**
 * 媒体用户接口
 */
public interface MediaMemberDomainService {

    MemberSignRecord queryMemberSignRecordByMemberIdAndAgreementId(Long memberId, Long agreementId);

    void addMemberSignRecord(MemberSignRecord recordDO);

    MemberBaseInfo queryMemberBaseInfoByMemberId(Long memberId);

    EnterpriseInfo getEnterpriseInfoById(Long enterpriseId);

    MemberRecipientInfo queryMemberRecipientInfoByMemberId(Long memberId);

    MemberReporterInfo queryMemberReporterInfoByMemberId(Long memberId,Long exhibitionId);

    MemberReporterInfo queryMemberReporterInfoByMemberIdAndExhibitionId(Long memberId,Long exhibitionId);

    void addOrModifyMemberBaseInfo(MemberBaseInfo baseInfoDO);

    MemberParticipateRecord queryMemberParticipateRecordByUserIdAndExhibitionId(Long memberId, Long exhibitionId);

    void modifyMemberParticipateRecordById(MemberParticipateRecord recordDO);

    void addOrModifyMemberRecipientInfo(MemberRecipientInfo recipientInfoDO);

    void addOrModifyMemberReporterInfo(MemberReporterInfo reporterInfoDO);

    EnterpriseInfo queryMediaEnterpriseByNameAndLocationCode(String enterpriseName, String enterpriseLocationCode);

    void addOrModifyEnterpriseInfo(EnterpriseInfo enterpriseInfo);
}
