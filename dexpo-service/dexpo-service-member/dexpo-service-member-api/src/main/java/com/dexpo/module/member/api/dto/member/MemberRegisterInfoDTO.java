package com.dexpo.module.member.api.dto.member;


import com.dexpo.module.member.api.dto.EnterpriseInfoDTO;
import com.dexpo.module.member.api.dto.ExhibitionInfoDTO;
import com.dexpo.module.member.api.dto.RegisterInfoSyncDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 媒体信息保存对象
 */

@Data
@Schema(description = "媒体信息保存对象")
public class MemberRegisterInfoDTO {

    @Schema(description = "会员基本信息")
    @NotNull
    @Valid
    private MemberBaseInfoDTO memberBaseInfo;

    @Schema(description = "媒体用户拓展信息")
    @NotNull
    @Valid
    private MemberReporterInfoDTO memberReporterInfo;

    @Schema(description = "用户收证信息")
    @NotNull
    @Valid
    private MemberRecipientInfoDTO memberRecipientInfo;

    @Schema(description = "企业信息")
    @NotNull
    @Valid
    private EnterpriseInfoDTO enterpriseInfo;

    @Schema(description = "展会信息")
    @NotNull
    @Valid
    private ExhibitionInfoDTO exhibitionInfo;

    @Schema(description = "注册信息同步对象,用于提交时给数仓传递数据")
    private RegisterInfoSyncDTO registerInfo;

    @Schema(description = "注册时语言环境：值集VS_LANGUAGE")
    @NotNull
    private String registerLanguage;

}
