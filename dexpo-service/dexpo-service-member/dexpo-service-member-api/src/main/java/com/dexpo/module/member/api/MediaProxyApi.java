package com.dexpo.module.member.api;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.member.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "媒体批量操作")
public interface MediaProxyApi {

    String PREFIX = "/mediaProxy";

    @PostMapping(value = PREFIX + "/registrationByFile")
    @Operation(summary = "媒体用户批量注册文件上传")
    CommonResult<Boolean> registrationByFile(@RequestParam("memberMediaProxyRecordId") Long memberMediaProxyRecordId,
                                             @RequestParam("exhibitionId") Long exhibitionId,
                                             @RequestParam("filename") String filename,
                                             @RequestBody byte[] content);

}
