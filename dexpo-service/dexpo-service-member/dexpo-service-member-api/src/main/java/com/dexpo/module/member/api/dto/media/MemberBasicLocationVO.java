package com.dexpo.module.member.api.dto.media;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * 业务地域信息数据对象
 */
@Data
@Schema(description = "业务地域信息数据对象")
public class MemberBasicLocationVO {
    
    @Schema(description = "主键ID")
    private Long id;
    
    @Schema(description = "地域编码")
    private String locationCode;
    
    @Schema(description = "地域名称-中文")
    private String locationNameCn;
    
    @Schema(description = "地域名称-英文")
    private String locationNameEn;
    
    @Schema(description = "地域标签：值集VS_ACTION_ENTERPRISE_TYPE")
    private String locationTag;
    
} 