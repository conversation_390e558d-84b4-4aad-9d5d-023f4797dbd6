package com.dexpo.module.member.api.vo.media;

import com.dexpo.module.member.api.vo.AttributeCountVO;
import lombok.Data;

import java.util.List;

/**
 * 媒体相关报表信息
 */
@Data
public class MediaReportVO {

    /**
     * 企业数量
     */
    private Long enterpriseCount;


    /**
     * 媒体注册人数
     */
    private Long mediaUserCount;

    /**
     * 境内媒体注册人数
     */
    private Long domesticMediaUserCount;

    /**
     * 境外媒体注册人数
     */
    private Long overseasMediaUserCount;

    /**
     * 媒体分布
     */
    private List<AttributeCountVO> mediaDistributionList;

    /**
     * 媒体类型
     */
    private List<AttributeCountVO> mediaTypeList;

    /**
     * 媒体职位
     */
    private List<AttributeCountVO> mediaPositionList;

}
