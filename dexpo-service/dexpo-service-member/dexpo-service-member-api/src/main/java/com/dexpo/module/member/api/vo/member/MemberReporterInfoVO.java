package com.dexpo.module.member.api.vo.member;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 媒体用户拓展信息VO对象
 */
@Data
@Schema(description = "媒体用户拓展信息VO对象")
@JsonIgnoreProperties(ignoreUnknown = true)
public class MemberReporterInfoVO {
    
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;
    
    /**
     * 会员ID
     */
    @Schema(description = "会员ID")
    private Long memberId;
    
    /**
     * 企业ID
     */
    @Schema(description = "企业ID")
    private Long enterpriseId;
    
    /**
     * 展会ID
     */
    @Schema(description = "展会ID")
    private Long exhibitionId;
    
    /**
     * 记者证号
     */
    @Schema(description = "记者证号")
    private String mediaNewsmanNo;

    /**
     * 媒体类型：值集VS_MEDIA_TYPE
     */
    @Schema(description = "media_type_code")
    private String mediaTypeCode;

    /**
     * 媒体类型名称
     */
    @Schema(description = "media_type_name_cn")
    private String mediaTypeNameCn;

    /**
     * 媒体类型英文名称
     */
    @Schema(description = "media_type_name_en")
    private String mediaTypeNameEn;

    /**
     * 媒体职位：值集VS_MEDIA_POSITION
     */
    @Schema(description = "媒体职位：值集VS_MEDIA_POSITION")
    private String mediaPositionCode;
    
    /**
     * 媒体职位名称
     */
    @Schema(description = "媒体职位名称")
    private String mediaPositionNameCn;

    /**
     * 媒体职位英文名称
     */
    @Schema(description = "媒体职位英文名称")
    private String mediaPositionNameEn;


    /**
     * 其他媒体职位
     */
    @Schema(description = "其他媒体职位")
    private String otherMediaPosition;

    /**
     * 媒体权限类型编码: 值集VS_MEDIA_PERMISSION_TYPE
     */
    @Schema(description = "记者信息-媒体权限类型编码")
    private String mediaPermissionType;


    /**
     * 是否申请直播：0否 1是
     */
    @Schema(description = "是否申请直播：0否 1是")
    private Boolean isApplyLiveStream;
    
    /**
     * 头像照
     */
    @Schema(description = "头像照")
    private String attachmentHeadPhoto;
    
    /**
     * 其他说明文件，多个文件用逗号隔开
     */
    @Schema(description = "其他说明文件，多个文件用逗号隔开")
    private String attachmentOtherDescribe;

    @Schema(description = "附件信息")
    private List<MemberAttachmentInfoVO> attachmentInfoList;

    @Schema(description = "头像附件信息")
    private MemberAttachmentInfoVO headPhotoAttachmentInfo;

    @Schema(description = "注册状态：值集VS_REGISTER_STATUS")
    private String registerStatus;
} 