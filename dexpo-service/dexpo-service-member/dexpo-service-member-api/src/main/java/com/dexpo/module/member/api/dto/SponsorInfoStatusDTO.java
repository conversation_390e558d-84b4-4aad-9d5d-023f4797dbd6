package com.dexpo.module.member.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 更新用户状态信息DTO
 */
@Data
public class SponsorInfoStatusDTO {

    @Schema(description = "状态（VO_SPONSOR_STATUS_1、VO_SPONSOR_STATUS_2）值集:VS_SPONSOR_STATUS")
    private String status;

    @Schema(description = "用户编码")
    private String sponsorCode;

    @Schema(description = "用户名称")
    private String sponsorName;
}
