package com.dexpo.module.member.api.dto.member;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 会员收证信息DTO
 */

@Data
public class MemberRecipientInfoDTO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 会员ID
     */
    @Schema(description = "会员ID")
    private Long memberId;

    /**
     * 领证方式：值集VS_CERTIFICATE_COLLECTION_METHOD
     */
    @Schema(description = "领证方式：值集VS_CERTIFICATE_COLLECTION_METHOD")
    private String certificateCollectionMethod;

    /**
     * 收件人姓名
     */
    @Schema(description = "收件人姓名")
    private String recipientName;

    /**
     * 收件人-名
     */
    @Schema(description = "收件人-名")
    private String recipientFirstName;

    /**
     * 收件人-姓
     */
    @Schema(description = "收件人-姓")
    private String recipientLastName;

    /**
     * 收件人手机号
     */
    @Schema(description = "收件人手机号")
    private String recipientMobile;

    /**
     * 收件人所在省编码
     */
    @Schema(description = "收件人所在省编码")
    private String recipientProvinceCode;

    /**
     * 收件人所在省名称
     */
    @Schema(description = "收件人所在省名称")
    private String recipientProvinceName;

    /**
     * 收件人所在市编码
     */
    @Schema(description = "收件人所在市编码")
    private String recipientCityCode;

    /**
     * 收件人所在市名称
     */
    @Schema(description = "收件人所在市名称")
    private String recipientCityName;

    /**
     * 收件人所在区编码
     */
    @Schema(description = "收件人所在区编码")
    private String recipientDistrictCode;

    /**
     * 收件人所在区名称
     */
    @Schema(description = "收件人所在区名称")
    private String recipientDistrictName;

    /**
     * 收件人详细地址
     */
    @Schema(description = "收件人详细地址")
    private String recipientAddress;
}
