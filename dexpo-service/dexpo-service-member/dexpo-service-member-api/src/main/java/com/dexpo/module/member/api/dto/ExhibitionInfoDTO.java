package com.dexpo.module.member.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 展会信息对象 用于传递给数仓
 */

@Data
public class ExhibitionInfoDTO {

    /**
     * 展会id
     */
    @Schema(description = "参展信息-展会ID")
    private Long id;

    /**
     * 展会编码
     */
    @Schema(description = "参展信息-展会编码")
    private String exhibitionCode;

    /**
     * 展会中文名称
     */
    @Schema(description = "参展信息-展会中文名称")
    private String exhibitionNameCn;

    /**
     * 展会英文名称
     */
    @Schema(description = "参展信息-展会英文名称")
    private String exhibitionNameEn;

    /**
     * 展会届数
     */
    @Schema(description = "参展信息-展会届数")
    private String exhibitionSessionKey;

    /**
     * 展会标签
     */
    @Schema(description = "参展信息-展会标签")
    private String exhibitionTagCode;

    /**
     * 注册时间
     */
    @Schema(description = "参展信息-注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String registerTime;

    /**
     * 注册状态
     */
    @Schema(description = "参展信息-注册状态")
    private String registerStatus;

    /**
     * 注册系统
     */
    @Schema(description = "参展信息-注册系统")
    private String registerSystem;

    /**
     * 注册方式
     */
    @Schema(description = "参展信息-注册方式")
    private String registerMethod;

    /**
     * 注册来源
     */
    @Schema(description = "参展信息-注册来源")
    private String registerSource;
}
