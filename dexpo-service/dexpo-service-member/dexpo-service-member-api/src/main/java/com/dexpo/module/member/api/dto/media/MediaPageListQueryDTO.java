package com.dexpo.module.member.api.dto.media;

import com.dexpo.framework.common.pojo.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 服务中台 媒体分页列表查询DTO
 */
@Data
public class MediaPageListQueryDTO extends PageParam {

    /**
     * 注册状态
     */
    private String registerStatus;

    /**
     * 姓名
     */
    private String memberName;

    /**
     * 手机号
     */
    private String memberMobile;

    /**
     * 邮箱
     */
    private String memberEmail;

    /**
     * 所属地域CODE
     */
    private List<String> enterpriseLocationCodes;

    /**
     * 媒体类型 media_type_code
     */
    private List<String> mediaTypeCodes;

    /**
     * 展会tag code -> 当前登录归属展会的下级
     */
    private List<String> exhibitionTagCodes;

    /**
     * 展会年份（届）
     */
    private List<String> exhibitionSessionKeys;

    /**
     * 注册来源 值集VS_REGISTER_SOURCE
     */
    private String registerSource;

    /**
     * 媒体权限
     */
    private List<String> mediaPermissionType;

    /**
     * 注册时间开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime registerTimeStart;

    /**
     * 注册时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime registerTimeEnd;

}
