package com.dexpo.module.member.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 企业信息数据对象
 */
@Data
@Schema(description = "企业信息数据对象")
public class EnterpriseInfoVO  {
    
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;
    
    /**
     * 企业名称
     */
    @Schema(description = "企业名称")
    private String enterpriseName;
    
    /**
     * 企业类型：值集VS_ACTION_ENTERPRISE_TYPE
     */
    @Schema(description = "企业类型：值集VS_ACTION_ENTERPRISE_TYPE")
    private String enterpriseType;
    
    /**
     * 所属地域Code
     */
    @Schema(description = "所属地域Code")
    private String enterpriseLocationCode;


    /**
     * 所属地域中文名称
     */
    @Schema(description = "enterprise_location_name_cn")
    private String enterpriseLocationNameCn;

    /**
     * 所属地域英文名称
     */
    @Schema(description = "enterprise_location_name_en")
    private String enterpriseLocationNameEn;

    /**
     * 是否可用：0否 1是
     */
    private Boolean isUseAble;

} 