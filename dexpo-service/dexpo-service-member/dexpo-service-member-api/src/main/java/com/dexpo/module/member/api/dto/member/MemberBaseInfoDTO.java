package com.dexpo.module.member.api.dto.member;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * 用户信息DTO
 */
@Data
public class MemberBaseInfoDTO {

    /**
     * 主键ID
     */
    @Schema(description = "基础信息-主键ID")
    private Long id;

    /**
     * 会员编码（系统自动生成）
     */
    @Schema(description = "基础信息-会员编码")
    private String memberCode;

    /**
     * 姓名
     */
    @Schema(description = "基础信息-姓名")
    private String memberName;

    /**
     * 英文名
     */
    @Schema(description = "基础信息-英文名")
    private String memberFirstName;

    /**
     * 英文姓
     */
    @Schema(description = "基础信息-英文姓")
    private String memberLastName;

    /**
     * 性别：值集VS_GENDER
     */
    @NotNull
    @Schema(description = "基础信息-性别")
    private String memberGender;

    /**
     * 生日
     */
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "基础信息-生日")
    private LocalDate memberBirthDay;

    /**
     * 手机号
     */
    @Schema(description = "基础信息-手机号")
    private String memberMobile;

    /**
     * 邮箱
     */
    @NotNull
    @Schema(description = "基础信息-邮箱")
    private String memberEmail;

    /**
     * 证件类型：值集VS_ID_CATEGORY
     */
    @NotNull
    @Schema(description = "基础信息-证件类型")
    private String idCategory;

    /**
     * 证件号码
     */
    @NotNull
    @Schema(description = "基础信息-证件号码")
    private String idNumber;

    /**
     * 国家CODE
     */
    @NotNull
    @Schema(description = "基础信息-国家编码")
    private String countryCode;

    /**
     * 国家名称
     */
    @Schema(description = "基础信息-国家名称")
    private String countryNameCn;

    /**
     * 国家名称(英文)
     */
    @Schema(description = "基础信息-国家英文名称")
    private String countryNameEn;

    /**
     * 居住地所在省CODE
     */
    @Schema(description = "基础信息-居住地所在省编码")
    private String currentHomeProvinceCode;

    /**
     * 居住地所在省名称
     */
    @Schema(description = "基础信息-居住地所在省名称")
    private String currentHomeProvinceName;

    /**
     * 居住地所在市CODE
     */
    @Schema(description = "基础信息-居住地所在市编码")
    private String currentHomeCityCode;

    /**
     * 居住地所在市名称
     */
    @Schema(description = "基础信息-居住地所在市名称")
    private String currentHomeCityName;

    /**
     * 居住地所在区CODE
     */
    @Schema(description = "基础信息-居住地所在区编码")
    private String currentHomeDistrictCode;

    /**
     * 居住地所在区名称
     */
    @Schema(description = "基础信息-居住地所在区名称")
    private String currentHomeDistrictName;

    /**
     * 居住地详细地址
     */
    @Schema(description = "基础信息-居住地详细地址")
    private String currentHomeDetailAddress;

}
