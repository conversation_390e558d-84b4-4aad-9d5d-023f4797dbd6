package com.dexpo.module.member.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class SponsorAllInfoVO {
    /**
     * 用户编号
     */
    @Schema(description = "用户编号")
    private String sponsorCode;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String sponsorName;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String sponsorMobile;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String sponsorEmail;

    /**
     * 管理组织代码
     */
    @Schema(description = "管理组织代码")
    private Integer organizationCode;

    /**
     * 状态
     */
    @Schema(description = "状态（正常、冻结）")
    private String status;

    @Schema(description = "页码")
    private Integer pageNo = 1;

    @Schema(description = "每页条数")
    private Integer pageSize = 10;
}
