package com.dexpo.module.member.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 运营人员用户登陆信息DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SponsorLoginExhibitionTagDTO {

    @Schema(description = "exhibitionTag")
    @NotNull
    private String exhibitionTagCode;

}
