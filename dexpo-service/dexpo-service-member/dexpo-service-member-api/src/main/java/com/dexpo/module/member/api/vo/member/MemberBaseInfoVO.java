package com.dexpo.module.member.api.vo.member;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * 会员基本信息数据PO对象 用于反序列化redis中存储的user信息
 *
 */
@Data
@Schema(description = "会员基本信息VO对象")
@JsonIgnoreProperties(ignoreUnknown = true)
public class MemberBaseInfoVO {
    
    @Schema(description = "主键ID")
    private Long id;
    
    @Schema(description = "会员编码（系统自动生成）")
    private String memberCode;
    
    @Schema(description = "姓名")
    private String memberName;
    
    @Schema(description = "英文名")
    private String memberFirstName;
    
    @Schema(description = "英文姓")
    private String memberLastName;
    
    @Schema(description = "性别：值集VS_GENDER")
    private String memberGender;
    
    @Schema(description = "生日", example = "1990-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate memberBirthDay;
    
    @Schema(description = "手机号", example = "13800138000")
    private String memberMobile;
    
    @Schema(description = "邮箱", example = "<EMAIL>")
    private String memberEmail;
    
    @Schema(description = "证件类型：值集VS_ID_CATEGORY")
    private String idCategory;
    
    @Schema(description = "证件号码")
    private String idNumber;
    
    @Schema(description = "国家CODE")
    private String countryCode;

    @Schema(description = "基础信息-国家名称")
    private String countryNameCn;

    @Schema(description = "基础信息-国家英文名称")
    private String countryNameEn;
    
    @Schema(description = "居住地所在省CODE")
    private String currentHomeProvinceCode;
    
    @Schema(description = "居住地所在省名称")
    private String currentHomeProvinceName;
    
    @Schema(description = "居住地所在市CODE")
    private String currentHomeCityCode;
    
    @Schema(description = "居住地所在市名称")
    private String currentHomeCityName;
    
    @Schema(description = "居住地所在区CODE")
    private String currentHomeDistrictCode;
    
    @Schema(description = "居住地所在区名称")
    private String currentHomeDistrictName;
    
    @Schema(description = "居住地详细地址")
    private String currentHomeDetailAddress;
} 