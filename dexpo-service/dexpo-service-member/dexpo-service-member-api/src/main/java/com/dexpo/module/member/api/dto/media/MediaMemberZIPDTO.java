package com.dexpo.module.member.api.dto.media;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 媒体批量导入
 */
@Data
public class MediaMemberZIPDTO {

    @Schema(description = "展会ID")
    @NotNull
    private Long exhibitionId;

    @Schema(description = "上传的压缩包")
    @NotNull
    private MultipartFile file;
}
