package com.dexpo.module.member.api.dto.message;

import lombok.Data;

/**
 * 媒体注册事件消息
 */
@Data
public class MediaRegisterEventDTO {


    private Long memberId;

    /**
     * 媒体注册状态
     */
    private String registerStatus;

    /**
     * 会展id
     */
    private Long exhibitionId;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 当前时间
     * yyyy-MM-dd hh:mm:ss
     */
    private String eventTime;

    /**
     * 操作人
     */
    private Long operatorId;

    /**
     * 操作人名称
     */
    private String operatorName;
}
