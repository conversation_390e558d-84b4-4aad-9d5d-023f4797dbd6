package com.dexpo.module.member.api.vo.member;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: <PERSON><PERSON>e
 * @CreateTime: 2025-06-22
 * @Description:
 */

@Data
public class MemberParticipateRecordVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long memberId;

    /**
     * 参与码
     */
    private String participateCode;

    /**
     * 展会ID
     */
    private Long exhibitionId;

    /**
     * 用户类型：值集VS_ACTION_USER_TYPE
     */
    private String memberType;


    /**
     * 注册时语言环境：值集VS_LANGUAGE
     */
    private String registerLanguage;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 注册状态：值集VS_REGISTER_STATUS
     */
    private String registerStatus;

    /**
     * 注册系统：值集VS_REGISTER_SYSTEM
     */
    private String registerSystem;

    /**
     * 注册方式：值集VS_REGISTER_METHOD
     */
    private String registerMethod;

    /**
     * 注册来源：值集VS_REGISTER_SOURCE
     */
    private String registerSource;
}
