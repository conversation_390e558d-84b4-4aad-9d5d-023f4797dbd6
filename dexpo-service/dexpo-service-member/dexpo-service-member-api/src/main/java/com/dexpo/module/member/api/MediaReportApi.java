package com.dexpo.module.member.api;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.member.api.dto.media.MediaReportQueryDTO;
import com.dexpo.module.member.api.vo.media.MediaReportVO;
import com.dexpo.module.member.enums.ApiConstants;

import io.swagger.v3.oas.annotations.parameters.RequestBody;
import jakarta.validation.Valid;

@FeignClient(name = ApiConstants.NAME)
public interface MediaReportApi {

    String PREFIX = "/mediaReport";

    /**
     * 获取媒体报表
     *
     * @param queryDTO 查询条件
     * @return 媒体报表
     */
    @PostMapping(PREFIX + "/getMediaReport")
    CommonResult<MediaReportVO> getMediaReport(@Valid @RequestBody MediaReportQueryDTO queryDTO);

}
