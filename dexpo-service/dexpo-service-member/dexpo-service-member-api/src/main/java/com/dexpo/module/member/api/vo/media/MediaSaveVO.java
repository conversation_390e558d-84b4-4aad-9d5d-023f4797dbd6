package com.dexpo.module.member.api.vo.media;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 媒体用户信息保存对象
 */
@Data
public class MediaSaveVO {

    @Schema(description = "会员id")
    private Long memberId;

    @Schema(description = "收件人id")
    private Long recipientId;

    @Schema(description = "媒体用户id")
    private Long reporterId;

    @Schema(description = "企业id")
    private Long enterpriseId;
}
