package com.dexpo.module.member.api.vo.member;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "附件信息VO对象")
@JsonIgnoreProperties(ignoreUnknown = true)
public class MemberAttachmentInfoVO {

    @Schema(description = "主键ID")
    private Long id;

    /**
     * 附件名
     */
    @Schema(description = "附件名")
    private String attachmentName;

    /**
     * 附件类型
     */
    @Schema(description = "附件类型")
    private String attachmentType;

    /**
     * 附件大小
     */
    @Schema(description = "附件大小")
    private Long attachmentSize;

    /**
     * 附件存储地址
     * /日期20250521/用户编码/uuid_文件名称.文件类型
     */
    @Schema(description = "附件存储地址")
    private String attachmentPath;
}
