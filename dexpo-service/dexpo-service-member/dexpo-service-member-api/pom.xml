<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.dexpo</groupId>
        <artifactId>dexpo-service-member</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>dexpo-service-member-api</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        member 模块 API，暴露给其它模块调用
    </description>

    <dependencies>


        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-integration-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-excel</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>io.swagger.core.v3</groupId> <!-- 接口文档：使用最新版本的 Swagger 模型 -->
            <artifactId>swagger-annotations</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- 参数校验 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

</project>
