# 基础镜像
FROM ciif-acr-registry.cn-shanghai.cr.aliyuncs.com/dev/eclipse-temurin:21-jdk-jammy

# 挂载目录
VOLUME /home/<USER>

# 创建目录
RUN mkdir -p /home/<USER>/dexpo-service-member

# 指定路径
WORKDIR /home/<USER>/dexpo-service-member

# 复制jar文件到路径
COPY target/dexpo-service-member-starter.jar /home/<USER>/dexpo-service-member/dexpo-service-member.jar

ENV TZ=Asia/Shanghai

EXPOSE 48085
# 启动
CMD java -jar dexpo-service-member.jar
