#--- #################### 数据库相关配置 ####################
#spring:
#
#  # 数据源配置项
#  autoconfigure:
#    exclude:
#      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 排除 Druid 的自动配置，使用 dynamic-datasource-spring-boot-starter 配置多数据源
#  datasource:
#    druid: # Druid 【监控】相关的全局配置
#      web-stat-filter:
#        enabled: true
#      stat-view-servlet:
#        enabled: true
#        allow: # 设置白名单，不填则允许所有访问
#        url-pattern: /druid/*
#        login-username: # 控制台管理用户名和密码
#        login-password:
#      filter:
#        stat:
#          enabled: true
#          log-slow-sql: true # 慢 SQL 记录
#          slow-sql-millis: 100
#          merge-sql: true
#        wall:
#          config:
#            multi-statement-allow: true
#    dynamic: # 多数据源配置
#      druid: # Druid 【连接池】相关的全局配置
#        initial-size: 5 # 初始连接数
#        min-idle: 10 # 最小连接池数量
#        max-active: 20 # 最大连接池数量
#        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
#        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
#        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
#        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
#        validation-query: SELECT 1 FROM DUAL # 配置检测连接是否有效
#        test-while-idle: true
#        test-on-borrow: false
#        test-on-return: false
#      primary: master
#      datasource:
#          url: *****************************************************/${spring.datasource.dynamic.datasource.master.name}?allowMultiQueries=true&useUnicode=true&useSSL=false&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&autoReconnect=true&nullCatalogMeansCurrent=true # MySQL Connector/J 8.X 连接的示例
#          #          url: ***************************/${spring.datasource.dynamic.datasource.master.name}?useSSL=false&allowPublicKeyRetrieval=true&useUnicode=true&characterEncoding=UTF-8&serverTimezone=CTT # MySQL Connector/J 5.X 连接的示例
#          #          url: ********************************/${spring.datasource.dynamic.datasource.slave.name} # PostgreSQL 连接的示例
#          #          url: *********************************** # Oracle 连接的示例
#          #          url: ********************************************=${spring.datasource.dynamic.datasource.master.name} # SQLServer 连接的示例
#          username: root
#          password: '!QAZ2wsx'
#
#  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
#  redis:
#    host: ************* # 地址
#    port: 6379 # 端口
#    database: 2 # 数据库索引
##    password: 123456 # 密码，建议生产环境开启
#
#--- #################### MQ 消息队列相关配置 ####################
#spring:
#  cloud:
#    stream:
#      rocketmq:
#        # RocketMQ Binder 配置项，对应 RocketMQBinderConfigurationProperties 类
#        binder:
#          name-server: **************:9876 # RocketMQ Namesrv 地址
#      binding-retry-interval: 7200 # 消息绑定重试间隔时间，单位：秒，默认为 30 秒。考虑到本地可能不启动 RocketMQ 服务，设置为 2 小时
#
#--- #################### 定时任务相关配置 ####################
#xxl:
#  job:
#    enabled: false # 是否开启调度中心，默认为 true 开启
#    admin:
#      addresses: http://127.0.0.1:9090/xxl-job-admin # 调度中心部署跟地址
#
#--- #################### 服务保障相关配置 ####################
#
## Lock4j 配置项
#lock4j:
#  acquire-timeout: 3000 # 获取分布式锁超时时间，默认为 3000 毫秒
#  expire: 30000 # 分布式锁的超时时间，默认为 30 毫秒
#
#--- #################### 监控相关配置 ####################
#
## Actuator 监控端点的配置项
#management:
#  endpoints:
#    web:
#      base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
#      exposure:
#        include: '*' # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。
#
## Spring Boot Admin 配置项
#spring:
#  boot:
#    admin:
#      # Spring Boot Admin Client 客户端的相关配置
#      client:
#        instance:
#          service-host-type: IP # 注册实例时，优先使用 IP [IP, HOST_NAME, CANONICAL_HOST_NAME]
#      # Spring Boot Admin Server 服务端的相关配置
#      context-path: /admin # 配置 Spring
#
## 日志文件配置
#logging:
#  level:
#    # 配置自己写的 MyBatis Mapper 打印日志
#    com.dtt.module.infra.dal.mysql: debug
#
#--- #################### RR低代码相关配置 ####################
#
## RR低代码配置项，设置当前项目所有自定义的配置
#dtt:
#  env: # 多环境的配置项
#    tag: ${HOSTNAME}
#  security:
#    mock-enable: true
#  xss:
#    enable: false
#    exclude-urls: # 如下两个 url，仅仅是为了演示，去掉配置也没关系
#      - ${spring.boot.admin.context-path}/** # 不处理 Spring Boot Admin 的请求
#      - ${management.endpoints.web.base-path}/** # 不处理 Actuator 的请求
#  access-log: # 访问日志的配置项
#    enable: false
#  error-code: # 错误码相关配置项
#    enable: false
#  demo: false # 关闭演示模式
