package com.dexpo.module.member.infrastructure.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.dexpo.module.member.domain.model.member.MemberRecipientInfo;
import com.dexpo.module.member.infrastructure.converter.MemberRecipientInfoDoConverter;
import com.dexpo.module.member.infrastructure.dal.dataobject.MemberRecipientInfoDO;
import com.dexpo.module.member.infrastructure.dal.mysql.MemberRecipientInfoMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MemberRecipientInfoRepositoryImplTest {

    @Mock
    private MemberRecipientInfoMapper baseMapper;

    @Spy
    @InjectMocks
    private MemberRecipientInfoRepositoryImpl repository;

    @BeforeEach
    void setUp() throws Exception {
        java.lang.reflect.Field field = CrudRepository.class.getDeclaredField("baseMapper");
        field.setAccessible(true);
        field.set(repository, baseMapper);
    }

    @Test
    void testQueryByMemberId_Normal() {
        Long memberId = 1L;
        MemberRecipientInfoDO do1 = new MemberRecipientInfoDO();
        when(baseMapper.queryByMemberId(memberId)).thenReturn(do1);
        MemberRecipientInfo result = repository.queryByMemberId(memberId);
        assertNotNull(result);
    }

    @Test
    void testAddOrModify_Normal() {
        MemberRecipientInfo model = new MemberRecipientInfo();
        MemberRecipientInfoDO do1 = MemberRecipientInfoDoConverter.INSTANCE.toDo(model);
        do1.setId(2L);
        doReturn(true).when(baseMapper).insertOrUpdate((MemberRecipientInfoDO) any());
        repository.addOrModify(model);
        assertNull(model.getId());
        verify(baseMapper).insertOrUpdate((MemberRecipientInfoDO) any());
    }

    @Test
    void testAddBatch_Normal() {
        List<MemberRecipientInfo> modelList = new ArrayList<>();
        MemberRecipientInfo model = new MemberRecipientInfo();
        modelList.add(model);
        List<MemberRecipientInfoDO> doList = MemberRecipientInfoDoConverter.INSTANCE.toDoList(modelList);
        doReturn(true).when(repository).saveBatch(doList);
        repository.addBatch(modelList);
        verify(repository).saveBatch(doList);
    }
} 