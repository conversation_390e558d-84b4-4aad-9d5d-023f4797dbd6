package com.dexpo.module.member.infrastructure.mq.producer;

import com.dexpo.module.member.api.dto.message.MediaRegisterEventDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.stream.function.StreamBridge;

import static org.mockito.Mockito.*;

/**
 * MediaRegisterStatusEventProducer 单元测试
 */
@ExtendWith(MockitoExtension.class)
class MediaRegisterStatusEventProducerTest {

    @Mock
    private StreamBridge streamBridge;

    @InjectMocks
    private MediaRegisterStatusEventProducer producer;

    @Test
    void testMediaRegisterStatusEventChannel_Normal() {
        MediaRegisterEventDTO message = mock(MediaRegisterEventDTO.class);
        when(streamBridge.send(anyString(), any())).thenReturn(true);

        producer.mediaRegisterStatusEventChannel(message);

        verify(streamBridge, times(1)).send(eq("mediaRegisterStatusEventChannel-out-0"), eq(message));
    }

    @Test
    void testMediaRegisterStatusEventChannel_NullMessage() {
        when(streamBridge.send(anyString(), any())).thenReturn(true);

        producer.mediaRegisterStatusEventChannel(null);

        verify(streamBridge, times(1)).send(eq("mediaRegisterStatusEventChannel-out-0"), isNull());
    }

    @Test
    void testMediaRegisterStatusEventChannel_SendReturnsFalse() {
        MediaRegisterEventDTO message = mock(MediaRegisterEventDTO.class);
        when(streamBridge.send(anyString(), any())).thenReturn(false);

        producer.mediaRegisterStatusEventChannel(message);

        verify(streamBridge, times(1)).send(eq("mediaRegisterStatusEventChannel-out-0"), eq(message));
    }
} 