package com.dexpo.module.member.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.dexpo.module.member.domain.model.member.MemberParticipateRecord;
import com.dexpo.module.member.infrastructure.converter.MemberParticipateRecordDoConverter;
import com.dexpo.module.member.infrastructure.dal.dataobject.MemberParticipateRecordDO;
import com.dexpo.module.member.infrastructure.dal.mysql.MemberParticipateRecordMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MemberParticipateRecordRepositoryImplTest {

    @Mock
    private MemberParticipateRecordMapper baseMapper;

    @Spy
    @InjectMocks
    private MemberParticipateRecordRepositoryImpl repository;

    @BeforeEach
    void setUp() throws Exception {
        java.lang.reflect.Field field = CrudRepository.class.getDeclaredField("baseMapper");
        field.setAccessible(true);
        field.set(repository, baseMapper);
    }

    @Test
    void testActive_Normal() {
        List<Long> ids = Arrays.asList(1L, 2L);
        doNothing().when(baseMapper).active(ids);
        repository.active(ids);
        verify(baseMapper).active(ids);
    }

    @Test
    void testQueryByUserIdAndExhibitionId_Normal() {
        Long memberId = 1L;
        Long exhibitionId = 10L;
        MemberParticipateRecordDO do1 = new MemberParticipateRecordDO();
        when(baseMapper.queryByUserIdAndExhibitionId(memberId, exhibitionId)).thenReturn(do1);
        MemberParticipateRecord result = repository.queryByUserIdAndExhibitionId(memberId, exhibitionId);
        assertNotNull(result);
    }

    @Test
    void testModifyById_Normal() {
        MemberParticipateRecord model = new MemberParticipateRecord();
        MemberParticipateRecordDO do1 = MemberParticipateRecordDoConverter.INSTANCE.toDo(model);
        doReturn(true).when(repository).updateById(do1);
        repository.modifyById(model);
        verify(repository).updateById(do1);
    }

    @Test
    void testAdd_Normal() {
        MemberParticipateRecord model = new MemberParticipateRecord();
        MemberParticipateRecordDO do1 = MemberParticipateRecordDoConverter.INSTANCE.toDo(model);
        do1.setId(123L);
        doReturn(true).when(repository).save(any());
        repository.add(model);
        assertNull(model.getId());
        verify(repository).save(any());
    }

    @Test
    void testModifyBatch_Normal() {
        List<MemberParticipateRecord> modelList = new ArrayList<>();
        MemberParticipateRecord model = new MemberParticipateRecord();
        modelList.add(model);
        List<MemberParticipateRecordDO> doList = MemberParticipateRecordDoConverter.INSTANCE.toDoList(modelList);
        doReturn(true).when(repository).updateBatchById(doList);
        repository.modifyBatch(modelList);
        verify(repository).updateBatchById(doList);
    }

    @Test
    void testQueryByMemberIdAndExhibitionId_Normal() {
        Set<Long> memberIdSet = Set.of(1L, 2L);
        Long exhibitionId = 10L;
        List<MemberParticipateRecordDO> doList = new ArrayList<>();
        MemberParticipateRecordDO do1 = new MemberParticipateRecordDO();
        doList.add(do1);
        doReturn(doList).when(repository).list(any(Wrapper.class));
        List<MemberParticipateRecord> modelList = MemberParticipateRecordDoConverter.INSTANCE.toModelList(doList);
        List<MemberParticipateRecord> result = repository.queryByMemberIdAndExhibitionId(memberIdSet, exhibitionId);
        assertNotNull(result);
        assertEquals(modelList.size(), result.size());
    }

    @Test
    void testAddBatch_Normal() {
        List<MemberParticipateRecord> modelList = new ArrayList<>();
        MemberParticipateRecord model = new MemberParticipateRecord();
        modelList.add(model);
        List<MemberParticipateRecordDO> doList = MemberParticipateRecordDoConverter.INSTANCE.toDoList(modelList);
        doReturn(true).when(repository).saveBatch(doList);
        repository.addBatch(modelList);
        verify(repository).saveBatch(doList);
    }
} 