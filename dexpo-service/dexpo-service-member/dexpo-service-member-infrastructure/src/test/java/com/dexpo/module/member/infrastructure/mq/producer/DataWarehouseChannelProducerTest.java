package com.dexpo.module.member.infrastructure.mq.producer;

import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseSyncMessage;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.stream.function.StreamBridge;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * DataWarehouseChannelProducer 单元测试
 */
@ExtendWith(MockitoExtension.class)
class DataWarehouseChannelProducerTest {

    @Mock
    private StreamBridge streamBridge;

    @InjectMocks
    private DataWarehouseChannelProducer producer;

    @Test
    void testDataWarehouseChannel_Normal() {
        DataWarehouseSyncMessage message = mock(DataWarehouseSyncMessage.class);
        when(streamBridge.send(anyString(), any())).thenReturn(true);

        assertDoesNotThrow(() -> producer.dataWarehouseChannel(message));

        verify(streamBridge, times(1)).send(eq("dataWarehouseChannel-out-0"), eq(message));
    }

    @Test
    void testDataWarehouseChannel_NullMessage() {
        when(streamBridge.send(anyString(), any())).thenReturn(true);

        assertDoesNotThrow(() -> producer.dataWarehouseChannel(null));

        verify(streamBridge, times(1)).send(eq("dataWarehouseChannel-out-0"), isNull());
    }

    @Test
    void testDataWarehouseChannel_SendReturnsFalse() {
        DataWarehouseSyncMessage message = mock(DataWarehouseSyncMessage.class);
        when(streamBridge.send(anyString(), any())).thenReturn(false);

        assertDoesNotThrow(() -> producer.dataWarehouseChannel(message));

        verify(streamBridge, times(1)).send(eq("dataWarehouseChannel-out-0"), eq(message));
    }

    @Test
    void testDataWarehouseChannel_LogOutput() {
        DataWarehouseSyncMessage message = mock(DataWarehouseSyncMessage.class);
        when(streamBridge.send(anyString(), any())).thenReturn(true);

        assertDoesNotThrow(() -> producer.dataWarehouseChannel(message));
        // 捕获日志输出（可选，依赖slf4j-test等库，这里仅做结构展示）
        // 断言日志内容可用slf4j-test等库实现
    }
} 