package com.dexpo.module.member.infrastructure.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.dexpo.module.member.domain.model.enterprise.EnterpriseInfo;
import com.dexpo.module.member.domain.model.enterprise.EnterpriseInfoQuery;
import com.dexpo.module.member.infrastructure.converter.EnterpriseInfoDoConverter;
import com.dexpo.module.member.infrastructure.dal.dataobject.EnterpriseInfoDO;
import com.dexpo.module.member.infrastructure.dal.mysql.EnterpriseInfoMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EnterpriseInfoRepositoryImplTest {

    @Mock
    private EnterpriseInfoMapper baseMapper;

    @InjectMocks
    private EnterpriseInfoRepositoryImpl repository;

    @BeforeEach
    void setUp() throws Exception {
        java.lang.reflect.Field field = CrudRepository.class.getDeclaredField("baseMapper");
        field.setAccessible(true);
        field.set(repository, baseMapper);
    }

    @Test
    void testGetEnterpriseInfoList_Normal() {
        EnterpriseInfoQuery query = new EnterpriseInfoQuery();
        query.setIsUseAble(true);
        query.setEnterpriseName("测试企业");
        List<EnterpriseInfoDO> doList = new ArrayList<>();
        EnterpriseInfoDO do1 = new EnterpriseInfoDO();
        do1.setId(1L);
        do1.setEnterpriseName("测试企业");
        doList.add(do1);
        when(baseMapper.selectList(any())).thenReturn(doList);
        List<EnterpriseInfo> result = repository.getEnterpriseInfoList(query);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("测试企业", result.get(0).getEnterpriseName());
    }

    @Test
    void testGetEnterpriseInfoList_Empty() {
        EnterpriseInfoQuery query = new EnterpriseInfoQuery();
        when(baseMapper.selectList(any())).thenReturn(Collections.emptyList());
        List<EnterpriseInfo> result = repository.getEnterpriseInfoList(query);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetEnterpriseInfoList_Null() {
        EnterpriseInfoQuery query = new EnterpriseInfoQuery();
        when(baseMapper.selectList(any())).thenReturn(null);
        List<EnterpriseInfo> result = repository.getEnterpriseInfoList(query);
        assertNull(result);
    }

    @Test
    void testGetById_Normal() {
        Long id = 1L;
        EnterpriseInfoDO do1 = new EnterpriseInfoDO();
        do1.setId(id);
        do1.setEnterpriseName("企业A");
        when(baseMapper.queryById(id)).thenReturn(do1);
        EnterpriseInfo result = repository.getById(id);
        assertNotNull(result);
        assertEquals(id, result.getId());
        assertEquals("企业A", result.getEnterpriseName());
    }

    @Test
    void testGetById_Null() {
        Long id = 2L;
        when(baseMapper.queryById(id)).thenReturn(null);
        EnterpriseInfo result = repository.getById(id);
        assertNull(result);
    }

    @Test
    void testQueryMediaEnterpriseByNameAndLocationCode_Normal() {
        String name = "企业B";
        String code = "LOC123";
        EnterpriseInfoDO do1 = new EnterpriseInfoDO();
        do1.setEnterpriseName(name);
        when(baseMapper.queryMediaEnterpriseByNameAndLocationCode(name, code)).thenReturn(do1);
        EnterpriseInfo result = repository.queryMediaEnterpriseByNameAndLocationCode(name, code);
        assertNotNull(result);
        assertEquals(name, result.getEnterpriseName());
    }

    @Test
    void testQueryMediaEnterpriseByNameAndLocationCode_Null() {
        String name = "企业C";
        String code = "LOC999";
        when(baseMapper.queryMediaEnterpriseByNameAndLocationCode(name, code)).thenReturn(null);
        EnterpriseInfo result = repository.queryMediaEnterpriseByNameAndLocationCode(name, code);
        assertNull(result);
    }

    @Test
    void testAddOrModify_Normal() {
        EnterpriseInfo model = new EnterpriseInfo();
        model.setEnterpriseName("企业D");
        EnterpriseInfoDO do1 = EnterpriseInfoDoConverter.INSTANCE.toDo(model);
        do1.setId(123L);
        when(baseMapper.insertOrUpdate((EnterpriseInfoDO) any())).thenReturn( true);
//        when(baseMapper.insertOrUpdate((EnterpriseInfoDO) any())).then(invocation -> {
//            // 模拟设置ID
//            model.setId(do1.getId());
//            return null;
//        });
        repository.addOrModify(model);
        assertNull( model.getId());
        verify(baseMapper).insertOrUpdate((EnterpriseInfoDO) any());
    }

    @Test
    void testAddOrModify_Null() {
        EnterpriseInfo model = null;
        when(baseMapper.insertOrUpdate((EnterpriseInfoDO) any())).thenReturn(true);
        assertThrows(NullPointerException.class, () -> repository.addOrModify(model));
    }

    @Test
    void testQueryAll_Normal() {
        List<EnterpriseInfoDO> doList = new ArrayList<>();
        EnterpriseInfoDO do1 = new EnterpriseInfoDO();
        do1.setId(1L);
        do1.setEnterpriseName("企业E");
        doList.add(do1);
        when(repository.list()).thenReturn(doList);
        List<EnterpriseInfo> result = repository.queryAll();
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("企业E", result.get(0).getEnterpriseName());
    }

    @Test
    void testQueryAll_Empty() {
        when(repository.list()).thenReturn(Collections.emptyList());
        List<EnterpriseInfo> result = repository.queryAll();
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testAddBatch_Normal() {
        List<EnterpriseInfo> modelList = new ArrayList<>();
        EnterpriseInfo model = new EnterpriseInfo();
        model.setEnterpriseName("企业F");
        modelList.add(model);
        List<EnterpriseInfoDO> doList = EnterpriseInfoDoConverter.INSTANCE.toDoList(modelList);
        doNothing().when(baseMapper).insertBatch(doList);
        repository.addBatch(modelList);
        verify(baseMapper).insertBatch(doList);
    }

    @Test
    void testAddBatch_Empty() {
        List<EnterpriseInfo> modelList = new ArrayList<>();
        List<EnterpriseInfoDO> doList = EnterpriseInfoDoConverter.INSTANCE.toDoList(modelList);
        doNothing().when(baseMapper).insertBatch(doList);
        repository.addBatch(modelList);
        verify(baseMapper).insertBatch(doList);
    }
} 