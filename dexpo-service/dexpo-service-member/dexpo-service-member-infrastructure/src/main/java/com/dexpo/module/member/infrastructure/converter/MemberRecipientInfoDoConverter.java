package com.dexpo.module.member.infrastructure.converter;

import com.dexpo.module.member.domain.model.member.MemberRecipientInfo;
import com.dexpo.module.member.infrastructure.dal.dataobject.MemberRecipientInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MemberRecipientInfoDoConverter {


    MemberRecipientInfoDoConverter INSTANCE = Mappers.getMapper(MemberRecipientInfoDoConverter.class);

    @Mappings({})
    MemberRecipientInfo toModel(MemberRecipientInfoDO data);

    MemberRecipientInfoDO toDo(MemberRecipientInfo data);

    List<MemberRecipientInfoDO> toDoList(List<MemberRecipientInfo> dataList);
}
