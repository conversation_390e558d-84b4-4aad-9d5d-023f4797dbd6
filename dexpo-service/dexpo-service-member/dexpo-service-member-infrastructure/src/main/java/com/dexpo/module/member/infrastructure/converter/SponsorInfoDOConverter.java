package com.dexpo.module.member.infrastructure.converter;

import com.dexpo.module.member.domain.model.SponsorInfo;
import com.dexpo.module.member.infrastructure.dal.dataobject.SponsorInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> Xiaohua 20/06/2025 11:30
 **/
@Mapper
public interface SponsorInfoDOConverter {

    SponsorInfoDOConverter INSTANCE = Mappers.getMapper(SponsorInfoDOConverter.class);

    SponsorInfo doToEntity(SponsorInfoDO sponsorInfoDO);

}
