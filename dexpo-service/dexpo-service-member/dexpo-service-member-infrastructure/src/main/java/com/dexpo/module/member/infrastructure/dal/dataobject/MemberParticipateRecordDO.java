package com.dexpo.module.member.infrastructure.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户参展记录数据对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("member_participate_record")
public class MemberParticipateRecordDO extends BaseDO {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    @TableField("member_id")
    private Long memberId;
    
    /**
     * 展会ID
     */
    @TableField("exhibition_id")
    private Long exhibitionId;

    /**
     * 用户类型：值集VS_ACTION_USER_TYPE
     */
    @TableField(value = "member_type",updateStrategy = FieldStrategy.NOT_NULL)
    private String memberType;

    /**
     * 是否激活
     */
    @TableField(value="is_active",updateStrategy = FieldStrategy.NOT_NULL)
    private Boolean isActive;

    /**
     * 注册时语言环境：值集VS_LANGUAGE
     */
    @TableField(value="register_language",updateStrategy = FieldStrategy.NOT_NULL)
    private String registerLanguage;

    /**
     * 注册时间
     */
    @TableField(value="register_time",updateStrategy = FieldStrategy.NOT_NULL)
    private LocalDateTime registerTime;
    
    /**
     * 注册状态：值集VS_REGISTER_STATUS
     */
    @TableField(value="register_status",updateStrategy = FieldStrategy.NOT_NULL)
    private String registerStatus;
    
    /**
     * 注册系统：值集VS_REGISTER_SYSTEM
     */
    @TableField(value="register_system",updateStrategy = FieldStrategy.NOT_NULL)
    private String registerSystem;
    
    /**
     * 注册方式：值集VS_REGISTER_METHOD
     */
    @TableField(value="register_method",updateStrategy = FieldStrategy.NOT_NULL)
    private String registerMethod;
    
    /**
     * 注册来源：值集VS_REGISTER_SOURCE
     */
    @TableField(value="register_source",updateStrategy = FieldStrategy.NOT_NULL)
    private String registerSource;
} 