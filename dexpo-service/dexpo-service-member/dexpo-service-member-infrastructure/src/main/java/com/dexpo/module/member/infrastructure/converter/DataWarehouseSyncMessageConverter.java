package com.dexpo.module.member.infrastructure.converter;

import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseSyncMessage;
import com.dexpo.module.member.domain.model.member.message.MemberDataWarehouseSyncMessage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface DataWarehouseSyncMessageConverter {


    DataWarehouseSyncMessageConverter INSTANCE = Mappers.getMapper(DataWarehouseSyncMessageConverter.class);


    List<MemberDataWarehouseSyncMessage> toMemberDataWarehouseSyncMessages(List<DataWarehouseSyncMessage> dataList);
    List<DataWarehouseSyncMessage> toDataWarehouseSyncMessages(List<MemberDataWarehouseSyncMessage> dataList);
}
