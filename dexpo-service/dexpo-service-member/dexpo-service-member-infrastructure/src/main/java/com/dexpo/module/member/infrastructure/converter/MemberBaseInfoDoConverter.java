package com.dexpo.module.member.infrastructure.converter;

import com.dexpo.framework.cache.redis.entity.MemberBaseInfoCache;
import com.dexpo.module.member.domain.model.member.MemberBaseInfo;
import com.dexpo.module.member.infrastructure.dal.dataobject.MemberBaseInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MemberBaseInfoDoConverter {


    MemberBaseInfoDoConverter INSTANCE = Mappers.getMapper(MemberBaseInfoDoConverter.class);

    @Mappings({})
    MemberBaseInfoDO toDo(MemberBaseInfo data);

    @Mappings({})
    MemberBaseInfo toModel(MemberBaseInfoDO memberBaseInfoDO);

    List<MemberBaseInfo> toModelList(List<MemberBaseInfoDO> dataList);

    /**
     * 缓存对象转聚合根
     *
     * @param memberBaseInfoCache 缓存对象
     * @return 聚合根
     */
    MemberBaseInfo cacheToEntity(MemberBaseInfoCache memberBaseInfoCache);

    List<MemberBaseInfoDO> toDoList(List<MemberBaseInfo> dataList);
}
