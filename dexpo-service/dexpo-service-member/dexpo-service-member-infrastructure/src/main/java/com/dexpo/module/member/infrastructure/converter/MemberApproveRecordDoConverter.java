package com.dexpo.module.member.infrastructure.converter;

import com.dexpo.module.member.domain.model.member.MemberApproveRecord;
import com.dexpo.module.member.infrastructure.dal.dataobject.MemberApproveRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MemberApproveRecordDoConverter {


    MemberApproveRecordDoConverter INSTANCE = Mappers.getMapper(MemberApproveRecordDoConverter.class);

    List<MemberApproveRecordDO> toDoList(List<MemberApproveRecord> dataList);
}
