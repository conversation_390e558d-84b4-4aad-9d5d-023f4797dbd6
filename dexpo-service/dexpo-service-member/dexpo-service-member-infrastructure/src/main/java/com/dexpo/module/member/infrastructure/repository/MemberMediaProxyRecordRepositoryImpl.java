package com.dexpo.module.member.infrastructure.repository;

import com.dexpo.module.log.api.SysUploadDownloadRecordApi;
import com.dexpo.module.log.api.dto.SysUploadDownloadRecordDTO;
import com.dexpo.module.log.enums.MediaProxyRecordEnums;
import com.dexpo.module.member.domain.repository.MemberMediaProxyRecordRepository;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class MemberMediaProxyRecordRepositoryImpl implements MemberMediaProxyRecordRepository {

    @Resource
    private SysUploadDownloadRecordApi sysUploadDownloadRecordApi;

    @Override
    public void saveCheckErrorInfo(Long memberMediaProxyRecordId, String errorInfo) {
        SysUploadDownloadRecordDTO dto = new SysUploadDownloadRecordDTO();
        dto.setId(memberMediaProxyRecordId);
        dto.setStatus(MediaProxyRecordEnums.VO_ASYN_STATUS_3.getCode());
        dto.setRemarks(errorInfo);
        dto.setDoEndTime(LocalDateTime.now());
        sysUploadDownloadRecordApi.updateDto(dto);
    }

    @Override
    public void saveSuccess(Long memberMediaProxyRecordId) {
        SysUploadDownloadRecordDTO dto = new SysUploadDownloadRecordDTO();
        dto.setId(memberMediaProxyRecordId);
        dto.setStatus(MediaProxyRecordEnums.VO_ASYN_STATUS_2.getCode());
        dto.setRemarks(null);
        dto.setDoEndTime(LocalDateTime.now());
        sysUploadDownloadRecordApi.updateDto(dto);
    }
}
