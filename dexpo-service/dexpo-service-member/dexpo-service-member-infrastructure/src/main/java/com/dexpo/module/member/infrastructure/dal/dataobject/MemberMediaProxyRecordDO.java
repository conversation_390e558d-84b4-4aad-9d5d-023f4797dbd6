package com.dexpo.module.member.infrastructure.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 批量媒体代注册记录表
 */
@Data
@TableName("member_media_proxy_record")
public class MemberMediaProxyRecordDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 文件名
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 上传人姓名
     */
    @TableField("uploader_name")
    private String uploaderName;

    /**
     * 上传人ID
     */
    @TableField("uploader_id")
    private Long uploaderId;

    /**
     * 上传时间
     */
    @TableField("upload_time")
    private LocalDateTime uploadTime;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 文件url
     */
    @TableField("file_url")
    private String fileUrl;

    /**
     * 错误记录
     */
    @TableField("error_info")
    private String errorInfo;

}
