package com.dexpo.module.member.infrastructure.dal.dataobject;

import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 会员基本信息数据对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("member_base_info")
public class MemberBaseInfoDO extends BaseDO {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 会员编码（系统自动生成）
     */
    @TableField("member_code")
    private String memberCode;
    
    /**
     * 姓名
     */
    @TableField("member_name")
    private String memberName;
    
    /**
     * 英文名
     */
    @TableField("member_first_name")
    private String memberFirstName;
    
    /**
     * 英文姓
     */
    @TableField("member_last_name")
    private String memberLastName;
    
    /**
     * 性别：值集VS_GENDER
     */
    @TableField("member_gender")
    private String memberGender;
    
    /**
     * 生日
     */
    @TableField("member_birth_day")
    private LocalDate memberBirthDay;
    
    /**
     * 手机号
     */
    @TableField("member_mobile")
    private String memberMobile;
    
    /**
     * 邮箱
     */
    @TableField("member_email")
    private String memberEmail;
    
    /**
     * 证件类型：值集VS_ID_CATEGORY
     */
    @TableField("id_category")
    private String idCategory;
    
    /**
     * 证件号码
     */
    @TableField("id_number")
    private String idNumber;
    
    /**
     * 国家CODE
     */
    @TableField("country_code")
    private String countryCode;

    /**
     * 国家名称中文
     */
    @TableField("country_name_cn")
    private String countryNameCn;

    /**
     * 国家名称英文
     */
    @TableField("country_name_en")
    private String countryNameEn;

    /**
     * 居住地所在省CODE
     */
    @TableField("current_home_province_code")
    private String currentHomeProvinceCode;
    
    /**
     * 居住地所在省名称
     */
    @TableField("current_home_province_name")
    private String currentHomeProvinceName;
    
    /**
     * 居住地所在市CODE
     */
    @TableField("current_home_city_code")
    private String currentHomeCityCode;
    
    /**
     * 居住地所在市名称
     */
    @TableField("current_home_city_name")
    private String currentHomeCityName;
    
    /**
     * 居住地所在区CODE
     */
    @TableField("current_home_district_code")
    private String currentHomeDistrictCode;
    
    /**
     * 居住地所在区名称
     */
    @TableField("current_home_district_name")
    private String currentHomeDistrictName;
    
    /**
     * 居住地详细地址
     */
    @TableField("current_home_detail_address")
    private String currentHomeDetailAddress;

    public static MemberBaseInfoDO initMemberBaseInfo(){
        MemberBaseInfoDO infoDO = new MemberBaseInfoDO();
        infoDO.setMemberCode(UUID.fastUUID().toString());
        return infoDO;
    }
} 