package com.dexpo.module.member.infrastructure.dal.mysql;

import com.dexpo.framework.mybatis.core.mapper.BaseMapperX;
import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseSyncMessage;
import com.dexpo.module.member.infrastructure.dal.dataobject.MemberApproveRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户信息审核记录 Mapper
 */
@Mapper
public interface MemberApproveRecordMapper extends BaseMapperX<MemberApproveRecordDO> {

    List<DataWarehouseSyncMessage> selectDataWarehouseSyncMessageList(@Param("memberIds") List<Long> memberIds,@Param("registerStatus") List<String> statusList,@Param("exhibitionId") Long exhibitionId);
} 