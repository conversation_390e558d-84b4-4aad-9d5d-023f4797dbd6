package com.dexpo.module.member.infrastructure.converter;

import com.dexpo.module.member.domain.model.enterprise.EnterpriseInfo;
import com.dexpo.module.member.infrastructure.dal.dataobject.EnterpriseInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface EnterpriseInfoDoConverter {


    EnterpriseInfoDoConverter INSTANCE = Mappers.getMapper(EnterpriseInfoDoConverter.class);

    @Mappings({})
    List<EnterpriseInfo> toModelList(List<EnterpriseInfoDO> dataList);

    @Mappings({})
    EnterpriseInfo toModel(EnterpriseInfoDO data);

    EnterpriseInfoDO toDo(EnterpriseInfo enterpriseInfo);

    List<EnterpriseInfoDO> toDoList(List<EnterpriseInfo> enterpriseInfos);
}
