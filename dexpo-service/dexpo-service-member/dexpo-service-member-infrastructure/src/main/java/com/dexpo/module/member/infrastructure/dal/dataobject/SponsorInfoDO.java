package com.dexpo.module.member.infrastructure.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sponsor_info")
public class SponsorInfoDO extends BaseDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("sponsor_name")
    private String sponsorName;

    @TableField("sponsor_code")
    private String sponsorCode;

    @TableField("sponsor_mobile")
    private String sponsorMobile;

    @TableField("sponsor_email")
    private String sponsorEmail;

    @TableField("organization_code")
    private String organizationCode;

    @TableField("is_use_able")
    private Boolean isUseAble;
} 