package com.dexpo.module.member.infrastructure.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseSyncMessage;
import com.dexpo.module.member.infrastructure.converter.DataWarehouseSyncMessageConverter;
import com.dexpo.module.member.domain.model.member.MemberApproveRecord;
import com.dexpo.module.member.domain.model.member.message.MemberDataWarehouseSyncMessage;
import com.dexpo.module.member.domain.repository.MemberApproveRecordRepository;
import com.dexpo.module.member.infrastructure.converter.MemberApproveRecordDoConverter;
import com.dexpo.module.member.infrastructure.dal.dataobject.MemberApproveRecordDO;
import com.dexpo.module.member.infrastructure.dal.mysql.MemberApproveRecordMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MemberApproveRecordRepositoryImpl extends ServiceImpl<MemberApproveRecordMapper, MemberApproveRecordDO> implements MemberApproveRecordRepository {

    @Override
    public void addBatch(List<MemberApproveRecord> memberApproveRecords) {
        List<MemberApproveRecordDO> memberApproveRecordDOS = MemberApproveRecordDoConverter.INSTANCE.toDoList(memberApproveRecords);
        saveBatch(memberApproveRecordDOS);
    }

    @Override
    public List<MemberDataWarehouseSyncMessage> selectDataWarehouseSyncMessageList(List<Long> ids, String code, Long exhibitionId) {
        List<DataWarehouseSyncMessage> dataWarehouseSyncMessages = baseMapper.selectDataWarehouseSyncMessageList(ids, code, exhibitionId);
        return DataWarehouseSyncMessageConverter.INSTANCE.toMemberDataWarehouseSyncMessages(dataWarehouseSyncMessages);
    }
}
