package com.dexpo.module.member.infrastructure.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 媒体用户拓展信息数据对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("member_reporter_info")
public class MemberReporterInfoDO extends BaseDO {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 会员ID
     */
    @TableField("member_id")
    private Long memberId;
    
    /**
     * 企业ID
     */
    @TableField("enterprise_id")
    private Long enterpriseId;
    
    /**
     * 展会ID
     */
    @TableField("exhibition_id")
    private Long exhibitionId;
    
    /**
     * 记者证号
     */
    @TableField("media_newsman_no")
    private String mediaNewsmanNo;
    
    /**
     * 媒体类型：值集VS_MEDIA_TYPE
     */
    @TableField("media_type_code")
    private String mediaTypeCode;

    /**
     * 媒体类型名称
     */
    @TableField("media_type_name_cn")
    private String mediaTypeNameCn;

    /**
     * 媒体类型英文名称
     */
    @TableField("media_type_name_en")
    private String mediaTypeNameEn;

    /**
     * 媒体职位分类code：值集VS_MEDIA_POSITION
     */
    @TableField("media_position_category_code")
    private String mediaPositionCategoryCode;

    /**
     * 媒体职位分类名称
     */
    @TableField("media_position_category_name_cn")
    private String mediaPositionCategoryNameCn;

    /**
     * 媒体职位分类英文名称
     */
    @TableField("media_position_category_name_en")
    private String mediaPositionCategoryNameEn;

    /**
     * 其他媒体职位
     */
    @TableField("other_media_position")
    private String otherMediaPosition;

    /**
     * 媒体职位：值集VS_MEDIA_POSITION
     */
    @TableField("media_position_code")
    private String mediaPositionCode;
    
    /**
     * 媒体职位名称
     */
    @TableField("media_position_name_cn")
    private String mediaPositionNameCn;

    /**
     * 媒体职位英文名称
     */
    @TableField("media_position_name_en")
    private String mediaPositionNameEn;

    /**
     * 媒体权限类型编码: 值集VS_MEDIA_PERMISSION_TYPE
     */
    @TableField("media_permission_type")
    private String mediaPermissionType;

    /**
     * 是否申请直播：0否 1是
     */
    @TableField("is_apply_live_stream")
    private Boolean isApplyLiveStream;
    
    /**
     * 头像照
     */
    @TableField("attachment_head_photo")
    private String attachmentHeadPhoto;
    
    /**
     * 其他说明文件，多个文件用逗号隔开
     */
    @TableField("attachment_other_describe")
    private String attachmentOtherDescribe;
} 