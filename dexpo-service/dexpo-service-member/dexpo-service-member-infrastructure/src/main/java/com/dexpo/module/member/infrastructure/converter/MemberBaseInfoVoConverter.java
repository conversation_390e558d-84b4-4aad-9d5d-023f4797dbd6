package com.dexpo.module.member.infrastructure.converter;

import com.dexpo.module.member.api.vo.member.MemberBaseInfoVO;
import com.dexpo.module.member.domain.model.member.MemberBaseInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MemberBaseInfoVoConverter {


    MemberBaseInfoVoConverter INSTANCE = Mappers.getMapper(MemberBaseInfoVoConverter.class);


    MemberBaseInfoVO toVo(MemberBaseInfo data);
}
