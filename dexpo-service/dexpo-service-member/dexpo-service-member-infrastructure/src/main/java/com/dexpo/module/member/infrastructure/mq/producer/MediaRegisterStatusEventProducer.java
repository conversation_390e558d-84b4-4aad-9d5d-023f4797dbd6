package com.dexpo.module.member.infrastructure.mq.producer;

import cn.hutool.json.JSONUtil;
import com.dexpo.module.member.api.dto.message.MediaRegisterEventDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class MediaRegisterStatusEventProducer {

    @Resource
    private StreamBridge streamBridge;

    public void mediaRegisterStatusEventChannel(MediaRegisterEventDTO content) {
        log.info("mediaRegisterStatusEventChannel: {}", JSONUtil.toJsonStr( content));
        streamBridge.send("mediaRegisterStatusEventChannel-out-0", content);
    }
} 