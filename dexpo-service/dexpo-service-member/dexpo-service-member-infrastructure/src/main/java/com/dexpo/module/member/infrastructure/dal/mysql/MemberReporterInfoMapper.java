package com.dexpo.module.member.infrastructure.dal.mysql;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dexpo.framework.common.Constants;
import com.dexpo.framework.mybatis.core.mapper.BaseMapperX;
import com.dexpo.framework.security.core.util.SecurityFrameworkUtils;
import com.dexpo.module.member.infrastructure.dal.dataobject.MemberReporterInfoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 媒体用户拓展信息 Mapper
 */
@Mapper
public interface MemberReporterInfoMapper extends BaseMapperX<MemberReporterInfoDO> {

    default MemberReporterInfoDO queryByMemberId(Long memberId ){
        LambdaQueryWrapper<MemberReporterInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberReporterInfoDO::getMemberId, memberId)
                .eq(MemberReporterInfoDO::getDelFlg, Boolean.FALSE)
                .orderByDesc(MemberReporterInfoDO::getId)
                .last(Constants.LIMIT_ONE);
        return selectOne(queryWrapper);

    }

    default MemberReporterInfoDO queryByMemberIdAndExhibitionId(Long memberId,Long exhibitionId){
        LambdaQueryWrapper<MemberReporterInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberReporterInfoDO::getMemberId, memberId)
                .eq(MemberReporterInfoDO::getExhibitionId, exhibitionId)
                .eq(MemberReporterInfoDO::getDelFlg, Boolean.FALSE);
        return selectOne(queryWrapper);
    }

    /**
     * 根据展会id查询已提交的媒体信息记录
     */
    List<MemberReporterInfoDO> listByCommited(@Param("exhibitionIds") List<Long> exhibitionIds, @Param("memberType") String memberType);


    default void updateMediaPermissionType(List<Long> memberIds,String mediaPermissionType,Long exhibitionId){
        LambdaQueryWrapper<MemberReporterInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MemberReporterInfoDO::getMemberId, memberIds)
                .eq(MemberReporterInfoDO::getExhibitionId, exhibitionId)
                .eq(MemberReporterInfoDO::getDelFlg, Boolean.FALSE);
        MemberReporterInfoDO memberReporterInfoDO = new MemberReporterInfoDO();
        memberReporterInfoDO.setMediaPermissionType(mediaPermissionType);
        memberReporterInfoDO.setUpdateTime(LocalDateTime.now());
        memberReporterInfoDO.setUpdateUser(SecurityFrameworkUtils.getLoginUserId());
        memberReporterInfoDO.setUpdateUserName(SecurityFrameworkUtils.getLoginUserName());
        update(memberReporterInfoDO, queryWrapper);
    }
    
} 