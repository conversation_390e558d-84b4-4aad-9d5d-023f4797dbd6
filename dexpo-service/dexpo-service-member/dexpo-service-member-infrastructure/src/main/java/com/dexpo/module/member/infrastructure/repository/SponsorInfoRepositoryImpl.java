package com.dexpo.module.member.infrastructure.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.exception.enums.MemberServiceErrorCodeEnum;
import com.dexpo.module.member.domain.model.SponsorInfo;
import com.dexpo.module.member.domain.repository.SponsorInfoRepository;
import com.dexpo.module.member.infrastructure.converter.SponsorInfoDOConverter;
import com.dexpo.module.member.infrastructure.dal.dataobject.SponsorInfoDO;
import com.dexpo.module.member.infrastructure.dal.mysql.SponsorInfoMapper;
import org.springframework.stereotype.Component;

@Component
public class SponsorInfoRepositoryImpl extends ServiceImpl<SponsorInfoMapper, SponsorInfoDO> implements SponsorInfoRepository {
    @Override
    public SponsorInfo getSponsorUser(String loginTool) {
        // 用户不存在，请检查输入信息是否有误
        SponsorInfoDO sponsorInfoDO = lambdaQuery().eq(SponsorInfoDO::getSponsorMobile, loginTool)
                .or().eq(SponsorInfoDO::getSponsorEmail, loginTool)
                .oneOpt()
                .orElseThrow(() -> new ServiceException(MemberServiceErrorCodeEnum.MEMBER_SPONSOR_USER_NOT_EXIST));

        return SponsorInfoDOConverter.INSTANCE.doToEntity(sponsorInfoDO);

    }
}
