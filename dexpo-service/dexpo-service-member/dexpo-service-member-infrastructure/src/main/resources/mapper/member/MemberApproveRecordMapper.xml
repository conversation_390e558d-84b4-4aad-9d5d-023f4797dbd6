<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dexpo.module.member.infrastructure.dal.mysql.MemberApproveRecordMapper">

    <sql id="selectFields">
        id, member_id, exhibition_id, approver_id, approver_name,
        approve_time, approve_result, approve_reject_reason, approve_reject_remark,
        del_flg, create_user, create_user_name, create_time,
        update_user, update_user_name, update_time
    </sql>

    <select id="selectDataWarehouseSyncMessageList" resultType="com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseSyncMessage">
        select
            member_code,
            member_name,
            member_first_name,
            member_last_name,
            member_gender,
            member_birth_day,
            member_mobile,
            member_email,
            id_category,
            id_number,
            country_code,
            country_name_cn as country_name,
            certificate_collection_method,
            recipient_name,
            recipient_first_name,
            recipient_last_name,
            recipient_mobile,
            recipient_province_code,
            recipient_province_name,
            recipient_city_code,
            recipient_city_name,
            recipient_district_code,
            recipient_district_name,
            recipient_address,
            enterprise_name,
            media_newsman_no,
            media_type_code,
            media_position_category_code,
            media_position_code,
            is_apply_live_stream,
            attachment_head_photo,
            member_type,
            register_time,
            register_status,
            register_system,
            register_method,
            register_source
            from member_base_info mbi
             inner join member_participate_record mpr on mbi.id  = mpr.member_id and mpr.exhibition_id  = #{exhibitionId}
             inner join member_recipient_info mri on mbi.id  = mri.member_id
             inner join member_reporter_info reporter on mbi.id =reporter.member_id and reporter.exhibition_id  = #{exhibitionId}
             inner join enterprise_info ei on ei.id  = reporter.enterprise_id
           where mbi.id in
            <foreach item="item" collection="memberIds" open="(" separator="," close=")">
                #{item}
            </foreach>
            and mpr.register_status  in
            <foreach item="status" collection="registerStatus" open="(" separator="," close=")">
                #{status}
            </foreach>

             and mpr.exhibition_id = #{exhibitionId}
    </select>

</mapper> 