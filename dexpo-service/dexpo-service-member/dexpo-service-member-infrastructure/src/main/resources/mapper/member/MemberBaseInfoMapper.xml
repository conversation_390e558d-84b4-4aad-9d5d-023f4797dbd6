<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dexpo.module.member.infrastructure.dal.mysql.MemberBaseInfoMapper">

    <sql id="selectFields">
        id, member_code, member_name, member_first_name, member_last_name,
        member_type, member_gender, member_birth_day, member_mobile, member_email,
        id_category, id_number, country_code,
        current_home_province_code, current_home_province_name,
        current_home_city_code, current_home_city_name,
        current_home_district_code, current_home_district_name,
        current_home_detail_address,
        del_flg, create_user, create_user_name, create_time,
        update_user, update_user_name, update_time
    </sql>


    <select id="getMediaList" resultType="com.dexpo.module.member.domain.model.media.MediaPageList">

        select
        mpr.id memberParticipateRecordId,
        mbi.id memberId,
        mbi.member_name,
        mbi.member_mobile,
        mbi.member_email,
        ei.enterprise_location_code,
        ei.enterprise_location_name_cn,
        ei.enterprise_location_name_en,
        mri.media_type_code,
        mri.media_type_name_cn,
        mri.media_type_name_en,
        mri.media_permission_type,
        mpr.exhibition_id,
        mpr.register_status,
        mpr.register_source,
        mpr.register_time
        from member_participate_record mpr
        inner join member_base_info mbi on mpr.member_id = mbi.id and mpr.member_type = #{userType}
        left join member_reporter_info mri on mri.member_id = mpr.member_id and mri.exhibition_id = mpr.exhibition_id
        left join enterprise_info ei on ei.id = mri.enterprise_id
        <where>
            mpr.del_flg = 0
            and mbi.del_flg = 0
            <if test="queryDO.exhibitionIds != null and queryDO.exhibitionIds.size() != 0">
                and mpr.exhibition_id in
                <foreach collection="queryDO.exhibitionIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryDO.enterpriseLocationCodes != null and queryDO.enterpriseLocationCodes.size() != 0">
                and ei.enterprise_location_code in
                <foreach collection="queryDO.enterpriseLocationCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryDO.mediaTypeCodes != null and queryDO.mediaTypeCodes.size() != 0">
                and mri.media_type_code in
                <foreach collection="queryDO.mediaTypeCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryDO.registerStatus != null and queryDO.registerStatus!=''">
                and mpr.register_status = #{queryDO.registerStatus}
            </if>
            <if test="queryDO.registerSource != null and queryDO.registerSource != ''">
                and mpr.register_source = #{queryDO.registerSource}
            </if>

            <if test="queryDO.mediaPermissionType != null and queryDO.mediaPermissionType.size() !=0 ">
                and mri.media_permission_type in
                <foreach collection="queryDO.mediaPermissionType" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="queryDO.registerTimeStart != null">
                and mpr.register_time &gt;= #{queryDO.registerTimeStart}
            </if>
            <if test="queryDO.registerTimeEnd != null">
                and mpr.register_time &lt;= #{queryDO.registerTimeEnd}
            </if>

            <if test="queryDO.memberName != null and queryDO.memberName != ''">
                and mbi.member_name like concat('%', #{queryDO.memberName}, '%')
            </if>
            <if test="queryDO.memberMobile != null and queryDO.memberMobile != ''">
                and mbi.member_mobile like concat('%', #{queryDO.memberMobile}, '%')
            </if>
            <if test="queryDO.memberEmail != null and queryDO.memberEmail != ''">
                and mbi.member_email like concat('%', #{queryDO.memberEmail}, '%')
            </if>
            order by
            case when register_status = #{registerStatus} then 0 else 1 end asc,
            case when register_status = #{registerStatus} then mpr.register_time end ,
            case when register_status != #{registerStatus} then mpr.register_time end desc
        </where>


    </select>

    <select id="getMemberBaseInfoByMobileOrEmail" resultType="com.dexpo.module.member.infrastructure.dal.dataobject.MemberBaseInfoDO">
        select <include refid="selectFields"/>
           from member_base_info
           where (member_mobile = #{mobileOrEmail}
        or member_email = #{mobileOrEmail}
        )
           and del_flg = 0
    </select>

</mapper>