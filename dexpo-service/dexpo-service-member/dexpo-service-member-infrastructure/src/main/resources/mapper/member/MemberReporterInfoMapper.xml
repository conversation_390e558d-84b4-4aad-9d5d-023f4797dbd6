<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dexpo.module.member.infrastructure.dal.mysql.MemberReporterInfoMapper">

    <sql id="selectFields">
        id, member_id, enterprise_id, exhibition_id, media_newsman_no,
        media_type, media_position_code, media_position_name_cn, is_apply_live_stream,
        attachment_head_photo, attachment_other_describe,
        del_flg, create_user, create_user_name, create_time,
        update_user, update_user_name, update_time
    </sql>

    <select id="listByCommited" resultType="com.dexpo.module.member.infrastructure.dal.dataobject.MemberReporterInfoDO">
        select mri.id,
               mri.member_id,
               mri.enterprise_id,
               mri.exhibition_id,
               mri.media_newsman_no,
               mri.media_position_category_code,
               mri.media_position_category_name_cn,
               mri.media_position_category_name_en,
               mri.media_type_code,
               mri.media_type_name_cn,
               mri.media_type_name_en,
               mri.media_position_code,
               mri.media_position_name_cn,
               mri.media_position_name_en,
               mri.other_media_position,
               mri.is_apply_live_stream,
               mri.attachment_head_photo,
               mri.attachment_other_describe,
               mri.media_permission_type
        from member_reporter_info mri
                 inner join member_participate_record mpr
                            on mri.member_id = mpr.member_id and mpr.member_type = 'VO_ACTION_USER_TYPE_2'
        where mri.exhibition_id in
        <foreach collection="exhibitionIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and mpr.register_status in('VO_REGISTER_STATUS_2','VO_REGISTER_STATUS_3','VO_REGISTER_STATUS_4')
        and mri.del_flg = 0
        and mpr.del_flg = 0;
    </select>
</mapper>