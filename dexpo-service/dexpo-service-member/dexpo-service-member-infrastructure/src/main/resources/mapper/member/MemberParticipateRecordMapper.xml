<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dexpo.module.member.infrastructure.dal.mysql.MemberParticipateRecordMapper">

    <sql id="selectFields">
        id, member_id, exhibition_id, register_time, register_status,
        register_system, register_method, register_source,is_active,
        del_flg, create_user, create_user_name, create_time,
        update_user, update_user_name, update_time
    </sql>

    <select id="selectRegisterRecordByMemberIdAndExhibitionId" resultType="com.dexpo.module.member.api.vo.media.MediaProxyRecordCheckVO">
        select
            mbi.member_mobile,
            mbi.member_email,
            mbi.id as member_id,
            mpr.exhibition_id,
            mpr.register_status
        from member_base_info mbi
                 inner join member_participate_record mpr on mbi.id = mpr.member_id and mpr.del_flg =0 and mbi.del_flg =0 and mpr.exhibition_id = #{exhibitionId}
        where ( mbi.member_mobile =#{memberMobile}  or mbi.member_email =#{memberEmail} ) and mpr.exhibition_id  =#{exhibitionId}
        order by mbi.id desc limit 1
    </select>

    <select id="selectMediaAuditQueryVO" resultType="com.dexpo.module.member.api.vo.media.MediaAuditQueryVO">
        select
            mbi.member_mobile,
            mbi.member_email,
            mpr.id as member_participate_id,
            mpr.member_id,
            mpr.exhibition_id,
            mpr.register_status
        from
            member_base_info mbi
                inner join
            member_participate_record mpr
            on mbi.id = mpr.member_id
                and mpr.del_flg =0
                and mbi.del_flg =0
        where mpr.id = #{recordId}
    </select>

    <update id="active">
        update member_participate_record
        set is_active=1 where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
</mapper>