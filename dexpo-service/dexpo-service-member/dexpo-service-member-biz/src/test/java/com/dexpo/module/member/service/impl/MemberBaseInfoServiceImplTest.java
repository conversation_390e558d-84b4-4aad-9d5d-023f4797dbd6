package com.dexpo.module.member.service.impl;

import com.dexpo.module.member.dal.dataobject.MemberBaseInfoDO;
import com.dexpo.module.member.dal.mysql.MemberBaseInfoMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MemberBaseInfoServiceImplTest {

    @Mock
    private MemberBaseInfoMapper baseMapper;

    @InjectMocks
    private MemberBaseInfoServiceImpl memberBaseInfoService;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(memberBaseInfoService, "baseMapper", baseMapper);
    }


    @Test
    void updateMemberBaseInfo() {
        // Arrange
        MemberBaseInfoDO memberBaseInfo = new MemberBaseInfoDO();
        memberBaseInfo.setId(1L);
        memberBaseInfo.setMemberName("测试用户");

        // Act
        memberBaseInfoService.updateMemberBaseInfo(memberBaseInfo);

        // Assert
        verify(baseMapper).updateById(memberBaseInfo);
    }

    @Test
    void deleteMemberBaseInfo() {
        // Arrange
        Long id = 1L;

        // Act
        memberBaseInfoService.deleteMemberBaseInfo(id);

        // Assert
        verify(baseMapper).deleteById(id);
    }

    @Test
    void getMemberBaseInfo() {
        // Arrange
        Long id = 1L;
        MemberBaseInfoDO expectedMember = new MemberBaseInfoDO();
        expectedMember.setId(id);
        expectedMember.setMemberName("测试用户");

        when(baseMapper.selectById(id)).thenReturn(expectedMember);

        // Act
        MemberBaseInfoDO result = memberBaseInfoService.getMemberBaseInfo(id);

        // Assert
        assertNotNull(result);
        assertEquals(id, result.getId());
        assertEquals("测试用户", result.getMemberName());
        verify(baseMapper).selectById(id);
    }

} 