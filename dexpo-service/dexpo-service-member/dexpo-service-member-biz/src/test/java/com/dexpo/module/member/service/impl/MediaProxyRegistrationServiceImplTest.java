package com.dexpo.module.member.service.impl;

import com.dexpo.module.member.service.MediaProxyCheckDateService;
import com.dexpo.module.member.service.MediaProxySaveDateService;
import com.dexpo.module.member.utils.mediaproxy.MediaProxyActionDTO;
import com.dexpo.module.member.utils.mediaproxy.MediaProxyConstant;
import com.dexpo.module.member.utils.mediaproxy.MediaProxyRegistrationDTO;
import com.dexpo.module.member.utils.mediaproxy.ZIPUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MediaProxyRegistrationServiceImplTest {

    @InjectMocks
    private MediaProxyRegistrationServiceImpl mediaProxyRegistrationService;

    @Mock
    private ZIPUtils zipUtils;

    @Mock
    private MediaProxyCheckDateService mediaProxyCheckDateService;

    @Mock
    private MediaProxySaveDateService mediaProxySaveDateService;

    private Map<String, String> mockFileNameAndFileMap;

    @BeforeEach
    void setUp() {
        mockFileNameAndFileMap = new HashMap<>();
        mockFileNameAndFileMap.put(MediaProxyConstant.EXCEL_KEY, "mockFilePath");
    }

    @Test
    void testImportMediaList_SuccessfulExecution() throws Exception {
        // Arrange
        Long exhibitionId = 1L;
        Long memberMediaProxyRecordId = 1L;
        byte[] content = new byte[0];
        MediaProxyActionDTO mediaProxyActionDTO = new MediaProxyActionDTO();
        mediaProxyActionDTO.setExhibitionId(exhibitionId);

        List<MediaProxyRegistrationDTO> mockDtoList = List.of(new MediaProxyRegistrationDTO());
        when(zipUtils.unZip(anyString(), any(byte[].class), anyString())).thenReturn(mockFileNameAndFileMap);
        when(mediaProxyRegistrationService.getMediaProxyRegistrationDTOS(anyMap())).thenReturn(mockDtoList);
        doNothing().when(mediaProxyCheckDateService).checkExcelToType(any(MediaProxyActionDTO.class));
        doNothing().when(mediaProxyCheckDateService).checkExcelToInfo(any(MediaProxyActionDTO.class));
        doNothing().when(mediaProxyCheckDateService).checkExcelToVerification(any(MediaProxyActionDTO.class));
        when(mediaProxyActionDTO.isHasError()).thenReturn(false);

        // Act
        mediaProxyRegistrationService.importMediaList(memberMediaProxyRecordId, exhibitionId, content);

        // Assert
        verify(mediaProxyCheckDateService, times(1)).checkExcelToType(any(MediaProxyActionDTO.class));
        verify(mediaProxySaveDateService, times(1)).batchAddNewMediaUser(any(MediaProxyActionDTO.class));
    }

    @Test
    void testImportMediaList_ExcelReadingException() throws Exception {
        // Arrange
        Long exhibitionId = 1L;
        Long memberMediaProxyRecordId = 1L;
        byte[] content = new byte[0];
        when(zipUtils.unZip(anyString(), any(byte[].class), anyString())).thenThrow(new RuntimeException("Excel reading failed"));

        // Act & Assert
        try {
            mediaProxyRegistrationService.importMediaList(memberMediaProxyRecordId, exhibitionId, content);
        } catch (RuntimeException e) {
            verify(zipUtils, times(1)).unZip(anyString(), any(byte[].class), anyString());
        }
    }

    @Test
    void testImportMediaList_ErrorHandling() throws Exception {
        // Arrange
        Long exhibitionId = 1L;
        Long memberMediaProxyRecordId = 1L;
        byte[] content = new byte[0];
        MediaProxyActionDTO mediaProxyActionDTO = new MediaProxyActionDTO();
        mediaProxyActionDTO.setExhibitionId(exhibitionId);
        mediaProxyActionDTO.setHasError(true);

        List<MediaProxyRegistrationDTO> mockDtoList = List.of(new MediaProxyRegistrationDTO());
        when(zipUtils.unZip(anyString(), any(byte[].class), anyString())).thenReturn(mockFileNameAndFileMap);
        when(mediaProxyRegistrationService.getMediaProxyRegistrationDTOS(anyMap())).thenReturn(mockDtoList);
        doNothing().when(mediaProxyCheckDateService).checkExcelToType(any(MediaProxyActionDTO.class));
        doNothing().when(mediaProxyCheckDateService).checkExcelToInfo(any(MediaProxyActionDTO.class));
        doNothing().when(mediaProxyCheckDateService).checkExcelToVerification(any(MediaProxyActionDTO.class));

        // Act
        mediaProxyRegistrationService.importMediaList(memberMediaProxyRecordId, exhibitionId, content);

        // Assert
        verify(mediaProxyRegistrationService, times(1)).saveCheckErrorInfo(any(MediaProxyActionDTO.class));
    }
}