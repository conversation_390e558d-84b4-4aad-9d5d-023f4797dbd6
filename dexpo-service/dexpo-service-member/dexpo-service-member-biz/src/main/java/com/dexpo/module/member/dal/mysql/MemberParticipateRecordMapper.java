package com.dexpo.module.member.dal.mysql;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dexpo.framework.mybatis.core.mapper.BaseMapperX;
import com.dexpo.module.member.api.vo.media.MediaAuditQueryVO;
import com.dexpo.module.member.api.vo.media.MediaProxyRecordCheckVO;
import com.dexpo.module.member.dal.dataobject.MemberParticipateRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户参展记录 Mapper
 */
@Mapper
public interface MemberParticipateRecordMapper extends BaseMapperX<MemberParticipateRecordDO> {

   default  MemberParticipateRecordDO queryByUserIdAndExhibitionId(Long userId,Long exhibitionId){
        return selectOne(new LambdaQueryWrapper<MemberParticipateRecordDO>()
                .eq(MemberParticipateRecordDO::getMemberId, userId)
                .eq(MemberParticipateRecordDO::getExhibitionId, exhibitionId)
                .eq(MemberParticipateRecordDO::getDelFlg,Boolean.FALSE));
    }

    MediaProxyRecordCheckVO selectRegisterRecordByMemberIdAndExhibitionId(@Param("exhibitionId")Long exhibitionId ,@Param("memberMobile")String memberMobile ,@Param("memberEmail")String memberEmail);


    MediaAuditQueryVO selectMediaAuditQueryVO(@Param("recordId") Long recordId);

    /**
     * 批量激活
     * @param ids ids
     */
    void active(@Param("ids") List<Long> ids);
} 