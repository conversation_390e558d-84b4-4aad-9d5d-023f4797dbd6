package com.dexpo.module.member.convert;

import com.dexpo.framework.common.pojo.IConvert;
import com.dexpo.module.member.api.dto.EnterpriseInfoDTO;
import com.dexpo.module.member.api.vo.EnterpriseInfoVO;
import com.dexpo.module.member.dal.dataobject.EnterpriseInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface EnterpriseInfoConvert extends IConvert<EnterpriseInfoDTO, EnterpriseInfoVO, EnterpriseInfoDO> {

    EnterpriseInfoConvert INSTANCE = Mappers.getMapper(EnterpriseInfoConvert.class);
}
