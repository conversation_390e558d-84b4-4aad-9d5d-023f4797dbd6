package com.dexpo.module.member.utils.mediaproxy;

import cn.hutool.core.text.CharSequenceUtil;
import com.dexpo.module.base.api.basic.vo.BasicLocationVO;
import com.dexpo.module.base.api.basic.vo.BasicRegionVO;
import com.dexpo.module.member.dal.dataobject.EnterpriseInfoDO;
import com.dexpo.module.member.dal.dataobject.MemberBaseInfoDO;
import lombok.Data;

import java.util.*;

@Data
public class MediaProxyActionDTO {

    /**
     * 批量上传记录id
     */
    private Long memberMediaProxyRecordId;

    /**
     * 本次导入的会展id
     */
    private Long exhibitionId;

    /**
     * 待注册用户信息
     */
    private List<MediaProxyRegistrationDTO> mediaProxyRegistrationList;

    /**
     * 已注册用户信息
     */
    private List<MemberBaseInfoDO> memberBaseInfoDOList;

    /**
     * 手机号或者邮箱为key，手机号优先
     */
    private Map<String, MediaProxyRegistrationDTO> mediaProxyRegistrationMap = new HashMap<>();
    /**
     * 需要进行身份认证的用户信息
     */
    private List<MediaProxyRegistrationDTO> checkIdVerificationList = new ArrayList<>();
    /**
     * 是否有错误
     */
    private boolean hasError = false;
    /**
     * 错误信息
     */
    private StringBuilder errorInfo = new StringBuilder();
    /**
     * 需要新增的企业
     */
    private List<EnterpriseInfoDO> newEnterprise = new ArrayList<>();
    /**
     * 需要更新的企业
     */
    private List<EnterpriseInfoDO> updateEnterprise = new ArrayList<>();
    /**
     * 导入的手机号
     */
    private Set<String> phoneSet = new HashSet<>();
    /**
     * 导入的邮箱
     */
    private Set<String> emailSet = new HashSet<>();
    /**
     * 导入的手机号
     */
    private Set<String> existPhoneSet = new HashSet<>();
    /**
     * 导入的邮箱
     */
    private Set<String> existEmailSet = new HashSet<>();

    /**
     * 媒体地域
     */
    private List<BasicLocationVO> basicLocationList;

    /**
     * 行政区域信息
     */
    private Map<String, Map<String, BasicRegionVO>> regionListByLevel;

    public MediaProxyRegistrationDTO getMediaProxyRegistrationMapOne(String phone, String email) {
        return mediaProxyRegistrationMap.get(CharSequenceUtil.isEmpty(phone) ? email : phone);
    }
}
