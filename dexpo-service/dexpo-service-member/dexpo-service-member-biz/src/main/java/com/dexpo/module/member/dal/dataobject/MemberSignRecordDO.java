package com.dexpo.module.member.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户签署协议记录数据对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("member_sign_record")
public class MemberSignRecordDO extends BaseDO {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 会员ID
     */
    @TableField("member_id")
    private Long memberId;
    
    /**
     * 协议ID
     */
    @TableField("agreement_id")
    private Long agreementId;
    
    /**
     * 签署时间
     */
    @TableField("sign_time")
    private LocalDateTime signTime;
    
    /**
     * 是否有效
     */
    @TableField("is_effect")
    private Boolean isEffect;
} 