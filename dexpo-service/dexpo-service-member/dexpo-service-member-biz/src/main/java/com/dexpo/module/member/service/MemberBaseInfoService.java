package com.dexpo.module.member.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dexpo.module.member.dal.dataobject.MemberBaseInfoDO;

/**
 * 会员基本信息 Service 接口
 */
public interface MemberBaseInfoService extends IService<MemberBaseInfoDO> {


    /**
     * 更新会员基本信息
     *
     * @param memberBaseInfo 会员基本信息
     */
    void updateMemberBaseInfo(MemberBaseInfoDO memberBaseInfo);

    /**
     * 删除会员基本信息
     *
     * @param id 编号
     */
    void deleteMemberBaseInfo(Long id);

    /**
     * 获得会员基本信息
     *
     * @param id 编号
     * @return 会员基本信息
     */
    MemberBaseInfoDO getMemberBaseInfo(Long id);

    /**
     * 获得会员基本信息
     * @param mobileOrEmail
     * @return
     */
    MemberBaseInfoDO getMemberBaseInfoByMobileOrEmail(String mobileOrEmail);

} 