package com.dexpo.module.member.controller.member;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.member.api.MediaProxyApi;
import com.dexpo.module.member.api.dto.media.MediaProxyRecordDTO;
import com.dexpo.module.member.api.vo.media.MediaProxyRecordVO;
import com.dexpo.module.member.service.MediaProxyRegistrationService;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * 媒体批量操作
 */
@RestController
@AllArgsConstructor
public class MediaProxyController implements MediaProxyApi {

    @Resource
    private MediaProxyRegistrationService mediaProxyRegistrationService;

    @Override
    public PageResult<MediaProxyRecordVO> queryMemberMediaProxyRecordPage(MediaProxyRecordDTO mediaProxyRecordDTO) {
        return mediaProxyRegistrationService.queryMemberMediaProxyRecordPage(mediaProxyRecordDTO);
    }

    @Override
    public CommonResult<Long> saveMemberMediaProxyRecord(MediaProxyRecordDTO mediaProxyRecordDTO) {
        Long id = mediaProxyRegistrationService.saveMemberMediaProxyRecord(mediaProxyRecordDTO);
        return CommonResult.success(id);
    }

    @Override
    public CommonResult<Boolean> registrationByFile(Long memberMediaProxyRecordId, Long exhibitionId, byte[] content) {
        mediaProxyRegistrationService.importMediaList(memberMediaProxyRecordId,
                exhibitionId, content);
        return CommonResult.success(true);
    }
}
