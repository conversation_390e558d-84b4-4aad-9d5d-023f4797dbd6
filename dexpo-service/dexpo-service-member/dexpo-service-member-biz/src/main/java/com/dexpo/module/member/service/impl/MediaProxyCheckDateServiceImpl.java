package com.dexpo.module.member.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.base.api.basic.BasicLocationApi;
import com.dexpo.module.base.api.basic.BasicRegionApi;
import com.dexpo.module.base.api.basic.dto.BasicLocationDTO;
import com.dexpo.module.base.api.basic.vo.BasicLocationVO;
import com.dexpo.module.base.api.basic.vo.BasicRegionVO;
import com.dexpo.module.base.enums.BasicRegionLevelEnums;
import com.dexpo.module.base.enums.MediaPositionEnums;
import com.dexpo.module.integration.api.verification.VerificationApi;
import com.dexpo.module.integration.api.verification.dto.VerificationDTO;
import com.dexpo.module.integration.api.verification.vo.VerificationVO;
import com.dexpo.module.member.dal.dataobject.EnterpriseInfoDO;
import com.dexpo.module.member.dal.dataobject.MemberBaseInfoDO;
import com.dexpo.module.member.dal.dataobject.MemberParticipateRecordDO;
import com.dexpo.module.member.dal.mysql.EnterpriseInfoMapper;
import com.dexpo.module.member.dal.mysql.MemberBaseInfoMapper;
import com.dexpo.module.member.dal.mysql.MemberParticipateRecordMapper;
import com.dexpo.module.member.enums.*;
import com.dexpo.module.member.service.MediaProxyCheckDateService;
import com.dexpo.module.member.utils.mediaproxy.MediaProxyActionDTO;
import com.dexpo.module.member.utils.mediaproxy.MediaProxyConstant;
import com.dexpo.module.member.utils.mediaproxy.MediaProxyRegistrationDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Validated
@Slf4j
public class MediaProxyCheckDateServiceImpl implements MediaProxyCheckDateService {

    @Resource
    private EnterpriseInfoMapper enterpriseInfoMapper;

    @Resource
    private MemberBaseInfoMapper memberBaseInfoMapper;

    @Resource
    private VerificationApi verificationApi;

    @Resource
    private MemberParticipateRecordMapper memberParticipateRecordMapper;

    @Resource
    private BasicLocationApi basicLocationApi;

    @Resource
    private BasicRegionApi basicRegionApi;

    @Override
    public void checkExcelToVerification(MediaProxyActionDTO mediaProxyActionDTO) {
        List<MediaProxyRegistrationDTO> checkIdVerificationList = mediaProxyActionDTO.getCheckIdVerificationList();
        if (CollUtil.isEmpty(checkIdVerificationList)) {
            return;
        }
        // 整理身份信息
        List<VerificationDTO> verificationDTOList = new ArrayList<>();
        checkIdVerificationList.forEach(e -> {
            VerificationDTO verificationDTO = new VerificationDTO();
            verificationDTO.setMemberName(e.getMemberName());
            verificationDTO.setIdNumber(e.getIdNumber());
            verificationDTOList.add(verificationDTO);
        });
        // 实人认证
        CommonResult<List<VerificationVO>> resultList = verificationApi.Id2MetaVerifyList(verificationDTOList);
        boolean flag = false;
        List<String> nameList = new ArrayList<>();
        List<VerificationVO> data = resultList.getData();
        for (VerificationVO vo : data) {
            if (!Boolean.TRUE.equals(vo.getResult())) {
                flag = true;
                nameList.add(vo.getMemberName());
            }
        }
        // 记录未通过认证的信息
        if (flag) {
            mediaProxyActionDTO.getErrorInfo()
                    .append(MediaProxyConstant.ERROR_ID_VERIFICATION)
                    .append(nameList);
        }
    }

    /**
     * 检查注册数据内容是否合法
     *
     * @param mediaProxyActionDTO mediaProxyActionDTO
     */
    @Override
    public void checkExcelToInfo(MediaProxyActionDTO mediaProxyActionDTO) {
        // 检查数据
        // 查看用户数据是否已注册,有已注册的返回true
        checkPhoneAndEmail(mediaProxyActionDTO);

    }

    @Override
    public void checkExcelToType(MediaProxyActionDTO mediaProxyActionDTO) {
        List<MediaProxyRegistrationDTO> list = mediaProxyActionDTO.getMediaProxyRegistrationList();
        // 字段规则：
        //1.姓名：支持中文、英文、“·”输入，限20字符
        //2. 证件号码：最多支持输入20字符，支持数字、字母输入 done
        //3. 出生日期：“YYYY-MM-DD”格式 done
        //4. 手机号：11位正则校验 done
        //5. 邮箱：正则表达式，需包含“@”符号，限100字符  done
        //6. 工作单位：支持中文、数字、字母、“-”输入，限200字符 done
        //7. 记者证号：支持数字、字母、“-”输入，15位 done
        // "填写注意事项：
        //1. 若“证件类型”选择非“中华人民共和国居民身份证”，则“手机号”为非必填 done
        //2. 若“媒体类型”选择非“主流媒体”，则“记者证号”为非必填 done
        //3. 若“媒体职位”选择“其他”，则需手动输入
        //4. 若“证件领取方式”选择“自取”，则无需填写“收件人”、“手机号”、“邮寄地址” done
        //5. “国籍”、“省”、“市”、“区”请参考子表进行填写

        // 查询现有企业-媒体工作单位 - 无新增/有获取id
        List<EnterpriseInfoDO> enterpriseInfoDOList = enterpriseInfoMapper.selectList();
        Map<String, EnterpriseInfoDO> enterpriseByNameMap = enterpriseInfoDOList.stream()
                .collect(Collectors.toMap(EnterpriseInfoDO::getEnterpriseName, e -> e, (v1, v2) -> v1));
        BasicLocationDTO basicLocationDTO = new BasicLocationDTO();
        basicLocationDTO.setLocationTag(EnterpriseTypeEnum.MEDIA_ENTERPRISE.getCode());
        CommonResult<List<BasicLocationVO>> exhibitionLocationList = basicLocationApi.getLocationList(basicLocationDTO);
        mediaProxyActionDTO.setBasicLocationList(exhibitionLocationList.getData());
        mediaProxyActionDTO.setRegionListByLevel(basicRegionApi.getRegionListByLevel());

        for (int j = 0; j < list.size(); j++) {
            MediaProxyRegistrationDTO registrationDTO = list.get(j);
            String idNumber = registrationDTO.getIdNumber();
            StringBuilder errorInfo = new StringBuilder();
            int i = j + 3;
            // 检查证件号
            if (!isValidIdNumber(idNumber)) {
                appendErrorInfo(errorInfo, MediaProxyConstant.ERROR_ID_NUMBER_TYPE, i);
            }

            // 检查性别
            checkMemberGender(registrationDTO, errorInfo, i);

            // 检查证件类型
            IdCategoryEnum idCategoryEnum = checkIdCategory(mediaProxyActionDTO, registrationDTO, errorInfo, i);

            // 检查出生日期格式
            String memberBirthDayValue = registrationDTO.getMemberBirthDayValue();
            if (!isValidDateFormat(memberBirthDayValue)) {
                appendErrorInfo(errorInfo, MediaProxyConstant.ERROR_MEMBER_BIRTH_DAY_TYPE, i);
            }

            // 检查手机号
            checkMemberMobile(registrationDTO, idCategoryEnum, errorInfo, i);

            // 检查邮箱
            String email = registrationDTO.getMemberEmail();
            if (StringUtils.isNotEmpty(email) && (!email.contains("@") || email.length() > 100)) {
                appendErrorInfo(errorInfo, MediaProxyConstant.ERROR_EMAIL_TYPE, i);
            }

            // 检查国籍
            checkCountryName(registrationDTO, errorInfo, i, mediaProxyActionDTO);

            // 检查工作单位
            checkEnterpriseName(registrationDTO, errorInfo, i);

            // 检查媒体地域
            checkEnterpriseLocation(registrationDTO, errorInfo, i, mediaProxyActionDTO);

            // 检查媒体职位
            checkMediaPosition(registrationDTO, errorInfo, i);

            // 媒体类型
            MediaCategoryEnum mediaCategoryEnum = getMediaCategoryEnum(registrationDTO, errorInfo, i);

            // 检查记者证号
            checkMediaNewsmanNo(registrationDTO, mediaCategoryEnum, errorInfo, i);

            // 检查证件领取方式
            CertificateCollectionMethodEnum collectionMethodEnum = checkCertificateCollectionMethod(registrationDTO, errorInfo, i);

            // 检查邮寄地址
            checkRecipient(collectionMethodEnum, registrationDTO, errorInfo, idCategoryEnum, i, mediaProxyActionDTO);

            // 检查是否申请直播
            checkIsApplyLiveStream(registrationDTO, errorInfo, i);

            // 检查权限分配
            checkMediaPermissionType(registrationDTO, errorInfo, i);

            // 如果存在错误，则记录到checkResult中
            if (StringUtils.isNotEmpty(errorInfo)) {
                mediaProxyActionDTO.getErrorInfo().append(errorInfo);
                mediaProxyActionDTO.setHasError(true);
            }

            cleanData(mediaProxyActionDTO, registrationDTO);

            addNewEnterprise(enterpriseByNameMap, registrationDTO, mediaProxyActionDTO);
        }
    }

    private void checkMediaPermissionType(MediaProxyRegistrationDTO registrationDTO, StringBuilder errorInfo, int i) {
        MediaPermissionTypeEnums byCode = MediaPermissionTypeEnums.getByDescriptionCN(registrationDTO.getMediaPermissionTypeValue());
        if (null != byCode) {
            registrationDTO.setMediaPermissionType(byCode.getCode());
        } else {
            appendErrorInfo(errorInfo, MediaProxyConstant.ERROR_PERMISSION_TYPE, i);
        }
    }

    private void checkMediaPosition(MediaProxyRegistrationDTO registrationDTO, StringBuilder errorInfo, int i) {
        MediaPositionEnums positionEnums = MediaPositionEnums.getByDescriptionCN(registrationDTO.getMediaPositionNameCn());
        if (null != positionEnums) {
            registrationDTO.setMediaPositionCode(positionEnums.getCode());
            registrationDTO.setMediaPositionCode(positionEnums.getDescriptionEN());
        } else {
            appendErrorInfo(errorInfo, MediaProxyConstant.ERROR_MEDIA_POSITION, i);
        }
    }

    /**
     * 检查国籍
     */
    private void checkCountryName(MediaProxyRegistrationDTO registrationDTO, StringBuilder errorInfo,
                                  int i, MediaProxyActionDTO mediaProxyActionDTO) {
        Map<String, BasicRegionVO> regionVOMap = mediaProxyActionDTO.getRegionListByLevel()
                .get(BasicRegionLevelEnums.LEVEL_COUNTRY.getCode());
        if (regionVOMap.containsKey(registrationDTO.getCountryName())) {
            registrationDTO.setCountryCode(regionVOMap.get(registrationDTO.getCountryName()).getAdcode());
        } else {
            appendErrorInfo(errorInfo, MediaProxyConstant.ERROR_COUNTRY_NAME, i);
        }
    }

    /**
     * 检查媒体地域
     */
    private void checkEnterpriseLocation(MediaProxyRegistrationDTO registrationDTO, StringBuilder errorInfo,
                                         int i, MediaProxyActionDTO mediaProxyActionDTO) {
        mediaProxyActionDTO.getBasicLocationList().stream()
                .filter(e -> e.getLocationNameCn().equals(registrationDTO.getEnterpriseLocationNameCn()))
                .findFirst()
                .ifPresent(location -> {
                    registrationDTO.setEnterpriseLocation(location.getLocationCode());
                    registrationDTO.setEnterpriseLocationNameEn(location.getLocationNameEn());
                });
        if (StringUtils.isEmpty(registrationDTO.getEnterpriseLocation())) {
            appendErrorInfo(errorInfo, MediaProxyConstant.ERROR_ENTERPRISE_LOCATION, i);
        }
    }

    /**
     * 检查性别
     */
    private void checkMemberGender(MediaProxyRegistrationDTO registrationDTO, StringBuilder errorInfo, int i) {
        String memberGenderValue = registrationDTO.getMemberGenderCN();
        GenderEnum genderEnum = GenderEnum.getByDescriptionCN(memberGenderValue);
        if (genderEnum == null) {
            appendErrorInfo(errorInfo, MediaProxyConstant.ERROR_GENDER_TYPE, i);
        } else {
            registrationDTO.setMemberGender(genderEnum.getCode());
        }
    }

    @Nullable
    private CertificateCollectionMethodEnum checkCertificateCollectionMethod(MediaProxyRegistrationDTO registrationDTO,
                                                                             StringBuilder errorInfo, int i) {
        String certificateCollectionMethodValue = registrationDTO.getCertificateCollectionMethodValue();
        CertificateCollectionMethodEnum collectionMethodEnum = CertificateCollectionMethodEnum
                .getByDescriptionCN(certificateCollectionMethodValue);
        if (collectionMethodEnum == null) {
            appendErrorInfo(errorInfo, MediaProxyConstant.ERROR_CERTIFICATE_COLLECTION_METHOD_TYPE, i);
        } else {
            registrationDTO.setCertificateCollectionMethod(collectionMethodEnum.getCode());
        }
        return collectionMethodEnum;
    }

    private void checkEnterpriseName(MediaProxyRegistrationDTO registrationDTO, StringBuilder errorInfo, int i) {
        String workUnit = registrationDTO.getEnterpriseName();
        if (StringUtils.isNotEmpty(workUnit) && (workUnit.length() > 200
                || !workUnit.matches("^[\\u4e00-\\u9fa5a-zA-Z0-9\\-]+$"))) {
            appendErrorInfo(errorInfo, MediaProxyConstant.ERROR_WORK_UNIT_TYPE, i);
        }
    }

    @Nullable
    private IdCategoryEnum checkIdCategory(MediaProxyActionDTO mediaProxyActionDTO,
                                           MediaProxyRegistrationDTO registrationDTO, StringBuilder errorInfo, int i) {
        IdCategoryEnum idCategoryEnum = IdCategoryEnum.getByDescriptionCN(registrationDTO.getIdCategoryCN());
        if (idCategoryEnum == null) {
            appendErrorInfo(errorInfo, MediaProxyConstant.ERROR_ID_CATEGORY_TYPE, i);
        } else {
            registrationDTO.setIdCategory(idCategoryEnum.getCode());
            if (IdCategoryEnum.RESIDENT_ID_CARD.equals(idCategoryEnum)) {
                mediaProxyActionDTO.getCheckIdVerificationList().add(registrationDTO);
            }
        }
        return idCategoryEnum;
    }

    private void cleanData(MediaProxyActionDTO mediaProxyActionDTO, MediaProxyRegistrationDTO registrationDTO) {
        // 收集邮箱
        mediaProxyActionDTO.getEmailSet().add(registrationDTO.getMemberEmail());

        // 收集手机号
        mediaProxyActionDTO.getPhoneSet().add(registrationDTO.getMemberMobile());

        // 使用手机号或者邮箱作为key存入map
        String key = StringUtils.isNotEmpty(registrationDTO.getMemberMobile())
                ? registrationDTO.getMemberMobile() : registrationDTO.getMemberEmail();
        mediaProxyActionDTO.getMediaProxyRegistrationMap().put(key, registrationDTO);
    }

    private void appendErrorInfo(StringBuilder errorInfo, String errorMsg, int i) {
        if (StringUtils.isEmpty(errorInfo)) {
            errorInfo.append(MediaProxyConstant.INFO_ROW_ONE).append(i).append(MediaProxyConstant.INFO_ROW_TWO);
        }
        errorInfo.append(errorMsg);
    }

    private void addNewEnterprise(Map<String, EnterpriseInfoDO> enterpriseByNameMap,
                                  MediaProxyRegistrationDTO registrationDTO,
                                  MediaProxyActionDTO mediaProxyActionDTO) {
        if (!enterpriseByNameMap.containsKey(registrationDTO.getEnterpriseName())) {
            EnterpriseInfoDO enterpriseInfoDO = new EnterpriseInfoDO();
            enterpriseInfoDO.setEnterpriseName(registrationDTO.getEnterpriseName());
            enterpriseInfoDO.setEnterpriseType(EnterpriseTypeEnum.MEDIA_ENTERPRISE.getCode());
            enterpriseInfoDO.setEnterpriseLocationNameCn(registrationDTO.getEnterpriseLocationNameCn());
            enterpriseInfoDO.setEnterpriseLocationCode(registrationDTO.getEnterpriseLocation());
            enterpriseInfoDO.setEnterpriseLocationNameEn(registrationDTO.getEnterpriseLocationNameEn());
            enterpriseInfoDO.setIsUseAble(Boolean.TRUE);
            mediaProxyActionDTO.getNewEnterprise().add(enterpriseInfoDO);
        } else {
            EnterpriseInfoDO enterpriseInfoDO = enterpriseByNameMap.get(registrationDTO.getEnterpriseName());
            enterpriseInfoDO.setEnterpriseLocationNameCn(registrationDTO.getEnterpriseLocationNameCn());
            enterpriseInfoDO.setEnterpriseLocationCode(registrationDTO.getEnterpriseLocation());
            enterpriseInfoDO.setEnterpriseLocationNameEn(registrationDTO.getEnterpriseLocationNameEn());
            mediaProxyActionDTO.getUpdateEnterprise().add(enterpriseInfoDO);
            registrationDTO.setEnterpriseId(enterpriseInfoDO.getId());
        }
    }

    private void checkRecipient(CertificateCollectionMethodEnum collectionMethodEnum,
                                MediaProxyRegistrationDTO registrationDTO, StringBuilder errorInfo,
                                IdCategoryEnum idCategoryEnum, int i, MediaProxyActionDTO mediaProxyActionDTO) {
        if (CertificateCollectionMethodEnum.MAIL.equals(collectionMethodEnum)) {
            // 收件人
            checkRecipientName(registrationDTO, errorInfo, i);

            // 手机号
            checkPhoneNo(errorInfo, registrationDTO.getRecipientMobile(), MediaProxyConstant.ERROR_GET_METHOD, i);

            // 省市区
            if (IdCategoryEnum.RESIDENT_ID_CARD.equals(idCategoryEnum)) {
                checkRegion(registrationDTO, errorInfo, i, mediaProxyActionDTO);
            }

            // 详细地址
            String recipientAddress = registrationDTO.getRecipientAddress();
            if (StringUtils.isEmpty(recipientAddress)) {
                appendErrorInfo(errorInfo, MediaProxyConstant.ERROR_RECIPIENT_ADDRESS_TYPE, i);
            }
        }
    }

    private void checkRegion(MediaProxyRegistrationDTO registrationDTO, StringBuilder errorInfo, int i, MediaProxyActionDTO mediaProxyActionDTO) {
        // 校验必填
        String recipientProvinceName = registrationDTO.getRecipientProvinceName();
        String recipientCityName = registrationDTO.getRecipientCityName();
        String recipientDistrictName = registrationDTO.getRecipientDistrictName();
        if (StringUtils.isEmpty(recipientProvinceName)
                || StringUtils.isEmpty(recipientCityName)
                || StringUtils.isEmpty(recipientDistrictName)) {
            appendErrorInfo(errorInfo, MediaProxyConstant.ERROR_RECIPIENT_TYPE, i);
        }
        // 校验 省市区的取值值
        Map<String, Map<String, BasicRegionVO>> regionListByLevel = mediaProxyActionDTO.getRegionListByLevel();
        checkRecipientName(registrationDTO, errorInfo, i, regionListByLevel, recipientProvinceName,
                MediaProxyConstant.ERROR_LEVEL_PROVINCE, BasicRegionLevelEnums.LEVEL_PROVINCE);

        checkRecipientName(registrationDTO, errorInfo, i, regionListByLevel, recipientCityName,
                MediaProxyConstant.ERROR_LEVEL_CITY, BasicRegionLevelEnums.LEVEL_CITY);

        checkRecipientName(registrationDTO, errorInfo, i, regionListByLevel, recipientDistrictName,
                MediaProxyConstant.ERROR_LEVEL_DISTRICT, BasicRegionLevelEnums.LEVEL_DISTRICT);
    }

    private void checkRecipientName(MediaProxyRegistrationDTO registrationDTO, StringBuilder errorInfo,
                                    int i, Map<String, Map<String, BasicRegionVO>> regionListByLevel,
                                    String recipientName, String errorMsg, BasicRegionLevelEnums enums) {
        BasicRegionVO basicRegionVO = getBasicRegionVO(enums, regionListByLevel, recipientName);
        if (ObjectUtil.isNotNull(basicRegionVO)) {
            addRecipientCode(registrationDTO, basicRegionVO.getAdcode(), enums);
        } else {
            appendErrorInfo(errorInfo, errorMsg, i);
        }
    }

    private void addRecipientCode(MediaProxyRegistrationDTO registrationDTO, String adCode, BasicRegionLevelEnums enums) {
        if (BasicRegionLevelEnums.LEVEL_PROVINCE.equals(enums)) {
            registrationDTO.setRecipientProvinceCode(adCode);
        }
        if (BasicRegionLevelEnums.LEVEL_CITY.equals(enums)) {
            registrationDTO.setRecipientCityCode(adCode);
        }
        if (BasicRegionLevelEnums.LEVEL_DISTRICT.equals(enums)) {
            registrationDTO.setRecipientDistrictCode(adCode);
        }
    }

    private BasicRegionVO getBasicRegionVO(BasicRegionLevelEnums enums, Map<String, Map<String, BasicRegionVO>> regionListByLevel, String recipientName) {
        return regionListByLevel.get(enums.getCode()).get(recipientName);
    }


    private void checkIsApplyLiveStream(MediaProxyRegistrationDTO registrationDTO, StringBuilder errorInfo, int i) {
        String isApplyLiveStreamValue = registrationDTO.getIsApplyLiveStreamValue();
        Boolean isApplyLiveStream = BooleanUtil.toBooleanObject(isApplyLiveStreamValue);
        if (isApplyLiveStream == null) {
            appendErrorInfo(errorInfo, MediaProxyConstant.ERROR_IS_APPLY_LIVE_STREAM_TYPE, i);
        } else {
            registrationDTO.setIsApplyLiveStream(isApplyLiveStream);
        }
    }

    private void checkRecipientName(MediaProxyRegistrationDTO registrationDTO, StringBuilder errorInfo, int i) {
        String recipientName = registrationDTO.getRecipientName();
        if (StringUtils.isNotEmpty(recipientName) &&
                (!recipientName.matches("^[\\u4e00-\\u9fa5a-zA-Z·]+$") || recipientName.length() > 20)) {
            appendErrorInfo(errorInfo, MediaProxyConstant.ERROR_RECIPIENT_NAME_TYPE, i);
        }
    }

    @Nullable
    private MediaCategoryEnum getMediaCategoryEnum(MediaProxyRegistrationDTO registrationDTO, StringBuilder errorInfo, int i) {
        String mediaTypeNameCn = registrationDTO.getMediaTypeNameCn();
        MediaCategoryEnum mediaCategoryEnum = MediaCategoryEnum.getByDescriptionCN(mediaTypeNameCn);
        if (mediaCategoryEnum == null) {
            appendErrorInfo(errorInfo, MediaProxyConstant.ERROR_MEDIA_TYPE_NAME_TYPE, i);
        } else {
            registrationDTO.setMediaTypeCode(mediaCategoryEnum.getValuesetCode());
            registrationDTO.setMediaTypeNameEn(mediaCategoryEnum.getDescriptionEN());
        }
        return mediaCategoryEnum;
    }

    private void checkMediaNewsmanNo(MediaProxyRegistrationDTO registrationDTO, MediaCategoryEnum mediaCategoryEnum,
                                     StringBuilder errorInfo, int i) {
        String mediaNewsmanNo = registrationDTO.getMediaNewsmanNo();
        if (MediaCategoryEnum.MAINSTREAM_MEDIA.equals(mediaCategoryEnum)
                && (StringUtils.isEmpty(mediaNewsmanNo) || mediaNewsmanNo.length() > 15)) {
            appendErrorInfo(errorInfo, MediaProxyConstant.ERROR_MEDIA_NEWSMAN_NO_TYPE, i);
        }
    }

    private void checkMemberMobile(MediaProxyRegistrationDTO registrationDTO,
                                   IdCategoryEnum idCategoryEnum, StringBuilder errorInfo, int i) {
        String phoneNumber = registrationDTO.getMemberMobile();
        // 若“证件类型”选择非“中华人民共和国居民身份证”，则“手机号”为非必填
        if (IdCategoryEnum.RESIDENT_ID_CARD.equals(idCategoryEnum)) {
            checkPhoneNo(errorInfo, phoneNumber, "", i);
        }
    }

    private void checkPhoneNo(StringBuilder errorInfo, String phoneNumber, String type, int i) {
        if (StringUtils.isNotEmpty(phoneNumber) && !phoneNumber.matches("^\\d{11}$")) {
            appendErrorInfo(errorInfo, type, i);
            appendErrorInfo(errorInfo, MediaProxyConstant.ERROR_PHONE_NO_TYPE, i);
        }
    }

    private boolean isValidIdNumber(String idNumber) {
        return idNumber != null && idNumber.length() <= 20 && idNumber.matches("[a-zA-Z0-9]+");
    }

    private boolean isValidDateFormat(String date) {
        try {
            LocalDate.parse(date, java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private boolean checkPhoneAndEmail(MediaProxyActionDTO mediaProxyActionDTO) {

        LambdaQueryWrapper<MemberBaseInfoDO> queryMemberBaseInfoDO = new LambdaQueryWrapper<>();
        queryMemberBaseInfoDO.in(MemberBaseInfoDO::getMemberMobile, mediaProxyActionDTO.getPhoneSet());
        queryMemberBaseInfoDO.in(MemberBaseInfoDO::getMemberEmail, mediaProxyActionDTO.getEmailSet());
        List<MemberBaseInfoDO> memberBaseInfoDOList = memberBaseInfoMapper.selectList(queryMemberBaseInfoDO);
        if (CollUtil.isEmpty(memberBaseInfoDOList)) {
            return true;
        }

        Map<Long, MemberBaseInfoDO> mapByMemberId = memberBaseInfoDOList.stream()
                .collect(Collectors.toMap(MemberBaseInfoDO::getId, e -> e));

        LambdaQueryWrapper<MemberParticipateRecordDO> queryDO = new LambdaQueryWrapper<>();
        queryDO.in(MemberParticipateRecordDO::getMemberId, mapByMemberId.keySet())
                .in(MemberParticipateRecordDO::getExhibitionId, mediaProxyActionDTO.getExhibitionId());
        queryDO.eq(MemberParticipateRecordDO::getDelFlg, false);
        List<MemberParticipateRecordDO> memberParticipateRecordDOS = memberParticipateRecordMapper.selectList(queryDO);

        memberParticipateRecordDOS.stream().forEach(memberParticipateRecordDO -> {
            String mobile = mapByMemberId.get(memberParticipateRecordDO.getMemberId()).getMemberMobile();
            String email = mapByMemberId.get(memberParticipateRecordDO.getMemberId()).getMemberEmail();
            if (mediaProxyActionDTO.getPhoneSet().contains(mobile)) {
                mediaProxyActionDTO.getExistPhoneSet().add(mobile);
            }
            if (mediaProxyActionDTO.getEmailSet().contains(email)) {
                mediaProxyActionDTO.getExistEmailSet().add(email);
            }
        });

        // 添加错误信息
        if (CollUtil.isNotEmpty(mediaProxyActionDTO.getExistPhoneSet())) {
            mediaProxyActionDTO.getErrorInfo().append(MediaProxyConstant.ERROR_EXIST_PHONE)
                    .append(mediaProxyActionDTO.getExistPhoneSet());
        }
        if (CollUtil.isNotEmpty(mediaProxyActionDTO.getExistEmailSet())) {
            mediaProxyActionDTO.getErrorInfo().append(MediaProxyConstant.ERROR_EXIST_EMAIL)
                    .append(mediaProxyActionDTO.getExistEmailSet());
        }
        return CollUtil.isNotEmpty(memberBaseInfoDOList);
    }
}
