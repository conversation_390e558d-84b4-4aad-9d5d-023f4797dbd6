package com.dexpo.module.member.enums;

import lombok.Getter;

@Getter
public enum IdCategoryEnum {

    RESIDENT_ID_CARD(
            EnumConstants.VS_ID_CATEGORY,
            "VO_ID_CATEGORY_1",
            "中华人民共和国居民身份证",
            "Resident Identity Card (PRC)"
    ),
    PASSPORT(
            EnumConstants.VS_ID_CATEGORY,
            "VO_ID_CATEGORY_2",
            "护照",
            "Passport"
    ),
    HONGKONG_MACAO_PERMIT(
            EnumConstants.VS_ID_CATEGORY,
            "VO_ID_CATEGORY_3",
            "港澳居民来往内地通行证（回乡证）",
            "Mainland Travel Permit for Hong Kong and Macao Residents"
    ),
    TAIWAN_PERMIT(
            EnumConstants.VS_ID_CATEGORY,
            "VO_ID_CATEGORY_4",
            "台湾居民来往大陆通行证（台胞证）",
            "Mainland Travel Permit for Taiwan Residents"
    ),
    FOREIGN_PERMANENT_RESIDENT_ID(
            EnumConstants.VS_ID_CATEGORY,
            "VO_ID_CATEGORY_5",
            "外国人永久居留身份证",
            "Foreign Permanent Resident ID Card"
    );

    /**
     * 值集分类编码
     */
    private final String valuesetCode;

    /**
     * 枚举项编码
     */
    private final String code;

    /**
     * 中文描述
     */
    private final String descriptionCN;

    /**
     * 英文描述
     */
    private final String descriptionEN;

    IdCategoryEnum(String valuesetCode, String code, String descriptionCN, String descriptionEN) {
        this.valuesetCode = valuesetCode;
        this.code = code;
        this.descriptionCN = descriptionCN;
        this.descriptionEN = descriptionEN;
    }

    /**
     * 根据枚举编码获取枚举实例
     * @param code VO_ID_CATEGORY_1 格式的编码
     * @return 匹配的枚举实例，未找到返回null
     */
    public static IdCategoryEnum getByCode(String code) {
        for (IdCategoryEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据枚举编码获取枚举实例
     *
     * @param descriptionCN descriptionCN
     * @return 匹配的枚举实例，未找到返回null
     */
    public static IdCategoryEnum getByDescriptionCN(String descriptionCN) {
        for (IdCategoryEnum type : values()) {
            if (type.descriptionCN.equals(descriptionCN)) {
                return type;
            }
        }
        return null;
    }
} 