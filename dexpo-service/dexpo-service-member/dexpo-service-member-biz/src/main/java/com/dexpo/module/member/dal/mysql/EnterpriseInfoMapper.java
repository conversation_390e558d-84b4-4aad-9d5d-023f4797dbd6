package com.dexpo.module.member.dal.mysql;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dexpo.framework.common.Constants;
import com.dexpo.framework.mybatis.core.mapper.BaseMapperX;
import com.dexpo.module.member.dal.dataobject.EnterpriseInfoDO;
import com.dexpo.module.member.enums.EnterpriseTypeEnum;
import org.apache.ibatis.annotations.Mapper;

/**
 * 企业信息Mapper接口
 */
@Mapper
public interface EnterpriseInfoMapper extends BaseMapperX<EnterpriseInfoDO> {

    /**
     * 根据企业名称寻找已经生效的企业信息
     * @param enterpriseName
     * @return
     */
    default EnterpriseInfoDO queryMediaEnterpriseByNameAndLocationCode(String enterpriseName,String enterpriseLocationCode){
        LambdaQueryWrapper<EnterpriseInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EnterpriseInfoDO::getEnterpriseName,enterpriseName)
                .eq(EnterpriseInfoDO::getEnterpriseType, EnterpriseTypeEnum.MEDIA_ENTERPRISE.getCode())
                .eq(EnterpriseInfoDO::getIsUseAble,Boolean.TRUE)
                .eq(EnterpriseInfoDO::getEnterpriseLocationCode,enterpriseLocationCode)
                .eq(EnterpriseInfoDO::getDelFlg,Boolean.FALSE).last(Constants.LIMIT_ONE);
        return selectOne(queryWrapper);
    }

    default EnterpriseInfoDO queryById(Long enterpriseId){
        LambdaQueryWrapper<EnterpriseInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EnterpriseInfoDO::getId, enterpriseId)
                .eq(EnterpriseInfoDO::getDelFlg,Boolean.FALSE);
        return selectOne(queryWrapper);
    }
} 