package com.dexpo.module.member.convert;

import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseSyncMessage;
import com.dexpo.module.member.api.dto.member.MemberBaseInfoDTO;
import com.dexpo.module.member.api.dto.member.MemberRegisterInfoDTO;
import com.dexpo.module.member.api.dto.RegisterInfoSyncDTO;
import com.dexpo.module.member.dal.dataobject.MemberParticipateRecordDO;
import com.dexpo.module.member.enums.RegisterLanguageEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Mapper
public interface DataWarehouseSyncConvert {

    DataWarehouseSyncConvert INSTANCE = Mappers.getMapper(DataWarehouseSyncConvert.class);


    RegisterInfoSyncDTO toRegisterInfoSync(MemberParticipateRecordDO recordDO);

    @Mapping(source = "memberBaseInfo.memberCode", target = "memberCode")
    @Mapping(source = "memberBaseInfo.memberName", target = "memberName")
    @Mapping(source = "memberBaseInfo.memberFirstName", target = "memberFirstName")
    @Mapping(source = "memberBaseInfo.memberLastName", target = "memberLastName")
    @Mapping(source = "memberBaseInfo.memberGender", target = "memberGender")
    @Mapping(source = "memberBaseInfo.memberBirthDay", target = "memberBirthDay", qualifiedByName = "formatLocalDate")
    @Mapping(source = "memberBaseInfo.memberMobile", target = "memberMobile")
    @Mapping(source = "memberBaseInfo.memberEmail", target = "memberEmail")
    @Mapping(source = "memberBaseInfo.idCategory", target = "idCategory")
    @Mapping(source = "memberBaseInfo.idNumber", target = "idNumber")
    @Mapping(source = "memberBaseInfo.countryCode", target = "countryCode")
    @Mapping(target = "countryName", expression = "java(getCountryNameByLanguage(dto.getMemberBaseInfo(), dto.getRegisterLanguage()))")
    
    @Mapping(source = "memberRecipientInfo.certificateCollectionMethod", target = "certificateCollectionMethod")
    @Mapping(source = "memberRecipientInfo.recipientName", target = "recipientName")
    @Mapping(source = "memberRecipientInfo.recipientFirstName", target = "recipientFirstName")
    @Mapping(source = "memberRecipientInfo.recipientLastName", target = "recipientLastName")
    @Mapping(source = "memberRecipientInfo.recipientMobile", target = "recipientMobile")
    @Mapping(source = "memberRecipientInfo.recipientProvinceCode", target = "recipientProvinceCode")
    @Mapping(source = "memberRecipientInfo.recipientProvinceName", target = "recipientProvinceName")
    @Mapping(source = "memberRecipientInfo.recipientCityCode", target = "recipientCityCode")
    @Mapping(source = "memberRecipientInfo.recipientCityName", target = "recipientCityName")
    @Mapping(source = "memberRecipientInfo.recipientDistrictCode", target = "recipientDistrictCode")
    @Mapping(source = "memberRecipientInfo.recipientDistrictName", target = "recipientDistrictName")
    @Mapping(source = "memberRecipientInfo.recipientAddress", target = "recipientAddress")
    
    @Mapping(source = "enterpriseInfo.enterpriseName", target = "enterpriseName")
    @Mapping(source = "memberReporterInfo.mediaNewsmanNo", target = "mediaNewsmanNo")
    @Mapping(source = "memberReporterInfo.mediaTypeCode", target = "mediaTypeCode")
    @Mapping(source = "memberReporterInfo.mediaPositionCategoryCode", target = "mediaPositionCategoryCode")
    @Mapping(source = "memberReporterInfo.mediaPositionCode", target = "mediaPositionCode")
    @Mapping(source = "memberReporterInfo.isApplyLiveStream", target = "isApplyLiveStream")
    @Mapping(source = "memberReporterInfo.attachmentHeadPhoto", target = "attachmentHeadPhoto")
    
    @Mapping(source = "exhibitionInfo.exhibitionCode", target = "exhibitionCode")
    @Mapping(source = "exhibitionInfo.exhibitionNameCn", target = "exhibitionNameCn")
    @Mapping(source = "exhibitionInfo.exhibitionNameEn", target = "exhibitionNameEn")
    @Mapping(source = "exhibitionInfo.exhibitionSessionKey", target = "exhibitionSessionKey")
    @Mapping(source = "exhibitionInfo.exhibitionTagCode", target = "exhibitionTagCode")
    @Mapping(source = "registerInfo.registerTime", target = "registerTime", qualifiedByName = "formatLocalDateTime")
    @Mapping(source = "registerInfo.registerStatus", target = "registerStatus")
    @Mapping(source = "registerInfo.registerSystem", target = "registerSystem")
    @Mapping(source = "registerInfo.registerMethod", target = "registerMethod")
    @Mapping(source = "registerInfo.registerSource", target = "registerSource")
    @Mapping(source = "registerInfo.memberType", target = "memberType")
    DataWarehouseSyncMessage convert(MemberRegisterInfoDTO dto);

    @Named("formatLocalDate")
    default String formatLocalDate(LocalDate date) {
        if (date == null) {
            return null;
        }
        return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    @Named("formatLocalDateTime")
    default String formatLocalDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    default String getCountryNameByLanguage(MemberBaseInfoDTO memberBaseInfo, String registerLanguage) {
        if (memberBaseInfo == null) {
            return null;
        }
        RegisterLanguageEnum language = RegisterLanguageEnum.getByCode(registerLanguage);
        if (language != null && RegisterLanguageEnum.CHINESE.getCode().equals(language.getCode())) {
            return memberBaseInfo.getCountryNameCn();
        }
        return memberBaseInfo.getCountryNameEn();
    }
} 