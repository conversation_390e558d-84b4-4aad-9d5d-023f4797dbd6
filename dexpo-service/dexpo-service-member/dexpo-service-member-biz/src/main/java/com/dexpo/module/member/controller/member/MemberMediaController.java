package com.dexpo.module.member.controller.member;


import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.member.api.MediaApi;
import com.dexpo.module.member.api.dto.media.*;
import com.dexpo.module.member.api.dto.member.MemberRegisterInfoDTO;
import com.dexpo.module.member.api.vo.LoginInfoResVO;
import com.dexpo.module.member.api.vo.media.MediaRegisterInfoVO;
import com.dexpo.module.member.api.vo.media.MediaSaveVO;
import com.dexpo.module.member.service.MediaMemberService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Validated
public class MemberMediaController implements MemberMediaApi {

    @Resource
    private MediaMemberService mediaMemberService;
    @Override
    public CommonResult<LoginInfoResVO> mediaLogin(@Valid @RequestBody MediaMemberLoginDTO loginDTO) {
        return CommonResult.success(mediaMemberService.mediaLogin(loginDTO));
    }

    @Override
    public CommonResult<MediaRegisterInfoVO> getMediaRegisterInfo(@Valid @RequestBody MediaRegisterInfoQueryDTO infoQueryDTO) {
        return CommonResult.success(mediaMemberService.getMediaRegisterInfo(infoQueryDTO));
    }

    @Override
    public CommonResult<MediaSaveVO> saveMediaInfo(@RequestBody MemberRegisterInfoDTO registerInfoDTO) {
        return CommonResult.success(mediaMemberService.saveMediaDraftInfo(registerInfoDTO));
    }

    @Override
    public CommonResult<Long> submitMediaInfo(@Valid @RequestBody MemberRegisterInfoDTO registerInfoDTO) {
        return CommonResult.success(mediaMemberService.submitMediaInfo(registerInfoDTO));
    }

    @Override
    public CommonResult<Long> recall(@Valid @RequestBody MediaRecallDTO recallDTO) {
        return CommonResult.success(mediaMemberService.recall(recallDTO));
    }

    @Override
    public CommonResult<Long> proxyRegister(@Valid @RequestBody MemberRegisterInfoDTO registerInfoDTO) {
        return CommonResult.success(mediaMemberService.proxyRegister(registerInfoDTO));
    }

    @Override
    public CommonResult<List<Long>> audit(@Valid @RequestBody MediaAuditDTO mediaAuditDTO) {
        return CommonResult.success(mediaMemberService.audit(mediaAuditDTO));
    }

    @Override
    public CommonResult<List<Long>> syncApprovedData(MediaSyncDataDTO syncDataDTO) {
        return CommonResult.success(mediaMemberService.syncData(syncDataDTO));
    }
}
