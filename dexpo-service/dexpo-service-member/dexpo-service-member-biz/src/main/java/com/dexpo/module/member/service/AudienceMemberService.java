package com.dexpo.module.member.service;


import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.member.api.dto.audience.AudiencePageQueryDTO;
import com.dexpo.module.member.api.vo.audience.AudiencePageListVO;

import java.util.List;

/**
 * 媒体用户接口
 */
public interface AudienceMemberService {

    /**
     * 分页获取观众列表
     *
     * @param queryDTO 分页查询条件
     * @return 媒体列表
     */
    PageResult<AudiencePageListVO> getAudiencePage(AudiencePageQueryDTO queryDTO);


    /**
     * 激活信息
     *
     * @param ids ids
     * @return true 成功
     */
    Boolean active(List<Long> ids);
}
