package com.dexpo.module.member.convert;

import java.util.List;
import java.util.Objects;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import com.dexpo.framework.cache.redis.entity.BasicValuesetOptionCache;
import com.dexpo.framework.common.pojo.IConvert;
import com.dexpo.module.base.api.attachment.vo.AttachmentInfoVO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetOptionVO;
import com.dexpo.module.member.api.dto.audience.AudiencePageQueryDTO;
import com.dexpo.module.member.api.dto.media.MediaPageListQueryDTO;
import com.dexpo.module.member.api.dto.member.MemberReporterInfoDTO;
import com.dexpo.module.member.api.vo.audience.AudiencePageListVO;
import com.dexpo.module.member.api.vo.media.MediaPageListVO;
import com.dexpo.module.member.api.vo.member.MemberAttachmentInfoVO;
import com.dexpo.module.member.api.vo.member.MemberReporterInfoVO;
import com.dexpo.module.member.dal.dataobject.AudiencePageListDO;
import com.dexpo.module.member.dal.dataobject.AudiencePageListQueryDO;
import com.dexpo.module.member.dal.dataobject.MediaPageListDO;
import com.dexpo.module.member.dal.dataobject.MediaPageListQueryDO;
import com.dexpo.module.member.dal.dataobject.MemberReporterInfoDO;
import com.github.pagehelper.Page;

/**
 * 收证信息转换类
 */
@Mapper
public interface MemberReporterInfoConvert
        extends IConvert<MemberReporterInfoDTO, MemberReporterInfoVO, MemberReporterInfoDO> {

    MemberReporterInfoConvert INSTANCE = Mappers.getMapper(MemberReporterInfoConvert.class);

    MemberAttachmentInfoVO toMemberAttachmentInfoVO(AttachmentInfoVO attachmentInfoVO);

    List<MemberAttachmentInfoVO> toMemberAttachmentInfoVOList(List<AttachmentInfoVO> attachmentInfoVOList);

    BasicValuesetOptionVO cache2BasicValuesetOptionVO(BasicValuesetOptionCache cache);

    /**
     * 查询对象转换数据库中间层
     * 
     * @param queryDTO query
     * @return do
     */
    MediaPageListQueryDO toQueryDO(MediaPageListQueryDTO queryDTO);

    /**
     * 返回对象 数据库层 转换为VO
     * 
     * @param listDO do
     * @return vo
     */
    MediaPageListVO toMediaPageListVO(MediaPageListDO listDO);

    /**
     * 返回对象 数据库层 转换为VO
     * 
     * @param listDOS do
     * @return vo
     */
    List<MediaPageListVO> toMediaPageListVOS(List<MediaPageListDO> listDOS);

    /**
     * 观众查询对象转换数据库中间层
     */
    AudiencePageListQueryDO toAudiencePageQueryDO(AudiencePageQueryDTO queryDTO);

    /**
     * 观众分页列表转换VO
     * 
     * @param listDO do
     * @return vo
     */
    @Mapping(target = "isDomestic", source = "countryCode", qualifiedByName = "setDemoticValue")
    AudiencePageListVO toAudiencePageListVO(AudiencePageListDO listDO);

    /**
     * 观众分页列表转换VO
     * 
     * @param objects do
     * @return vo
     */
    List<AudiencePageListVO> toAudiencePageListVOS(Page<AudiencePageListDO> objects);

    @Named("setDemoticValue")
    default Boolean setDemoticValue(String countryCode) {
        return Objects.equals(countryCode, "1");
    }

}
