<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.dexpo</groupId>
        <artifactId>dexpo-framework-starter-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../../dexpo-framework/dexpo-framework-starter-parent/pom.xml</relativePath>
    </parent>

    <groupId>com.dexpo</groupId>
    <artifactId>dexpo-service-member</artifactId>
    <packaging>pom</packaging>
    <name>${project.artifactId}</name>

    <modules>
        <module>dexpo-service-member-api</module>
<!--        <module>dexpo-service-member-biz</module>-->
        <module>dexpo-service-member-entry</module>
        <module>dexpo-service-member-app</module>
        <module>dexpo-service-member-domain</module>
        <module>dexpo-service-member-infrastructure</module>
        <module>dexpo-service-member-starter</module>
    </modules>

    <description>
        member 展会模块
    </description>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-member-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
