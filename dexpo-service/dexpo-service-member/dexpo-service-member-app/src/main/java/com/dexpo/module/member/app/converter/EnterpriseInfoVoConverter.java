package com.dexpo.module.member.app.converter;

import com.dexpo.module.member.api.vo.EnterpriseInfoVO;
import com.dexpo.module.member.domain.model.enterprise.EnterpriseInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface EnterpriseInfoVoConverter {


    EnterpriseInfoVoConverter INSTANCE = Mappers.getMapper(EnterpriseInfoVoConverter.class);

    @Mappings({})
    List<EnterpriseInfoVO> toVoList(List<EnterpriseInfo> dataList);

    EnterpriseInfoVO toVo(EnterpriseInfo data);
}
