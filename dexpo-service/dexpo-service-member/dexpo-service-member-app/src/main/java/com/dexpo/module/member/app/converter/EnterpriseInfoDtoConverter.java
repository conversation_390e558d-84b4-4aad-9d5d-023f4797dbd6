package com.dexpo.module.member.app.converter;

import com.dexpo.module.member.api.dto.EnterpriseInfoDTO;
import com.dexpo.module.member.domain.model.enterprise.EnterpriseInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface EnterpriseInfoDtoConverter {


    EnterpriseInfoDtoConverter INSTANCE = Mappers.getMapper(EnterpriseInfoDtoConverter.class);


    EnterpriseInfo toModel(EnterpriseInfoDTO data);

    EnterpriseInfoDTO toDto(EnterpriseInfo data);

    List<EnterpriseInfo> toModelList(List<EnterpriseInfoDTO> dataList);

    List<EnterpriseInfoDTO> e2tList(List<EnterpriseInfo> enterpriseInfos);
}
