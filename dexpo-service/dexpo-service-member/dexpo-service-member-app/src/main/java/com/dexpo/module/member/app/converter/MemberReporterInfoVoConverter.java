package com.dexpo.module.member.app.converter;

import com.dexpo.module.member.api.vo.member.MemberReporterInfoVO;
import com.dexpo.module.member.domain.model.member.MemberReporterInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MemberReporterInfoVoConverter {


    MemberReporterInfoVoConverter INSTANCE = Mappers.getMapper(MemberReporterInfoVoConverter.class);


    MemberReporterInfoVO toVo(MemberReporterInfo data);
}
