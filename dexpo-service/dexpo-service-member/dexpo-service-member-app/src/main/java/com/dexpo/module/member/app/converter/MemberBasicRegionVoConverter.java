package com.dexpo.module.member.app.converter;

import com.dexpo.module.base.api.basic.vo.BasicRegionVO;
import com.dexpo.module.member.api.dto.media.MemberBasicRegionVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MemberBasicRegionVoConverter {


    MemberBasicRegionVoConverter INSTANCE = Mappers.getMapper(MemberBasicRegionVoConverter.class);


    MemberBasicRegionVO toMemberBasicRegionVo(BasicRegionVO data);
}
