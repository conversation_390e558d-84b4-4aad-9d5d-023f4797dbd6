package com.dexpo.module.member.app.converter;

import com.dexpo.module.member.api.vo.member.MemberRecipientInfoVO;
import com.dexpo.module.member.domain.model.member.MemberRecipientInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MediaRegisterInfoVoConverter {


    MediaRegisterInfoVoConverter INSTANCE = Mappers.getMapper(MediaRegisterInfoVoConverter.class);


    MemberRecipientInfoVO toVo(MemberRecipientInfo data);
}
