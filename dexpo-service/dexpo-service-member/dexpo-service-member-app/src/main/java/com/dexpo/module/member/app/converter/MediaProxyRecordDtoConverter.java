package com.dexpo.module.member.app.converter;

import com.dexpo.module.member.api.dto.media.MediaProxyRecordDTO;
import com.dexpo.module.member.domain.model.member.MemberMediaProxyRecord;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MediaProxyRecordDtoConverter {


    MediaProxyRecordDtoConverter INSTANCE = Mappers.getMapper(MediaProxyRecordDtoConverter.class);


    MemberMediaProxyRecord toModel(MediaProxyRecordDTO data);
}
