package com.dexpo.module.member.app.converter;

import com.dexpo.module.member.api.dto.member.MemberReporterInfoDTO;
import com.dexpo.module.member.domain.model.member.MemberReporterInfo;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MemberReporterInfoDtoConverter {


    MemberReporterInfoDtoConverter INSTANCE = Mappers.getMapper(MemberReporterInfoDtoConverter.class);


    MemberReporterInfo toModel(MemberReporterInfoDTO data);
}
