package com.dexpo.module.member.app.converter;

import com.dexpo.module.member.api.dto.member.MemberBaseInfoDTO;
import com.dexpo.module.member.domain.model.member.MemberBaseInfo;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MemberBaseInfoDtoConverter {


    MemberBaseInfoDtoConverter INSTANCE = Mappers.getMapper(MemberBaseInfoDtoConverter.class);


    MemberBaseInfo toDto(MemberBaseInfoDTO data);

    List<MemberBaseInfoDTO> toDtoList(List<MemberBaseInfo> dataList);
}
