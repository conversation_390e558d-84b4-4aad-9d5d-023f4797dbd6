package com.dexpo.module.member.app.converter;

import com.dexpo.framework.security.core.LoginUser;
import com.dexpo.module.member.api.vo.SponsorInfoVO;
import com.dexpo.module.member.domain.model.SponsorInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * sponsor
 *
 * <AUTHOR> Xiaohua 20/06/2025 11:26
 **/
@Mapper
public interface SponsorVOConverter {

    SponsorVOConverter INSTANCE = Mappers.getMapper(SponsorVOConverter.class);

    SponsorInfoVO entityToVO(SponsorInfo sponsorInfo);

    @Mapping(source = "sponsorCode", target = "memberCode")
    @Mapping(source = "sponsorName", target = "userName")
    @Mapping(source = "sponsorEmail", target = "memberEmail")
    @Mapping(source = "sponsorMobile", target = "memberMobile")
    @Mapping(target = "userType", expression = "java(com.dexpo.framework.common.enums.ValueSetUserTypeEnum.SPONSOR.getCode())")
    LoginUser entityToLoginUser(SponsorInfo sponsorInfo);

}
