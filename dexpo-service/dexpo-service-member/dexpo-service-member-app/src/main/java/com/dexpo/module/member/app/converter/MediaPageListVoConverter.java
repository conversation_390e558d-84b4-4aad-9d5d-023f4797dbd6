package com.dexpo.module.member.app.converter;

import com.dexpo.module.member.api.vo.media.MediaPageListVO;
import com.dexpo.module.member.domain.model.media.MediaPageList;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MediaPageListVoConverter {


    MediaPageListVoConverter INSTANCE = Mappers.getMapper(MediaPageListVoConverter.class);


    List<MediaPageListVO> toVoList(List<MediaPageList> data);
}
