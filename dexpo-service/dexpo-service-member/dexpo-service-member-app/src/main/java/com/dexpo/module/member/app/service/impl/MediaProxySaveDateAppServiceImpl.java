package com.dexpo.module.member.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import com.dexpo.framework.cache.redis.entity.exhibition.ExhibitionInfoCache;
import com.dexpo.framework.cache.redis.operate.exhibition.ExhibitionInfoCacheOpt;
import com.dexpo.framework.common.enums.RegisterLanguageEnum;
import com.dexpo.framework.common.enums.RegisterMethodEnum;
import com.dexpo.framework.common.enums.RegisterSourceEnum;
import com.dexpo.framework.common.enums.RegisterSystemEnum;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.base.enums.MediaCategoryEnums;
import com.dexpo.module.base.enums.MediaPositionEnums;
import com.dexpo.module.exhibition.api.ExhibitionApi;
import com.dexpo.module.exhibition.api.vo.ExhibitionVO;
import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseSyncMessage;
import com.dexpo.module.member.api.dto.media.MediaProxyActionDTO;
import com.dexpo.module.member.api.dto.media.MediaProxyRegistrationDTO;
import com.dexpo.module.member.api.dto.member.MemberBaseInfoDTO;
import com.dexpo.module.member.app.converter.MediaProxyRegistrationDtoConverter;
import com.dexpo.module.member.app.converter.MemberBaseInfoDtoConverter;
import com.dexpo.module.member.app.service.MediaProxySaveDateAppService;
import com.dexpo.module.member.domain.constant.MediaProxyConstant;
import com.dexpo.module.member.domain.enums.ActionUserTypeEnum;
import com.dexpo.module.member.domain.enums.RegisterStatusEnum;
import com.dexpo.module.member.domain.model.member.MemberBaseInfo;
import com.dexpo.module.member.domain.model.member.MemberParticipateRecord;
import com.dexpo.module.member.domain.model.member.MemberReporterInfo;
import com.dexpo.module.member.domain.model.member.message.MemberDataWarehouseSyncMessage;
import com.dexpo.module.member.domain.repository.MemberApproveRecordRepository;
import com.dexpo.module.member.domain.repository.MemberBaseInfoRepository;
import com.dexpo.module.member.domain.repository.MemberParticipateRecordRepository;
import com.dexpo.module.member.domain.repository.MemberReporterInfoRepository;
import com.dexpo.module.member.infrastructure.converter.DataWarehouseSyncMessageConverter;
import com.dexpo.module.member.infrastructure.mq.producer.DataWarehouseChannelProducer;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Validated
@Slf4j
public class MediaProxySaveDateAppServiceImpl implements MediaProxySaveDateAppService {

    @Resource
    private MemberBaseInfoRepository memberBaseInfoRepository;

    @Resource
    private DataWarehouseChannelProducer dataWarehouseChannelProducer;

    @Resource
    private MemberReporterInfoRepository reporterInfoRepository;

    @Resource
    private MemberParticipateRecordRepository memberParticipateRecordRepository;

    @Override
    public void batchAddNewMediaUser(MediaProxyActionDTO mediaProxyActionDTO) {
        List<MemberBaseInfo> memberBaseInfoDOList = MediaProxyRegistrationDtoConverter.INSTANCE
                .toMemberBaseInfoList(mediaProxyActionDTO.getMediaProxyRegistrationList());
        if (CollUtil.isEmpty(memberBaseInfoDOList)) {
            return;
        }
        for (MemberBaseInfo e : memberBaseInfoDOList) {
            e.setMemberCode(UUID.fastUUID().toString().replace("-", ""));
        }
        memberBaseInfoDOList = memberBaseInfoRepository.addBatch(memberBaseInfoDOList);
        log.info("memberId -> {}", memberBaseInfoDOList);
        mediaProxyActionDTO.setMemberBaseInfoDOList(MemberBaseInfoDtoConverter.INSTANCE.toDtoList(memberBaseInfoDOList));
    }

    @Override
    public void batchAddNewMemberReporterInfo(MediaProxyActionDTO mediaProxyActionDTO, Map<String, String> fileMap) {
        List<MemberReporterInfo> reporterInfoDOList = new ArrayList<>();
        List<MemberBaseInfoDTO> memberBaseInfoDOList = mediaProxyActionDTO.getMemberBaseInfoDOList();

        for (MemberBaseInfoDTO baseInfoDO : memberBaseInfoDOList) {
            MemberReporterInfo memberReporterInfo = new MemberReporterInfo();
            //整理信息
            MediaProxyRegistrationDTO mediaProxyRegistrationMapOne = mediaProxyActionDTO
                    .getMediaProxyRegistrationMapOne(baseInfoDO.getMemberMobile(), baseInfoDO.getMemberEmail());
            memberReporterInfo.setMemberId(baseInfoDO.getId());
            memberReporterInfo.setExhibitionId(mediaProxyActionDTO.getExhibitionId());
            memberReporterInfo.setEnterpriseId(mediaProxyRegistrationMapOne.getEnterpriseId());
            memberReporterInfo.setMediaPermissionType(mediaProxyRegistrationMapOne.getMediaPermissionType());
            memberReporterInfo.setOtherMediaPosition(mediaProxyRegistrationMapOne.getOtherMediaPosition());
            memberReporterInfo.setIsApplyLiveStream(mediaProxyRegistrationMapOne.getIsApplyLiveStream());
            memberReporterInfo.setMediaNewsmanNo(mediaProxyRegistrationMapOne.getMediaNewsmanNo());
            MediaCategoryEnums mediaCategoryEnums = MediaPositionEnums
                    .getByCode(memberReporterInfo.getMediaPositionCode()).getMediaCategoryEnums();
            memberReporterInfo.setMediaPositionCategoryCode(mediaCategoryEnums.getCode());
            memberReporterInfo.setMediaPositionCategoryNameEn(mediaCategoryEnums.getDescriptionEN());
            memberReporterInfo.setMediaPositionCategoryNameCn(mediaCategoryEnums.getDescriptionCN());
            memberReporterInfo.setMediaPositionCode(mediaProxyRegistrationMapOne.getMediaPositionCode());
            memberReporterInfo.setMediaPositionNameEn(mediaProxyRegistrationMapOne.getMediaPositionNameEn());
            memberReporterInfo.setMediaPositionNameCn(mediaProxyRegistrationMapOne.getMediaPositionNameCn());
            memberReporterInfo.setMediaTypeCode(mediaProxyRegistrationMapOne.getMediaTypeCode());
            memberReporterInfo.setMediaTypeNameEn(mediaProxyRegistrationMapOne.getMediaTypeNameEn());
            memberReporterInfo.setMediaTypeNameCn(mediaProxyRegistrationMapOne.getMediaTypeNameCn());
            // 保存头像
            savePhoto(fileMap, baseInfoDO, memberReporterInfo);

            reporterInfoDOList.add(memberReporterInfo);
        }

        // 批量新增
        reporterInfoRepository.addBatch(reporterInfoDOList);
    }

    private void savePhoto(Map<String, String> fileMap, MemberBaseInfoDTO baseInfoDO, MemberReporterInfo memberReporterInfo) {
        StringBuilder attachmentOtherDescribe = new StringBuilder();
        if (CollUtil.isEmpty(fileMap)) {
            return;
        }

        for (Map.Entry<String, String> entry : fileMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (key.contains(baseInfoDO.getIdNumber())) {
                if (key.contains(MediaProxyConstant.TYPE_ID_PHOTO)) {
                    memberReporterInfo.setAttachmentHeadPhoto(value);
                }
                if (key.contains(MediaProxyConstant.TYPE_ATTACHMENTS)) {
                    attachmentOtherDescribe.append(value).append(",");
                }
            }
        }
        if (!attachmentOtherDescribe.isEmpty()) {
            memberReporterInfo.setAttachmentOtherDescribe(
                    attachmentOtherDescribe.deleteCharAt(attachmentOtherDescribe.length() - 1).toString());
        }
    }

    @Override
    public void batchAddNewMemberParticipateRecord(MediaProxyActionDTO mediaProxyActionDTO) {
        List<MemberParticipateRecord> memberParticipateRecordDOList = new ArrayList<>();
        List<MemberBaseInfoDTO> memberBaseInfoDOList = mediaProxyActionDTO.getMemberBaseInfoDOList();
        for (MemberBaseInfoDTO baseInfoDO : memberBaseInfoDOList) {
            MemberParticipateRecord recordDO = new MemberParticipateRecord();
            recordDO.setMemberId(baseInfoDO.getId());
            recordDO.setExhibitionId(mediaProxyActionDTO.getExhibitionId());
            recordDO.setMemberType(ActionUserTypeEnum.MEDIA_USER.getCode());
            recordDO.setRegisterTime(LocalDateTime.now());
            recordDO.setRegisterSource(RegisterSourceEnum.NORMAL.getCode());
            recordDO.setRegisterMethod(RegisterMethodEnum.AGENT_REGISTER.getCode());
            recordDO.setRegisterLanguage(RegisterLanguageEnum.CHINESE.getCode());

            recordDO.setRegisterStatus(RegisterStatusEnum.APPROVED.getCode());
            recordDO.setRegisterSystem(RegisterSystemEnum.CENTRAL_PLATFORM.getCode());
            recordDO.setCreateUser(baseInfoDO.getId());
            recordDO.setCreateTime(LocalDateTime.now());
            recordDO.setDelFlg(Boolean.FALSE);
            memberParticipateRecordDOList.add(recordDO);
        }
        memberParticipateRecordRepository.addBatch(memberParticipateRecordDOList);
    }

    @Resource
    private MemberApproveRecordRepository memberApproveRecordRepository;
    @Resource
    private ExhibitionInfoCacheOpt exhibitionInfoOpt;
    @Resource
    private ExhibitionApi exhibitionApi;

    @Override
    public void batchAddNewMemberDataWarehouse(MediaProxyActionDTO mediaProxyActionDTO) {
        List<MemberBaseInfoDTO> memberBaseInfoDOList = mediaProxyActionDTO.getMemberBaseInfoDOList();
        List<Long> ids = memberBaseInfoDOList.stream().map(MemberBaseInfoDTO::getId).toList();
        List<MemberDataWarehouseSyncMessage> messages = memberApproveRecordRepository.selectDataWarehouseSyncMessageList(ids, List.of(RegisterStatusEnum.APPROVED.getCode()), mediaProxyActionDTO.getExhibitionId());
        log.info("auditApproved: {}", messages);
        List<DataWarehouseSyncMessage> dataWarehouseSyncMessages = DataWarehouseSyncMessageConverter.INSTANCE.toDataWarehouseSyncMessages(messages);
        syncMediaInfoListToDataWarehouse(dataWarehouseSyncMessages, mediaProxyActionDTO.getExhibitionId());
    }

    private void syncMediaInfoListToDataWarehouse(List<DataWarehouseSyncMessage> messages, Long exhibitionId) {
        if (CollectionUtils.isEmpty(messages)) {
            return;
        }
        ExhibitionInfoCache exhibitionInfoCache = exhibitionInfoOpt.getExhibitionInfoCacheById(exhibitionId);
        if (ObjectUtils.isEmpty(exhibitionInfoCache)) {
            CommonResult<ExhibitionVO> result = exhibitionApi.getExhibitionById(exhibitionId);
            if (result != null && result.isSuccess()) {
                ExhibitionVO exhibitionVO = result.getData();
                if (exhibitionVO != null) {
                    exhibitionInfoCache.setExhibitionNameCn(exhibitionVO.getExhibitionNameCn());
                    exhibitionInfoCache.setExhibitionNameEn(exhibitionVO.getExhibitionNameEn());
                    exhibitionInfoCache.setExhibitionCode(exhibitionVO.getExhibitionCode());
                    exhibitionInfoCache.setExhibitionSessionKey(exhibitionVO.getExhibitionSessionKey());
                    exhibitionInfoCache.setExhibitionTagCode(exhibitionVO.getExhibitionTagCode());
                }
            }
        }

        messages.forEach(message -> {
            //添加countryName和展会相关信息
            //添加展会相关信息
            message.setExhibitionNameCn(exhibitionInfoCache.getExhibitionNameCn());
            message.setExhibitionNameEn(exhibitionInfoCache.getExhibitionNameEn());
            message.setExhibitionCode(exhibitionInfoCache.getExhibitionCode());
            message.setExhibitionSessionKey(exhibitionInfoCache.getExhibitionSessionKey());
            message.setExhibitionTagCode(exhibitionInfoCache.getExhibitionTagCode());
            log.info("appendDataWarehouseSyncMessageInfo: {}", message);
            try {
                dataWarehouseChannelProducer.dataWarehouseChannel(message);
            } catch (Exception e) {
                log.error("Failed to send message to data warehouse channel", e);
                // 不抛出异常，避免影响主业务流程
            }
        });

    }
}
