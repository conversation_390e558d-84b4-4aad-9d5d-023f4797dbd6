package com.dexpo.module.member.app.converter;

import com.dexpo.module.base.api.attachment.vo.AttachmentInfoVO;
import com.dexpo.module.member.api.vo.member.MemberAttachmentInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MemberAttachmentInfoVoConverter {


    MemberAttachmentInfoVoConverter INSTANCE = Mappers.getMapper(MemberAttachmentInfoVoConverter.class);


    MemberAttachmentInfoVO toVo(AttachmentInfoVO data);

    List<MemberAttachmentInfoVO> toVoList(List<AttachmentInfoVO> dataList);
}
