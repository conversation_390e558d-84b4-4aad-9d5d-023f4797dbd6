package com.dexpo.module.member.app.converter;

import com.dexpo.module.member.api.dto.member.MemberRecipientInfoDTO;
import com.dexpo.module.member.domain.model.member.MemberRecipientInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MemberRecipientInfoDtoConverter {


    MemberRecipientInfoDtoConverter INSTANCE = Mappers.getMapper(MemberRecipientInfoDtoConverter.class);


    MemberRecipientInfo toModel(MemberRecipientInfoDTO data);
}
