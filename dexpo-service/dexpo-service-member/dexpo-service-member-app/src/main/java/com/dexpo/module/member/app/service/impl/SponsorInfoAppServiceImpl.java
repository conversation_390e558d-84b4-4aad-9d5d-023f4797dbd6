package com.dexpo.module.member.app.service.impl;

import com.dexpo.framework.cache.redis.constant.ICacheKey;
import com.dexpo.framework.cache.redis.constant.MemberRedisKey;
import com.dexpo.framework.cache.redis.entity.sponsor.SponsorProfileCache;
import com.dexpo.framework.cache.redis.operate.base.ValidCodeOpt;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.exception.enums.IntegrationServiceErrorCodeEnum;
import com.dexpo.framework.common.exception.enums.MemberServiceErrorCodeEnum;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.message.core.MessageProducer;
import com.dexpo.framework.message.enums.ValidScenarioEnum;
import com.dexpo.framework.message.handler.ValidMessageHandler;
import com.dexpo.framework.security.core.LoginUser;
import com.dexpo.framework.security.core.service.TokenService;
import com.dexpo.framework.security.core.util.SecurityFrameworkUtils;
import com.dexpo.module.integration.api.message.dto.ValidCodeDTO;
import com.dexpo.module.member.api.dto.SponsorInfoStatusDTO;
import com.dexpo.module.member.api.dto.SponsorLoginDTO;
import com.dexpo.module.member.api.dto.SponsorLoginExhibitionTagDTO;
import com.dexpo.module.member.api.dto.SponsorInfoDTO;
import com.dexpo.module.member.api.dto.SponsorUpdateInfoDTO;
import com.dexpo.module.member.api.vo.SponsorAllInfoVO;
import com.dexpo.module.member.api.vo.SponsorInfoVO;
import com.dexpo.module.member.api.vo.SponsorLoginVO;
import com.dexpo.module.member.app.converter.SponsorInfoDTOConvert;
import com.dexpo.module.member.app.converter.SponsorVOConverter;
import com.dexpo.module.member.app.service.SponsorInfoAppService;
import com.dexpo.module.member.domain.model.SponsorInfo;
import com.dexpo.module.member.domain.model.member.SponsorAllInfo;
import com.dexpo.module.member.domain.service.SponsorInfoDomainService;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Xiaohua 20/06/2025 11:19
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class SponsorInfoAppServiceImpl implements SponsorInfoAppService {

    private static final String DEFAULT_LOGIN_EXHIBITION_TAG_CODE = "CIIF";

    private final SponsorInfoDomainService sponsorInfoDomainService;
    private final MessageProducer messageProducer;
    private final ValidCodeOpt validCodeOpt;
    private final RedisService redisService;
    private final TokenService tokenService;
    private final ValidMessageHandler validMessageHandler;

    @Override
    public Boolean loginValidCode(ValidCodeDTO dto) {

        SponsorInfo sponsorInfo = sponsorInfoDomainService.getSponsorUser(dto.getText());
        if (!Objects.equals(true, sponsorInfo.getIsUseAble())) {
            // 用户已禁用，请联系管理员
            throw new ServiceException(MemberServiceErrorCodeEnum.MEMBER_SPONSOR_USER_DISABLE);
        }
        validMessageHandler.sendValidMessage(ValidScenarioEnum.SPONSOR_LOGIN.getCode(),
                dto.getText(), null, null);
        return true;
    }

    @Override
    public SponsorLoginVO login(SponsorLoginDTO loginDTO) {

        boolean isValid = validCodeOpt.validCode(loginDTO.getLoginTool(), loginDTO.getValidCode());
        if (!isValid) {
            // 认证失败
            throw new ServiceException(MemberServiceErrorCodeEnum.MEMBER_SPONSOR_LOGIN_VALID_ERROR);
        }
        SponsorInfo sponsorUser = sponsorInfoDomainService.getSponsorUser(loginDTO.getLoginTool());
        if (sponsorUser == null) {
            //
            throw new ServiceException(MemberServiceErrorCodeEnum.MEMBER_SPONSOR_USER_NOT_EXIST);
        }

        SponsorLoginVO result = new SponsorLoginVO();
        SponsorInfoVO sponsorInfoVO = SponsorVOConverter.INSTANCE.entityToVO(sponsorUser);
        result.setSponsorInfoVO(sponsorInfoVO);
        LoginUser loginUser = SponsorVOConverter.INSTANCE.entityToLoginUser(sponsorUser);
        String token = tokenService.createToken(loginUser);
        result.setExhibitionTagCode(DEFAULT_LOGIN_EXHIBITION_TAG_CODE);
        result.setToken(token);
        this.chooseTag(DEFAULT_LOGIN_EXHIBITION_TAG_CODE, loginUser.getId());
        return result;
    }

    @Override
    public void logout() {
       //登出
    }

    @Override
    public void chooseTag(SponsorLoginExhibitionTagDTO req) {
        chooseTag(req.getExhibitionTagCode(), SecurityFrameworkUtils.getLoginUserId());
    }

    @Override
    public CommonResult<List<SponsorAllInfoVO>> sponsorPageInfo(SponsorInfoDTO req) {
        log.info("开始查询运营人员列表, 查询条件: {}", req);
        SponsorAllInfo convertReq = SponsorInfoDTOConvert.INSTANCE.convertDTO(req);
        List<SponsorAllInfo> sponsorAllInfoPage = sponsorInfoDomainService.sponsorPageInfo(convertReq);
        // 转换为VO列表
        List<SponsorAllInfoVO> voList = sponsorAllInfoPage.stream()
            .map(SponsorInfoDTOConvert.INSTANCE::entityToVO)
            .toList();

        return CommonResult.success(voList);
    }

    @Override
    public CommonResult<Boolean> updateStatus(SponsorInfoStatusDTO req) {
        log.info("开始更新运营人员状态, 状态: {}, userId: {}", req.getStatus(), req.getSponsorCode());
        SponsorAllInfo convertReq = SponsorInfoDTOConvert.INSTANCE.convertStatusDTO(req);
        return CommonResult.success(sponsorInfoDomainService.updateStatus(convertReq));

    }

    @Override
    public CommonResult<Boolean> updateSponsor(SponsorUpdateInfoDTO req) {
        log.info("开始更新运营人员信息, userId: {}", req.getSponsorCode());

        // 参数校验
        // 1. name 支持中文、字母、"·"输入，限20个字符（可为空）
        if (req.getName() != null && !req.getName().isEmpty() && !req.getName().matches("^[A-Za-z\u4e00-\u9fa5·]{1,20}$")) {
            throw new ServiceException(MemberServiceErrorCodeEnum.MEMBER_SPONSOR_NAME_INVALID);
        }
        // 2. mobile 11位国内手机号正则校验（可为空）
        if (req.getMobile() != null && !req.getMobile().isEmpty() && !req.getMobile().matches("^1[3-9]\\d{9}$")) {
            throw new ServiceException(MemberServiceErrorCodeEnum.MEMBER_SPONSOR_MOBILE_INVALID);
        }
        // 3. email 必须包含"@"符号，邮箱正则校验（可为空）
        if (req.getEmail() != null && !req.getEmail().isEmpty() && !req.getEmail().matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+$")) {
            throw new ServiceException(MemberServiceErrorCodeEnum.MEMBER_SPONSOR_EMAIL_INVALID);
        }
        SponsorAllInfo convertReq = SponsorInfoDTOConvert.INSTANCE.convertSponsorUpdateInfoDTO(req);
        return CommonResult.success(sponsorInfoDomainService.updateSponsor(convertReq));
    }

    private void chooseTag(String exhibitionTagCode, Long userId) {

        log.info("运营人员选择会展标签 userId:{},tag:{}", userId, exhibitionTagCode);
        String key = ICacheKey.generateKey(MemberRedisKey.MEMBER_SPONSOR_USER_INFO, String.valueOf(userId));
        SponsorProfileCache cache = new SponsorProfileCache();
        cache.setExhibitionTagCode(exhibitionTagCode);
        redisService.setCacheObject(key, cache);
    }
}
