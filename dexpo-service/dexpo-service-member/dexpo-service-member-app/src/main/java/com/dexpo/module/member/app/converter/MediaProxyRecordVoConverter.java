package com.dexpo.module.member.app.converter;

import com.dexpo.module.member.api.vo.media.MediaProxyRecordVO;
import com.dexpo.module.member.infrastructure.dal.dataobject.MemberMediaProxyRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MediaProxyRecordVoConverter {


    MediaProxyRecordVoConverter INSTANCE = Mappers.getMapper(MediaProxyRecordVoConverter.class);


    List<MediaProxyRecordVO> toVoList(List<MemberMediaProxyRecordDO> dataList);
}
