package com.dexpo.module.member.app.converter;

import com.dexpo.module.member.api.dto.EnterpriseInfoQueryDTO;
import com.dexpo.module.member.domain.model.enterprise.EnterpriseInfoQuery;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface EnterpriseInfoQueryDtoConverter {


    EnterpriseInfoQueryDtoConverter INSTANCE = Mappers.getMapper(EnterpriseInfoQueryDtoConverter.class);

    @Mappings({})
    EnterpriseInfoQuery toModel(EnterpriseInfoQueryDTO data);
}
