package com.dexpo.module.member.app.converter;

import com.dexpo.framework.cache.redis.entity.MemberBaseInfoCache;
import com.dexpo.module.member.domain.model.member.MemberBaseInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MemberBaseInfoCacheConverter {


    MemberBaseInfoCacheConverter INSTANCE = Mappers.getMapper(MemberBaseInfoCacheConverter.class);

    @Mappings({})
    MemberBaseInfoCache toMemberBaseInfoCache(MemberBaseInfo data);

    @Mappings({})
    MemberBaseInfo toMemberBaseInfo(MemberBaseInfoCache data);

}
