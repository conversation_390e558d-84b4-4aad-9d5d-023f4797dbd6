package com.dexpo.module.member.app.converter;

import com.dexpo.module.base.api.basic.vo.BasicLocationVO;
import com.dexpo.module.member.api.dto.media.MemberBasicLocationVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MemberBasicLocationVoConverter {


    MemberBasicLocationVoConverter INSTANCE = Mappers.getMapper(MemberBasicLocationVoConverter.class);


    List<MemberBasicLocationVO> toMemberBasicLocationVoList(List<BasicLocationVO> dataList);
}
