package com.dexpo.module.member.app.converter;

import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseSyncMessage;
import com.dexpo.module.member.api.dto.member.MemberRegisterInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MemberRegisterInfoDtoConverter {


    MemberRegisterInfoDtoConverter INSTANCE = Mappers.getMapper(MemberRegisterInfoDtoConverter.class);


    DataWarehouseSyncMessage toDataWarehouseSyncMessage(MemberRegisterInfoDTO data);
}
