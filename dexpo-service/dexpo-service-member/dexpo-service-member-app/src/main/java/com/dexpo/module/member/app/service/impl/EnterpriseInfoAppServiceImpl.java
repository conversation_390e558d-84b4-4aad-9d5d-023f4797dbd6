package com.dexpo.module.member.app.service.impl;

import com.dexpo.module.member.api.dto.EnterpriseInfoQueryDTO;
import com.dexpo.module.member.api.vo.EnterpriseInfoVO;
import com.dexpo.module.member.app.converter.EnterpriseInfoQueryDtoConverter;
import com.dexpo.module.member.app.converter.EnterpriseInfoVoConverter;
import com.dexpo.module.member.app.service.EnterpriseInfoAppService;
import com.dexpo.module.member.domain.model.enterprise.EnterpriseInfo;
import com.dexpo.module.member.domain.model.enterprise.EnterpriseInfoQuery;
import com.dexpo.module.member.domain.repository.EnterpriseInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 企业信息 Service 实现类
 */
@Service
@RequiredArgsConstructor
public class EnterpriseInfoAppServiceImpl implements EnterpriseInfoAppService {

    private final EnterpriseInfoRepository enterpriseInfoRepository;


    @Override
    public List<EnterpriseInfoVO> getEnterpriseInfoList(EnterpriseInfoQueryDTO infoQueryDTO) {
        EnterpriseInfoQuery infoQuery = EnterpriseInfoQueryDtoConverter.INSTANCE.toModel(infoQueryDTO);
        List<EnterpriseInfo> enterpriseDatas = enterpriseInfoRepository.getEnterpriseInfoList(infoQuery);
        return EnterpriseInfoVoConverter.INSTANCE.toVoList(enterpriseDatas);
    }


} 