package com.dexpo.module.member.app.service.impl;

import com.dexpo.module.member.api.dto.EnterpriseInfoQueryDTO;
import com.dexpo.module.member.api.vo.EnterpriseInfoVO;
import com.dexpo.module.member.app.converter.EnterpriseInfoQueryDtoConverter;
import com.dexpo.module.member.app.converter.EnterpriseInfoVoConverter;
import com.dexpo.module.member.app.service.EnterpriseInfoAppService;
import com.dexpo.module.member.domain.model.enterprise.EnterpriseInfo;
import com.dexpo.module.member.domain.model.enterprise.EnterpriseInfoQuery;
import com.dexpo.module.member.domain.repository.EnterpriseInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 企业信息 Service 实现类
 */
@Service
@RequiredArgsConstructor
public class EnterpriseInfoAppServiceImpl implements EnterpriseInfoAppService {

    private final EnterpriseInfoRepository enterpriseInfoRepository;


    @Override
    public List<EnterpriseInfoVO> getEnterpriseInfoList(EnterpriseInfoQueryDTO infoQueryDTO) {
        EnterpriseInfoQuery infoQuery = EnterpriseInfoQueryDtoConverter.INSTANCE.toModel(infoQueryDTO);
        List<EnterpriseInfo> enterpriseDatas = enterpriseInfoRepository.getEnterpriseInfoList(infoQuery);
        //遍历集合 去除enterpriseName重复的项
        List<EnterpriseInfo> enterpriseDataFilterDuplicate = enterpriseDatas.stream()
                .collect(Collectors.toMap(
                        EnterpriseInfo::getEnterpriseName,  // 以enterpriseName作为key
                        enterpriseInfo -> enterpriseInfo,   // 值就是对象本身
                        (existing, replacement) -> existing // 如果有重复的key，保留第一个
                ))
                .values()
                .stream()
                .collect(Collectors.toList());
        return EnterpriseInfoVoConverter.INSTANCE.toVoList(enterpriseDataFilterDuplicate);
    }




} 