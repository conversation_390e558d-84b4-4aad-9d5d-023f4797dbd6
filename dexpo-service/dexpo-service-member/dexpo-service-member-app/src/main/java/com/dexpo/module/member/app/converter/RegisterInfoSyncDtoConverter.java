package com.dexpo.module.member.app.converter;

import com.dexpo.module.member.api.dto.RegisterInfoSyncDTO;
import com.dexpo.module.member.domain.model.member.MemberParticipateRecord;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface RegisterInfoSyncDtoConverter {


    RegisterInfoSyncDtoConverter INSTANCE = Mappers.getMapper(RegisterInfoSyncDtoConverter.class);


    RegisterInfoSyncDTO toDto(MemberParticipateRecord data);
}
