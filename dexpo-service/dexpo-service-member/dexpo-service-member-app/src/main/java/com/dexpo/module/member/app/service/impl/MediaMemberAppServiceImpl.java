package com.dexpo.module.member.app.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.UUID;
import com.dexpo.framework.cache.redis.entity.MemberBaseInfoCache;
import com.dexpo.framework.cache.redis.entity.exhibition.ExhibitionInfoCache;
import com.dexpo.framework.cache.redis.operate.base.ValidCodeOpt;
import com.dexpo.framework.cache.redis.operate.exhibition.ExhibitionInfoCacheOpt;
import com.dexpo.framework.cache.redis.operate.member.MemberBaseInfoOpt;
import com.dexpo.framework.common.enums.*;
import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.exception.enums.MemberServiceErrorCodeEnum;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.framework.common.util.validation.ValidationUtils;
import com.dexpo.framework.message.enums.ValidScenarioEnum;
import com.dexpo.framework.security.core.LoginUser;
import com.dexpo.framework.security.core.service.TokenService;
import com.dexpo.framework.security.core.util.SecurityFrameworkUtils;
import com.dexpo.module.base.api.attachment.AttachmentApi;
import com.dexpo.module.base.api.attachment.vo.AttachmentInfoVO;
import com.dexpo.module.exhibition.api.ExhibitionApi;
import com.dexpo.module.exhibition.api.dto.ExhibitionQueryDTO;
import com.dexpo.module.exhibition.api.vo.ExhibitionVO;
import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseSyncMessage;
import com.dexpo.module.integration.api.verification.VerificationApi;
import com.dexpo.module.integration.api.verification.dto.VerificationDTO;
import com.dexpo.module.integration.api.verification.vo.VerificationVO;
import com.dexpo.module.member.api.dto.media.*;
import com.dexpo.module.member.api.dto.member.MemberRegisterInfoDTO;
import com.dexpo.module.member.api.dto.message.MediaRegisterEventDTO;
import com.dexpo.module.member.api.vo.LoginInfoResVO;
import com.dexpo.module.member.api.vo.media.*;
import com.dexpo.module.member.api.vo.member.MemberReporterInfoVO;
import com.dexpo.module.member.app.converter.*;
import com.dexpo.module.member.app.external.exhibition.ExhibitionExternalService;
import com.dexpo.module.member.enums.MediaPermissionTypeEnums;
import com.dexpo.module.member.infrastructure.mq.producer.DataWarehouseChannelProducer;
import com.dexpo.module.member.infrastructure.mq.producer.MediaRegisterStatusEventProducer;
import com.dexpo.module.member.app.service.MediaMemberAppService;
import com.dexpo.module.member.domain.enums.*;
import com.dexpo.module.member.app.handler.MessageHandler;
import com.dexpo.module.member.domain.model.enterprise.EnterpriseInfo;
import com.dexpo.module.member.domain.model.media.MediaPageList;
import com.dexpo.module.member.domain.model.media.MediaPageListQuery;
import com.dexpo.module.member.domain.model.member.*;
import com.dexpo.module.member.domain.model.member.message.MemberDataWarehouseSyncMessage;
import com.dexpo.module.member.domain.repository.*;
import com.dexpo.module.member.domain.service.MediaMemberDomainService;
import com.dexpo.module.member.domain.service.MemberBaseInfoDomainService;
import com.dexpo.module.member.domain.enums.RegisterStatusEnum;
import com.dexpo.module.member.infrastructure.converter.DataWarehouseSyncMessageConverter;
import com.dexpo.module.member.infrastructure.converter.MemberBaseInfoVoConverter;
import com.dexpo.module.member.infrastructure.dal.mysql.MemberParticipateRecordMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Validated
@Slf4j
public class MediaMemberAppServiceImpl implements MediaMemberAppService {


    @Resource
    private MemberBaseInfoRepository memberBaseInfoRepository;



    @Resource
    private ValidCodeOpt validCodeOpt;

    @Resource
    private MemberBaseInfoOpt memberBaseInfoOpt;

    @Resource
    private TokenService tokenService;



    @Resource
    private MemberReporterInfoRepository memberReporterInfoRepository;

    @Resource
    private AttachmentApi attachmentApi;

    @Resource
    private MemberParticipateRecordMapper participateRecordMapper;
    
    @Resource
    private MemberParticipateRecordRepository memberParticipateRecordRepository;



    @Resource
    private VerificationApi verificationApi;

    @Resource
    private ExhibitionInfoCacheOpt exhibitionInfoOpt;

    @Resource
    private ExhibitionExternalService exhibitionExternalService;

    @Resource
    private DataWarehouseChannelProducer dataWarehouseChannelProducer;

    @Resource
    private MemberBaseInfoDomainService baseInfoService;

    @Resource
    private MediaRegisterStatusEventProducer mediaRegisterStatusEventProducer;


    @Resource
    private MemberApproveRecordRepository memberApproveRecordRepository;

    @Resource
    private ExhibitionApi exhibitionApi;

    @Resource
    private MessageHandler messageHandler;

    @Resource
    private MediaMemberDomainService mediaMemberDomainService;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT)
    public LoginInfoResVO mediaLogin(MediaMemberLoginDTO loginDTO) {
        log.info("mediaLogin login starting");
        // 验证码校验
        checkValidCode(loginDTO);
        // 校验邮箱或手机号
        String loginTool = loginDTO.getLoginTool();
        LoginInfoResVO loginInfoResVO = new LoginInfoResVO();
        MemberBaseInfo baseInfoDO = checkLoginToolAndInitMemberBaseInfo(loginTool, loginInfoResVO);
        Long id;
        MemberBaseInfo oldBaseInfoDO = getOldMemberBaseInfoDO(loginTool);
        boolean isNewMember;
        if(oldBaseInfoDO == null){//说明是新用户
            /**
             * 这里catch DuplicateKeyException是因为有可能极端情形下会
             * 出现在redis没有而在数据量有数据的状况,这里如果触发了手机号或邮箱的唯一
             * 索引异常则说明该数据已经在数据库存在了 需要查询出这个数据并set到redis中
             */
            try {
                memberBaseInfoRepository.add(baseInfoDO);
                isNewMember = Boolean.TRUE;
            }catch (DuplicateKeyException e){
                log.info("mediaLogin duplicate key"+e);
                isNewMember = Boolean.FALSE;
                baseInfoDO = memberBaseInfoRepository.getMemberBaseInfoByMobileOrEmail(loginTool);
            }
            id = baseInfoDO.getId();

            // 将新用户写到redis中
            memberBaseInfoOpt.setMemberBaseInfoCache(MemberBaseInfoCacheConverter.INSTANCE.toMemberBaseInfoCache(baseInfoDO),
                    loginTool);
        } else {// 老用户
            id = oldBaseInfoDO.getId();
            isNewMember = Boolean.FALSE;
            loginInfoResVO.setRegisterStatusCode(getRegisterStatusCode(id, loginDTO.getExhibitionId()));
        }
        MemberBaseInfo userInfoValue = oldBaseInfoDO == null ? baseInfoDO : oldBaseInfoDO;
        // 插入协议记录
        insertSignRecord(userInfoValue, loginDTO.getAgreementId(), isNewMember);
        // 生成token 缓存token和用户信息
        String token = getToken(id, userInfoValue);

        // 设置返回值
        loginInfoResVO.setToken(token);
        loginInfoResVO.setMemberBaseInfoVO(MemberBaseInfoVoConverter.INSTANCE.toVo(userInfoValue));
        loginInfoResVO.setLoginTool(loginDTO.getLoginTool());
        loginInfoResVO.setNewMember(isNewMember);
        log.info("mediaLogin login end");
        return loginInfoResVO;
    }

    /**
     * 获取用户在当前展会的注册状态
     *
     * @param memberId
     * @param exhibitionId
     * @return
     */
    private String getRegisterStatusCode(Long memberId, Long exhibitionId) {
        MemberParticipateRecord participateRecordDO = memberParticipateRecordRepository.queryByUserIdAndExhibitionId(memberId,
                exhibitionId);
        if (ObjectUtils.isNotEmpty(participateRecordDO)) {
            return participateRecordDO.getRegisterStatus();
        }
        return null;
    }

    private void checkValidCode(MediaMemberLoginDTO loginDTO) {
        boolean validBoolean = validCodeOpt.validCode(loginDTO.getLoginTool(), loginDTO.getValidCode());
        if (!validBoolean) {// 校验失败
            throw new ServiceException(MemberServiceErrorCodeEnum.MEMBER_SPONSOR_LOGIN_VALID_ERROR);
        }
    }

    private String getToken(Long id, MemberBaseInfo baseInfoDO) {
        LoginUser loginUser = new LoginUser();
        loginUser.setId(id);
        loginUser.setUserType(UserTypeEnum.MEDIA.getValue());
        loginUser.setMemberCode(baseInfoDO.getMemberCode());
        /**
         * 因为媒体注册的姓名在审核之前不是有效的(可能会变化),所以这里不放memberName到token中,
         * 防止因为媒体注册的姓名被修改,导致token中的memberName和数据库中的memberName不一致,以及冗余存储的memberName出现错误
         */
        loginUser.setMemberMobile(baseInfoDO.getMemberMobile());
        loginUser.setMemberEmail(baseInfoDO.getMemberEmail());
        return tokenService.createToken(loginUser);
    }

    private MemberBaseInfo checkLoginToolAndInitMemberBaseInfo(String loginTool, LoginInfoResVO loginInfoResVO) {
        boolean emailBoolean = ValidationUtils.isEmail(loginTool);
        boolean mobileBoolean = ValidationUtils.isMobile(loginTool);
        MemberBaseInfo baseInfoDO = MemberBaseInfo.initMemberBaseInfo();
        if (emailBoolean) {
            baseInfoDO.setMemberEmail(loginTool);
            loginInfoResVO.setLoginType(LoginTypeEnum.EMAIL.getCode());
        } else if (mobileBoolean) {
            baseInfoDO.setMemberMobile(loginTool);
            loginInfoResVO.setLoginType(LoginTypeEnum.PHONE.getCode());
        } else {
            throw new ServiceException(MemberServiceErrorCodeEnum.MEMBER_INVALID_CONTACT);
        }
        return baseInfoDO;
    }

    private MemberBaseInfo getOldMemberBaseInfoDO(String loginTool) {
        // 根据loginTool 从redis查询是否已有缓存 所有用户都会提前加载到redis中
        MemberBaseInfoCache baseInfoCache = memberBaseInfoOpt.getMemberBaseInfoCache(loginTool);
        log.info("getOldMemberBaseInfoDO redis查询用户信息：loginTool:{} value:{}", loginTool, baseInfoCache);
        return MemberBaseInfoCacheConverter.INSTANCE.toMemberBaseInfo(baseInfoCache);
    }

    private void insertSignRecord(MemberBaseInfo baseInfoDO, Long agreementId, boolean isNewMember) {
        if (!isNewMember) {
            // 使用LambdaQueryWrapper根据会员ID查询和AgreementID的查询会员参与记录
            MemberSignRecord oldSignRecordDO = mediaMemberDomainService.queryMemberSignRecordByMemberIdAndAgreementId(baseInfoDO.getId(),
                    agreementId);
            if (ObjectUtils.isNotEmpty(oldSignRecordDO)) {
                log.info("insertSignRecord the member had been signed");
                return;
            }
        }
        MemberSignRecord recordDO = new MemberSignRecord();
        recordDO.setMemberId(baseInfoDO.getId());
        recordDO.setSignTime(LocalDateTime.now());
        recordDO.setAgreementId(agreementId);
        recordDO.setIsEffect(Boolean.TRUE);
        recordDO.setCreateUser(baseInfoDO.getId());
        recordDO.setCreateTime(LocalDateTime.now());
        mediaMemberDomainService.addMemberSignRecord(recordDO);

    }

    @Override
    public MediaRegisterInfoVO getMediaRegisterInfo(MediaRegisterInfoQueryDTO infoQueryDTO) {
        if(infoQueryDTO.getMemberId() == null){
            infoQueryDTO.setMemberId(SecurityFrameworkUtils.getLoginUserId());// 从token中获取用户id
        }
        log.info("getMediaRegisterInfo start");
        MediaRegisterInfoVO mediaRegisterInfoVO = new MediaRegisterInfoVO();
        MemberBaseInfo baseInfoDO = mediaMemberDomainService.queryMemberBaseInfoByMemberId(infoQueryDTO.getMemberId());
        if (ObjectUtils.isEmpty(baseInfoDO)) {
            return mediaRegisterInfoVO;
        }
        mediaRegisterInfoVO.setMemberBaseInfo(MemberBaseInfoVoConverter.INSTANCE.toVo(baseInfoDO));
        // 用户id查询最新一条媒体用户拓展信息
        setMemberReporterInfo(infoQueryDTO, mediaRegisterInfoVO);
        // 根据用户id查询收件人信息
        setMemberRecipientInfo(infoQueryDTO, mediaRegisterInfoVO);
        // 根据企业id设置企业信息
        Long enterpriseId = mediaRegisterInfoVO.getMemberReporterInfo() == null ? null
                : mediaRegisterInfoVO.getMemberReporterInfo().getEnterpriseId();
        if (enterpriseId != null) {
            setEnterpriseInfo(enterpriseId, mediaRegisterInfoVO);
        }
        //设置参展记录
        setMemberParticipateRecord(mediaRegisterInfoVO, infoQueryDTO.getMemberId(), infoQueryDTO.getExhibitionId());
        setRegisterStatus(mediaRegisterInfoVO, infoQueryDTO.getExhibitionId());
        return mediaRegisterInfoVO;
    }

    private void setMemberParticipateRecord(MediaRegisterInfoVO mediaRegisterInfoVO,Long memberId,Long exhibitionId){
        MemberParticipateRecord memberParticipateRecord = memberParticipateRecordRepository.queryByUserIdAndExhibitionId(memberId,exhibitionId);
        mediaRegisterInfoVO.setMemberParticipateRecordVO(MemberParticipateRecordConvert.INSTANCE.toMemberParticipateRecordVO(memberParticipateRecord));

    }

    private void setRegisterStatus(MediaRegisterInfoVO mediaRegisterInfoVO, Long exhibitionId) {
        if (mediaRegisterInfoVO.getMemberReporterInfo() != null) {
            String registerStatus = getRegisterStatusCode(mediaRegisterInfoVO.getMemberBaseInfo().getId(),
                    exhibitionId);
            mediaRegisterInfoVO.getMemberReporterInfo().setRegisterStatus(registerStatus);
        }
    }

    /**
     * 设置媒体企业信息
     *
     * @param enterpriseId
     * @param mediaRegisterInfoVO
     */
    private void setEnterpriseInfo(Long enterpriseId, MediaRegisterInfoVO mediaRegisterInfoVO) {
        EnterpriseInfo enterpriseInfo = mediaMemberDomainService.getEnterpriseInfoById(enterpriseId);
        mediaRegisterInfoVO.setEnterpriseInfoVO(EnterpriseInfoVoConverter.INSTANCE.toVo(enterpriseInfo));
    }

    private void setMemberRecipientInfo(MediaRegisterInfoQueryDTO infoQueryDTO,
            MediaRegisterInfoVO mediaRegisterInfoVO) {
        MemberRecipientInfo recipientInfoDO = mediaMemberDomainService.queryMemberRecipientInfoByMemberId(infoQueryDTO.getMemberId());
        mediaRegisterInfoVO.setMemberRecipientInfo(MediaRegisterInfoVoConverter.INSTANCE.toVo(recipientInfoDO));
        log.info("getMediaRegisterInfo end");
    }

    private void setMemberReporterInfo(MediaRegisterInfoQueryDTO infoQueryDTO,
            MediaRegisterInfoVO mediaRegisterInfoVO) {
        MemberReporterInfo reporterInfoDO = mediaMemberDomainService.queryMemberReporterInfoByMemberId(infoQueryDTO.getMemberId());
        MemberReporterInfoVO memberReporterInfoVO = MemberReporterInfoVoConverter.INSTANCE.toVo(reporterInfoDO);
        setAttachmentInfo(memberReporterInfoVO);// 添加附件信息
        mediaRegisterInfoVO.setMemberReporterInfo(memberReporterInfoVO);
    }

    private void setAttachmentInfo(MemberReporterInfoVO memberReporterInfoVO) {
        if (ObjectUtils.isEmpty(memberReporterInfoVO)) {
            return;
        }
        List<Long> ids = new ArrayList<>();
        String attachmentIds = memberReporterInfoVO.getAttachmentOtherDescribe();
        if (StringUtils.isNotEmpty(attachmentIds)) {
            ids = Arrays.stream(attachmentIds.split(","))
                    .map(String::trim) // 去除前后空格
                    .filter(s -> !s.isEmpty()) // 过滤空字符串
                    .map(Long::valueOf) // 转换为 Long 对象
                    .collect(Collectors.toList()); // 收集为 List<Long>
        }
        Long photoId;
        if (StringUtils.isNotBlank(memberReporterInfoVO.getAttachmentHeadPhoto())) {
            photoId = Long.valueOf(memberReporterInfoVO.getAttachmentHeadPhoto());
            ids.add(photoId);
        } else {
            photoId = 0L;
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(ids)) {
            CommonResult<List<AttachmentInfoVO>> attachmentInfoList = attachmentApi.findFileByIds(ids);
            if (attachmentInfoList.isSuccess()) {
                List<AttachmentInfoVO> attachmentInfoVOList = attachmentInfoList.getData();
                // 在attachmentInfoVOList中获取到photoId所对应的对象,并将它移除掉
                AttachmentInfoVO headPhotoAttachmentInfo = attachmentInfoVOList.stream()
                        .filter(attachmentInfoVO -> attachmentInfoVO.getId().equals(photoId))
                        .findFirst()
                        .orElse(new AttachmentInfoVO());
                memberReporterInfoVO.setHeadPhotoAttachmentInfo(
                        MemberAttachmentInfoVoConverter.INSTANCE.toVo(headPhotoAttachmentInfo));
                attachmentInfoVOList.remove(headPhotoAttachmentInfo);
                memberReporterInfoVO.setAttachmentInfoList(
                        MemberAttachmentInfoVoConverter.INSTANCE.toVoList(attachmentInfoVOList));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT)
    public MediaSaveVO saveMediaDraftInfo(MemberRegisterInfoDTO registerInfoDTO) {
        log.info("saveMediaDraftInfo start");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        updateMemberBaseInfoDO(registerInfoDTO, userId);
        insertIfNewEnterprise(registerInfoDTO);
        MemberReporterInfo reporterInfoDO = saveOrUpdateMemberReporterInfoDO(registerInfoDTO);
        Long recipientInfoId = saveOrUpdateMemberRecipientInfoDO(registerInfoDTO);
        saveOrUpdateParticipateRecord(registerInfoDTO.getRegisterLanguage(), userId,
                registerInfoDTO.getExhibitionInfo().getId(), RegisterSubmitTypeEnum.DRAFT.getCode());
        MediaSaveVO mediaSaveVO = new MediaSaveVO();
        mediaSaveVO.setMemberId(userId);
        mediaSaveVO.setEnterpriseId(reporterInfoDO.getEnterpriseId());
        mediaSaveVO.setRecipientId(recipientInfoId);
        mediaSaveVO.setReporterId(reporterInfoDO.getId());
        log.info("saveMediaDraftInfo end");
        stopWatch.stop();
        log.info("saveMediaDraftInfo time:" + stopWatch.getTotalTimeMillis());
        return mediaSaveVO;
    }

    private MemberBaseInfo updateMemberBaseInfoDO(MemberRegisterInfoDTO registerInfoDTO, Long userId) {
        MemberBaseInfo baseInfoDO = MemberBaseInfoDtoConverter.INSTANCE.toDto(registerInfoDTO.getMemberBaseInfo());
        String memberName = buildMemberName(baseInfoDO, registerInfoDTO.getRegisterLanguage());
        if(StringUtils.isNotBlank(memberName)){
            //回写拼接的memberName,用于传递给数仓
            registerInfoDTO.getMemberBaseInfo().setMemberName(memberName);
        }
        if (userId == null) {
            baseInfoDO.setMemberCode(UUID.randomUUID().toString().replace("-", ""));
        } else {
            baseInfoDO.setId(userId);
        }
        try {
            //因为要进行唯一性索引检查 所以这里手机号和邮箱不能出现""
            if("".equals(baseInfoDO.getMemberMobile())){
                baseInfoDO.setMemberMobile(null);
            }
            if("".equals(baseInfoDO.getMemberEmail())){
                baseInfoDO.setMemberEmail(null);
            }
            mediaMemberDomainService.addOrModifyMemberBaseInfo(baseInfoDO);
        }catch (DuplicateKeyException e){//通过唯一索引进行重复性检查
            throw new ServiceException(MemberServiceErrorCodeEnum.MEMBER_MOBILE_OR_EMAIL_EXIST);
        }

        return baseInfoDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT)
    public Long submitMediaInfo(MemberRegisterInfoDTO registerInfoDTO) {
        log.info("submitMediaInfo start");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        // 实名认证
        identityAuthentication(registerInfoDTO);
        // 检查手机号和邮箱是否已注册
        checkMemberMobileAndEmail(registerInfoDTO, userId);
        checkMediaNewsmanNo(registerInfoDTO);
        MemberBaseInfo baseInfoDO = updateMemberBaseInfoDO(registerInfoDTO, userId);
        MemberParticipateRecord recordDO = saveRegisterInfo(registerInfoDTO, baseInfoDO.getId(),
                RegisterSubmitTypeEnum.SUBMIT.getCode());
        /**
         * 同步数据到数仓
         */
        buildRegisterInfoSync(registerInfoDTO, recordDO);
        syncMediaInfoToDataWarehouse(registerInfoDTO);
        /**
         * 发送代办消息
         */
        MediaRegisterEventDTO mediaRegisterEventDTO = buildMediaRegisterEventDTO(registerInfoDTO,recordDO.getRegisterStatus());
        mediaRegisterStatusEventProducer.mediaRegisterStatusEventChannel(mediaRegisterEventDTO);
        log.info("submitMediaInfo end");
        stopWatch.stop();
        log.info("submitMediaInfo time:" + stopWatch.getTotalTimeMillis());
        return userId;
    }

    private MediaRegisterEventDTO buildMediaRegisterEventDTO(MemberRegisterInfoDTO registerInfoDTO,String registerStatus){
        MediaRegisterEventDTO mediaRegisterEventDTO = new MediaRegisterEventDTO();
        mediaRegisterEventDTO.setMemberId(registerInfoDTO.getMemberBaseInfo().getId());
        mediaRegisterEventDTO.setExhibitionId(registerInfoDTO.getExhibitionInfo().getId());
        mediaRegisterEventDTO.setEnterpriseName(registerInfoDTO.getEnterpriseInfo().getEnterpriseName());
        mediaRegisterEventDTO.setEventTime(DateUtil.format(LocalDateTime.now(), DatePattern.NORM_DATETIME_PATTERN));
        mediaRegisterEventDTO.setRegisterStatus(registerStatus);
        mediaRegisterEventDTO.setOperatorId(registerInfoDTO.getMemberBaseInfo().getId());
        mediaRegisterEventDTO.setOperatorName(registerInfoDTO.getMemberBaseInfo().getMemberName());
        return mediaRegisterEventDTO;
    }

    /**
     * 保存注册信息
     *
     * @param registerInfoDTO
     * @param userId
     * @param registerSubmitType 提交类型 取自于 RegisterSubmitTypeEnum
     * @return
     */
    private MemberParticipateRecord saveRegisterInfo(MemberRegisterInfoDTO registerInfoDTO, Long userId,
            String registerSubmitType) {
        insertIfNewEnterprise(registerInfoDTO);
        registerInfoDTO.getMemberReporterInfo().setMemberId(userId);
        MemberReporterInfo reporterInfoDO = saveOrUpdateMemberReporterInfoDO(registerInfoDTO);
        MemberParticipateRecord recordDO = saveOrUpdateParticipateRecord(registerInfoDTO.getRegisterLanguage(),
                userId, reporterInfoDO.getExhibitionId(), registerSubmitType);
        registerInfoDTO.getMemberRecipientInfo().setMemberId(userId);
        saveOrUpdateMemberRecipientInfoDO(registerInfoDTO);
        return recordDO;
    }

    private void checkMediaNewsmanNo(MemberRegisterInfoDTO registerInfoDTO) {
        // 检查记者证号
        if (MediaCategoryEnum.MAINSTREAM_MEDIA.getCode()
                .equals(registerInfoDTO.getMemberReporterInfo().getMediaTypeCode())
                && StringUtils.isBlank(registerInfoDTO.getMemberReporterInfo().getMediaNewsmanNo())) {
            throw new ServiceException(MemberServiceErrorCodeEnum.MEDIA_NEWSMAN_NUM_NOT_FOUND);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT)
    public Long proxyRegister(MemberRegisterInfoDTO registerInfoDTO) {
        log.info("proxyRegister start");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        // 邮箱或手机号唯一性检查
        Long userId = checkProxyMemberMobileAndEmail(registerInfoDTO);
        // 实名认证
        identityAuthentication(registerInfoDTO);
        checkMediaNewsmanNo(registerInfoDTO);
        MemberBaseInfo baseInfoDO = updateMemberBaseInfoDO(registerInfoDTO, userId);
        MemberParticipateRecord recordDO = saveRegisterInfo(registerInfoDTO, baseInfoDO.getId(),
                RegisterSubmitTypeEnum.PROXY_REGISTER.getCode());
        /**
         * 同步数据到数仓
         */
        buildRegisterInfoSync(registerInfoDTO, recordDO);
        syncMediaInfoToDataWarehouse(registerInfoDTO);
        // 将新用户写到redis中
        memberBaseInfoOpt.setMemberBaseInfoCache(MemberBaseInfoCacheConverter.INSTANCE.toMemberBaseInfoCache(baseInfoDO),
                baseInfoDO.getMemberEmail());
        memberBaseInfoOpt.setMemberBaseInfoCache(MemberBaseInfoCacheConverter.INSTANCE.toMemberBaseInfoCache(baseInfoDO),
                baseInfoDO.getMemberMobile());
        //发送代注册成功消息
        sendMessage(List.of(baseInfoDO),registerInfoDTO.getExhibitionInfo().getId(),ValidScenarioEnum.MEDIA_PROXY_REGISTER);
        log.info("proxyRegister end");
        stopWatch.stop();
        log.info("proxyRegister time:" + stopWatch.getTotalTimeMillis());
        return recordDO.getMemberId();
    }

    // 自主注册时邮箱和手机号唯一性检查
    private void checkMemberMobileAndEmail(MemberRegisterInfoDTO registrationDTO, Long userId) {
        MemberBaseInfoCache baseInfoCacheByEmail = memberBaseInfoOpt
                .getMemberBaseInfoCache(registrationDTO.getMemberBaseInfo().getMemberEmail());
        if (ObjectUtils.isNotEmpty(baseInfoCacheByEmail) && !baseInfoCacheByEmail.getId().equals(userId)) {
            throw new ServiceException(MemberServiceErrorCodeEnum.MEMBER_EMAIL_EXIST);
        }
        String memberMobile=registrationDTO.getMemberBaseInfo().getMemberMobile();
        if(IdCategoryEnum.RESIDENT_ID_CARD.getCode().equals(registrationDTO.getMemberBaseInfo().getIdCategory())&& StringUtils.isBlank(memberMobile)){
            throw new ServiceException(MemberServiceErrorCodeEnum.MOBILE_CANNOT_EMPTY);
        }
        if(StringUtils.isNotBlank(memberMobile)){
            MemberBaseInfoCache baseInfoCacheByMobile = memberBaseInfoOpt
                    .getMemberBaseInfoCache(registrationDTO.getMemberBaseInfo().getMemberMobile());
            if (ObjectUtils.isNotEmpty(baseInfoCacheByMobile) && !baseInfoCacheByMobile.getId().equals(userId)) {
                throw new ServiceException(MemberServiceErrorCodeEnum.MEMBER_MOBILE_EXIST);
            }
        }

    }

    /**
     * 媒体会员代注册时邮箱和手机号唯一性检查
     *
     * @param registrationDTO
     * @return 用户id
     */
    private Long checkProxyMemberMobileAndEmail(MemberRegisterInfoDTO registrationDTO) {
        String email = registrationDTO.getMemberBaseInfo().getMemberEmail();
        String mobile = registrationDTO.getMemberBaseInfo().getMemberMobile();

        MediaProxyRecordCheckVO proxyRecordCheckVO = participateRecordMapper
                .selectRegisterRecordByMemberIdAndExhibitionId(registrationDTO.getExhibitionInfo().getId(), mobile,
                        email);
        if (ObjectUtils.isNotEmpty(proxyRecordCheckVO)) {// 如果存在本届注册记录,判断是否已审核
            if (RegisterStatusEnum.APPROVED.getCode().equals(proxyRecordCheckVO.getRegisterStatus())) {
                if (mobile.equals(proxyRecordCheckVO.getMemberMobile())) {
                    throw new ServiceException(MemberServiceErrorCodeEnum.MEMBER_MOBILE_EXIST);
                } else if (email.equals(proxyRecordCheckVO.getMemberEmail())) {
                    throw new ServiceException(MemberServiceErrorCodeEnum.MEMBER_EMAIL_EXIST);
                }
            }
            proxyRecordCheckVO.getMemberId();
        }
        return null;
    }

    @Override
    public Long recall(MediaRecallDTO recallDTO) {
        recallDTO.setMemberId(SecurityFrameworkUtils.getLoginUserId());// 撤回当前用户

        MemberParticipateRecord recordDO = mediaMemberDomainService
                .queryMemberParticipateRecordByUserIdAndExhibitionId(recallDTO.getMemberId(), recallDTO.getExhibitionId());
        if (recordDO == null) {
            throw new ServiceException(MemberServiceErrorCodeEnum.RECALL_FAIL_NOT_FOUND_RECORD);
        }
        /**
         * 验证状态
         */
        if (!RegisterStatusEnum.PENDING_REVIEW.getCode().equals(recordDO.getRegisterStatus())) {
            throw new ServiceException(MemberServiceErrorCodeEnum.RECALL_FAIL_STATUS_ERROR);
        }
        recordDO.setRegisterStatus(RegisterStatusEnum.RECALL.getCode());
        mediaMemberDomainService.modifyMemberParticipateRecordById(recordDO);
        return recordDO.getMemberId();
    }

    private void syncMediaInfoToDataWarehouse(MemberRegisterInfoDTO registerInfoDTO) {
        try {
            dataWarehouseChannelProducer.dataWarehouseChannel(buildDataWarehouseSyncVO(registerInfoDTO));
        } catch (Exception e) {
            log.error("Failed to sync media info to data warehouse", e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    private DataWarehouseSyncMessage buildDataWarehouseSyncVO(MemberRegisterInfoDTO registerInfoDTO) {

        return MemberRegisterInfoDtoConverter.INSTANCE.toDataWarehouseSyncMessage(registerInfoDTO);
    }

    private void identityAuthentication(MemberRegisterInfoDTO registerInfoDTO) {
        VerificationDTO verificationDTO = new VerificationDTO();
        verificationDTO.setIndex(0);
        verificationDTO.setMemberName(registerInfoDTO.getMemberBaseInfo().getMemberName());
        verificationDTO.setIdNumber(registerInfoDTO.getMemberBaseInfo().getIdNumber());
        CommonResult<VerificationVO> verificationVOCommonResult = verificationApi.Id2MetaVerify(verificationDTO);
        if (verificationVOCommonResult == null || !verificationVOCommonResult.getData().getResult()) {// 实名验证失败
            log.info("identityAuthentication failed" + verificationVOCommonResult);
            throw new ServiceException(MemberServiceErrorCodeEnum.AUTH_LOGIN_IDENTITY_AUTHENTICATION_FAILED);
        }
    }

    /**
     * 如果填写的是First Name和Last Name就拼接到MemberName中
     * 中文环境：姓+名，eg 姓：刘  名：玥，数据库：刘玥
     * 英文环境：First Name+空格+Last Name  eg. Last Name：Liu    First Name：Yue ，数据库：Yue Liu
     * @param baseInfoDO
     * @param registerLanguage 语言环境
     */
    private String buildMemberName(MemberBaseInfo baseInfoDO, String registerLanguage) {
        String memberName = null;
        if (StringUtils.isNotBlank(baseInfoDO.getMemberLastName())
                && StringUtils.isNotBlank(baseInfoDO.getMemberFirstName())) {
            if (RegisterLanguageEnum.CHINESE.getCode().equals(registerLanguage)) {
                memberName = baseInfoDO.getMemberLastName() + baseInfoDO.getMemberFirstName();
            } else {
                memberName = baseInfoDO.getMemberFirstName() + " " + baseInfoDO.getMemberLastName();
            }
            baseInfoDO.setMemberName(memberName);
        }
        return memberName;
    }

    @Override
    public void initMediaMemberInfoToRedisCache() {
        List<MemberBaseInfo> memberBaseInfoDOList = memberBaseInfoRepository.queryAll();
        log.info("initUserInfoToRedisCache start,member info Size:" + memberBaseInfoDOList.size());
        memberBaseInfoDOList.forEach(memberBaseInfo -> {
            MemberBaseInfoCache memberBaseInfoCache = MemberBaseInfoCacheConverter.INSTANCE
                    .toMemberBaseInfoCache(memberBaseInfo);
            String mobile = memberBaseInfo.getMemberMobile();
            if (StringUtils.isNotEmpty(mobile)) {
                memberBaseInfoOpt.setMemberBaseInfoCache(memberBaseInfoCache, mobile);
            }
            String email = memberBaseInfo.getMemberEmail();
            if (StringUtils.isNotEmpty(email)) {
                memberBaseInfoOpt.setMemberBaseInfoCache(memberBaseInfoCache, email);
            }
        });
    }

    private Long saveOrUpdateMemberRecipientInfoDO(MemberRegisterInfoDTO registerInfoDTO) {
        if (ObjectUtils.isEmpty(registerInfoDTO.getMemberRecipientInfo())) {// 保存草稿时可能没有收证信息
            return null;
        }
        MemberRecipientInfo recipientInfoDO = MemberRecipientInfoDtoConverter.INSTANCE
                .toModel(registerInfoDTO.getMemberRecipientInfo());
        mediaMemberDomainService.addOrModifyMemberRecipientInfo(recipientInfoDO);
        return recipientInfoDO.getId();
    }

    private MemberReporterInfo saveOrUpdateMemberReporterInfoDO(MemberRegisterInfoDTO registerInfoDTO) {
        if (ObjectUtils.isEmpty(registerInfoDTO.getMemberReporterInfo())) {// 保存草稿时可能没有媒体信息
            return new MemberReporterInfo();
        }
        log.info("saveOrUpdateMemberReporterInfoDO start");
        MemberReporterInfo reporterInfoDO = MemberReporterInfoDtoConverter.INSTANCE
                .toModel(registerInfoDTO.getMemberReporterInfo());
        // 传进来的企业可能是新增的 所以这里要重新设置一次企业id
        if (registerInfoDTO.getEnterpriseInfo() != null) {// 保存草稿这里可能没有企业信息
            reporterInfoDO.setEnterpriseId(registerInfoDTO.getEnterpriseInfo().getId());
        }
        // 查找category code
        setPositionCategoryInfo(reporterInfoDO.getMediaPositionCategoryCode(), reporterInfoDO);
        mediaMemberDomainService.addOrModifyMemberReporterInfo(reporterInfoDO);
        return reporterInfoDO;
    }

    private void setPositionCategoryInfo(String optionCode, MemberReporterInfo reporterInfoDO) {
        log.info("optionCode:" + optionCode);
        if (StringUtils.isBlank(optionCode)) {// 保存草稿时可能没有optionCode
            return;
        }
        MediaPositionTypeEnum mediaPositionTypeEnum =MediaPositionTypeEnum.getByCode(optionCode);
        buildPositionCategoryInfo(mediaPositionTypeEnum, reporterInfoDO);


    }

    private void buildPositionCategoryInfo(MediaPositionTypeEnum mediaPositionTypeEnum, MemberReporterInfo reporterInfoDO) {
        if (ObjectUtils.isNotEmpty(mediaPositionTypeEnum)) {
            log.info("父级值集选项信息为：{}", mediaPositionTypeEnum);
            reporterInfoDO.setMediaPositionCategoryCode(mediaPositionTypeEnum.getCode());
            reporterInfoDO.setMediaPositionCategoryNameCn(mediaPositionTypeEnum.getDescriptionCN());
            reporterInfoDO.setMediaPositionCategoryNameEn(mediaPositionTypeEnum.getDescriptionEN());
        }
    }

    /**
     * 检查是否有已经生效的企业信息 有的话返回
     *
     * @param registerInfoDTO
     * @return
     */
    private void insertIfNewEnterprise(MemberRegisterInfoDTO registerInfoDTO) {
        if (ObjectUtils.isEmpty(registerInfoDTO.getEnterpriseInfo())) {// 保存草稿时可能没有企业信息
            return;
        }
        EnterpriseInfo enterpriseInfo = EnterpriseInfoDtoConverter.INSTANCE.toModel(registerInfoDTO.getEnterpriseInfo());
        // 根据企业名称查询企业信息
        if (registerInfoDTO.getEnterpriseInfo().getId() == null) {// 说明是新增的记录
            EnterpriseInfo oldEnterpriseInfoDO = mediaMemberDomainService.queryMediaEnterpriseByNameAndLocationCode(
                    enterpriseInfo.getEnterpriseName(), enterpriseInfo.getEnterpriseLocationCode());
            if (ObjectUtils.isNotEmpty(oldEnterpriseInfoDO)) {// 说明有已经生效的数据存在了直接绑定此数据
                enterpriseInfo.setId(oldEnterpriseInfoDO.getId());
                registerInfoDTO.getEnterpriseInfo().setId(enterpriseInfo.getId());
                return;
            }
            enterpriseInfo.setIsUseAble(Boolean.FALSE);
        }
        enterpriseInfo.setEnterpriseType(EnterpriseTypeEnum.MEDIA_ENTERPRISE.getCode());
        mediaMemberDomainService.addOrModifyEnterpriseInfo(enterpriseInfo);
        // 如果是新的enterprise则需要设置id
        registerInfoDTO.getEnterpriseInfo().setId(enterpriseInfo.getId());
    }

    /**
     * 记录参与记录
     *
     * @param userId
     * @param exhibitionId
     * @param registerSubmitType 注册的提交类型 根据RegisterSubmitType取值进行判断
     */
    private MemberParticipateRecord saveOrUpdateParticipateRecord(String registerLanguage, Long userId,
            Long exhibitionId, String registerSubmitType) {
        // 使用LambdaQueryWrapper根据会员ID查询和展会ID的查询会员参与记录
        MemberParticipateRecord oldRecordDO = memberParticipateRecordRepository.queryByUserIdAndExhibitionId(userId,
                exhibitionId);
        if (ObjectUtils.isNotEmpty(oldRecordDO)) {
            log.info("saveOrUpdateParticipateRecord the member had been registered");
            /**
             * 检查注册状态
             * 保存草稿或提交时,状态只能为空 草稿 撤回 其中之一
             */
            boolean registerStatusBoolean = RegisterStatusEnum
                    .checkDraftOrRecallStatus(oldRecordDO.getRegisterStatus());
            if (!registerStatusBoolean) {
                throw new ServiceException(MemberServiceErrorCodeEnum.MEMBER_REGISTER_STATUS_ERROR);
            }
            if (RegisterSubmitTypeEnum.SUBMIT.getCode().equals(registerSubmitType)) {
                oldRecordDO.setRegisterStatus(RegisterStatusEnum.PENDING_REVIEW.getCode());
            } else if (RegisterSubmitTypeEnum.PROXY_REGISTER.getCode().equals(registerSubmitType)) {
                oldRecordDO.setRegisterStatus(RegisterStatusEnum.APPROVED.getCode());
            }
            oldRecordDO.setRegisterLanguage(registerLanguage);
            oldRecordDO.setRegisterTime(LocalDateTime.now());//更新注册时间
            memberParticipateRecordRepository.modifyById(oldRecordDO);
            return oldRecordDO;
        }
        MemberParticipateRecord recordDO = new MemberParticipateRecord();
        recordDO.setMemberId(userId);
        recordDO.setExhibitionId(exhibitionId);
        recordDO.setMemberType(ActionUserTypeEnum.MEDIA_USER.getCode());
        recordDO.setParticipateCode(UUID.randomUUID().toString().replace("-",""));
        recordDO.setRegisterTime(LocalDateTime.now());
        recordDO.setRegisterSource(RegisterSourceEnum.NORMAL.getCode());
        recordDO.setRegisterMethod(RegisterMethodEnum.SELF_REGISTER.getCode());
        recordDO.setRegisterLanguage(registerLanguage);
        if (RegisterSubmitTypeEnum.SUBMIT.getCode().equals(registerSubmitType)) {
            recordDO.setRegisterStatus(RegisterStatusEnum.PENDING_REVIEW.getCode());
        } else if (RegisterSubmitTypeEnum.PROXY_REGISTER.getCode().equals(registerSubmitType)) {
            recordDO.setRegisterStatus(RegisterStatusEnum.APPROVED.getCode());
        } else {
            recordDO.setRegisterStatus(RegisterStatusEnum.DRAFT.getCode());
        }
        recordDO.setRegisterSystem(RegisterSystemEnum.CENTRAL_PLATFORM.getCode());
        recordDO.setCreateUser(userId);
        recordDO.setCreateTime(LocalDateTime.now());
        recordDO.setIsActive(Boolean.FALSE);
        recordDO.setDelFlg(Boolean.FALSE);
        memberParticipateRecordRepository.add(recordDO);
        return recordDO;
    }

    /**
     * 创建注册信息对象用于传递给数仓
     *
     * @param registerInfoDTO
     * @param recordDO
     */
    private void buildRegisterInfoSync(MemberRegisterInfoDTO registerInfoDTO, MemberParticipateRecord recordDO) {
        registerInfoDTO.setRegisterInfo(RegisterInfoSyncDtoConverter.INSTANCE.toDto(recordDO));
    }

    @Override
    public PageResult<MediaPageListVO> getMediaPage(MediaPageListQueryDTO queryDTO) {
        MediaPageListQuery queryDO = MediaPageListQueryDtoConverter.INSTANCE.toModel(queryDTO);
        // 不管用户是否选择了展会，都需要一个默认的条件，即当前用户登录对应的展会权限。
        ExhibitionQueryDTO exhibitionQueryDTO = new ExhibitionQueryDTO(queryDTO.getExhibitionTagCodes(),
                queryDTO.getExhibitionSessionKeys(), true);
        Map<Long, ExhibitionVO> exhibitionMap = exhibitionExternalService.getExhibitionMap(exhibitionQueryDTO);
        if (CollectionUtils.isEmpty(exhibitionMap)) {
            return new PageResult<>(List.of(), 0L);
        }
        queryDO.setExhibitionIds(exhibitionMap.keySet().stream().toList());

        PageResult<MediaPageList> pageResult = memberBaseInfoRepository.getMediaPage(queryDO, ValueSetUserTypeEnum.MEDIA.getOptionCode());
        List<MediaPageListVO> vos = MediaPageListVoConverter.INSTANCE.toVoList(pageResult.getList());
        // 回填展会信息
        vos.forEach(v -> {
            v.setExhibitionNameCn(exhibitionMap.get(v.getExhibitionId()).getExhibitionNameCn());
            v.setExhibitionNameEn(exhibitionMap.get(v.getExhibitionId()).getExhibitionNameEn());
            v.setExhibitionCode(exhibitionMap.get(v.getExhibitionId()).getExhibitionCode());
            v.setExhibitionYear(exhibitionMap.get(v.getExhibitionId()).getExhibitionYear());
            v.setExhibitionSession(exhibitionMap.get(v.getExhibitionId()).getExhibitionSession());
        });

        return new PageResult<>(vos, pageResult.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT)
    public List<Long> audit(MediaAuditDTO mediaAuditDTO) {
        List<Long> memberParticipateIds = mediaAuditDTO.getMemberParticipateIds();
        List<MemberBaseInfo> memberBaseInfoDOList = new ArrayList<>();
        Long exhibitionId = null;
        if(ApproveResultEnum.getByCode(mediaAuditDTO.getApproveResult()) == null){
            log.info("audit: approveResult is invalid");
            throw  new ServiceException(MemberServiceErrorCodeEnum.MEDIA_AUDIT_FAILED);
        }
        for (Long memberParticipateId : memberParticipateIds){
            MediaAuditQueryVO queryVO = participateRecordMapper.selectMediaAuditQueryVO(memberParticipateId);
            if(queryVO == null ||!RegisterStatusEnum.PENDING_REVIEW.getCode().equals(queryVO.getRegisterStatus())){
                log.info("audit 待审核单状态错误:"+queryVO);
                throw  new ServiceException(MemberServiceErrorCodeEnum.MEDIA_AUDIT_FAILED);
            }
            if(exhibitionId ==null){
                exhibitionId = queryVO.getExhibitionId();
            }
            MemberBaseInfo baseInfoDO = baseInfoService.getMemberBaseInfo(queryVO.getMemberId());
            memberBaseInfoDOList.add(baseInfoDO);
        }
        String registerResult =  mediaAuditDTO.getApproveResult();
        String registerStatus = ApproveResultEnum.APPROVED.getCode().equals(registerResult)?RegisterStatusEnum.APPROVED.getCode():RegisterStatusEnum.REJECTED.getCode();
        //插入审核记录
        if(ApproveResultEnum.APPROVED.getCode().equals(registerResult)){
            if(StringUtils.isEmpty(mediaAuditDTO.getMediaPermissionType()) || MediaPermissionTypeEnums.getByCode(mediaAuditDTO.getMediaPermissionType()) ==null){
                log.error("媒体权限类型不能为空:",mediaAuditDTO);
                throw  new ServiceException(MemberServiceErrorCodeEnum.MEDIA_AUDIT_FAILED);
            }
            memberReporterInfoRepository.updateMediaPermissionType(memberBaseInfoDOList.stream().map(MemberBaseInfo::getId).toList(), mediaAuditDTO.getMediaPermissionType(),exhibitionId);
        }
        insertAuditResult(memberBaseInfoDOList,mediaAuditDTO,exhibitionId);
        updateRegisterStatus(memberParticipateIds,registerStatus);
        initMediaMemberInfoToRedisCache(memberBaseInfoDOList);

        //以下是异步操作 同步数据到数仓 发送通知消息 即使出错也不应该阻断正常业务的执行
        syncAndSendMessage(registerResult, memberBaseInfoDOList, exhibitionId);
        return memberParticipateIds;
    }

    @Override
    public List<Long> syncData(MediaSyncDataDTO syncDataDTO) {
        Long exhibitionId = syncDataDTO.getExhibitionId();
        List<Long> memberIds = syncDataDTO.getMemberIds();
        List<MemberDataWarehouseSyncMessage> messages =memberApproveRecordRepository.selectDataWarehouseSyncMessageList(memberIds,RegisterStatusEnum.APPROVED.getCode(),exhibitionId);
        log.info("auditApproved: {}",messages);
        List<DataWarehouseSyncMessage> dataWarehouseSyncMessages = DataWarehouseSyncMessageConverter.INSTANCE.toDataWarehouseSyncMessages(messages);
        syncMediaInfoListToDataWarehouse(dataWarehouseSyncMessages,exhibitionId);
        return memberIds;
    }

    private void syncAndSendMessage( String registerResult, List<MemberBaseInfo> memberBaseInfoDOList, Long exhibitionId) {
        try {
            if(ApproveResultEnum.APPROVED.getCode().equals(registerResult)){
                auditApproved(memberBaseInfoDOList, exhibitionId);
            }else if(ApproveResultEnum.REJECTED.getCode().equals(registerResult)){
                auditRejected(memberBaseInfoDOList, exhibitionId);
            }
        }catch (Exception e){
            log.info("sync failed error",e);
        }
    }

    private void auditApproved(List<MemberBaseInfo> memberBaseInfoDOList,Long exhibitionId){
        List<Long> ids = memberBaseInfoDOList.stream().map(MemberBaseInfo::getId).toList();
        List<MemberDataWarehouseSyncMessage> messages =memberApproveRecordRepository.selectDataWarehouseSyncMessageList(ids,RegisterStatusEnum.APPROVED.getCode(),exhibitionId);
        log.info("auditApproved: {}",messages);
        List<DataWarehouseSyncMessage> dataWarehouseSyncMessages = DataWarehouseSyncMessageConverter.INSTANCE.toDataWarehouseSyncMessages(messages);
        syncMediaInfoListToDataWarehouse(dataWarehouseSyncMessages,exhibitionId);
        // 发送审核通过的消息通知
        sendMessage(memberBaseInfoDOList,exhibitionId,ValidScenarioEnum.MEDIA_AUDIT_PASS);
    }

    private void auditRejected(List<MemberBaseInfo> memberBaseInfoDOList,Long exhibitionId){
        //发送审核拒绝的消息通知
        sendMessage(memberBaseInfoDOList,exhibitionId,ValidScenarioEnum.MEDIA_AUDIT_REJECT);
    }

    private void syncMediaInfoListToDataWarehouse(List<DataWarehouseSyncMessage> messages,Long exhibitionId){
        if (CollectionUtils.isEmpty(messages)){
            return;
        }
        messages.forEach(message -> {
            //添加countryName和展会相关信息
            //添加展会相关信息
            ExhibitionInfoCache exhibitionInfoCache = exhibitionInfoOpt.getExhibitionInfoCacheById(exhibitionId);
            if(ObjectUtils.isNotEmpty(exhibitionInfoCache)){
                message.setExhibitionNameCn(exhibitionInfoCache.getExhibitionNameCn());
                message.setExhibitionNameEn(exhibitionInfoCache.getExhibitionNameEn());
                message.setExhibitionCode(exhibitionInfoCache.getExhibitionCode());
                message.setExhibitionSessionKey(exhibitionInfoCache.getExhibitionSessionKey());
                message.setExhibitionTagCode(exhibitionInfoCache.getExhibitionTagCode());
            }else{
                CommonResult<ExhibitionVO> result =exhibitionApi.getExhibitionById(exhibitionId);
                if(result!=null && result.isSuccess()){
                    ExhibitionVO exhibitionVO = result.getData();
                    if(exhibitionVO!= null){
                        message.setExhibitionNameCn(exhibitionVO.getExhibitionNameCn());
                        message.setExhibitionNameEn(exhibitionVO.getExhibitionNameEn());
                        message.setExhibitionCode(exhibitionVO.getExhibitionCode());
                        message.setExhibitionSessionKey(exhibitionVO.getExhibitionSessionKey());
                        message.setExhibitionTagCode(exhibitionVO.getExhibitionTagCode());
                    }
                }
            }
            log.info("appendDataWarehouseSyncMessageInfo: {}",message);
            try {
                dataWarehouseChannelProducer.dataWarehouseChannel(message);
            } catch (Exception e) {
                log.error("Failed to send message to data warehouse channel", e);
                // 不抛出异常，避免影响主业务流程
            }
        });

    }

    /**
     * 记录审核结果
     */
    private void insertAuditResult(List<MemberBaseInfo> memberBaseInfoDOList,MediaAuditDTO mediaAuditDTO,Long exhibitionId){
        List<MemberApproveRecord> auditResultDOList = new ArrayList<>();
        for (MemberBaseInfo memberBaseInfo : memberBaseInfoDOList){
            MemberApproveRecord auditResultDO = new MemberApproveRecord();
            auditResultDO.setMemberId(memberBaseInfo.getId());
            auditResultDO.setExhibitionId(exhibitionId);
            auditResultDO.setApproveResult(mediaAuditDTO.getApproveResult());
            auditResultDO.setApproveTime(LocalDateTime.now());
            auditResultDO.setApproverId(SecurityFrameworkUtils.getLoginUserId());
            auditResultDO.setApproverName(SecurityFrameworkUtils.getLoginUserName());
            auditResultDO.setApproveRejectReason(mediaAuditDTO.getApproveRejectedReason());
            auditResultDO.setApproveRejectRemark(mediaAuditDTO.getApproveRejectedRemark());
            auditResultDOList.add(auditResultDO);
        }
        memberApproveRecordRepository.addBatch(auditResultDOList);

    }

    /**
     * 更新注册状态
     * @param memberParticipateIds
     * @param registerStatus
     */
    private void updateRegisterStatus(List<Long> memberParticipateIds,String registerStatus) {
        List<MemberParticipateRecord> recordDOList = new ArrayList<>();
        for (Long memberParticipateId : memberParticipateIds){
            MemberParticipateRecord recordDO = new MemberParticipateRecord();
            recordDO.setId(memberParticipateId);
            recordDO.setRegisterStatus(registerStatus);
            recordDOList.add(recordDO);
        }
        memberParticipateRecordRepository.modifyBatch(recordDOList);
    }

    /**
     * 初始化会员信息到缓存
     * @param memberBaseInfoDOList
     */
    private void initMediaMemberInfoToRedisCache(List<MemberBaseInfo> memberBaseInfoDOList){
        memberBaseInfoDOList.forEach(memberBaseInfo -> {
            memberBaseInfoOpt.setMemberBaseInfoCache(MemberBaseInfoCacheConverter.INSTANCE.toMemberBaseInfoCache(memberBaseInfo),memberBaseInfo.getMemberMobile());
            memberBaseInfoOpt.setMemberBaseInfoCache(MemberBaseInfoCacheConverter.INSTANCE.toMemberBaseInfoCache(memberBaseInfo),memberBaseInfo.getMemberEmail());
        });
    }

    private void sendMessage(List<MemberBaseInfo> memberBaseInfoDOList,Long exhibitionId,ValidScenarioEnum scenarioEnum){
        String exhibitionTagCode;
        String exhibitionCode;
        ExhibitionInfoCache exhibitionInfoCache =exhibitionInfoOpt.getExhibitionInfoCacheById(exhibitionId);
        if(exhibitionInfoCache== null){
            CommonResult<ExhibitionVO> result  = exhibitionApi.getExhibitionById(exhibitionId);
            if (result!=null && result.isSuccess()){
                ExhibitionVO exhibitionVO = exhibitionApi.getExhibitionById(exhibitionId).getData();
                exhibitionTagCode = exhibitionVO.getExhibitionTagCode();
                exhibitionCode = exhibitionVO.getExhibitionCode();
            }else{
                log.error("can not found exhibition info exhibitionId:{}",exhibitionId);
                return;
            }
        }else{
            exhibitionTagCode = exhibitionInfoCache.getExhibitionTagCode();
            exhibitionCode = exhibitionInfoCache.getExhibitionCode();
        }
        /**
         * 如果身份证类型为中华人民共和国身份证则发送短信消息
         * 否则发送邮箱消息
         */
        for (MemberBaseInfo memberBaseInfo : memberBaseInfoDOList){
            if(IdCategoryEnum.RESIDENT_ID_CARD.getCode().equals(memberBaseInfo.getIdCategory())){
                messageHandler.sendSmsMessage(List.of(memberBaseInfo.getMemberMobile()), scenarioEnum.getCode(), exhibitionTagCode, exhibitionCode);
            }else{
                messageHandler.sendEmailMessage(List.of(memberBaseInfo.getMemberEmail()), scenarioEnum.getCode(), exhibitionTagCode, exhibitionCode);
            }
        }
    }

    @Override
    public void initMediaMemberBaseInfoToRedisCache() {
        List<MemberBaseInfo> memberBaseInfoDOList = memberBaseInfoRepository.queryAll();
        log.info("initUserInfoToRedisCache start,member info Size:" + memberBaseInfoDOList.size());
        memberBaseInfoDOList.forEach(memberBaseInfoDO -> {
            MemberBaseInfoCache memberBaseInfoCache = MemberBaseInfoCacheConverter.INSTANCE
                    .toMemberBaseInfoCache(memberBaseInfoDO);
            String mobile = memberBaseInfoDO.getMemberMobile();
            if (StringUtils.isNotEmpty(mobile)) {
                memberBaseInfoOpt.setMemberBaseInfoCache(memberBaseInfoCache, mobile);
            }
            String email = memberBaseInfoDO.getMemberEmail();
            if (StringUtils.isNotEmpty(email)) {
                memberBaseInfoOpt.setMemberBaseInfoCache(memberBaseInfoCache, email);
            }
        });
    }


}
