package com.dexpo.module.member.app.service.impl;

import com.dexpo.module.member.api.dto.media.MediaProxyActionDTO;
import com.dexpo.module.member.api.dto.media.MediaProxyRegistrationDTO;
import com.dexpo.module.member.api.dto.member.MemberBaseInfoDTO;
import com.dexpo.module.member.domain.model.member.MemberBaseInfo;
import com.dexpo.module.member.domain.repository.MemberApproveRecordRepository;
import com.dexpo.module.member.domain.repository.MemberBaseInfoRepository;
import com.dexpo.module.member.domain.repository.MemberParticipateRecordRepository;
import com.dexpo.module.member.domain.repository.MemberReporterInfoRepository;
import com.dexpo.module.member.infrastructure.mq.producer.DataWarehouseChannelProducer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MediaProxySaveDateAppServiceImplTest {
    @InjectMocks
    private MediaProxySaveDateAppServiceImpl service;
    @Mock
    private MemberBaseInfoRepository memberBaseInfoRepository;
    @Mock
    private DataWarehouseChannelProducer dataWarehouseChannelProducer;
    @Mock
    private MemberReporterInfoRepository reporterInfoRepository;
    @Mock
    private MemberParticipateRecordRepository memberParticipateRecordRepository;
    @Mock
    private MemberApproveRecordRepository memberApproveRecordRepository;

    @Test
    void testBatchAddNewMediaUser_success() {
        MediaProxyActionDTO dto = new MediaProxyActionDTO();
        MediaProxyRegistrationDTO reg = new MediaProxyRegistrationDTO();
        reg.setMemberFirstName("张");
        reg.setMemberLastName("三");
        reg.setIdNumber("A123456789");
        dto.setMediaProxyRegistrationList(List.of(reg));
        when(memberBaseInfoRepository.addBatch(any())).thenReturn(List.of(new MemberBaseInfo()));
        service.batchAddNewMediaUser(dto);
        assertNotNull(dto.getMemberBaseInfoDOList());
    }

    @Test
    void testBatchAddNewMemberReporterInfo_success() {
        MediaProxyActionDTO dto = new MediaProxyActionDTO();
        Map<String, MediaProxyRegistrationDTO> mediaProxyRegistrationMap = new HashMap<>();
        mediaProxyRegistrationMap.put("<EMAIL>", new MediaProxyRegistrationDTO());
        dto.setMediaProxyRegistrationMap(mediaProxyRegistrationMap);
        MemberBaseInfoDTO baseInfo = new MemberBaseInfoDTO();
        baseInfo.setId(1L);
        baseInfo.setMemberEmail("<EMAIL>");
        dto.setMemberBaseInfoDOList(List.of(baseInfo));
        Map<String, String> fileMap = Map.of();
        doNothing().when(reporterInfoRepository).addBatch(any());
        assertDoesNotThrow(() -> service.batchAddNewMemberReporterInfo(dto, fileMap));
    }

    @Test
    void testBatchAddNewMemberParticipateRecord_success() {
        MediaProxyActionDTO dto = new MediaProxyActionDTO();
        MemberBaseInfoDTO baseInfo = new MemberBaseInfoDTO();
        baseInfo.setId(1L);
        dto.setMemberBaseInfoDOList(List.of(baseInfo));
        doNothing().when(memberParticipateRecordRepository).addBatch(any());
        assertDoesNotThrow(() -> service.batchAddNewMemberParticipateRecord(dto));
    }

    @Test
    void testBatchAddNewMemberDataWarehouse_success() {
        MediaProxyActionDTO dto = new MediaProxyActionDTO();
        MemberBaseInfoDTO baseInfo = new MemberBaseInfoDTO();
        baseInfo.setId(1L);
        dto.setMemberBaseInfoDOList(List.of(baseInfo));
//        doNothing().when(dataWarehouseChannelProducer).dataWarehouseChannel(any());
        assertDoesNotThrow(() -> service.batchAddNewMemberDataWarehouse(dto));
    }
} 