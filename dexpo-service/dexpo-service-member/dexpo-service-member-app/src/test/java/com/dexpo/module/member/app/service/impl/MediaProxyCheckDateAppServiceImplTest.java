package com.dexpo.module.member.app.service.impl;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.base.api.basic.BasicLocationApi;
import com.dexpo.module.base.api.basic.BasicRegionApi;
import com.dexpo.module.base.api.basic.vo.BasicRegionVO;
import com.dexpo.module.integration.api.verification.VerificationApi;
import com.dexpo.module.member.api.dto.media.MediaProxyActionDTO;
import com.dexpo.module.member.api.dto.media.MediaProxyRegistrationDTO;
import com.dexpo.module.member.domain.repository.EnterpriseInfoRepository;
import com.dexpo.module.member.domain.repository.MemberBaseInfoRepository;
import com.dexpo.module.member.domain.repository.MemberParticipateRecordRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MediaProxyCheckDateAppServiceImplTest {
    @InjectMocks
    private MediaProxyCheckDateAppServiceImpl service;
    @Mock
    private EnterpriseInfoRepository enterpriseInfoRepository;
    @Mock
    private MemberBaseInfoRepository memberBaseInfoRepository;
    @Mock
    private MemberParticipateRecordRepository memberParticipateRecordRepository;
    @Mock
    private BasicLocationApi basicLocationApi;
    @Mock
    private BasicRegionApi basicRegionApi;
    @Mock
    private VerificationApi verificationApi;

    @Test
    void testCheckExcelToType_validData() {
        MediaProxyActionDTO dto = new MediaProxyActionDTO();
        MediaProxyRegistrationDTO reg = new MediaProxyRegistrationDTO();
        reg.setMemberFirstName("张");
        reg.setMemberLastName("三");
        reg.setIdNumber("A123456789");
        reg.setMemberGenderCN("男");
        reg.setMemberBirthDayValue("2000-01-01");
        reg.setMemberEmail("<EMAIL>");
        reg.setEnterpriseName("企业");
        reg.setEnterpriseLocationNameCn("上海");
        reg.setMediaTypeNameCn("主流媒体");
        reg.setMediaPositionNameCn("记者");
        reg.setCertificateCollectionMethodValue("自取");
        reg.setCountryName("中国");
        reg.setIsApplyLiveStreamValue("true");
        dto.setMediaProxyRegistrationList(List.of(reg));
        when(enterpriseInfoRepository.queryAll()).thenReturn(List.of());
        when(basicLocationApi.getLocationList(any())).thenReturn(CommonResult.success(List.of()));
        Map<String, BasicRegionVO> mapVal = new HashMap<>();
        mapVal.put("k1", new BasicRegionVO());
        when(basicRegionApi.getRegionListByLevel()).thenReturn(Map.of("country", mapVal));
        assertDoesNotThrow(() -> service.checkExcelToType(dto));
    }

    @Test
    void testCheckExcelToType_invalidIdNumber() {
        MediaProxyActionDTO dto = new MediaProxyActionDTO();
        MediaProxyRegistrationDTO reg = new MediaProxyRegistrationDTO();
        reg.setIdNumber("!@#");
        reg.setMemberFirstName("张");
        reg.setMemberLastName("三");
        reg.setMemberGenderCN("男");
        reg.setMemberBirthDayValue("2000-01-01");
        reg.setMemberEmail("<EMAIL>");
        reg.setEnterpriseName("企业");
        reg.setEnterpriseLocationNameCn("上海");
        reg.setMediaTypeNameCn("主流媒体");
        reg.setMediaPositionNameCn("记者");
        reg.setCertificateCollectionMethodValue("自取");
        reg.setCountryName("中国");
        reg.setIsApplyLiveStreamValue("true");
        dto.setMediaProxyRegistrationList(List.of(reg));
        when(enterpriseInfoRepository.queryAll()).thenReturn(List.of());
        when(basicLocationApi.getLocationList(any())).thenReturn(CommonResult.success(List.of()));
        when(basicRegionApi.getRegionListByLevel()).thenReturn(Map.of());
        Map<String, BasicRegionVO> mapVal = new HashMap<>();
        mapVal.put("k1", new BasicRegionVO());
        when(basicRegionApi.getRegionListByLevel()).thenReturn(Map.of("country", mapVal));
        service.checkExcelToType(dto);
        assertTrue(dto.isHasError());
    }

    @Test
    void testCheckExcelToVerification_emptyList() {
        MediaProxyActionDTO dto = new MediaProxyActionDTO();
        dto.setCheckIdVerificationList(Collections.emptyList());
        assertDoesNotThrow(() -> service.checkExcelToVerification(dto));
    }

    @Test
    void testCheckExcelToInfo_callsCheckPhoneAndEmail() {
        MediaProxyActionDTO dto = new MediaProxyActionDTO();
        // 只需保证无异常
        assertDoesNotThrow(() -> service.checkExcelToInfo(dto));
    }
} 