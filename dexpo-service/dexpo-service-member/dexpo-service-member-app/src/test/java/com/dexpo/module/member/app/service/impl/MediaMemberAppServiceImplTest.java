package com.dexpo.module.member.app.service.impl;

import com.dexpo.framework.cache.redis.operate.base.ValidCodeOpt;
import com.dexpo.framework.cache.redis.operate.exhibition.ExhibitionInfoCacheOpt;
import com.dexpo.framework.cache.redis.operate.member.MemberBaseInfoOpt;
import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.framework.security.core.service.TokenService;
import com.dexpo.module.base.api.attachment.AttachmentApi;
import com.dexpo.module.base.api.attachment.vo.AttachmentInfoVO;
import com.dexpo.module.exhibition.api.ExhibitionApi;
import com.dexpo.module.exhibition.api.dto.ExhibitionQueryDTO;
import com.dexpo.module.exhibition.api.vo.ExhibitionVO;
import com.dexpo.module.integration.api.verification.VerificationApi;
import com.dexpo.module.integration.api.verification.vo.VerificationVO;
import com.dexpo.module.member.api.dto.EnterpriseInfoDTO;
import com.dexpo.module.member.api.dto.ExhibitionInfoDTO;
import com.dexpo.module.member.api.dto.media.*;
import com.dexpo.module.member.api.dto.member.MemberBaseInfoDTO;
import com.dexpo.module.member.api.dto.member.MemberRecipientInfoDTO;
import com.dexpo.module.member.api.dto.member.MemberRegisterInfoDTO;
import com.dexpo.module.member.api.dto.member.MemberReporterInfoDTO;
import com.dexpo.module.member.api.vo.LoginInfoResVO;
import com.dexpo.module.member.api.vo.media.*;
import com.dexpo.module.member.app.handler.MessageHandler;
import com.dexpo.module.member.domain.model.member.MemberBaseInfo;
import com.dexpo.module.member.domain.model.member.MemberParticipateRecord;
import com.dexpo.module.member.domain.model.media.MediaPageList;
import com.dexpo.module.member.domain.repository.MemberApproveRecordRepository;
import com.dexpo.module.member.domain.repository.MemberBaseInfoRepository;
import com.dexpo.module.member.domain.repository.MemberParticipateRecordRepository;
import com.dexpo.module.member.domain.service.MediaMemberDomainService;
import com.dexpo.module.member.domain.service.MemberBaseInfoDomainService;
import com.dexpo.module.member.infrastructure.dal.mysql.MemberParticipateRecordMapper;
import com.dexpo.module.member.infrastructure.mq.producer.MediaRegisterStatusEventProducer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class MediaMemberAppServiceImplTest {
    @InjectMocks
    private MediaMemberAppServiceImpl service;
    @Mock
    private ValidCodeOpt validCodeOpt;
    @Mock
    private MemberBaseInfoOpt memberBaseInfoOpt;
    @Mock
    private TokenService tokenService;
    @Mock
    private MemberBaseInfoRepository memberBaseInfoRepository;
    @Mock
    private MemberParticipateRecordRepository memberParticipateRecordRepository;
    @Mock
    private MediaMemberDomainService mediaMemberDomainService;
    @Mock
    private MemberBaseInfoDomainService baseInfoService;
    @Mock
    private MessageHandler messageHandler;
    @Mock
    private VerificationApi verificationApi;
    @Mock
    private MemberApproveRecordRepository memberApproveRecordRepository;
    @Mock
    private MemberParticipateRecordMapper participateRecordMapper;
    @Mock
    private ExhibitionInfoCacheOpt exhibitionInfoOpt;
    @Mock
    private ExhibitionApi exhibitionApi;
    @Mock
    private MediaRegisterStatusEventProducer mediaRegisterStatusEventProducer;
    @Mock
    private com.dexpo.module.member.infrastructure.mq.producer.DataWarehouseChannelProducer dataWarehouseChannelProducer;
    @Mock
    private com.dexpo.module.member.domain.repository.MemberReporterInfoRepository memberReporterInfoRepository;
    @Mock
    private com.dexpo.module.member.domain.repository.EnterpriseInfoRepository enterpriseInfoRepository;
    @Mock
    private com.dexpo.module.member.app.external.exhibition.ExhibitionExternalService exhibitionExternalService;

    @Test
    void testMediaLogin_newUser_success() {
        MediaMemberLoginDTO loginDTO = new MediaMemberLoginDTO();
        loginDTO.setLoginTool("<EMAIL>");
        loginDTO.setValidCode("123456");
        loginDTO.setAgreementId(1L);
        loginDTO.setExhibitionId(2L);
        when(validCodeOpt.validCode(any(), any())).thenReturn(true);
        when(memberBaseInfoOpt.getMemberBaseInfoCache(any())).thenReturn(null);
        MemberBaseInfo baseInfo = new MemberBaseInfo();
        baseInfo.setId(10L);
        doNothing().when(memberBaseInfoRepository).add(any());
        when(tokenService.createToken(any())).thenReturn("token123");
        doNothing().when(memberBaseInfoOpt).setMemberBaseInfoCache(any(), any());
        doNothing().when(mediaMemberDomainService).addMemberSignRecord(any());
        LoginInfoResVO vo = service.mediaLogin(loginDTO);
        assertNotNull(vo);
        assertEquals("token123", vo.getToken());
        assertTrue(vo.isNewMember());
    }

    @Test
    void testMediaLogin_invalidCode() {
        MediaMemberLoginDTO loginDTO = new MediaMemberLoginDTO();
        loginDTO.setLoginTool("<EMAIL>");
        loginDTO.setValidCode("wrong");
        loginDTO.setAgreementId(1L);
        loginDTO.setExhibitionId(2L);
        when(validCodeOpt.validCode(any(), any())).thenReturn(false);
        assertThrows(ServiceException.class, () -> service.mediaLogin(loginDTO));
    }

    @Test
    void testGetMediaRegisterInfo_success() {
        MediaRegisterInfoQueryDTO queryDTO = new MediaRegisterInfoQueryDTO();
        queryDTO.setMemberId(1L);
        queryDTO.setExhibitionId(2L);
        queryDTO.setIsFrontend(Boolean.TRUE);
        
        MemberBaseInfo baseInfo = new MemberBaseInfo();
        baseInfo.setId(1L);
        baseInfo.setMemberName("测试用户");
        when(mediaMemberDomainService.queryMemberBaseInfoByMemberId(any())).thenReturn(baseInfo);
        when(mediaMemberDomainService.queryMemberReporterInfoByMemberId(any(), any())).thenReturn(null);
        when(mediaMemberDomainService.queryMemberRecipientInfoByMemberId(any())).thenReturn(null);
        when(memberParticipateRecordRepository.queryByUserIdAndExhibitionId(any(), any())).thenReturn(null);
        
        MediaRegisterInfoVO result = service.getMediaRegisterInfo(queryDTO);
        assertNotNull(result);
        assertNotNull(result.getMemberBaseInfo());
        assertEquals(1L, result.getMemberBaseInfo().getId());
    }

    @Test
    void testSaveMediaDraftInfo_success() {
        MemberRegisterInfoDTO dto = new MemberRegisterInfoDTO();
        MemberBaseInfoDTO memberBaseInfo = new MemberBaseInfoDTO();
        memberBaseInfo.setMemberGender("M");
        memberBaseInfo.setMemberBirthDay(java.time.LocalDate.of(1990, 1, 1));
        memberBaseInfo.setMemberEmail("<EMAIL>");
        memberBaseInfo.setMemberMobile("13800000000");
        memberBaseInfo.setIdCategory("ID");
        memberBaseInfo.setIdNumber("1234567890");
        memberBaseInfo.setCountryCode("CN");
        dto.setMemberBaseInfo(memberBaseInfo);
        MemberReporterInfoDTO memberReporterInfo = new MemberReporterInfoDTO();
        memberReporterInfo.setExhibitionId(1L);
        memberReporterInfo.setMediaTypeCode("MEDIA");
        memberReporterInfo.setMediaTypeNameCn("媒体");
        memberReporterInfo.setMediaTypeNameEn("Media");
        memberReporterInfo.setMediaPositionCode("POS");
        memberReporterInfo.setMediaPositionNameCn("职位");
        memberReporterInfo.setMediaPositionNameEn("Position");
        memberReporterInfo.setIsApplyLiveStream(true);
        memberReporterInfo.setAttachmentHeadPhoto("photo.jpg");
        memberReporterInfo.setAttachmentOtherDescribe("desc");
        dto.setMemberReporterInfo(memberReporterInfo);
        MemberRecipientInfoDTO memberRecipientInfo = new MemberRecipientInfoDTO();
        dto.setMemberRecipientInfo(memberRecipientInfo);
        EnterpriseInfoDTO enterpriseInfo = new EnterpriseInfoDTO();
        enterpriseInfo.setEnterpriseName("企业名");
        enterpriseInfo.setEnterpriseType("类型");
        enterpriseInfo.setEnterpriseLocationCode("LOC");
        dto.setEnterpriseInfo(enterpriseInfo);
        ExhibitionInfoDTO exhibitionInfo = new ExhibitionInfoDTO();
        exhibitionInfo.setId(1L);
        exhibitionInfo.setExhibitionCode("EXPO2024");
        exhibitionInfo.setExhibitionNameCn("中国展会");
        exhibitionInfo.setExhibitionNameEn("China Expo");
        exhibitionInfo.setExhibitionSessionKey("2024");
        exhibitionInfo.setExhibitionTagCode("TAG001");
        exhibitionInfo.setRegisterTime("2024-06-24");
        exhibitionInfo.setRegisterStatus("REGISTERED");
        exhibitionInfo.setRegisterSystem("SYSTEM1");
        exhibitionInfo.setRegisterMethod("ONLINE");
        exhibitionInfo.setRegisterSource("WEB");
        dto.setExhibitionInfo(exhibitionInfo);
        dto.setRegisterLanguage("zh_CN");
        try (MockedStatic<com.dexpo.framework.security.core.util.SecurityFrameworkUtils> mocked = mockStatic(com.dexpo.framework.security.core.util.SecurityFrameworkUtils.class)) {
            mocked.when(com.dexpo.framework.security.core.util.SecurityFrameworkUtils::getLoginUserId).thenReturn(1L);
            doNothing().when(mediaMemberDomainService).addOrModifyMemberBaseInfo(any());
            doNothing().when(mediaMemberDomainService).addOrModifyMemberReporterInfo(any());
            doNothing().when(mediaMemberDomainService).addOrModifyMemberRecipientInfo(any());
            doNothing().when(memberParticipateRecordRepository).add(any());
            MediaSaveVO vo = service.saveMediaDraftInfo(dto);
            assertNotNull(vo);
        }
    }

    @Test
    void testSubmitMediaInfo_success() {
        MemberRegisterInfoDTO dto = new MemberRegisterInfoDTO();
        MemberBaseInfoDTO memberBaseInfo = new MemberBaseInfoDTO();
        memberBaseInfo.setMemberGender("M");
        memberBaseInfo.setMemberBirthDay(java.time.LocalDate.of(1990, 1, 1));
        memberBaseInfo.setMemberEmail("<EMAIL>");
        memberBaseInfo.setMemberMobile("13800000000");
        memberBaseInfo.setIdCategory("VO_ID_CATEGORY_1");
        memberBaseInfo.setIdNumber("1234567890");
        memberBaseInfo.setCountryCode("CN");
        dto.setMemberBaseInfo(memberBaseInfo);
        MemberReporterInfoDTO memberReporterInfo = new MemberReporterInfoDTO();
        memberReporterInfo.setExhibitionId(1L);
        memberReporterInfo.setMediaTypeCode("MEDIA");
        memberReporterInfo.setMediaTypeNameCn("媒体");
        memberReporterInfo.setMediaTypeNameEn("Media");
        memberReporterInfo.setMediaPositionCode("POS");
        memberReporterInfo.setMediaPositionNameCn("职位");
        memberReporterInfo.setMediaPositionNameEn("Position");
        memberReporterInfo.setIsApplyLiveStream(true);
        memberReporterInfo.setAttachmentHeadPhoto("photo.jpg");
        memberReporterInfo.setAttachmentOtherDescribe("desc");
        dto.setMemberReporterInfo(memberReporterInfo);
        MemberRecipientInfoDTO memberRecipientInfo = new MemberRecipientInfoDTO();
        dto.setMemberRecipientInfo(memberRecipientInfo);
        EnterpriseInfoDTO enterpriseInfo = new EnterpriseInfoDTO();
        enterpriseInfo.setEnterpriseName("企业名");
        enterpriseInfo.setEnterpriseType("类型");
        enterpriseInfo.setEnterpriseLocationCode("LOC");
        dto.setEnterpriseInfo(enterpriseInfo);
        ExhibitionInfoDTO exhibitionInfo = new ExhibitionInfoDTO();
        exhibitionInfo.setId(1L);
        exhibitionInfo.setExhibitionCode("EXPO2024");
        exhibitionInfo.setExhibitionNameCn("中国展会");
        exhibitionInfo.setExhibitionNameEn("China Expo");
        exhibitionInfo.setExhibitionSessionKey("2024");
        exhibitionInfo.setExhibitionTagCode("TAG001");
        exhibitionInfo.setRegisterTime("2024-06-24");
        exhibitionInfo.setRegisterStatus("REGISTERED");
        exhibitionInfo.setRegisterSystem("SYSTEM1");
        exhibitionInfo.setRegisterMethod("ONLINE");
        exhibitionInfo.setRegisterSource("WEB");
        dto.setExhibitionInfo(exhibitionInfo);
        dto.setRegisterLanguage("zh_CN");
        try (MockedStatic<com.dexpo.framework.security.core.util.SecurityFrameworkUtils> mocked = mockStatic(com.dexpo.framework.security.core.util.SecurityFrameworkUtils.class)) {
            mocked.when(com.dexpo.framework.security.core.util.SecurityFrameworkUtils::getLoginUserId).thenReturn(1L);
            doNothing().when(mediaMemberDomainService).addOrModifyMemberBaseInfo(any());
            doNothing().when(mediaMemberDomainService).addOrModifyMemberReporterInfo(any());
            doNothing().when(mediaMemberDomainService).addOrModifyMemberRecipientInfo(any());
            doNothing().when(memberParticipateRecordRepository).add(any());
            MediaProxyRecordCheckVO mediaProxyRecordCheckVO = new MediaProxyRecordCheckVO();
            mediaProxyRecordCheckVO.setMemberId(1L);
            VerificationVO data = new VerificationVO();
            data.setResult(true);
            when(verificationApi.Id2MetaVerify(any())).thenReturn(CommonResult.success(data));
            doNothing().when(mediaRegisterStatusEventProducer).mediaRegisterStatusEventChannel(any());
            doNothing().when(dataWarehouseChannelProducer).dataWarehouseChannel(any());
            Long result = service.submitMediaInfo(dto);
            assertNotNull(result);
        }
    }

    @Test
    void testSubmitMediaInfo_identityAuthFail() {
        MemberRegisterInfoDTO dto = new MemberRegisterInfoDTO();
        MemberBaseInfoDTO memberBaseInfo = new MemberBaseInfoDTO();
        memberBaseInfo.setMemberGender("M");
        memberBaseInfo.setMemberBirthDay(java.time.LocalDate.of(1990, 1, 1));
        memberBaseInfo.setMemberEmail("<EMAIL>");
        memberBaseInfo.setMemberMobile("13800000000");
        memberBaseInfo.setIdCategory("VO_ID_CATEGORY_1");
        memberBaseInfo.setIdNumber("1234567890");
        memberBaseInfo.setCountryCode("CN");
        dto.setMemberBaseInfo(memberBaseInfo);
        MemberReporterInfoDTO memberReporterInfo = new MemberReporterInfoDTO();
        memberReporterInfo.setExhibitionId(1L);
        memberReporterInfo.setMediaTypeCode("MEDIA");
        memberReporterInfo.setMediaTypeNameCn("媒体");
        memberReporterInfo.setMediaTypeNameEn("Media");
        memberReporterInfo.setMediaPositionCode("POS");
        memberReporterInfo.setMediaPositionNameCn("职位");
        memberReporterInfo.setMediaPositionNameEn("Position");
        memberReporterInfo.setIsApplyLiveStream(true);
        memberReporterInfo.setAttachmentHeadPhoto("photo.jpg");
        memberReporterInfo.setAttachmentOtherDescribe("desc");
        dto.setMemberReporterInfo(memberReporterInfo);
        MemberRecipientInfoDTO memberRecipientInfo = new MemberRecipientInfoDTO();
        dto.setMemberRecipientInfo(memberRecipientInfo);
        EnterpriseInfoDTO enterpriseInfo = new EnterpriseInfoDTO();
        enterpriseInfo.setEnterpriseName("企业名");
        enterpriseInfo.setEnterpriseType("类型");
        enterpriseInfo.setEnterpriseLocationCode("LOC");
        dto.setEnterpriseInfo(enterpriseInfo);
        ExhibitionInfoDTO exhibitionInfo = new ExhibitionInfoDTO();
        exhibitionInfo.setId(1L);
        exhibitionInfo.setExhibitionCode("EXPO2024");
        exhibitionInfo.setExhibitionNameCn("中国展会");
        exhibitionInfo.setExhibitionNameEn("China Expo");
        exhibitionInfo.setExhibitionSessionKey("2024");
        exhibitionInfo.setExhibitionTagCode("TAG001");
        exhibitionInfo.setRegisterTime("2024-06-24");
        exhibitionInfo.setRegisterStatus("REGISTERED");
        exhibitionInfo.setRegisterSystem("SYSTEM1");
        exhibitionInfo.setRegisterMethod("ONLINE");
        exhibitionInfo.setRegisterSource("WEB");
        dto.setExhibitionInfo(exhibitionInfo);
        dto.setRegisterLanguage("zh_CN");
        try (MockedStatic<com.dexpo.framework.security.core.util.SecurityFrameworkUtils> mocked = mockStatic(com.dexpo.framework.security.core.util.SecurityFrameworkUtils.class)) {
            mocked.when(com.dexpo.framework.security.core.util.SecurityFrameworkUtils::getLoginUserId).thenReturn(1L);
            VerificationVO failVO = new VerificationVO();
            failVO.setResult(false);
            when(verificationApi.Id2MetaVerify(any())).thenReturn(CommonResult.success(failVO));
            assertThrows(ServiceException.class, () -> service.submitMediaInfo(dto));
        }
    }

    @Test
    void testProxyRegister_success() {
        MemberRegisterInfoDTO dto = new MemberRegisterInfoDTO();
        MemberBaseInfoDTO memberBaseInfo = new MemberBaseInfoDTO();
        memberBaseInfo.setMemberGender("M");
        memberBaseInfo.setMemberBirthDay(java.time.LocalDate.of(1990, 1, 1));
        memberBaseInfo.setMemberEmail("<EMAIL>");
        memberBaseInfo.setMemberMobile("13800000000");
        memberBaseInfo.setIdCategory("VO_ID_CATEGORY_1");
        memberBaseInfo.setIdNumber("1234567890");
        memberBaseInfo.setCountryCode("CN");
        dto.setMemberBaseInfo(memberBaseInfo);
        MemberReporterInfoDTO memberReporterInfo = new MemberReporterInfoDTO();
        memberReporterInfo.setExhibitionId(1L);
        memberReporterInfo.setMediaTypeCode("MEDIA");
        memberReporterInfo.setMediaTypeNameCn("媒体");
        memberReporterInfo.setMediaTypeNameEn("Media");
        memberReporterInfo.setMediaPositionCode("POS");
        memberReporterInfo.setMediaPositionNameCn("职位");
        memberReporterInfo.setMediaPositionNameEn("Position");
        memberReporterInfo.setIsApplyLiveStream(true);
        memberReporterInfo.setAttachmentHeadPhoto("photo.jpg");
        memberReporterInfo.setAttachmentOtherDescribe("desc");
        dto.setMemberReporterInfo(memberReporterInfo);
        MemberRecipientInfoDTO memberRecipientInfo = new MemberRecipientInfoDTO();
        dto.setMemberRecipientInfo(memberRecipientInfo);
        EnterpriseInfoDTO enterpriseInfo = new EnterpriseInfoDTO();
        enterpriseInfo.setEnterpriseName("企业名");
        enterpriseInfo.setEnterpriseType("类型");
        enterpriseInfo.setEnterpriseLocationCode("LOC");
        dto.setEnterpriseInfo(enterpriseInfo);
        ExhibitionInfoDTO exhibitionInfo = new ExhibitionInfoDTO();
        exhibitionInfo.setId(1L);
        exhibitionInfo.setExhibitionCode("EXPO2024");
        exhibitionInfo.setExhibitionNameCn("中国展会");
        exhibitionInfo.setExhibitionNameEn("China Expo");
        exhibitionInfo.setExhibitionSessionKey("2024");
        exhibitionInfo.setExhibitionTagCode("TAG001");
        exhibitionInfo.setRegisterTime("2024-06-24");
        exhibitionInfo.setRegisterStatus("REGISTERED");
        exhibitionInfo.setRegisterSystem("SYSTEM1");
        exhibitionInfo.setRegisterMethod("ONLINE");
        exhibitionInfo.setRegisterSource("WEB");
        dto.setExhibitionInfo(exhibitionInfo);
        dto.setRegisterLanguage("zh_CN");
        doNothing().when(mediaMemberDomainService).addOrModifyMemberBaseInfo(any());
        doNothing().when(mediaMemberDomainService).addOrModifyMemberReporterInfo(any());
        doNothing().when(mediaMemberDomainService).addOrModifyMemberRecipientInfo(any());
        doNothing().when(memberBaseInfoOpt).setMemberBaseInfoCache(any(), any());
        doNothing().when(messageHandler).sendEmailMessage(any(), any(), any(), any());
        MediaProxyRecordCheckVO mediaProxyRecordCheckVO = new MediaProxyRecordCheckVO();
        mediaProxyRecordCheckVO.setMemberId(1L);
        when(participateRecordMapper.selectRegisterRecordByMemberIdAndExhibitionId(any(), any(), any())).thenReturn(mediaProxyRecordCheckVO);
        VerificationVO data = new VerificationVO();
        data.setResult(true);
        when(verificationApi.Id2MetaVerify(any())).thenReturn(CommonResult.success(data));
        Long result = service.proxyRegister(dto);
        assertEquals(1L, result);
    }

    @Test
    void testRecall_success() {
        MediaRecallDTO recallDTO = new MediaRecallDTO();
        recallDTO.setExhibitionId(1L);
        recallDTO.setEnterpriseName("测试企业");
        
        MemberParticipateRecord record = new MemberParticipateRecord();
        record.setId(1L);
        record.setMemberId(1L);
        record.setExhibitionId(1L);
        record.setRegisterStatus("VO_REGISTER_STATUS_2"); // 待审核状态
        
        when(mediaMemberDomainService.queryMemberParticipateRecordByUserIdAndExhibitionId(any(), any())).thenReturn(record);
        doNothing().when(mediaMemberDomainService).modifyMemberParticipateRecordById(any());
        doNothing().when(mediaRegisterStatusEventProducer).mediaRegisterStatusEventChannel(any());
        
        try (MockedStatic<com.dexpo.framework.security.core.util.SecurityFrameworkUtils> mocked = mockStatic(com.dexpo.framework.security.core.util.SecurityFrameworkUtils.class)) {
            mocked.when(com.dexpo.framework.security.core.util.SecurityFrameworkUtils::getLoginUserId).thenReturn(1L);
            mocked.when(com.dexpo.framework.security.core.util.SecurityFrameworkUtils::getLoginUserName).thenReturn("测试用户");
            
            Long result = service.recall(recallDTO);
            assertEquals(1L, result);
        }
    }

    @Test
    void testRecall_recordNotFound() {
        MediaRecallDTO recallDTO = new MediaRecallDTO();
        recallDTO.setExhibitionId(1L);
        
        when(mediaMemberDomainService.queryMemberParticipateRecordByUserIdAndExhibitionId(any(), any())).thenReturn(null);
        
        try (MockedStatic<com.dexpo.framework.security.core.util.SecurityFrameworkUtils> mocked = mockStatic(com.dexpo.framework.security.core.util.SecurityFrameworkUtils.class)) {
            mocked.when(com.dexpo.framework.security.core.util.SecurityFrameworkUtils::getLoginUserId).thenReturn(1L);
            
            assertThrows(ServiceException.class, () -> service.recall(recallDTO));
        }
    }

    @Test
    void testRecall_invalidStatus() {
        MediaRecallDTO recallDTO = new MediaRecallDTO();
        recallDTO.setExhibitionId(1L);
        
        MemberParticipateRecord record = new MemberParticipateRecord();
        record.setId(1L);
        record.setMemberId(1L);
        record.setExhibitionId(1L);
        record.setRegisterStatus("VO_REGISTER_STATUS_3"); // 已审核状态
        
        when(mediaMemberDomainService.queryMemberParticipateRecordByUserIdAndExhibitionId(any(), any())).thenReturn(record);
        
        try (MockedStatic<com.dexpo.framework.security.core.util.SecurityFrameworkUtils> mocked = mockStatic(com.dexpo.framework.security.core.util.SecurityFrameworkUtils.class)) {
            mocked.when(com.dexpo.framework.security.core.util.SecurityFrameworkUtils::getLoginUserId).thenReturn(1L);
            
            assertThrows(ServiceException.class, () -> service.recall(recallDTO));
        }
    }

    @Test
    void testGetMediaPage_success() {
        MediaPageListQueryDTO queryDTO = new MediaPageListQueryDTO();
        queryDTO.setExhibitionTagCodes(List.of("TAG001"));
        queryDTO.setExhibitionSessionKeys(List.of("2024"));
        
        ExhibitionVO exhibitionVO = new ExhibitionVO();
        exhibitionVO.setId(1L);
        exhibitionVO.setExhibitionNameCn("中国展会");
        exhibitionVO.setExhibitionNameEn("China Expo");
        exhibitionVO.setExhibitionCode("EXPO2024");
        exhibitionVO.setExhibitionYear(2024);
        exhibitionVO.setExhibitionSession(2024);
        
        when(exhibitionExternalService.getExhibitionMap(any())).thenReturn(java.util.Map.of(1L, exhibitionVO));
        
        MediaPageList mediaPageList = new MediaPageList();
        mediaPageList.setMemberId(1L);
        mediaPageList.setMemberName("测试用户");
        mediaPageList.setExhibitionId(1L);
        
        PageResult<MediaPageList> pageResult = new PageResult<>(List.of(mediaPageList), 1L);
        when(memberBaseInfoRepository.getMediaPage(any(), any())).thenReturn(pageResult);
        
        PageResult<MediaPageListVO> result = service.getMediaPage(queryDTO);
        assertNotNull(result);
        assertEquals(1L, result.getTotal());
        assertEquals(1, result.getList().size());
    }

    @Test
    void testGetMediaPage_emptyExhibitionMap() {
        MediaPageListQueryDTO queryDTO = new MediaPageListQueryDTO();
        queryDTO.setExhibitionTagCodes(List.of("TAG001"));
        queryDTO.setExhibitionSessionKeys(List.of("2024"));
        
        when(exhibitionExternalService.getExhibitionMap(any())).thenReturn(java.util.Map.of());
        
        PageResult<MediaPageListVO> result = service.getMediaPage(queryDTO);
        assertNotNull(result);
        assertEquals(0L, result.getTotal());
        assertTrue(result.getList().isEmpty());
    }

    @Test
    void testAudit_success() {
        MediaAuditDTO auditDTO = new MediaAuditDTO();
        auditDTO.setMemberParticipateIds(List.of(1L));
        auditDTO.setApproveResult("VO_APPROVE_RESULT_1"); // 审核通过
        auditDTO.setMediaPermissionType("VO_MEDIA_PERMISSION_TYPE_1");
        
        MediaAuditQueryVO queryVO = new MediaAuditQueryVO();
        queryVO.setMemberParticipateId(1L);
        queryVO.setMemberId(1L);
        queryVO.setExhibitionId(1L);
        queryVO.setRegisterStatus("VO_REGISTER_STATUS_2"); // 待审核
        queryVO.setEnterpriseId(1L);
        queryVO.setEnterpriseName("测试企业");
        queryVO.setIsUseAble(false);
        
        when(participateRecordMapper.selectMediaAuditQueryVO(any())).thenReturn(queryVO);
        
        MemberBaseInfo baseInfo = new MemberBaseInfo();
        baseInfo.setId(1L);
        baseInfo.setMemberName("测试用户");
        when(baseInfoService.getMemberBaseInfo(any())).thenReturn(baseInfo);
        
        doNothing().when(memberReporterInfoRepository).updateMediaPermissionType(any(), any(), any());
        doNothing().when(enterpriseInfoRepository).updateById(any());
        doNothing().when(memberApproveRecordRepository).addBatch(any());
        doNothing().when(memberParticipateRecordRepository).modifyBatch(any());
        doNothing().when(memberBaseInfoOpt).setMemberBaseInfoCache(any(), any());
        doNothing().when(dataWarehouseChannelProducer).dataWarehouseChannel(any());
        doNothing().when(messageHandler).sendSmsMessage(any(), any(), any(), any());
        doNothing().when(mediaRegisterStatusEventProducer).mediaRegisterStatusEventChannel(any());
        
        List<Long> result = service.audit(auditDTO);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1L, result.get(0));
    }

    @Test
    void testAudit_invalidApproveResult() {
        MediaAuditDTO auditDTO = new MediaAuditDTO();
        auditDTO.setMemberParticipateIds(List.of(1L));
        auditDTO.setApproveResult("INVALID_RESULT");
        
        assertThrows(ServiceException.class, () -> service.audit(auditDTO));
    }

    @Test
    void testAudit_recordNotFound() {
        MediaAuditDTO auditDTO = new MediaAuditDTO();
        auditDTO.setMemberParticipateIds(List.of(1L));
        auditDTO.setApproveResult("VO_APPROVE_RESULT_1");
        
        when(participateRecordMapper.selectMediaAuditQueryVO(any())).thenReturn(null);
        
        assertThrows(ServiceException.class, () -> service.audit(auditDTO));
    }

    @Test
    void testAudit_invalidStatus() {
        MediaAuditDTO auditDTO = new MediaAuditDTO();
        auditDTO.setMemberParticipateIds(List.of(1L));
        auditDTO.setApproveResult("VO_APPROVE_RESULT_1");
        
        MediaAuditQueryVO queryVO = new MediaAuditQueryVO();
        queryVO.setMemberParticipateId(1L);
        queryVO.setMemberId(1L);
        queryVO.setExhibitionId(1L);
        queryVO.setRegisterStatus("VO_REGISTER_STATUS_3"); // 已审核状态
        
        when(participateRecordMapper.selectMediaAuditQueryVO(any())).thenReturn(queryVO);
        
        assertThrows(ServiceException.class, () -> service.audit(auditDTO));
    }

    @Test
    void testAudit_missingPermissionType() {
        MediaAuditDTO auditDTO = new MediaAuditDTO();
        auditDTO.setMemberParticipateIds(List.of(1L));
        auditDTO.setApproveResult("VO_APPROVE_RESULT_1");
        // 不设置 mediaPermissionType
        
        MediaAuditQueryVO queryVO = new MediaAuditQueryVO();
        queryVO.setMemberParticipateId(1L);
        queryVO.setMemberId(1L);
        queryVO.setExhibitionId(1L);
        queryVO.setRegisterStatus("VO_REGISTER_STATUS_2");
        queryVO.setEnterpriseId(1L);
        queryVO.setEnterpriseName("测试企业");
        queryVO.setIsUseAble(false);
        
        when(participateRecordMapper.selectMediaAuditQueryVO(any())).thenReturn(queryVO);
        
        MemberBaseInfo baseInfo = new MemberBaseInfo();
        baseInfo.setId(1L);
        baseInfo.setMemberName("测试用户");
        when(baseInfoService.getMemberBaseInfo(any())).thenReturn(baseInfo);
        
        assertThrows(ServiceException.class, () -> service.audit(auditDTO));
    }

    @Test
    void testSyncData_success() {
        MediaSyncDataDTO dto = new MediaSyncDataDTO();
        dto.setExhibitionId(1L);
        dto.setMemberIds(List.of(1L, 2L));
        
        when(memberApproveRecordRepository.selectDataWarehouseSyncMessageList(any(), any(), any())).thenReturn(List.of());
        doNothing().when(dataWarehouseChannelProducer).dataWarehouseChannel(any());
        
        List<Long> result = service.syncData(dto);
        assertEquals(List.of(1L, 2L), result);
    }

    @Test
    void testInitMediaMemberBaseInfoToRedisCache_success() {
        MemberBaseInfo baseInfo1 = new MemberBaseInfo();
        baseInfo1.setId(1L);
        baseInfo1.setMemberMobile("13800000000");
        baseInfo1.setMemberEmail("<EMAIL>");
        
        MemberBaseInfo baseInfo2 = new MemberBaseInfo();
        baseInfo2.setId(2L);
        baseInfo2.setMemberMobile("13900000000");
        baseInfo2.setMemberEmail("<EMAIL>");
        
        when(memberBaseInfoRepository.queryAll()).thenReturn(List.of(baseInfo1, baseInfo2));
        doNothing().when(memberBaseInfoOpt).setMemberBaseInfoCache(any(), any());
        
        service.initMediaMemberBaseInfoToRedisCache();
        
        verify(memberBaseInfoOpt, times(4)).setMemberBaseInfoCache(any(), any());
    }
} 