package com.dexpo.module.log.api.dto;

import com.dexpo.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 展会协议信息 DO
 *
 * <AUTHOR>
 */
@Data
@ToString()
public class SysInterfaceLogDTO extends PageParam {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 集成接口：值集VS_INTERFACE
     */
    private String interfaceCode;

    /**
     * 传入的参数
     */
    private String params;

    /**
     * 返回的信息
     */
    private String returnMessage;

    /**
     * 返回编码
     */
    private String returnCode;

    /**
     * 错误消息
     */
    private Integer errorMessage;


    /**
     * '方法执行开始时间'
     */
    private LocalDateTime startTime;

    /**
     * '方法执行结束时间'
     */
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 