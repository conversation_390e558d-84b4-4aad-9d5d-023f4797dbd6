package com.dexpo.module.log.api;

import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.log.api.dto.SysOperateLogDTO;
import com.dexpo.module.log.api.vo.SysOperateLogVO;
import com.dexpo.module.log.enums.ApiConstants;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 *
 */
@FeignClient(name = ApiConstants.NAME)
public interface SysOperateLogApi {


    String API_PREFIX = "/sysOperateLog";

    /**
     *
     */
    @PostMapping(API_PREFIX + "/page")
    PageResult<SysOperateLogVO> page(@Valid @RequestBody SysOperateLogDTO dto);

    @PostMapping(API_PREFIX + "/insert")
    void insert(@Valid @RequestBody SysOperateLogDTO dto);
}
