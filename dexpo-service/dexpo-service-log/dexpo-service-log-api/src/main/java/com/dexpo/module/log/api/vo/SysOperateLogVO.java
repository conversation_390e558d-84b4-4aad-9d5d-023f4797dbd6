package com.dexpo.module.log.api.vo;

import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 展会协议信息 DO
 *
 * <AUTHOR>
 */
@Data
@ToString()
public class SysOperateLogVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 操作行为：值集VS_OPER_BEHAVIOR
     */
    private String operBehavior;

    /**
     * 操作结果：值集VS_OPER_RESULT
     */
    private String operResult;

    /**
     * 操作入参
     */
    private String operInputParams;


    /**
     * 操作出参
     */
    private String operOutputParams;

    /**
     * 错误消息
     */
    private Integer errorMessage;

    /**
     * 操作人ID
     */
    private String operUserId;

    /**
     * '操作人编码'
     */
    private String operUserCode;

    /**
     * 操作人类型：值集VS_ACTION_USER_TYPE
     */
    private String operUserType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;

    private String createUserName;

    private Long createUser;

    private String updateUserName;

    private Long updateUser;

} 