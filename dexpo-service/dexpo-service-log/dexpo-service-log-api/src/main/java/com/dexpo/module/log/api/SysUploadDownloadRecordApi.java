package com.dexpo.module.log.api;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.log.api.dto.SysUploadDownloadRecordDTO;
import com.dexpo.module.log.api.vo.SysUploadDownloadRecordVO;
import com.dexpo.module.log.enums.ApiConstants;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 *
 */
@FeignClient(name = ApiConstants.NAME)
public interface SysUploadDownloadRecordApi {


    String API_PREFIX = "/sysUploadDownloadRecord";

    /**
     *
     */
    @PostMapping(API_PREFIX + "/page")
    PageResult<SysUploadDownloadRecordVO> page(@Valid @RequestBody SysUploadDownloadRecordDTO dto);

    @PostMapping(API_PREFIX + "/findById")
    CommonResult<SysUploadDownloadRecordVO> findById(@Valid @RequestBody SysUploadDownloadRecordDTO dto);

    @PostMapping(API_PREFIX + "/list")
    CommonResult<List<SysUploadDownloadRecordVO>> list(@Valid @RequestBody SysUploadDownloadRecordDTO dto);

    @PostMapping(API_PREFIX + "/insert")
    CommonResult<SysUploadDownloadRecordVO> insert(@Valid @RequestBody SysUploadDownloadRecordDTO dto);

    @PostMapping(API_PREFIX + "/update")
    CommonResult<SysUploadDownloadRecordVO> updateDto(@Valid @RequestBody SysUploadDownloadRecordDTO dto);

    @PostMapping(API_PREFIX + "/updateTimeDto")
    void updateTimeDto(@RequestBody SysUploadDownloadRecordDTO sysUploadDownloadRecordDTO);
}
