package com.dexpo.module.log.controller;

import com.alibaba.fastjson.JSON;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.log.api.SysOperateLogApi;
import com.dexpo.module.log.api.dto.SysOperateLogDTO;
import com.dexpo.module.log.api.vo.SysOperateLogVO;
import com.dexpo.module.log.service.SysOperateLogService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * 展会信息 Controller
 */
@Tag(name = "展会信息")
@RestController
@RequiredArgsConstructor
public class SysOperateLogController implements SysOperateLogApi {

    private final SysOperateLogService sysOperateLogService;


    @Override
    public PageResult<SysOperateLogVO> page(SysOperateLogDTO dto) {
        return sysOperateLogService.pageByDto(dto);
    }

    @Override
    public void insert(SysOperateLogDTO dto) {
        sysOperateLogService.insert(JSON.toJSONString(dto));
    }
}