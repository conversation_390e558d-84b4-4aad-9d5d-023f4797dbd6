package com.dexpo.module.log.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;

import lombok.Data;

@Data
@TableName("biz_operate_log")
public class BizOperateLogDO extends BaseDO {

    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务ID
     */
    private Long businessId;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 业务类型名称
     */
    private String businessTypeName;

    /**
     * 操作行为
     */
    private String operBehavior;

    /**
     * 操作行为名称
     */
    private String operBehaviorName;

    /**
     * 操作结果
     */
    private String operResult;

    /**
     * 操作结果名称
     */
    private String operResultName;

    /**
     * 操作人ID
     */
    private Long operUserId;

    /**
     * 操作人编码
     */
    private String operUserCode;

    /**
     * 操作人姓名
     */
    private String operUserName;

    /**
     * 操作人类型
     */
    private String operUserType;

    /**
     * 操作人类型名称
     */
    private String operUserTypeName;

    /**
     * 备注
     */
    private String remark;

}
