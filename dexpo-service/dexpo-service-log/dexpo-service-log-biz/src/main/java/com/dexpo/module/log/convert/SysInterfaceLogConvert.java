package com.dexpo.module.log.convert;

import com.dexpo.framework.common.pojo.IConvert;
import com.dexpo.module.log.api.dto.SysInterfaceLogDTO;
import com.dexpo.module.log.api.vo.SysInterfaceLogVO;
import com.dexpo.module.log.dal.dataobject.SysInterfaceLogDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 展会信息 Convert
 */
@Mapper
public interface SysInterfaceLogConvert extends IConvert<SysInterfaceLogDTO, SysInterfaceLogVO, SysInterfaceLogDO> {

    SysInterfaceLogConvert INSTANCE = Mappers.getMapper(SysInterfaceLogConvert.class);

    SysInterfaceLogDO d2eByString(String message);
}