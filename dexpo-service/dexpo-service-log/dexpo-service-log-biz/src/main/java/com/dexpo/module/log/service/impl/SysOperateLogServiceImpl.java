package com.dexpo.module.log.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.framework.mybatis.core.query.QueryWrapperX;
import com.dexpo.module.log.api.dto.SysOperateLogDTO;
import com.dexpo.module.log.api.vo.SysOperateLogVO;
import com.dexpo.module.log.convert.SysOperateLogConvert;
import com.dexpo.module.log.dal.dataobject.SysOperateLogDO;
import com.dexpo.module.log.dal.mysql.SysOperateLogMapper;
import com.dexpo.module.log.service.SysOperateLogService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 操作日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SysOperateLogServiceImpl extends ServiceImpl<SysOperateLogMapper, SysOperateLogDO>
        implements SysOperateLogService {

    @Resource
    private SysOperateLogMapper sysOperateLogMapper;

    @Override
    public void insert(String message) {
        SysOperateLogDO sysOperateLogDO = SysOperateLogConvert.INSTANCE.d2eByString(message);
        sysOperateLogDO.setCreateTime(LocalDateTime.now());
        sysOperateLogMapper.insert(sysOperateLogDO);
    }

    @Override
    public PageResult<SysOperateLogVO> pageByDto(SysOperateLogDTO dto) {
        QueryWrapperX<SysOperateLogDO> wrapperX = new QueryWrapperX<>();
        PageResult<SysOperateLogDO> page = sysOperateLogMapper.selectPage(dto, wrapperX);
        PageResult<SysOperateLogVO> pageVo = new PageResult<>();
        pageVo.setList(SysOperateLogConvert.INSTANCE.e2vList(page.getList()));
        pageVo.setTotal(page.getTotal());
        return pageVo;
    }
}