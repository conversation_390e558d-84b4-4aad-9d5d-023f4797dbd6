package com.dexpo.module.log.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 展会协议信息 DO
 *
 * <AUTHOR>
 */
@TableName("sys_operate_log")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SysOperateLogDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 操作行为：值集VS_OPER_BEHAVIOR
     */
    private String operBehavior;

    /**
     * 操作结果：值集VS_OPER_RESULT
     */
    private String operResult;

    /**
     * 操作入参
     */
    private String operInputParams;


    /**
     * 操作出参
     */
    private String operOutputParams;

    /**
     * 错误消息
     */
    private Integer errorMessage;

    /**
     * 操作人ID
     */
    private String operUserId;

    /**
     * '操作人编码'
     */
    private String operUserCode;

    /**
     * 操作人类型：值集VS_ACTION_USER_TYPE
     */
    private String operUserType;

} 