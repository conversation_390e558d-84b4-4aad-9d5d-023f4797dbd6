package com.dexpo.module.log.convert;

import com.dexpo.framework.common.pojo.IConvert;
import com.dexpo.module.log.api.dto.SysUploadDownloadRecordDTO;
import com.dexpo.module.log.api.vo.SysUploadDownloadRecordVO;
import com.dexpo.module.log.dal.dataobject.SysUploadDownloadRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 展会信息 Convert
 */
@Mapper
public interface SysUploadDownloadRecordConvert
        extends IConvert<SysUploadDownloadRecordDTO, SysUploadDownloadRecordVO, SysUploadDownloadRecordDO> {

    SysUploadDownloadRecordConvert INSTANCE = Mappers.getMapper(SysUploadDownloadRecordConvert.class);

}