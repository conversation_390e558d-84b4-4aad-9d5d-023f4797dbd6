package com.dexpo.module.log.mq;

import com.dexpo.module.log.service.SysInterfaceLogService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.function.Consumer;

@Slf4j
@Configuration
public class SysInterfaceLogConsumer {

    @Resource
    private SysInterfaceLogService sysInterfaceLogService;

    @Bean()
    public Consumer<String> sysInterfaceLogChannel() {
        return message -> sysInterfaceLogService.insert(message);
    }

}
