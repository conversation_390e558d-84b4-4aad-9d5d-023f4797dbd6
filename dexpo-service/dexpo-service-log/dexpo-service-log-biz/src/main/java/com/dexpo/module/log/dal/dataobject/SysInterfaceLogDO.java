package com.dexpo.module.log.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 展会协议信息 DO
 *
 * <AUTHOR>
 */
@TableName("sys_interface_log")
@Data
@ToString(callSuper = true)
public class SysInterfaceLogDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 集成接口：值集VS_INTERFACE
     */
    private String interfaceCode;

    /**
     * 传入的参数
     */
    private String params;

    /**
     * 返回的信息
     */
    private String returnMessage;

    /**
     * 返回编码
     */
    private String returnCode;

    /**
     * 错误消息
     */
    private Integer errorMessage;


    /**
     * '方法执行开始时间'
     */
    private LocalDateTime startTime;

    /**
     * '方法执行结束时间'
     */
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 