package com.dexpo.module.log;


import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 项目的启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.dexpo.framework.security", "com.dexpo.module.log","com.dexpo.framework.web"},
        exclude = {MetricsAutoConfiguration.class, DataSourceAutoConfiguration.class})
@Slf4j
@EnableDiscoveryClient
@EnableFeignClients()
public class LogServerApplication {

    public static void main(String[] args) {

        SpringApplication.run(LogServerApplication.class, args);
        log.info("启动成功！");
    }

}
