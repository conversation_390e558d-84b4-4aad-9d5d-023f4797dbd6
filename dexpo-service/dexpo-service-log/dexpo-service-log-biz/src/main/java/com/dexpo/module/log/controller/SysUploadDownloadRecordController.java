package com.dexpo.module.log.controller;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.log.api.SysUploadDownloadRecordApi;
import com.dexpo.module.log.api.dto.SysUploadDownloadRecordDTO;
import com.dexpo.module.log.api.vo.SysUploadDownloadRecordVO;
import com.dexpo.module.log.service.SysUploadDownloadRecordService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 异步上传下载记录
 */
@Tag(name = "异步上传下载记录")
@RestController
@RequiredArgsConstructor
public class SysUploadDownloadRecordController implements SysUploadDownloadRecordApi {

    @Resource
    private SysUploadDownloadRecordService sysUploadDownloadRecordService;

    @Override
    public PageResult<SysUploadDownloadRecordVO> page(SysUploadDownloadRecordDTO dto) {
        return sysUploadDownloadRecordService.pageByDto(dto);
    }

    @Override
    public CommonResult<SysUploadDownloadRecordVO> findById(SysUploadDownloadRecordDTO dto) {
        return CommonResult.success(sysUploadDownloadRecordService.findById(dto));
    }

    @Override
    public CommonResult<List<SysUploadDownloadRecordVO>> list(SysUploadDownloadRecordDTO dto) {
        return sysUploadDownloadRecordService.listByDto(dto);
    }

    @Override
    public CommonResult<SysUploadDownloadRecordVO> insert(SysUploadDownloadRecordDTO dto) {
        SysUploadDownloadRecordVO vo = sysUploadDownloadRecordService.insert(dto);
        return CommonResult.success(vo);
    }

    @Override
    public CommonResult<SysUploadDownloadRecordVO> updateDto(SysUploadDownloadRecordDTO dto) {
        SysUploadDownloadRecordVO vo = sysUploadDownloadRecordService.updateDto(dto);
        return CommonResult.success(vo);
    }

    @Override
    public void updateTimeDto(SysUploadDownloadRecordDTO dto) {
        sysUploadDownloadRecordService.updateTimeDto(dto);
    }
}