package com.dexpo.module.log.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.framework.mybatis.core.query.QueryWrapperX;
import com.dexpo.module.log.api.dto.SysInterfaceLogDTO;
import com.dexpo.module.log.api.dto.SysInterfaceLogMessage;
import com.dexpo.module.log.api.vo.SysInterfaceLogVO;
import com.dexpo.module.log.convert.SysInterfaceLogConvert;
import com.dexpo.module.log.dal.dataobject.SysInterfaceLogDO;
import com.dexpo.module.log.dal.mysql.SysInterfaceLogMapper;
import com.dexpo.module.log.service.SysInterfaceLogService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 数据集成 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SysInterfaceLogServiceImpl extends ServiceImpl<SysInterfaceLogMapper, SysInterfaceLogDO>
        implements SysInterfaceLogService {

    @Resource
    private SysInterfaceLogMapper sysInterfaceLogMapper;

    @Override
    public void insert(SysInterfaceLogMessage message) {
        SysInterfaceLogDO sysInterfaceLogDO = SysInterfaceLogConvert.INSTANCE.message2DO(message);
        sysInterfaceLogDO.setCreateTime(LocalDateTime.now());
        sysInterfaceLogMapper.insert(sysInterfaceLogDO);
    }

    @Override
    public PageResult<SysInterfaceLogVO> pageByDto(SysInterfaceLogDTO dto) {
        QueryWrapperX<SysInterfaceLogDO> wrapperX = new QueryWrapperX<>();
        PageResult<SysInterfaceLogDO> page = sysInterfaceLogMapper.selectPage(dto, wrapperX);
        PageResult<SysInterfaceLogVO> pageVo = new PageResult<>();
        pageVo.setList(SysInterfaceLogConvert.INSTANCE.e2vList(page.getList()));
        pageVo.setTotal(page.getTotal());
        return pageVo;
    }
}