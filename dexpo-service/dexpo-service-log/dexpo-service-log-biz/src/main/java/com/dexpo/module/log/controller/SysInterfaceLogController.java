package com.dexpo.module.log.controller;

import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.log.api.SysInterfaceLogApi;
import com.dexpo.module.log.api.dto.SysInterfaceLogDTO;
import com.dexpo.module.log.api.vo.SysInterfaceLogVO;
import com.dexpo.module.log.service.SysInterfaceLogService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * 展会信息 Controller
 */
@Tag(name = "展会信息")
@RestController
@RequiredArgsConstructor
public class SysInterfaceLogController implements SysInterfaceLogApi {

    private final SysInterfaceLogService sysInterfaceLogService;


    @Override
    public PageResult<SysInterfaceLogVO> page(SysInterfaceLogDTO dto) {
        return sysInterfaceLogService.pageByDto(dto);
    }

}