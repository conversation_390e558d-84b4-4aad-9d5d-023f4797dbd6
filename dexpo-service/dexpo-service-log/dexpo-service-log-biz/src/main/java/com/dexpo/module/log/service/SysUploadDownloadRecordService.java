package com.dexpo.module.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.log.api.dto.SysUploadDownloadRecordDTO;
import com.dexpo.module.log.api.vo.SysUploadDownloadRecordVO;
import com.dexpo.module.log.dal.dataobject.SysUploadDownloadRecordDO;

import java.util.List;

/**
 * 展会信息 Service 接口
 */
public interface SysUploadDownloadRecordService extends IService<SysUploadDownloadRecordDO> {

    PageResult<SysUploadDownloadRecordVO> pageByDto(SysUploadDownloadRecordDTO dto);

    SysUploadDownloadRecordVO insert(SysUploadDownloadRecordDTO dto);

    SysUploadDownloadRecordVO updateDto(SysUploadDownloadRecordDTO dto);

    CommonResult<List<SysUploadDownloadRecordVO>> listByDto(SysUploadDownloadRecordDTO dto);

    SysUploadDownloadRecordVO findById(SysUploadDownloadRecordDTO dto);

    void updateTimeDto(SysUploadDownloadRecordDTO dto);
} 