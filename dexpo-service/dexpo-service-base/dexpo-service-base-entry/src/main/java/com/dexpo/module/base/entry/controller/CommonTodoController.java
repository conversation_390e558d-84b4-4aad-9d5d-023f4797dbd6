package com.dexpo.module.base.entry.controller;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.base.api.basic.CommonTodoApi;
import com.dexpo.module.base.api.basic.dto.CommenTodoPageQueryDTO;
import com.dexpo.module.base.api.basic.vo.CommonTodoVO;
import com.dexpo.module.base.app.api.CommonTodoAppService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "待办")
@RestController
@RequiredArgsConstructor
public class CommonTodoController implements CommonTodoApi {

    private final CommonTodoAppService commonTodoAppService;

    @Override
    public CommonResult<PageResult<CommonTodoVO>> getPage(@Valid @RequestBody CommenTodoPageQueryDTO req) {
        return CommonResult.success(commonTodoAppService.getPage(req));
    }

}
