package com.dexpo.module.base.entry.controller;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.base.api.basic.BasicRegionApi;
import com.dexpo.module.base.api.basic.dto.BasicRegionDTO;
import com.dexpo.module.base.api.basic.vo.BasicRegionTreeVO;
import com.dexpo.module.base.api.basic.vo.BasicRegionVO;
import com.dexpo.module.base.app.api.BasicRegionAppService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@Validated
public class BasicRegionController implements BasicRegionApi {

    @Resource
    private BasicRegionAppService baseRegionAppService;
    @Override
    public CommonResult<List<BasicRegionVO>> getRegionList(@Valid @RequestBody BasicRegionDTO regionDTO) {
        return CommonResult.success(baseRegionAppService.getRegionList(regionDTO));
    }

    @Override
    public List<BasicRegionTreeVO> getRegionListAll() {
        return baseRegionAppService.getRegionListAll();
    }

    @Override
    public Map<String, Map<String, BasicRegionVO>> getRegionListByLevel() {
        return baseRegionAppService.getRegionListByLevel();
    }
}
