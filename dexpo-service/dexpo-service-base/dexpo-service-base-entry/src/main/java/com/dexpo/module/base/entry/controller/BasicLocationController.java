package com.dexpo.module.base.entry.controller;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.base.api.basic.BasicLocationApi;
import com.dexpo.module.base.api.basic.dto.BasicExhibitionLocationDTO;
import com.dexpo.module.base.api.basic.dto.BasicLocationDTO;
import com.dexpo.module.base.api.basic.vo.BasicLocationVO;
import com.dexpo.module.base.app.api.BasicLocationAppService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Validated
public class BasicLocationController implements BasicLocationApi {
    @Resource
    private BasicLocationAppService basicLocationAppService;

    @Override
    public CommonResult<List<BasicLocationVO>> getLocationList(@Valid @RequestBody BasicLocationDTO locationDTO) {
        return CommonResult.success(basicLocationAppService.getLocationList(locationDTO));
    }

    @Override
    public CommonResult<List<BasicLocationVO>> getExhibitionLocationList(@Valid @RequestBody BasicExhibitionLocationDTO exhibitionLocationDTO) {
        return CommonResult.success(basicLocationAppService.getExhibitionLocationList(exhibitionLocationDTO));
    }
}
