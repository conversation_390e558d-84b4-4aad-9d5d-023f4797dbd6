package com.dexpo.module.base.entry.controller;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.base.api.basic.OrganizationApi;
import com.dexpo.module.base.api.basic.dto.UserPageQueryDTO;
import com.dexpo.module.base.api.basic.vo.ManageOrganizationVO;
import com.dexpo.module.base.api.basic.vo.SysOrganizationVO;
import com.dexpo.module.base.api.basic.vo.UserPageVO;
import com.dexpo.module.base.app.api.OrganizationAppService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "组织")
@RestController
@RequiredArgsConstructor
public class OrganizationController implements OrganizationApi {

    private final OrganizationAppService organizationAppService;

    @Override
    public CommonResult<List<SysOrganizationVO>> getOrganizationList(){
        return CommonResult.success(organizationAppService.getOrganizationList());
    }

    @Override
    public CommonResult<List<ManageOrganizationVO>> getManageOrganizationList() {
        return CommonResult.success(organizationAppService.getManageOrganizationList());
    }

    @Override
    public CommonResult<PageResult<UserPageVO>> getUserPage(UserPageQueryDTO req) {

        return CommonResult.success(organizationAppService.getUserPage(req));
    }
}
