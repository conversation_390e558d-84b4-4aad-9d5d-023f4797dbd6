package com.dexpo.module.base.entry.controller;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.base.api.basic.dto.CommenTodoPageQueryDTO;
import com.dexpo.module.base.api.basic.vo.CommonTodoVO;
import com.dexpo.module.base.app.api.CommonTodoAppService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CommonTodoControllerTest {
    @Mock
    private CommonTodoAppService commonTodoAppService;
    @InjectMocks
    private CommonTodoController controller;

    @Test
    void getPage_shouldReturnSuccess() {
        CommenTodoPageQueryDTO req = new CommenTodoPageQueryDTO();
        PageResult<CommonTodoVO> pageResult = new PageResult<>(List.of(new CommonTodoVO()), 1L);
        when(commonTodoAppService.getPage(any())).thenReturn(pageResult);
        CommonResult<PageResult<CommonTodoVO>> result = controller.getPage(req);
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getTotal());
    }
} 