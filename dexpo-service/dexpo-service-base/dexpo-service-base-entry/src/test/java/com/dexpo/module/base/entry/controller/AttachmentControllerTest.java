package com.dexpo.module.base.entry.controller;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.security.core.LoginUser;
import com.dexpo.framework.security.core.util.SecurityFrameworkUtils;
import com.dexpo.module.base.api.attachment.dto.AttachmentInfoDTO;
import com.dexpo.module.base.api.attachment.vo.AttachmentInfoVO;
import com.dexpo.module.base.app.api.AttachmentInfoAppService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AttachmentControllerTest {
    @Mock
    private AttachmentInfoAppService attachmentInfoAppService;
    @InjectMocks
    private AttachmentController controller;

    @Test
    void findFileById_shouldReturnSuccess() {
        try (var staticMock = Mockito.mockStatic(SecurityFrameworkUtils.class)) {
            LoginUser user = new LoginUser();
            staticMock.when(SecurityFrameworkUtils::getLoginUser).thenReturn(user);
            AttachmentInfoVO vo = new AttachmentInfoVO();
            when(attachmentInfoAppService.findById(any())).thenReturn(vo);
            CommonResult<AttachmentInfoVO> result = controller.findFileById(1L);
            assertNotNull(result);
            assertEquals(0, result.getCode());
            assertSame(vo, result.getData());
        }
    }

    @Test
    void createAttachment_shouldReturnSuccess() {
        try (var staticMock = Mockito.mockStatic(SecurityFrameworkUtils.class)) {
            LoginUser user = new LoginUser();
            staticMock.when(SecurityFrameworkUtils::getLoginUser).thenReturn(user);
            AttachmentInfoDTO dto = new AttachmentInfoDTO();
            AttachmentInfoVO vo = new AttachmentInfoVO();
            when(attachmentInfoAppService.createAttachment(any(), any())).thenReturn(vo);
            CommonResult<AttachmentInfoVO> result = controller.createAttachment(dto);
            assertNotNull(result);
            assertEquals(0, result.getCode());
            assertSame(vo, result.getData());
        }
    }

    @Test
    void findFileByIds_shouldReturnList() {
        List<Long> ids = List.of(1L, 2L);
        List<AttachmentInfoVO> voList = List.of(new AttachmentInfoVO(), new AttachmentInfoVO());
        when(attachmentInfoAppService.findByIdList(ids)).thenReturn(voList);
        CommonResult<List<AttachmentInfoVO>> result = controller.findFileByIds(ids);
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(2, result.getData().size());
    }

    @Test
    void findFileByBusinessType_shouldReturnResult() {
        AttachmentInfoVO vo = new AttachmentInfoVO();
        CommonResult<AttachmentInfoVO> serviceResult = CommonResult.success(vo);
        when(attachmentInfoAppService.findFileByBusinessType(any())).thenReturn(serviceResult);
        CommonResult<AttachmentInfoVO> result = controller.findFileByBusinessType("type");
        assertNotNull(result);
        assertSame(serviceResult, result);
    }
} 