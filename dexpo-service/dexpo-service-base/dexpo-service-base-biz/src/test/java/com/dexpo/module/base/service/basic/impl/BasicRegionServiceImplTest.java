package com.dexpo.module.base.service.basic.impl;

import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.module.base.api.basic.dto.BasicRegionDTO;
import com.dexpo.module.base.api.basic.vo.BasicRegionVO;
import com.dexpo.module.base.convert.basic.BasicRegionConvert;
import com.dexpo.module.base.dal.dataobject.basic.BasicRegionDO;
import com.dexpo.module.base.dal.mysql.basic.BasicRegionMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BasicRegionServiceImplTest {

    @Mock
    private BasicRegionMapper basicRegionMapper;

    @InjectMocks
    private BasicRegionServiceImpl basicRegionService;

    @BeforeEach
    void setUp() {
        // 设置 baseMapper
        ReflectionTestUtils.setField(basicRegionService, "baseMapper", basicRegionMapper);
    }

    @Test
    void getRegionList_Success() {
        // Arrange
        BasicRegionDTO regionDTO = new BasicRegionDTO();
        regionDTO.setLevel("1");
        regionDTO.setParentAdcode("100000");
        
        // 创建测试数据
        BasicRegionDO regionDO = new BasicRegionDO();
        regionDO.setLevel("1");
        regionDO.setParentAdcode("100000");
        List<BasicRegionDO> regionDOList = Arrays.asList(regionDO);
        List<BasicRegionVO> expectedResult = BasicRegionConvert.INSTANCE.e2vList(regionDOList);
        
        // 模拟selectByLevelAndParentAdcode方法
        doReturn(regionDOList).when(basicRegionMapper).selectByLevelAndParentAdcode(
            eq(regionDTO.getLevel()), 
            eq(regionDTO.getParentAdcode())
        );

        // Act
        List<BasicRegionVO> result = basicRegionService.getRegionList(regionDTO);

        // Assert
        assertEquals(expectedResult, result);
        verify(basicRegionMapper).selectByLevelAndParentAdcode(
            eq(regionDTO.getLevel()), 
            eq(regionDTO.getParentAdcode())
        );
    }

    @Test
    void getRegionList_WithNullLevel() {
        // Arrange
        BasicRegionDTO regionDTO = new BasicRegionDTO();
        regionDTO.setParentAdcode("100000");
        
        // 创建测试数据
        BasicRegionDO regionDO = new BasicRegionDO();
        regionDO.setParentAdcode("100000");
        List<BasicRegionDO> regionDOList = Arrays.asList(regionDO);
        List<BasicRegionVO> expectedResult = BasicRegionConvert.INSTANCE.e2vList(regionDOList);
        
        // 模拟selectByLevelAndParentAdcode方法
        doReturn(regionDOList).when(basicRegionMapper).selectByLevelAndParentAdcode(
            eq(null), 
            eq(regionDTO.getParentAdcode())
        );

        // Act
        List<BasicRegionVO> result = basicRegionService.getRegionList(regionDTO);

        // Assert
        assertEquals(expectedResult, result);
        verify(basicRegionMapper).selectByLevelAndParentAdcode(
            eq(null), 
            eq(regionDTO.getParentAdcode())
        );
    }

    @Test
    void getRegionList_WithNullParentAdcode() {
        // Arrange
        BasicRegionDTO regionDTO = new BasicRegionDTO();
        regionDTO.setLevel("1");
        
        // 创建测试数据
        BasicRegionDO regionDO = new BasicRegionDO();
        regionDO.setLevel("1");
        List<BasicRegionDO> regionDOList = Arrays.asList(regionDO);
        List<BasicRegionVO> expectedResult = BasicRegionConvert.INSTANCE.e2vList(regionDOList);
        
        // 模拟selectByLevelAndParentAdcode方法
        doReturn(regionDOList).when(basicRegionMapper).selectByLevelAndParentAdcode(
            eq(regionDTO.getLevel()), 
            eq(null)
        );

        // Act
        List<BasicRegionVO> result = basicRegionService.getRegionList(regionDTO);

        // Assert
        assertEquals(expectedResult, result);
        verify(basicRegionMapper).selectByLevelAndParentAdcode(
            eq(regionDTO.getLevel()), 
            eq(null)
        );
    }

    @Test
    void getRegionList_WithAllNullParams_ShouldThrowException() {
        // Arrange
        BasicRegionDTO regionDTO = new BasicRegionDTO();

        // Act & Assert
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            basicRegionService.getRegionList(regionDTO);
        });

        assertEquals(90001, exception.getCode());
        assertEquals("参数不能全部为空", exception.getMessage());
        verify(basicRegionMapper, never()).selectByLevelAndParentAdcode(any(), any());
    }
} 