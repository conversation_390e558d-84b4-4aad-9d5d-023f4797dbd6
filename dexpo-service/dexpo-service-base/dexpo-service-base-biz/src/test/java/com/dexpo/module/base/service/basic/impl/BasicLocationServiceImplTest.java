package com.dexpo.module.base.service.basic.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dexpo.module.base.api.basic.dto.BasicExhibitionLocationDTO;
import com.dexpo.module.base.api.basic.dto.BasicLocationDTO;
import com.dexpo.module.base.api.basic.vo.BasicLocationVO;
import com.dexpo.module.base.convert.basic.BasicLocationConvert;
import com.dexpo.module.base.dal.dataobject.basic.BasicLocationDO;
import com.dexpo.module.base.dal.mysql.basic.BasicLocationMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BasicLocationServiceImplTest {

    @Mock
    private BasicLocationMapper basicLocationMapper;

    @InjectMocks
    private BasicLocationServiceImpl basicLocationService;

    @BeforeEach
    void setUp() {
        // 设置 baseMapper
        ReflectionTestUtils.setField(basicLocationService, "baseMapper", basicLocationMapper);
    }

    @Test
    void getLocationList() {
        // Arrange
        BasicLocationDTO locationDTO = new BasicLocationDTO();
        locationDTO.setLocationTag("TEST_TAG");
        
        // 创建测试数据
        BasicLocationDO locationDO = new BasicLocationDO();
        locationDO.setLocationTag("TEST_TAG");
        List<BasicLocationDO> locationDOList = Arrays.asList(locationDO);
        List<BasicLocationVO> expectedResult = BasicLocationConvert.INSTANCE.e2vList(locationDOList);
        
        // 模拟selectList方法，当传入正确的LambdaQueryWrapper时返回测试数据
        doReturn(locationDOList).when(basicLocationMapper).selectList(any(LambdaQueryWrapper.class));

        // Act
        List<BasicLocationVO> result = basicLocationService.getLocationList(locationDTO);

        // Assert
        assertEquals(expectedResult, result);
        verify(basicLocationMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    void getExhibitionLocationList() {
        // Arrange
        BasicExhibitionLocationDTO exhibitionLocationDTO = new BasicExhibitionLocationDTO();
        exhibitionLocationDTO.setExhibitionTagCode("TEST_TAG_CODE");
        List<BasicLocationDO> locationDOList = Arrays.asList(new BasicLocationDO());
        List<BasicLocationVO> expectedResult = BasicLocationConvert.INSTANCE.e2vList(locationDOList);
        doReturn(locationDOList).when(basicLocationMapper).selectByExhibitionTagCode(exhibitionLocationDTO.getExhibitionTagCode());

        // Act
        List<BasicLocationVO> result = basicLocationService.getExhibitionLocationList(exhibitionLocationDTO);

        // Assert
        assertEquals(expectedResult, result);
        verify(basicLocationMapper).selectByExhibitionTagCode(exhibitionLocationDTO.getExhibitionTagCode());
    }

    @Test
    void save() {
        // Arrange
        BasicLocationDO locationDO = new BasicLocationDO();
        locationDO.setId(1L);
        doReturn(1).when(basicLocationMapper).insert(locationDO);

        // Act
        boolean result = basicLocationService.save(locationDO);

        // Assert
        assertEquals(true, result);
        verify(basicLocationMapper).insert(locationDO);
    }

    @Test
    void updateById() {
        // Arrange
        BasicLocationDO locationDO = new BasicLocationDO();
        locationDO.setId(1L);
        doReturn(1).when(basicLocationMapper).updateById(locationDO);

        // Act
        boolean result = basicLocationService.updateById(locationDO);

        // Assert
        assertEquals(true, result);
        verify(basicLocationMapper).updateById(locationDO);
    }

    @Test
    void removeById() {
        // Arrange
        Long id = 1L;
        doReturn(1).when(basicLocationMapper).deleteById(id);

        // Act
        boolean result = basicLocationService.removeById(id);

        // Assert
        assertEquals(true, result);
        verify(basicLocationMapper).deleteById(id);
    }
} 