package com.dexpo.module.base.service.basic.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dexpo.framework.cache.redis.constant.BasicRedisKey;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.module.base.api.basic.dto.ExhibitionTagValuesetDTO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;
import com.dexpo.module.base.dal.dataobject.basic.ExhibitionValuesetOptionRelationDO;
import com.dexpo.module.base.dal.mysql.ExhibitionValuesetOptionRelationMapper;
import com.dexpo.module.base.service.basic.BasicValuesetOptionService;
import com.dexpo.module.base.service.basic.entity.ExhibitionValuesetOptionRelationCache;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

class ExhibitionValuesetOptionRelationServiceImplTest {

    @InjectMocks
    private ExhibitionValuesetOptionRelationServiceImpl exhibitionValuesetOptionRelationService;

    @Mock
    private BasicValuesetOptionService valuesetOptionService;

    @Mock
    private RedisService redisService;

    @Mock
    private ExhibitionValuesetOptionRelationMapper exhibitionValuesetOptionRelationMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // 设置 baseMapper
        ReflectionTestUtils.setField(exhibitionValuesetOptionRelationService, "baseMapper", exhibitionValuesetOptionRelationMapper);
    }

    /**
     * 测试场景：从Redis缓存中成功获取展会值集选项关系列表
     * 预期结果：返回正确的值集信息列表
     */
    @Test
    void testGetExhibitionValuesetListByCodesWithCache() {
        // 准备测试数据
        String exhibitionTagCode = "TEST_TAG";
        String valuesetOptionCode = "TEST_OPTION";
        ExhibitionTagValuesetDTO request = new ExhibitionTagValuesetDTO();
        request.setExhibitionTagCode(exhibitionTagCode);
        request.setValuesetList(Collections.singletonList("TEST_VALUESET"));

        // 模拟Redis缓存数据
        List<ExhibitionValuesetOptionRelationCache> cacheList = Collections.singletonList(
                new ExhibitionValuesetOptionRelationCache().setValuesetOptionCode(valuesetOptionCode)
        );
        when(redisService.getCacheObject(anyString())).thenReturn(cacheList);

        // 模拟值集服务返回数据
        List<BasicValuesetInfoVO> expectedResult = Collections.singletonList(new BasicValuesetInfoVO());
        when(valuesetOptionService.listByValuesetOptionCodes(any(), any())).thenReturn(expectedResult);

        // 执行测试
        List<BasicValuesetInfoVO> result = exhibitionValuesetOptionRelationService
                .getExhibitionValuesetListByCodes(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(redisService).getCacheObject(anyString());
        verify(valuesetOptionService).listByValuesetOptionCodes(any(), any());
    }

    /**
     * 测试场景：Redis缓存为空，需要从数据库初始化缓存
     * 预期结果：成功初始化缓存并返回正确的值集信息列表
     */
    @Test
    void testGetExhibitionValuesetListByCodesWithoutCache() {
        // 准备测试数据
        String exhibitionTagCode = "TEST_TAG";
        String valuesetOptionCode = "TEST_OPTION";
        ExhibitionTagValuesetDTO request = new ExhibitionTagValuesetDTO();
        request.setExhibitionTagCode(exhibitionTagCode);
        request.setValuesetList(Collections.singletonList("TEST_VALUESET"));

        // 模拟Redis缓存为空
        when(redisService.getCacheObject(anyString())).thenReturn(null);

        // 模拟数据库数据
        ExhibitionValuesetOptionRelationDO relationDO = new ExhibitionValuesetOptionRelationDO();
        relationDO.setExhibitionTagCode(exhibitionTagCode);
        relationDO.setValuesetOptionCode(valuesetOptionCode);
        when(exhibitionValuesetOptionRelationMapper.selectList(any())).thenReturn(Collections.singletonList(relationDO));

        // 模拟值集服务返回数据
        List<BasicValuesetInfoVO> expectedResult = Collections.singletonList(new BasicValuesetInfoVO());
        when(valuesetOptionService.listByValuesetOptionCodes(any(), any())).thenReturn(expectedResult);

        // 执行测试
        List<BasicValuesetInfoVO> result = exhibitionValuesetOptionRelationService
                .getExhibitionValuesetListByCodes(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(redisService).setCacheObject(anyString(), any());
        verify(exhibitionValuesetOptionRelationMapper).selectList(any());
    }

    /**
     * 测试场景：缓存和数据库都没有相关数据
     * 预期结果：返回null
     */
    @Test
    void testGetExhibitionValuesetListByCodesWithNoData() {
        // 准备测试数据
        ExhibitionTagValuesetDTO request = new ExhibitionTagValuesetDTO();
        request.setExhibitionTagCode("TEST_TAG");
        request.setValuesetList(Collections.singletonList("TEST_VALUESET"));

        // 模拟Redis缓存为空
        when(redisService.getCacheObject(anyString())).thenReturn(null);

        // 模拟数据库数据为空
        when(exhibitionValuesetOptionRelationMapper.selectList(any())).thenReturn(Collections.emptyList());

        // 执行测试
        List<BasicValuesetInfoVO> result = exhibitionValuesetOptionRelationService
                .getExhibitionValuesetListByCodes(request);

        // 验证结果
        assertNull(result);
        verify(exhibitionValuesetOptionRelationMapper).selectList(any());
    }

    /**
     * 测试场景：初始化展会值集选项关系缓存
     * 预期结果：成功初始化缓存并返回正确的映射关系
     */
    @Test
    void testInitExhibitionValuesetOptionCache() {
        // 准备测试数据
        String exhibitionTagCode1 = "TAG_1";
        String exhibitionTagCode2 = "TAG_2";
        String valuesetOptionCode1 = "OPTION_1";
        String valuesetOptionCode2 = "OPTION_2";

        // 模拟数据库数据
        ExhibitionValuesetOptionRelationDO relation1 = new ExhibitionValuesetOptionRelationDO();
        relation1.setExhibitionTagCode(exhibitionTagCode1);
        relation1.setValuesetOptionCode(valuesetOptionCode1);

        ExhibitionValuesetOptionRelationDO relation2 = new ExhibitionValuesetOptionRelationDO();
        relation2.setExhibitionTagCode(exhibitionTagCode2);
        relation2.setValuesetOptionCode(valuesetOptionCode2);

        when(exhibitionValuesetOptionRelationMapper.selectList(any())).thenReturn(Arrays.asList(relation1, relation2));

        // 执行测试
        Map<String, List<ExhibitionValuesetOptionRelationCache>> result = 
                exhibitionValuesetOptionRelationService.initExhibitionValuesetOptionCache();

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey(exhibitionTagCode1));
        assertTrue(result.containsKey(exhibitionTagCode2));
        verify(redisService, times(2)).setCacheObject(anyString(), any());
        verify(exhibitionValuesetOptionRelationMapper).selectList(any());
    }
} 