package com.dexpo.module.base.service.basic.impl;

import com.dexpo.framework.cache.redis.constant.BasicRedisKey;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;
import com.dexpo.module.base.dal.dataobject.basic.BasicValuesetOptionDO;
import com.dexpo.module.base.dal.mysql.basic.BasicValuesetOptionMapper;
import com.dexpo.framework.cache.redis.entity.BasicValuesetOptionCache;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * BasicValuesetOptionServiceImpl 的单元测试类
 * 主要测试以下功能：
 * 1. 根据值集代码列表查询值集信息
 * 2. 根据值集代码和选项代码列表查询值集信息
 * 3. 初始化值集选项缓存
 */
@ExtendWith(MockitoExtension.class)
class BasicValuesetOptionServiceImplTest {

    @Mock
    private RedisService redisService;

    @Mock
    private BasicValuesetOptionMapper basicValuesetOptionMapper;

    @InjectMocks
    private BasicValuesetOptionServiceImpl basicValuesetOptionService;

    @BeforeEach
    void setUp() {
        // 设置 baseMapper
        ReflectionTestUtils.setField(basicValuesetOptionService, "baseMapper", basicValuesetOptionMapper);
    }

    /**
     * 测试场景：根据值集代码列表查询值集信息 - 正常数据场景
     * 验证点：
     * 1. 缓存中存在匹配的数据
     * 2. 返回正确的数据条数
     * 3. 返回的数据包含预期的值集代码
     */
    @Test
    void listByValuesetCodesWithValidData() {
        // Arrange
        List<String> valuesetCodes = Arrays.asList("CODE1", "CODE2");
        List<BasicValuesetOptionCache> cacheList = Arrays.asList(
            createCache("CODE1", "OPTION1"),
            createCache("CODE2", "OPTION2")
        );
        when(redisService.getCacheObject(BasicRedisKey.BASIC_VALUESET_OPTION)).thenReturn(cacheList);

        // Act
        List<BasicValuesetInfoVO> result = basicValuesetOptionService.listByValuesetCodes(valuesetCodes);

        // Assert
        assertEquals(2, result.size());
        Set<String> resultCodes = result.stream()
            .map(BasicValuesetInfoVO::getValuesetCode)
            .collect(Collectors.toSet());
        assertTrue(resultCodes.contains("CODE1"));
        assertTrue(resultCodes.contains("CODE2"));
    }

    /**
     * 测试场景：根据值集代码列表查询值集信息 - 空缓存场景
     * 验证点：
     * 1. 当缓存为空时返回空列表
     */
    @Test
    void listByValuesetCodesWithEmptyCache() {
        // Arrange
        List<String> valuesetCodes = Arrays.asList("CODE1", "CODE2");
        when(redisService.getCacheObject(BasicRedisKey.BASIC_VALUESET_OPTION)).thenReturn(Collections.emptyList());

        // Act
        List<BasicValuesetInfoVO> result = basicValuesetOptionService.listByValuesetCodes(valuesetCodes);

        // Assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试场景：根据值集代码列表查询值集信息 - 缓存为null场景
     * 验证点：
     * 1. 当缓存为null时返回空列表
     */
    @Test
    void listByValuesetCodesWithNullCache() {
        // Arrange
        List<String> valuesetCodes = Arrays.asList("CODE1", "CODE2");
        when(redisService.getCacheObject(BasicRedisKey.BASIC_VALUESET_OPTION)).thenReturn(null);

        // Act
        List<BasicValuesetInfoVO> result = basicValuesetOptionService.listByValuesetCodes(valuesetCodes);

        // Assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试场景：根据值集代码和选项代码列表查询值集信息 - 正常数据场景
     * 验证点：
     * 1. 缓存中存在匹配的数据
     * 2. 返回正确的数据条数
     * 3. 返回的数据包含预期的值集代码
     */
    @Test
    void listByValuesetOptionCodesWithValidData() {
        // Arrange
        List<String> valuesetCodes = Arrays.asList("CODE1", "CODE2");
        List<String> optionCodes = Arrays.asList("OPTION1", "OPTION2");
        List<BasicValuesetOptionCache> cacheList = Arrays.asList(
            createCache("CODE1", "OPTION1"),
            createCache("CODE2", "OPTION2")
        );
        when(redisService.getCacheObject(BasicRedisKey.BASIC_VALUESET_OPTION)).thenReturn(cacheList);

        // Act
        List<BasicValuesetInfoVO> result = basicValuesetOptionService.listByValuesetOptionCodes(valuesetCodes, optionCodes);

        // Assert
        assertEquals(2, result.size());
        Set<String> resultCodes = result.stream()
            .map(BasicValuesetInfoVO::getValuesetCode)
            .collect(Collectors.toSet());
        assertTrue(resultCodes.contains("CODE1"));
        assertTrue(resultCodes.contains("CODE2"));
    }

    /**
     * 测试场景：根据值集代码和选项代码列表查询值集信息 - 空缓存场景
     * 验证点：
     * 1. 当缓存为空时返回空列表
     */
    @Test
    void listByValuesetOptionCodesWithEmptyCache() {
        // Arrange
        List<String> valuesetCodes = Arrays.asList("CODE1", "CODE2");
        List<String> optionCodes = Arrays.asList("OPTION1", "OPTION2");
        when(redisService.getCacheObject(BasicRedisKey.BASIC_VALUESET_OPTION)).thenReturn(Collections.emptyList());

        // Act
        List<BasicValuesetInfoVO> result = basicValuesetOptionService.listByValuesetOptionCodes(valuesetCodes, optionCodes);

        // Assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试场景：根据值集代码和选项代码列表查询值集信息 - 缓存为null场景
     * 验证点：
     * 1. 当缓存为null时返回空列表
     */
    @Test
    void listByValuesetOptionCodesWithNullCache() {
        // Arrange
        List<String> valuesetCodes = Arrays.asList("CODE1", "CODE2");
        List<String> optionCodes = Arrays.asList("OPTION1", "OPTION2");
        when(redisService.getCacheObject(BasicRedisKey.BASIC_VALUESET_OPTION)).thenReturn(null);

        // Act
        List<BasicValuesetInfoVO> result = basicValuesetOptionService.listByValuesetOptionCodes(valuesetCodes, optionCodes);

        // Assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试场景：根据值集代码和选项代码列表查询值集信息 - 无匹配数据场景
     * 验证点：
     * 1. 当缓存中不存在匹配的值集代码和选项代码时返回空列表
     */
    @Test
    void listByValuesetOptionCodesWithNoMatchingCodes() {
        // Arrange
        List<String> valuesetCodes = Arrays.asList("CODE1", "CODE2");
        List<String> optionCodes = Arrays.asList("OPTION3", "OPTION4");
        List<BasicValuesetOptionCache> cacheList = Arrays.asList(
            createCache("CODE1", "OPTION1"),
            createCache("CODE2", "OPTION2")
        );
        when(redisService.getCacheObject(BasicRedisKey.BASIC_VALUESET_OPTION)).thenReturn(cacheList);

        // Act
        List<BasicValuesetInfoVO> result = basicValuesetOptionService.listByValuesetOptionCodes(valuesetCodes, optionCodes);

        // Assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试场景：初始化值集选项缓存
     * 验证点：
     * 1. 从数据库获取数据并正确转换
     * 2. 返回正确的数据条数
     * 3. 返回的数据包含预期的值集代码
     * 4. 成功将数据写入Redis缓存
     */
    @Test
    void initValueSetOptionCache() {
        // Arrange
        List<BasicValuesetOptionDO> optionList = Arrays.asList(
            createOptionDO("CODE1", "OPTION1"),
            createOptionDO("CODE2", "OPTION2")
        );
        when(basicValuesetOptionMapper.selectList(any())).thenReturn(optionList);

        // Act
        List<BasicValuesetOptionCache> result = basicValuesetOptionService.initValueSetOptionCache();

        // Assert
        assertEquals(2, result.size());
        Set<String> resultCodes = result.stream()
            .map(BasicValuesetOptionCache::getValuesetCode)
            .collect(Collectors.toSet());
        assertTrue(resultCodes.contains("CODE1"));
        assertTrue(resultCodes.contains("CODE2"));
        verify(redisService).setCacheObject(eq(BasicRedisKey.BASIC_VALUESET_OPTION), any());
    }

    /**
     * 创建测试用的缓存对象
     * @param valuesetCode 值集代码
     * @param optionCode 选项代码
     * @return BasicValuesetOptionCache对象
     */
    private BasicValuesetOptionCache createCache(String valuesetCode, String optionCode) {
        BasicValuesetOptionCache cache = new BasicValuesetOptionCache();
        cache.setValuesetCode(valuesetCode);
        cache.setOptionCode(optionCode);
        return cache;
    }

    /**
     * 创建测试用的数据库对象
     * @param valuesetCode 值集代码
     * @param optionCode 选项代码
     * @return BasicValuesetOptionDO对象
     */
    private BasicValuesetOptionDO createOptionDO(String valuesetCode, String optionCode) {
        BasicValuesetOptionDO option = new BasicValuesetOptionDO();
        option.setValuesetCode(valuesetCode);
        option.setOptionCode(optionCode);
        return option;
    }
} 