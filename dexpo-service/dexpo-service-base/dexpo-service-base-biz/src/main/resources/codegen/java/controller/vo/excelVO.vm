package ${basePackage}.module.${table.moduleName}.controller.${sceneEnum.basePackage}.${table.businessName}.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
#foreach ($column in $columns)
#if (${column.javaType} == "BigDecimal")
import java.math.BigDecimal;
#end
#if (${column.javaType} == "LocalDateTime")
import java.time.LocalDateTime;
#end
#end

import com.alibaba.excel.annotation.ExcelProperty;
#foreach ($column in $columns)
#if ("$!column.dictType" != "")## 有设置数据字典
import ${DictFormatClassName};
import ${DictConvertClassName};

#break
#end
#end

/**
 * ${table.classComment} Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ${sceneEnum.prefixClass}${table.className}ExcelVO {

#foreach ($column in $columns)
    #if (${column.listOperationResult})##返回字段
    #if ("$!column.dictType" != "")##处理枚举值
    @ExcelProperty(value = "${column.columnComment}", converter = DictConvert.class)
    @DictFormat("${column.dictType}") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    #else
    @ExcelProperty("${column.columnComment}")
    #end
    private ${column.javaType} ${column.javaField};

    #end
#end
}
