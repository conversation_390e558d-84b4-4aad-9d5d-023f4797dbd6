import request from '@/config/axios'
#set ($baseURL = "/${table.moduleName}/${simpleClassName_strikeCase}")

export interface ${simpleClassName}VO {
    #foreach ($column in $columns)
        #if ($column.createOperation || $column.updateOperation)
            #if(${column.javaType.toLowerCase()} == "long" || ${column.javaType.toLowerCase()} == "integer" || ${column.javaType.toLowerCase()} == "double" || ${column.javaType.toLowerCase()} == "bigdecimal")
                    ${column.javaField}: number
            #elseif(${column.javaType.toLowerCase()} == "date" || ${column.javaType.toLowerCase()} == "localdatetime")
                    ${column.javaField}: Date
            #else
                    ${column.javaField}: ${column.javaType.toLowerCase()}
            #end
        #end
    #end
}

// 查询${table.classComment}列表
export const get${simpleClassName}Page = async (params) => {
  return await request.get({ url: '${baseURL}/page', params })
}

// 查询${table.classComment}详情
export const get${simpleClassName} = async (id: number) => {
  return await request.get({ url: '${baseURL}/get?id=' + id })
}

// 新增${table.classComment}
export const create${simpleClassName} = async (data: ${simpleClassName}VO) => {
  return await request.post({ url: '${baseURL}/create', data })
}

// 修改${table.classComment}
export const update${simpleClassName} = async (data: ${simpleClassName}VO) => {
  return await request.put({ url: '${baseURL}/update', data })
}

// 删除${table.classComment}
export const delete${simpleClassName} = async (id: number) => {
  return await request.delete({ url: '${baseURL}/delete?id=' + id })
}

// 导出${table.classComment} Excel
export const export${simpleClassName}Api = async (params) => {
  return await request.download({ url: '${baseURL}/export-excel', params })
}
