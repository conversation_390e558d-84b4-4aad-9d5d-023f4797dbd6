<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
    #set ($dictMethods = [])## 使用到的 dict 字典方法
    #foreach($column in $columns)
        #if ($column.listOperation)
            #set ($dictType = $column.dictType)
            #set ($javaField = $column.javaField)
            #set ($javaType = $column.javaType)
            #set ($AttrName = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
            #set ($comment = $column.columnComment)
            #set ($dictMethod = "getDictOptions")## 计算使用哪个 dict 字典方法
            #if ($javaType == "Integer" || $javaType == "Long" || $javaType == "Byte" || $javaType == "Short")
                #set ($dictMethod = "getIntDictOptions")
            #elseif ($javaType == "String")
                #set ($dictMethod = "getStrDictOptions")
            #elseif ($javaType == "Boolean")
                #set ($dictMethod = "getBoolDictOptions")
            #end
            #if ($column.htmlType == "input")
      <el-form-item label="${comment}" prop="${javaField}">
        <el-input
          v-model="queryParams.${javaField}"
          placeholder="请输入${comment}"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
            #elseif ($column.htmlType == "select" || $column.htmlType == "radio")
      <el-form-item label="${comment}" prop="${javaField}">
                #if ($javaField.length() + $comment.length() > 8)
        <el-select
          v-model="queryParams.${javaField}"
          placeholder="请选择${comment}"
          clearable
          class="!w-240px"
        >
                #else
        <el-select v-model="queryParams.${javaField}" placeholder="请选择${comment}" clearable class="!w-240px">
                #end
                #if ("" != $dictType)## 设置了 dictType 数据字典的情况
                    #if (!$dictMethods.contains($dictMethod))## 如果不存在，则添加到 dictMethods 数组中，后续好 import
                        #set($ignore = $dictMethods.add($dictMethod) )
                    #end
          <el-option
            v-for="dict in $dictMethod(DICT_TYPE.$dictType.toUpperCase())"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
                #else## 未设置 dictType 数据字典的情况
          <el-option label="请选择字典生成" value="" />
                #end
        </el-select>
      </el-form-item>
    #elseif($column.htmlType == "datetime")
      #if ($column.listOperationCondition != "BETWEEN")## 非范围
      <el-form-item label="${comment}" prop="${javaField}">
        <el-date-picker
          v-model="queryParams.${javaField}"
          value-format="YYYY-MM-DD"
          type="date"
          placeholder="选择${comment}"
          clearable
          class="!w-240px"
        />
      </el-form-item>
      #else## 范围
      <el-form-item label="${comment}" prop="${javaField}">
        <el-date-picker
          v-model="queryParams.${javaField}"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      #end
    #end
    #end
    #end
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        #if ($permissionPrefix.length() <= 12)
        <el-button type="primary" @click="openForm('create')" v-hasPermi="['${permissionPrefix}:create']">
        #else
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['${permissionPrefix}:create']"
        >
        #end
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['${permissionPrefix}:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      #foreach($column in $columns)
      #if ($column.listOperationResult)
        #set ($dictType=$column.dictType)
        #set ($javaField = $column.javaField)
        #set ($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
        #set ($comment=$column.columnComment)
        #if ($column.javaType == "LocalDateTime")## 时间类型
      <el-table-column
        label="${comment}"
        align="center"
        prop="${javaField}"
        :formatter="dateFormatter"
        width="150px"
      />
        #elseif("" != $column.dictType)## 数据字典
      <el-table-column label="${comment}" align="center" prop="${javaField}" width="150px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.$dictType.toUpperCase()" :value="scope.row.${column.javaField}" />
        </template>
      </el-table-column>
        #else
      <el-table-column label="${comment}" align="center" prop="${javaField}" width="150px" />
        #end
      #end
    #end
      <el-table-column label="操作" align="center" width="150px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['${permissionPrefix}:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['${permissionPrefix}:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <${simpleClassName}Form ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
#if ($dictMethods.size() > 0)
import { DICT_TYPE#foreach ($dictMethod in $dictMethods), ${dictMethod}#end } from '@/utils/dict'
#end
#foreach ($column in $columns)
    #if ($column.listOperationResult && $column.htmlType == "datetime")
import { dateFormatter } from '@/utils/formatTime'
    #break
    #end
#end
import download from '@/utils/download'
import * as ${simpleClassName}Api from '@/api/${table.moduleName}/${classNameVar}'
import ${simpleClassName}Form from './${simpleClassName}Form.vue'

defineOptions({ name: '${table.className}' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  #set ($listOperationLastIndex = -1)## 求最后一个需要 , 的地方
  #foreach ($column in $columns)
    #if ($column.listOperation)
      #set ($listOperationLastIndex = $foreach.index)
    #end
  #end
  #foreach ($column in $columns)
    #if ($column.listOperation)
      #if ($column.listOperationCondition != 'BETWEEN')
  $column.javaField: null#if($foreach.index < $listOperationLastIndex),#end
  #end
      #if ($column.htmlType == "datetime" || $column.listOperationCondition == "BETWEEN")
  $column.javaField: []#if($foreach.index < $listOperationLastIndex),#end
      #end
    #end
  #end
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ${simpleClassName}Api.get${simpleClassName}Page(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ${simpleClassName}Api.delete${simpleClassName}(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ${simpleClassName}Api.export${simpleClassName}(queryParams)
    download.excel(data, '${table.classComment}.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
