package com.dexpo.module.base.controller.basic;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.base.api.basic.BasicValuesetApi;
import com.dexpo.module.base.api.basic.dto.BasicValuesetOptionDTO;
import com.dexpo.module.base.api.basic.dto.ExhibitionTagValuesetDTO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetOptionVO;
import com.dexpo.module.base.service.basic.BasicValuesetOptionService;
import com.dexpo.module.base.service.basic.BasicValuesetService;
import com.dexpo.module.base.service.basic.ExhibitionValuesetOptionRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.dexpo.framework.common.pojo.CommonResult.success;

@Tag(name = "值集管理")
@RestController
@Validated
public class BasicValuesetController implements BasicValuesetApi {

    @Resource
    private BasicValuesetService valuesetService;

    @Resource
    private ExhibitionValuesetOptionRelationService valuesetOptionRelationService;

    @Resource
    private BasicValuesetOptionService valuesetOptionService;


    @Operation(summary = "根据值集代码列表获取值集信息")
    public CommonResult<List<BasicValuesetInfoVO>> getValuesetListByCodes(
            @RequestBody List<String> valuesetList) {
        return success(valuesetService.getValuesetListByCodes(valuesetList));
    }

    @Override
    @Operation(summary = "根据会展tag和值集代码列表获取值集信息")
    public CommonResult<List<BasicValuesetInfoVO>> getExhibitionValuesetListByCodes(@RequestBody ExhibitionTagValuesetDTO request) {
        return success(valuesetOptionRelationService.getExhibitionValuesetListByCodes(request));
    }

    @Override
    @Operation(summary = "根据值集代码获取父值集选项信息")
    public CommonResult<BasicValuesetOptionVO> getValuesetOption(BasicValuesetOptionDTO request) {
        return success(valuesetOptionService.getValuesetOption(request));
    }

    @Override
    @Operation(summary = "根据值集option code 获取option信息")
    public CommonResult<List<BasicValuesetOptionVO>> getOptionListByCodes(@Valid @NotEmpty @RequestBody List<String> optionCodes) {
        return success(valuesetOptionService.getOptionListByCodes(optionCodes));
    }

} 