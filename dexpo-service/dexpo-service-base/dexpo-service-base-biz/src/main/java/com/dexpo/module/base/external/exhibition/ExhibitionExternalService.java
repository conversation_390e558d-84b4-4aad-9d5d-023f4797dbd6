package com.dexpo.module.base.external.exhibition;

import com.alibaba.fastjson.JSON;
import com.dexpo.framework.cache.redis.operate.exhibition.ExhibitionInfoCacheOpt;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.framework.common.core.BaseExternal;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.exhibition.api.ExhibitionApi;
import com.dexpo.module.exhibition.api.dto.ExhibitionQueryDTO;
import com.dexpo.module.exhibition.api.vo.ExhibitionVO;
import com.dexpo.module.exhibition.cache.ExhibitionCacheQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ExhibitionExternalService extends BaseExternal {

    private final ExhibitionApi exhibitionApi;

    private final ExhibitionInfoCacheOpt exhibitionInfoCacheOpt;

    private final RedisService redisService;

    /**
     * 根据条件获取展会id列表
     *
     * @param queryDTO queryDTO
     * @return 展会id列表
     */
    public List<Long> getExhibitionIds(ExhibitionQueryDTO queryDTO) {
        List<ExhibitionVO> exhibitionList = getExhibitionList(queryDTO);
        return exhibitionList.stream().map(ExhibitionVO::getId).toList();
    }

    /**
     * 根据条件获取展会id列表
     *
     * @param queryDTO queryDTO
     * @return 展会id列表
     */
    public Map<Long, ExhibitionVO> getExhibitionMap(ExhibitionQueryDTO queryDTO) {
        List<ExhibitionVO> exhibitionList = getExhibitionList(queryDTO);
        return exhibitionList.stream().collect(Collectors.toMap(ExhibitionVO::getId, Function.identity()));
    }

    /**
     * 根据条件获取展会列表
     *
     * @param queryDTO queryDTO
     * @return 展会列表
     */
    public List<ExhibitionVO> getExhibitionList(ExhibitionQueryDTO queryDTO) {
        CommonResult<List<ExhibitionVO>> result = exhibitionApi.getExhibitionList(queryDTO);
        List<ExhibitionVO> exhibitionList = getResult(result);
        log.info("根据条件获取展会列表 params:{}  result: {}", JSON.toJSONString(queryDTO), JSON.toJSONString(exhibitionList));
        return exhibitionList;
    }

    public ExhibitionVO getExhibition(Long id) {
        ExhibitionCacheQueryService queryService = new ExhibitionCacheQueryService(exhibitionInfoCacheOpt,
                exhibitionApi, redisService);
        return queryService.getExhibitionById(id);
    }

}