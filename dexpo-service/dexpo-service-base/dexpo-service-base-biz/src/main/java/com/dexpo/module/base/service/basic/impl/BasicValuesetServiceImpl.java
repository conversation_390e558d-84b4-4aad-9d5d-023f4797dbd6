package com.dexpo.module.base.service.basic.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;
import com.dexpo.module.base.dal.dataobject.basic.BasicValuesetDO;
import com.dexpo.module.base.dal.mysql.basic.BasicValuesetMapper;
import com.dexpo.module.base.service.basic.BasicValuesetOptionService;
import com.dexpo.module.base.service.basic.BasicValuesetService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 值集 Service 实现类
 */
@Service
@RequiredArgsConstructor
public class BasicValuesetServiceImpl extends ServiceImpl<BasicValuesetMapper, BasicValuesetDO>
        implements BasicValuesetService {

    private final BasicValuesetOptionService basicValuesetOptionService;

    @Override
    public List<BasicValuesetInfoVO> getValuesetListByCodes(List<String> valuesetCodes) {
        return basicValuesetOptionService.listByValuesetCodes(valuesetCodes);
    }
} 