package com.dexpo.module.base.dal.mysql.basic;

import com.dexpo.framework.mybatis.core.mapper.BaseMapperX;
import com.dexpo.module.base.dal.dataobject.basic.BasicLocationDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业务地域信息Mapper接口
 */
@Mapper
public interface BasicLocationMapper extends BaseMapperX<BasicLocationDO> {

    List<BasicLocationDO> selectByExhibitionTagCode(@Param("exhibitionTagCode") String exhibitionTagCode);
}