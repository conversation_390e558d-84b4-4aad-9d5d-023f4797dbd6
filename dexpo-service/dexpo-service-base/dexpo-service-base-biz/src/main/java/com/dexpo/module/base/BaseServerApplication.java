package com.dexpo.module.base;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * 基础服务模块启动类
 *
 * <p>该类是dexpo-service-base微服务的主启动类，负责初始化和配置整个基础服务模块。
 * 基础服务模块提供系统的基础设施功能和通用业务服务。</p>
 *
 * <p>服务功能：</p>
 * <ul>
 *   <li>基础设施运维与管理：定时任务管理、服务器信息等</li>
 *   <li>研发工具支持：代码生成器、接口文档等</li>
 *   <li>通用业务服务：地域管理、值集管理、文件管理等</li>
 *   <li>待办事项管理：业务流程跟踪和处理</li>
 * </ul>
 *
 * <p>技术架构：</p>
 * <ul>
 *   <li>基于Spring Boot 3.x构建</li>
 *   <li>集成Spring Cloud微服务组件</li>
 *   <li>支持服务发现和负载均衡</li>
 *   <li>使用Feign进行服务间通信</li>
 * </ul>
 *
 * <p>配置说明：</p>
 * <ul>
 *   <li>排除了默认的数据源自动配置，使用自定义数据源配置</li>
 *   <li>排除了Metrics自动配置，避免不必要的监控开销</li>
 *   <li>启用服务发现客户端，支持动态服务注册与发现</li>
 *   <li>配置了多个包的组件扫描，整合框架和业务组件</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 * @see org.springframework.boot.SpringApplication
 * @see org.springframework.cloud.client.discovery.EnableDiscoveryClient
 * @see org.springframework.cloud.openfeign.EnableFeignClients
 */
@SpringBootApplication(exclude = {MetricsAutoConfiguration.class, DataSourceAutoConfiguration.class})
@Slf4j
@EnableDiscoveryClient
@ComponentScan({
    "com.dexpo.framework.security",    // 安全框架组件
    "com.dexpo.module.base",           // 基础模块组件
    "com.dexpo.framework.cache",       // 缓存框架组件
    "com.dexpo.framework.web",         // Web框架组件
    "com.dexpo.module.exhibition"      // 展会模块组件
})
@EnableFeignClients(value = {
    "com.dexpo.module.integration",    // 集成模块Feign客户端
    "com.dexpo.module.exhibition"      // 展会模块Feign客户端
})
public class BaseServerApplication {

    /**
     * 应用程序主入口方法
     *
     * <p>启动Spring Boot应用程序，初始化所有配置的组件和服务。
     * 应用启动后会自动注册到服务发现中心，并开始接收请求。</p>
     *
     * <p>启动流程：</p>
     * <ol>
     *   <li>加载应用配置文件</li>
     *   <li>初始化Spring容器和所有Bean</li>
     *   <li>启动内嵌Web服务器</li>
     *   <li>注册到服务发现中心</li>
     *   <li>开始接收和处理请求</li>
     * </ol>
     *
     * @param args 命令行参数，可用于传递配置参数
     *
     * @throws Exception 当应用启动失败时抛出异常
     */
    public static void main(String[] args) {
        try {
            SpringApplication.run(BaseServerApplication.class, args);
            log.info("=== 基础服务模块启动成功 ===");
            log.info("服务名称: dexpo-service-base");
            log.info("服务功能: 基础设施管理、通用业务服务、研发工具支持");
        } catch (Exception e) {
            log.error("=== 基础服务模块启动失败 ===", e);
            throw e;
        }
    }

}
