package com.dexpo.module.base.dal.dataobject.basic;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 展会与值集项关系实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("exhibition_valueset_option_relation")
public class ExhibitionValuesetOptionRelationDO extends BaseDO {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 展会标签编码
     */
    @TableField("exhibition_tag_code")
    private String exhibitionTagCode;

    /**
     * 值集编码
     */
    @TableField("valueset_code")
    private String valuesetCode;

    /**
     * 值集项编码
     */
    @TableField("valueset_option_code")
    private String valuesetOptionCode;
} 