package com.dexpo.module.base.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate配置类
 *
 * <p>该配置类负责创建和配置RestTemplate Bean，用于HTTP客户端调用。
 * RestTemplate是Spring提供的同步HTTP客户端，适用于简单的REST API调用场景。</p>
 *
 * <p>使用场景：</p>
 * <ul>
 *   <li>调用第三方REST API</li>
 *   <li>微服务间的HTTP通信（非Feign场景）</li>
 *   <li>外部系统集成</li>
 * </ul>
 *
 * <p><strong>注意：</strong>在Spring 5.0+版本中，推荐使用WebClient替代RestTemplate，
 * 因为WebClient提供了更好的异步支持和响应式编程能力。</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 * @see org.springframework.web.client.RestTemplate
 * @see org.springframework.web.reactive.function.client.WebClient
 */
@Configuration
public class RestTemplateConfig {

    /**
     * 创建RestTemplate Bean
     *
     * <p>配置一个基础的RestTemplate实例，使用默认的HTTP客户端配置。
     * 该实例可以在整个应用中通过依赖注入使用。</p>
     *
     * <p>默认配置包括：</p>
     * <ul>
     *   <li>标准的HTTP消息转换器</li>
     *   <li>默认的连接超时和读取超时设置</li>
     *   <li>基本的错误处理机制</li>
     * </ul>
     *
     * @return 配置好的RestTemplate实例
     *
     * @example
     * <pre>
     * {@code @Autowired}
     * private RestTemplate restTemplate;
     *
     * public String callExternalApi() {
     *     return restTemplate.getForObject("http://api.example.com/data", String.class);
     * }
     * </pre>
     */
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
