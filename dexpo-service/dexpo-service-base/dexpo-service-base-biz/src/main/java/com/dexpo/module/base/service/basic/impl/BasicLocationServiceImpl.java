package com.dexpo.module.base.service.basic.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dexpo.module.base.api.basic.dto.BasicExhibitionLocationDTO;
import com.dexpo.module.base.api.basic.dto.BasicLocationDTO;
import com.dexpo.module.base.api.basic.vo.BasicLocationVO;
import com.dexpo.module.base.convert.basic.BasicLocationConvert;
import com.dexpo.module.base.dal.dataobject.basic.BasicLocationDO;
import com.dexpo.module.base.dal.mysql.basic.BasicLocationMapper;
import com.dexpo.module.base.service.basic.BasicLocationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务地域服务实现类
 *
 * <p>该类实现了业务地域服务接口，提供具体的业务地域查询和管理功能。
 * 基于MyBatis-Plus框架实现数据访问，支持灵活的查询条件构建。</p>
 *
 * <p>实现特点：</p>
 * <ul>
 *   <li>继承ServiceImpl提供基础CRUD操作</li>
 *   <li>使用LambdaQueryWrapper构建类型安全的查询条件</li>
 *   <li>通过Convert类实现DO和VO之间的转换</li>
 *   <li>支持自定义SQL查询复杂关联关系</li>
 * </ul>
 *
 * <p>性能优化：</p>
 * <ul>
 *   <li>使用精确的字段匹配避免全表扫描</li>
 *   <li>合理的索引设计支持高效查询</li>
 *   <li>批量查询减少数据库交互次数</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 * @see BasicLocationService
 * @see BasicLocationMapper
 * @see BasicLocationConvert
 */
@Service
@RequiredArgsConstructor
public class BasicLocationServiceImpl extends ServiceImpl<BasicLocationMapper, BasicLocationDO> implements BasicLocationService {

    /**
     * 根据地域标签获取业务地域信息列表
     *
     * <p>实现逻辑：</p>
     * <ol>
     *   <li>构建Lambda查询条件，根据地域标签进行精确匹配</li>
     *   <li>执行数据库查询获取DO对象列表</li>
     *   <li>通过转换器将DO对象转换为VO对象</li>
     *   <li>返回转换后的结果列表</li>
     * </ol>
     *
     * <p>查询性能：该方法使用地域标签字段的索引，查询效率较高。</p>
     *
     * @param locationDTO 地域查询条件，不能为null
     * @return 业务地域信息列表，保证不为null
     */
    @Override
    public List<BasicLocationVO> getLocationList(BasicLocationDTO locationDTO) {
        // 构建查询条件：根据地域标签精确匹配
        LambdaQueryWrapper<BasicLocationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BasicLocationDO::getLocationTag, locationDTO.getLocationTag());

        // 执行查询
        List<BasicLocationDO> list = baseMapper.selectList(queryWrapper);

        // 转换DO为VO并返回
        return BasicLocationConvert.INSTANCE.e2vList(list);
    }

    /**
     * 根据展会标签代码获取相关的业务地域信息列表
     *
     * <p>实现逻辑：</p>
     * <ol>
     *   <li>调用Mapper的自定义方法查询展会关联的地域信息</li>
     *   <li>该方法内部会处理展会标签与地域标签的关联关系</li>
     *   <li>通过转换器将DO对象转换为VO对象</li>
     *   <li>返回转换后的结果列表</li>
     * </ol>
     *
     * <p>关联查询：该方法涉及多表关联，查询复杂度较高，
     * 建议在相关表上建立适当的索引以提升性能。</p>
     *
     * @param exhibitionLocationDTO 展会地域查询条件，不能为null
     * @return 与展会相关的业务地域信息列表，保证不为null
     */
    @Override
    public List<BasicLocationVO> getExhibitionLocationList(BasicExhibitionLocationDTO exhibitionLocationDTO) {
        // 通过自定义SQL查询展会关联的地域信息
        List<BasicLocationDO> list = baseMapper.selectByExhibitionTagCode(exhibitionLocationDTO.getExhibitionTagCode());

        // 转换DO为VO并返回
        return BasicLocationConvert.INSTANCE.e2vList(list);
    }
}