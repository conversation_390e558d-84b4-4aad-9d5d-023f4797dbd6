package com.dexpo.module.base.convert.basic;

import com.dexpo.framework.common.pojo.IConvert;
import com.dexpo.module.base.api.basic.dto.BasicRegionDTO;
import com.dexpo.module.base.api.basic.vo.BasicRegionTreeVO;
import com.dexpo.module.base.api.basic.vo.BasicRegionVO;
import com.dexpo.module.base.dal.dataobject.basic.BasicRegionDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface BasicRegionConvert extends IConvert<BasicRegionDTO, BasicRegionVO, BasicRegionDO> {

    BasicRegionConvert INSTANCE = Mappers.getMapper(BasicRegionConvert.class);

    List<BasicRegionTreeVO> vToTreeList(List<BasicRegionDO> entity);
}
