package com.dexpo.module.base.controller.basic;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.base.api.basic.BasicLocationApi;
import com.dexpo.module.base.api.basic.dto.BasicExhibitionLocationDTO;
import com.dexpo.module.base.api.basic.dto.BasicLocationDTO;
import com.dexpo.module.base.api.basic.vo.BasicLocationVO;
import com.dexpo.module.base.service.basic.BasicLocationService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 业务地域控制器
 *
 * <p>该控制器实现了业务地域API接口，提供RESTful风格的地域信息查询服务。
 * 作为微服务架构中的API网关，负责处理来自其他服务的地域信息查询请求。</p>
 *
 * <p>主要功能：</p>
 * <ul>
 *   <li>提供基于地域标签的地域信息查询</li>
 *   <li>提供基于展会标签的地域信息查询</li>
 *   <li>统一的响应格式封装</li>
 *   <li>请求参数验证</li>
 * </ul>
 *
 * <p>技术特点：</p>
 * <ul>
 *   <li>实现Feign接口，支持服务间调用</li>
 *   <li>使用@Validated注解启用参数验证</li>
 *   <li>统一的异常处理和响应格式</li>
 *   <li>支持JSON格式的请求和响应</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 * @see BasicLocationApi
 * @see BasicLocationService
 */
@RestController
@Validated
public class BasicLocationController implements BasicLocationApi {

    /**
     * 业务地域服务
     * <p>注入业务地域服务实例，处理具体的业务逻辑</p>
     */
    @Resource
    private BasicLocationService basicLocationService;

    /**
     * 根据地域标签获取业务地域信息列表
     *
     * <p>该接口提供基于地域标签的地域信息查询功能，支持精确匹配查询。
     * 主要用于前端下拉框数据加载和业务系统的地域选择功能。</p>
     *
     * <p>请求处理流程：</p>
     * <ol>
     *   <li>接收并验证请求参数</li>
     *   <li>调用业务服务执行查询逻辑</li>
     *   <li>封装查询结果为统一响应格式</li>
     *   <li>返回JSON格式的响应数据</li>
     * </ol>
     *
     * @param locationDTO 地域查询条件，经过@Valid验证
     * @return 包含地域信息列表的统一响应对象
     *
     * @apiNote 该接口支持Feign客户端调用，响应格式为CommonResult包装的列表数据
     */
    @Override
    public CommonResult<List<BasicLocationVO>> getLocationList(@Valid @RequestBody BasicLocationDTO locationDTO) {
        List<BasicLocationVO> locationList = basicLocationService.getLocationList(locationDTO);
        return CommonResult.success(locationList);
    }

    /**
     * 根据展会标签代码获取相关的业务地域信息列表
     *
     * <p>该接口提供基于展会标签的地域信息查询功能，用于获取与特定展会
     * 相关联的地域信息。主要用于展会管理和地域范围限定功能。</p>
     *
     * <p>请求处理流程：</p>
     * <ol>
     *   <li>接收并验证展会地域查询参数</li>
     *   <li>调用业务服务查询展会关联的地域信息</li>
     *   <li>封装查询结果为统一响应格式</li>
     *   <li>返回JSON格式的响应数据</li>
     * </ol>
     *
     * @param exhibitionLocationDTO 展会地域查询条件，经过@Valid验证
     * @return 包含展会相关地域信息列表的统一响应对象
     *
     * @apiNote 该接口涉及多表关联查询，响应时间可能较长，建议客户端设置合适的超时时间
     */
    @Override
    public CommonResult<List<BasicLocationVO>> getExhibitionLocationList(@Valid @RequestBody BasicExhibitionLocationDTO exhibitionLocationDTO) {
        List<BasicLocationVO> locationList = basicLocationService.getExhibitionLocationList(exhibitionLocationDTO);
        return CommonResult.success(locationList);
    }
}
