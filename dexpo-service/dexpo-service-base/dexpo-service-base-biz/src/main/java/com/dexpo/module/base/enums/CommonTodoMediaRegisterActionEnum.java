package com.dexpo.module.base.enums;

import com.dexpo.module.member.enums.RegisterStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 媒体注册动作枚举类
 *
 * <p>该枚举定义了媒体注册流程中的各种动作类型，用于待办事项系统中
 * 跟踪和管理媒体注册相关的业务流程。</p>
 *
 * <p>每个枚举项包含：</p>
 * <ul>
 *   <li>注册状态：对应的会员注册状态</li>
 *   <li>动作描述：具体的业务动作说明</li>
 *   <li>类型描述：动作类型的分类说明</li>
 * </ul>
 *
 * <p>使用场景：</p>
 * <ul>
 *   <li>媒体注册流程管理</li>
 *   <li>待办事项生成和处理</li>
 *   <li>注册状态跟踪</li>
 *   <li>业务流程审计</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 * @see RegisterStatusEnum
 * @see com.dexpo.module.base.service.basic.CommonTodoService
 */
@Getter
@AllArgsConstructor
public enum CommonTodoMediaRegisterActionEnum {

    /**
     * 提交媒体注册申请
     *
     * <p>当媒体用户提交注册申请时触发此动作，
     * 系统会生成相应的待办事项供管理员审核。</p>
     *
     * <p>业务流程：</p>
     * <ol>
     *   <li>媒体用户填写注册信息</li>
     *   <li>提交注册申请</li>
     *   <li>系统生成待审核状态的待办事项</li>
     *   <li>通知相关审核人员</li>
     * </ol>
     */
    SUBMIT(RegisterStatusEnum.PENDING_REVIEW, "媒体注册报名", "申请待审核");

    /**
     * 对应的注册状态
     * <p>表示执行该动作后，注册申请应该处于的状态</p>
     */
    private final RegisterStatusEnum registerStatusEnum;

    /**
     * 动作描述
     * <p>对该动作的业务含义进行简要说明</p>
     */
    private final String actionDesc;

    /**
     * 类型描述
     * <p>对该动作类型的详细描述，用于界面显示和日志记录</p>
     */
    private final String typeDesc;

}