package com.dexpo.module.base.convert.attachment;

import com.dexpo.framework.common.pojo.IConvert;
import com.dexpo.module.base.api.attachment.dto.AttachmentInfoDTO;
import com.dexpo.module.base.api.attachment.vo.AttachmentInfoVO;
import com.dexpo.module.base.dal.dataobject.attachment.AttachmentInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AttachmentInfoConvert extends IConvert<AttachmentInfoDTO, AttachmentInfoVO, AttachmentInfoDO> {

    AttachmentInfoConvert INSTANCE = Mappers.getMapper(AttachmentInfoConvert.class);
}
