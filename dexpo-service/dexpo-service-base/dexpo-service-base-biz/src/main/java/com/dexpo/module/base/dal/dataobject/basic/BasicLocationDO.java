package com.dexpo.module.base.dal.dataobject.basic;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 业务地域信息数据对象
 *
 * <p>该类对应数据库表 basic_location，用于存储业务系统中使用的地域信息。
 * 业务地域与行政区域不同，它是根据业务需要划分的地理区域，
 * 可能包含多个行政区域或者是行政区域的子集。</p>
 *
 * <p>数据特点：</p>
 * <ul>
 *   <li>支持中英文双语地域名称</li>
 *   <li>通过地域标签进行分类管理</li>
 *   <li>提供唯一的地域编码标识</li>
 *   <li>继承基础DO类，包含创建时间、更新时间等审计字段</li>
 * </ul>
 *
 * <p>使用场景：</p>
 * <ul>
 *   <li>企业注册时的地域选择</li>
 *   <li>展会举办地域配置</li>
 *   <li>业务范围划分和统计</li>
 *   <li>地域相关的权限控制</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 * @see BaseDO
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("basic_location")
public class BasicLocationDO extends BaseDO {

    /**
     * 主键ID
     *
     * <p>数据库自增主键，唯一标识一条地域记录。
     * 使用Long类型以支持大量数据存储。</p>
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 地域编码
     *
     * <p>业务系统中地域的唯一标识码，通常采用有意义的编码规则。
     * 例如：CN-BJ表示中国北京，US-NY表示美国纽约等。</p>
     *
     * <p>编码规则：</p>
     * <ul>
     *   <li>全局唯一，不可重复</li>
     *   <li>建议使用国际标准或行业标准</li>
     *   <li>支持层级结构表示</li>
     * </ul>
     */
    @TableField("location_code")
    private String locationCode;

    /**
     * 地域名称-中文
     *
     * <p>地域的中文名称，用于中文界面显示和中文用户交互。
     * 应使用标准的中文地名，便于用户理解和搜索。</p>
     */
    @TableField("location_name_cn")
    private String locationNameCn;

    /**
     * 地域名称-英文
     *
     * <p>地域的英文名称，用于英文界面显示和国际化支持。
     * 建议使用国际通用的英文地名或官方英文译名。</p>
     */
    @TableField("location_name_en")
    private String locationNameEn;

    /**
     * 地域标签
     *
     * <p>用于对地域进行分类和筛选的标签标识。通过地域标签可以将
     * 地域按照不同的业务场景进行分组，如企业类型相关地域、
     * 展会相关地域等。</p>
     *
     * <p>标签来源：</p>
     * <ul>
     *   <li>值集系统中的标准标签</li>
     *   <li>例如：VS_ACTION_ENTERPRISE_TYPE（企业行动类型相关地域）</li>
     *   <li>支持多个标签的组合使用</li>
     * </ul>
     *
     * @see com.dexpo.module.base.service.basic.BasicValuesetService
     */
    @TableField("location_tag")
    private String locationTag;

}