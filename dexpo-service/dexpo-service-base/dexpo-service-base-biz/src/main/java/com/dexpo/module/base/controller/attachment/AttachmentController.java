package com.dexpo.module.base.controller.attachment;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.security.core.LoginUser;
import com.dexpo.framework.security.core.util.SecurityFrameworkUtils;
import com.dexpo.module.base.api.attachment.AttachmentApi;
import com.dexpo.module.base.api.attachment.dto.AttachmentInfoDTO;
import com.dexpo.module.base.api.attachment.vo.AttachmentInfoVO;
import com.dexpo.module.base.service.attachment.AttachmentInfoService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Validated
@Slf4j
public class AttachmentController implements AttachmentApi {

    @Resource
    private AttachmentInfoService attachmentInfoService;

    @Override
    public CommonResult<AttachmentInfoVO> findFileById(Long id) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        log.info("====loginUser===>>>{}",loginUser);
        AttachmentInfoVO byId = attachmentInfoService.findById(id);
        return CommonResult.success(byId);
    }

    @Override
    public CommonResult<AttachmentInfoVO> createAttachment(AttachmentInfoDTO attachmentInfoDTO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        AttachmentInfoVO fileVo = attachmentInfoService.createAttachment(attachmentInfoDTO,loginUser);
        return CommonResult.success(fileVo);
    }

    @Override
    public CommonResult<List<AttachmentInfoVO>> findFileByIds(List<Long> idList) {
        List<AttachmentInfoVO> list = attachmentInfoService.findByIdList(idList);
        return CommonResult.success(list);
    }

    @Override
    public CommonResult<AttachmentInfoVO> findFileByBusinessType(String businessType) {
        return attachmentInfoService.findFileByBusinessType(businessType);
    }
}
