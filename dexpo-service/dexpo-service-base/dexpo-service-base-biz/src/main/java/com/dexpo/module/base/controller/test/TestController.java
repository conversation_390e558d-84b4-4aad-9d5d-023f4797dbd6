package com.dexpo.module.base.controller.test;

import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.module.base.service.attachment.AttachmentInfoService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
public class TestController {

    @Resource
    private RedisService redisService;
    @Resource
    private AttachmentInfoService attachmentInfoService;

    @GetMapping("/heather")
    public String test() {
        redisService.setCacheObject("test","test01");
        String test = redisService.getCacheObject("test");
        log.info("test:--{}",test);
        return "success";
    }

}
