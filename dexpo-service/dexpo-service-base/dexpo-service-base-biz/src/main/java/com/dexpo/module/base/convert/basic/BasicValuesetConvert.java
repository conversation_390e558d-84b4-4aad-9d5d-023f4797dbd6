package com.dexpo.module.base.convert.basic;

import com.dexpo.framework.common.pojo.IConvert;
import com.dexpo.module.base.api.basic.dto.BasicValuesetInfoDTO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;
import com.dexpo.module.base.dal.dataobject.basic.BasicValuesetDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 值集信息转换接口
 */
@Mapper
public interface BasicValuesetConvert extends IConvert<BasicValuesetInfoVO, BasicValuesetInfoDTO, BasicValuesetDO> {

    BasicValuesetConvert INSTANCE = Mappers.getMapper(BasicValuesetConvert.class);
} 