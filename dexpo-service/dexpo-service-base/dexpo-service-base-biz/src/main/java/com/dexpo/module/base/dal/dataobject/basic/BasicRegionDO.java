package com.dexpo.module.base.dal.dataobject.basic;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 行政区域信息数据对象
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("basic_region")
public class BasicRegionDO extends BaseDO {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 上级行政区划编码
     */
    @TableField("parent_adcode")
    private String parentAdcode;
    
    /**
     * 行政区划级别
     */
    @TableField("level")
    private String level;
    
    /**
     * 区域代码
     */
    @TableField("adcode")
    private String adcode;
    
    /**
     * 城市编码
     */
    @TableField("citycode")
    private String citycode;
    
    /**
     * 行政区名称
     */
    @TableField("name")
    private String name;

    /**
     * 行政区名称(英文)
     */
    @TableField("name_en")
    private String nameEn;
    
    /**
     * 中心位置
     */
    @TableField("center")
    private String center;
    
    /**
     * 行政区边界坐标点
     */
    @TableField("polyline")
    private String polyline;
    
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

} 