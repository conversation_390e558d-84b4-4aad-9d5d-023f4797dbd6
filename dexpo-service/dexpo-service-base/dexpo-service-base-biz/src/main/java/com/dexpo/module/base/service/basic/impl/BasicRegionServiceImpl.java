package com.dexpo.module.base.service.basic.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.exception.enums.BaseServiceErrorCodeEnum;
import com.dexpo.module.base.api.basic.dto.BasicRegionDTO;
import com.dexpo.module.base.api.basic.vo.BasicRegionTreeVO;
import com.dexpo.module.base.api.basic.vo.BasicRegionVO;
import com.dexpo.module.base.convert.basic.BasicRegionConvert;
import com.dexpo.module.base.dal.dataobject.basic.BasicRegionDO;
import com.dexpo.module.base.dal.mysql.basic.BasicRegionMapper;
import com.dexpo.module.base.service.basic.BasicRegionService;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 行政区域 Service 实现类
 */
@Service
@RequiredArgsConstructor
public class BasicRegionServiceImpl  extends ServiceImpl<BasicRegionMapper,BasicRegionDO> implements BasicRegionService {


    @Override
    public List<BasicRegionVO> getRegionList(BasicRegionDTO regionDTO) {
        if(StringUtil.isAllBlank(regionDTO.getLevel(),regionDTO.getParentAdcode())){
            throw new ServiceException(BaseServiceErrorCodeEnum.PARAMETER_CANNOT_BE_ALL_EMPTY);
        }
        List<BasicRegionDO> list = baseMapper.selectByLevelAndParentAdcode(regionDTO.getLevel(),regionDTO.getParentAdcode());
        return BasicRegionConvert.INSTANCE.e2vList(list);
    }

    @Override
    public List<BasicRegionTreeVO> getRegionListAll() {
        List<BasicRegionDO> list = baseMapper.selectList();
        List<BasicRegionTreeVO> basicRegionVOS = BasicRegionConvert.INSTANCE.vToTreeList(list);
        return buildTree(basicRegionVOS);
    }

    private List<BasicRegionTreeVO> buildTree(List<BasicRegionTreeVO> regions) {
        Map<String, BasicRegionTreeVO> regionMap = regions.stream()
                .collect(Collectors.toMap(BasicRegionTreeVO::getAdcode, e -> e));
        List<BasicRegionTreeVO> rootRegions = new ArrayList<>();
        for (BasicRegionTreeVO region : regions) {
            if ("street".equals(region.getLevel())) {
                continue;
            }
            String parentAdcode = region.getParentAdcode();
            if (StringUtil.isBlank(parentAdcode)) {
                rootRegions.add(region);
            } else {
                BasicRegionTreeVO parentRegion = regionMap.get(parentAdcode);
                if (parentRegion != null) {
                    if (parentRegion.getChildrenList() == null) {
                        parentRegion.setChildrenList(new ArrayList<>());
                    }
                    parentRegion.getChildrenList().add(region);
                }
            }
        }
        return rootRegions;
    }

    @Override
    public Map<String, Map<String, BasicRegionVO>> getRegionListByLevel() {
        List<BasicRegionDO> list = baseMapper.selectList();
        List<BasicRegionVO> basicRegionVOS = BasicRegionConvert.INSTANCE.e2vList(list);
        return buildMapByLevel(basicRegionVOS);
    }

    private Map<String, Map<String, BasicRegionVO>> buildMapByLevel(List<BasicRegionVO> list) {
        Map<String, Map<String, BasicRegionVO>> map = new HashMap<>();
        for (BasicRegionVO basicRegionDO : list) {
            Map<String, BasicRegionVO> stringBasicRegionVOMap = map.get(basicRegionDO.getLevel());
            if (null == stringBasicRegionVOMap) {
                stringBasicRegionVOMap = new HashMap<>();
            }
            stringBasicRegionVOMap.put(basicRegionDO.getName(), basicRegionDO);
            map.put(basicRegionDO.getLevel(), stringBasicRegionVOMap);
        }
        return map;
    }
}