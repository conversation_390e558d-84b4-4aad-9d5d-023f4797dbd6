package com.dexpo.module.base.service.basic;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dexpo.module.base.api.basic.dto.BasicExhibitionLocationDTO;
import com.dexpo.module.base.api.basic.dto.BasicLocationDTO;
import com.dexpo.module.base.api.basic.vo.BasicLocationVO;
import com.dexpo.module.base.dal.dataobject.basic.BasicLocationDO;

import java.util.List;

/**
 * 业务地域服务接口
 *
 * <p>该接口定义了业务地域相关的核心服务方法，提供地域信息的查询和管理功能。
 * 业务地域是指在业务场景中使用的地理区域划分，可能与行政区域不完全一致。</p>
 *
 * <p>主要功能：</p>
 * <ul>
 *   <li>根据地域标签查询地域信息</li>
 *   <li>根据展会标签查询相关地域信息</li>
 *   <li>支持多语言的地域名称</li>
 *   <li>提供地域编码和名称的映射</li>
 * </ul>
 *
 * <p>使用场景：</p>
 * <ul>
 *   <li>展会地域选择</li>
 *   <li>企业注册地域配置</li>
 *   <li>业务范围划分</li>
 *   <li>地域统计分析</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 * @see BasicLocationDO
 * @see BasicLocationVO
 */
public interface BasicLocationService extends IService<BasicLocationDO> {

    /**
     * 根据地域标签获取业务地域信息列表
     *
     * <p>通过地域标签查询对应的业务地域信息。地域标签是业务系统中
     * 用于分类和筛选地域的标识符，通常对应特定的业务场景。</p>
     *
     * <p>查询逻辑：</p>
     * <ol>
     *   <li>根据传入的地域标签进行精确匹配</li>
     *   <li>返回所有匹配的地域信息</li>
     *   <li>结果按照地域编码排序</li>
     * </ol>
     *
     * @param locationDTO 地域查询条件，包含地域标签等筛选条件
     * @return 匹配的业务地域信息列表，如果没有匹配项则返回空列表
     *
     * @throws IllegalArgumentException 当locationDTO为null或地域标签为空时抛出
     *
     * @example
     * <pre>
     * BasicLocationDTO dto = new BasicLocationDTO();
     * dto.setLocationTag("ENTERPRISE_TYPE");
     * List&lt;BasicLocationVO&gt; locations = basicLocationService.getLocationList(dto);
     * </pre>
     */
    List<BasicLocationVO> getLocationList(BasicLocationDTO locationDTO);

    /**
     * 根据展会标签代码获取相关的业务地域信息列表
     *
     * <p>通过展会标签代码查询与该展会相关联的业务地域信息。
     * 这种关联关系通常在展会配置时建立，用于限定展会的地域范围。</p>
     *
     * <p>查询逻辑：</p>
     * <ol>
     *   <li>根据展会标签代码查找关联的地域标签</li>
     *   <li>通过地域标签查询对应的地域信息</li>
     *   <li>返回所有相关的地域信息</li>
     * </ol>
     *
     * @param exhibitionLocationDTO 展会地域查询条件，包含展会标签代码
     * @return 与展会相关的业务地域信息列表，如果没有关联地域则返回空列表
     *
     * @throws IllegalArgumentException 当exhibitionLocationDTO为null或展会标签代码为空时抛出
     *
     * @example
     * <pre>
     * BasicExhibitionLocationDTO dto = new BasicExhibitionLocationDTO();
     * dto.setExhibitionTagCode("TECH_EXPO_2024");
     * List&lt;BasicLocationVO&gt; locations = basicLocationService.getExhibitionLocationList(dto);
     * </pre>
     */
    List<BasicLocationVO> getExhibitionLocationList(BasicExhibitionLocationDTO exhibitionLocationDTO);

}