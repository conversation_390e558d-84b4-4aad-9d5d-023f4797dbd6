package com.dexpo.module.base.service.basic.entity;

import lombok.Data;

/**
 * ExhibitionValuesetOptionRelation 缓存对象 移除无用字段
 */
@Data
public class ExhibitionValuesetOptionRelationCache {

    private Long id;

    /**
     * 展会标签编码
     */
    private String exhibitionTagCode;

    /**
     * 值集编码
     */
    private String valuesetCode;

    /**
     * 值集项编码
     */
    private String valuesetOptionCode;
}
