<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.dexpo</groupId>
        <artifactId>dexpo-service-base</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>dexpo-service-base-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        infra 模块，主要提供两块能力：
            1. 我们放基础设施的运维与管理，支撑上层的通用与核心业务。 例如说：定时任务的管理、服务器的信息等等
            2. 研发工具，提升研发效率与质量。 例如说：代码生成器、接口文档等等
    </description>

    <dependencies>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-integration-api</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId> <!-- 代码生成器，使用它解析表结构 -->
        </dependency>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-cache</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-security</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-rpc</artifactId>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-kubernetes</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.alibaba.cloud</groupId>-->
<!--            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>-->
<!--        </dependency>-->

<!--        &lt;!&ndash; Config 配置中心相关 &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.alibaba.cloud</groupId>-->
<!--            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>-->
<!--        </dependency>-->

        <!-- Job 定时任务相关 -->
<!--        <dependency>-->
<!--            <groupId>com.dexpo</groupId>-->
<!--            <artifactId>dexpo-framework-starter-job</artifactId>-->
<!--        </dependency>-->

<!--        &lt;!&ndash; 消息队列相关 &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.dexpo</groupId>-->
<!--            <artifactId>dexpo-framework-starter-mq</artifactId>-->
<!--        </dependency>-->

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId> <!-- 实现代码生成 -->
        </dependency>

        <dependency>
            <groupId>cn.smallbun.screw</groupId>
            <artifactId>screw-core</artifactId> <!-- 实现数据库文档 -->
        </dependency>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-base-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-exhibition-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-member-api</artifactId>
        </dependency>


        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-mq</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>


    </dependencies>
    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
