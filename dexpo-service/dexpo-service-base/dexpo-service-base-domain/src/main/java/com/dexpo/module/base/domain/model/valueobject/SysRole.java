package com.dexpo.module.base.domain.model.valueobject;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统角色信息
 *
 * <AUTHOR>
 **/
@Data
public class SysRole {

    private List<String> roleCodeList;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 项目组织code
     */
    private String organizationCode;

    /**
     * 项目组织名称
     */
    private String organizationName;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色分类：值集VS_ACTION_USER_TYPE
     */
    private String roleCategory;

    /**
     * 角色类型：值集VS_ROLE_TYPE
     */
    private String roleType;

    /**
     * 角色描述
     */
    private String roleDescription;

    /**
     * 启用状态，1启用，0禁用
     */
    private Boolean isUseAble;

    /**
     * 删除标志
     */
    private Boolean delFlg;

    /**
     * 创建人ID
     */
    private Long createUser;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateUser;

    /**
     * 修改人姓名
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

}
