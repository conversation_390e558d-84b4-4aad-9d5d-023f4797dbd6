package com.dexpo.module.base.domain.model.agg;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 待办聚合根
 */
@Data
public class CommonTodo {
    private Long id;

    /**
     * 会展id
     */
    private Long exhibitionId;

    /**
     * 待办标题
     */
    private String todoTitle;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 业务单号
     */
    private String businessNo;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 待办状态
     */
    private String status;

    /**
     * 待办生成时间
     */
    private LocalDateTime generationTime;

    /**
     * 完成时间
     */
    private LocalDateTime doneTime;

    private String createUserName;

    private Long createUser;
    /**
     * 更新者，目前使用 SysUser 的 id 编号
     * <p>
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String updateUserName;

    private Long updateUser;
}
