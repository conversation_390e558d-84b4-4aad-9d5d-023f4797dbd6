package com.dexpo.module.base.domain.repository;

import com.dexpo.module.base.domain.model.valueobject.SysPermission;
import com.dexpo.module.base.domain.model.valueobject.SysPermissionInterfaceRelation;

import java.util.List;

public interface SysPermissionRepository {

    List<SysPermission> permissionByOrganizationList(SysPermission sysPermission);

    List<SysPermission> permissionByUserCode(SysPermission sysPermission);

    List<SysPermissionInterfaceRelation> findAllPermissionInterfaceRelation();
}