package com.dexpo.module.base.domain.model.valueobject;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统角色信息
 *
 * <AUTHOR>
 **/
@Data
public class SysOrganizationRoleRelation {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 项目组织code
     */
    private String organizationCode;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 删除标志
     */
    private Boolean delFlg;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateUser;

    /**
     * 修改人姓名
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

}
