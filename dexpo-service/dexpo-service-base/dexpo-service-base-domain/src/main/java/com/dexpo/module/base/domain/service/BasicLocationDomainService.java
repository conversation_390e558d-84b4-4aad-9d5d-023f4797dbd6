package com.dexpo.module.base.domain.service;

import com.dexpo.module.base.domain.model.agg.BasicLocation;
import com.dexpo.module.base.domain.repository.BasicLocationRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务地域 Service 实现类
 */
@Service
@RequiredArgsConstructor
public class BasicLocationDomainService {
    private final BasicLocationRepository basicLocationRepository;

    public List<BasicLocation> getLocationList(BasicLocation basicLocation) {
        return basicLocationRepository.getLocationList(basicLocation);

    }

    public List<BasicLocation> getExhibitionLocationList(String exhibitionTagCode) {
        return basicLocationRepository.getExhibitionLocationList(exhibitionTagCode);
    }
}