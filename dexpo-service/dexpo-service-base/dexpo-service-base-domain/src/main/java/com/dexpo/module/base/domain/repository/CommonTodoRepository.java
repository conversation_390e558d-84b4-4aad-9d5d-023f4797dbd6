package com.dexpo.module.base.domain.repository;

import com.dexpo.framework.common.pojo.PageParam;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.base.domain.model.agg.CommonTodo;


public interface CommonTodoRepository {

    /**
     * 分页获取待办事项
     *
     * @param commonTodo 查询条件对象，用于过滤待办事项
     * @param pageParam  分页参数，包含当前页码和每页大小
     * @return 返回符合条件的待办事项分页结果
     */
    PageResult<CommonTodo> getPage(CommonTodo commonTodo, PageParam pageParam);

    /**
     * 保存一个新的待办事项
     *
     * @param commonTodo 待保存的待办事项对象
     * @return 返回已保存的待办事项对象
     */
    CommonTodo save(CommonTodo commonTodo);

    /**
     * 根据提供的条件查询单个待办事项
     *
     * @param commonTodo 查询条件对象
     * @return 返回符合条件的待办事项对象，若无匹配项则返回 null
     */
    CommonTodo selectOneByCommonTodo(CommonTodo commonTodo);

    /**
     * 根据 ID 更新已有待办事项
     *
     * @param commonTodo 包含更新数据的待办事项对象
     */
    void updateById(CommonTodo commonTodo);
}