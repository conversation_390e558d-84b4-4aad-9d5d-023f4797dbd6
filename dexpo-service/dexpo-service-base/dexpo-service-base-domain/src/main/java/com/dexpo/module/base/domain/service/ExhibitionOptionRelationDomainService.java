package com.dexpo.module.base.domain.service;


import com.dexpo.module.base.domain.model.agg.ExhibitionValuesetOptionRelation;
import com.dexpo.module.base.domain.repository.ExhibitionOptionRelationRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ExhibitionOptionRelationDomainService {
    private final ExhibitionOptionRelationRepository exhibitionOptionRelationRepository;
    public List<ExhibitionValuesetOptionRelation> list(){
        return exhibitionOptionRelationRepository.list();
    }
}
