package com.dexpo.module.base.domain.service;

import com.dexpo.module.base.domain.model.agg.BasicRegion;
import com.dexpo.module.base.domain.repository.BasicRegionRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 行政区域 Service 实现类
 */
@Service
@RequiredArgsConstructor
public class BasicRegionDomainService {
    private final BasicRegionRepository basicRegionRepository;


    public List<BasicRegion> getRegionList(BasicRegion basicRegion) {
        return basicRegionRepository.getRegionList(basicRegion);
    }

    public List<BasicRegion> selectList() {
        return basicRegionRepository.selectList();
    }
}