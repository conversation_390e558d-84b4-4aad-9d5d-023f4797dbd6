package com.dexpo.module.base.domain.model;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class SysOrganization {
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "组织架构编码")
    private String organizationCode;

    @Schema(description = "组织架构名称")
    private String organizationName;

    @Schema(description = "组织架构层级")
    private Integer organizationLevel;

    @Schema(description = "父级组织架构编码")
    private String parentOrganizationCode;

    @Schema(description = "创建人ID")
    private Long createUser;

    @Schema(description = "创建人姓名")
    private String createUserName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改人ID")
    private Long updateUser;

    @Schema(description = "修改人姓名")
    private String updateUserName;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;
} 