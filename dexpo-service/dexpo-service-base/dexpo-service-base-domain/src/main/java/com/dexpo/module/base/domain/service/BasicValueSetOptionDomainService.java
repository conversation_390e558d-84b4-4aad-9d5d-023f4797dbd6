package com.dexpo.module.base.domain.service;

import com.dexpo.module.base.domain.model.agg.BasicValueSetOption;
import com.dexpo.module.base.domain.repository.BasicValueSetOptionRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 值集选项 Service 实现类
 */
@Service
@RequiredArgsConstructor
public class BasicValueSetOptionDomainService {
    private final BasicValueSetOptionRepository basicValueSetOptionRepository;
    public List<BasicValueSetOption> list(){
        return basicValueSetOptionRepository.list();
    }

    public BasicValueSetOption selectByOptionCode(String optionCode){
        return basicValueSetOptionRepository.selectByOptionCode(optionCode);
    }

}