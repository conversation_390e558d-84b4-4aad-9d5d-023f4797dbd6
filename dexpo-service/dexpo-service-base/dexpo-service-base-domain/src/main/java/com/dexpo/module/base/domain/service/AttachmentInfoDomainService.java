package com.dexpo.module.base.domain.service;


import com.dexpo.module.base.domain.model.agg.AttachmentInfo;
import com.dexpo.module.base.domain.repository.AttachmentInfoRepository;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 值集选项 Service 实现类
 */
@Service
@AllArgsConstructor
public class AttachmentInfoDomainService  {

    private final AttachmentInfoRepository attachmentInfoRepository;

    @SneakyThrows
    public AttachmentInfo findById(Long id) {
        return attachmentInfoRepository.findById(id);
    }

    public AttachmentInfo createAttachment(AttachmentInfo attachmentInfo) {
        return attachmentInfoRepository.save(attachmentInfo);
    }

    public List<AttachmentInfo> findByIdList(List<Long> idList) {
       return attachmentInfoRepository.findByIdList(idList);
    }

    public AttachmentInfo findFileByBusinessType(String businessType) {
        return attachmentInfoRepository.findFileByBusinessType(businessType);
    }

}