package com.dexpo.module.base.domain.model.agg;

import lombok.Data;

/**
 * 值集选项聚合根
 */
@Data
public class BasicValueSetOption {
    private Long id;
    /**
     * 值集编码
     */
    private String valuesetCode;

    /**
     * 选项编码
     */
    private String optionCode;

    /**
     * 选项中文描述
     */
    private String optionDescriptionCn;

    /**
     * 选项英文描述
     */
    private String optionDescriptionEn;

    /**
     * 选项排序
     */
    private Integer optionOrder;

    /**
     * 父级ID
     */
    private String parentCode;
}
