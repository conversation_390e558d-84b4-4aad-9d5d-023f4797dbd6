package com.dexpo.module.base.domain.repository;


import com.dexpo.module.base.domain.model.agg.AttachmentInfo;

import java.util.List;

/**
 * AttachmentInfo 仓储接口
 *
 * <AUTHOR> Xiaohua 18/06/2025 15:10
 **/
public interface AttachmentInfoRepository {

    /**
     * 保存附件
     *
     * @return attachmentInfo
     */
    AttachmentInfo save(AttachmentInfo attachmentInfo);

    /**
     * 通过id 查找
     *
     * @param id id
     * @return attachmentInfo
     */
    AttachmentInfo findById(Long id);

    List<AttachmentInfo> findByIdList(List<Long> idList);

    AttachmentInfo findFileByBusinessType(String businessType);

}
