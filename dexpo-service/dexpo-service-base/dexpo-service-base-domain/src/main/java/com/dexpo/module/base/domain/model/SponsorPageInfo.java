package com.dexpo.module.base.domain.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class SponsorPageInfo {
    /**
     * 用户编号
     */
    @Schema(description = "用户编号")
    private String sponsorCode;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String sponsorName;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String sponsorMobile;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String sponsorEmail;

    /**
     * 管理组织
     */
    @Schema(description = "管理组织")
    private String manageOrganization;

    /**
     * 管理组织编码
     */
    @Schema(description = "管理组织编码")
    private String manageOrganizationCode;

    /**
     * 项目组织
     */
    @Schema(description = "项目组织")
    private String sysOrganization;

    /**
     * 角色
     */
    @Schema(description = "角色")
    private String role;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    @Schema(description = "页码")
    private Integer pageNo = 1;

    @Schema(description = "每页条数")
    private Integer pageSize = 10;

}
