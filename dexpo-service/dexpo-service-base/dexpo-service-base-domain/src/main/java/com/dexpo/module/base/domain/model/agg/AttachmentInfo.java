package com.dexpo.module.base.domain.model.agg;

import lombok.Data;

/**
 * 值集信息数据对象聚合根
 */
@Data
public class AttachmentInfo {

    private Long id;

    /**
     * 附件名
     */
    private String attachmentName;

    /**
     * 附件类型
     */
    private String attachmentType;

    /**
     * 附件大小
     */
    private Long attachmentSize;

    /**
     * 附件存储地址
     * /日期20250521/用户编码/uuid_文件名称.文件类型
     */
    private String attachmentPath;

    private String createUserName;

    private Long createUser;

}
