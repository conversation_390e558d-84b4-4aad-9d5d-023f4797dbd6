#!/bin/bash

# DEXPO基础服务模块测试运行脚本
# 该脚本用于运行所有单元测试并生成覆盖率报告

echo "=========================================="
echo "DEXPO基础服务模块测试运行脚本"
echo "=========================================="

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：检查命令执行结果
check_result() {
    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ $1 成功"
    else
        print_message $RED "✗ $1 失败"
        exit 1
    fi
}

# 检查Java环境
print_message $BLUE "检查Java环境..."
java -version
check_result "Java环境检查"

# 检查Maven环境
print_message $BLUE "检查Maven环境..."
mvn -version
check_result "Maven环境检查"

# 清理之前的构建
print_message $BLUE "清理之前的构建..."
mvn clean
check_result "清理构建"

# 编译项目
print_message $BLUE "编译项目..."
mvn compile
check_result "项目编译"

# 编译测试代码
print_message $BLUE "编译测试代码..."
mvn test-compile
check_result "测试代码编译"

# 运行单元测试
print_message $BLUE "运行单元测试..."
mvn test
check_result "单元测试执行"

# 生成测试报告
print_message $BLUE "生成测试报告..."
mvn surefire-report:report
check_result "测试报告生成"

# 生成代码覆盖率报告
print_message $BLUE "生成代码覆盖率报告..."
mvn jacoco:report
check_result "覆盖率报告生成"

# 检查代码覆盖率
print_message $BLUE "检查代码覆盖率..."
mvn jacoco:check
if [ $? -eq 0 ]; then
    print_message $GREEN "✓ 代码覆盖率检查通过"
else
    print_message $YELLOW "⚠ 代码覆盖率未达到要求，但测试继续"
fi

# 运行特定测试套件（可选）
if [ "$1" = "suite" ]; then
    print_message $BLUE "运行完整测试套件..."
    mvn test -Dtest=AllTestSuite
    check_result "测试套件执行"
fi

# 运行性能测试（可选）
if [ "$1" = "performance" ]; then
    print_message $BLUE "运行性能测试..."
    mvn test -Pperformance
    check_result "性能测试执行"
fi

# 显示测试结果摘要
print_message $BLUE "测试结果摘要："
echo "----------------------------------------"

# 检查测试报告文件
if [ -f "target/surefire-reports/TEST-*.xml" ]; then
    # 统计测试数量
    TOTAL_TESTS=$(grep -h "tests=" target/surefire-reports/TEST-*.xml | sed 's/.*tests="\([0-9]*\)".*/\1/' | awk '{sum+=$1} END {print sum}')
    FAILED_TESTS=$(grep -h "failures=" target/surefire-reports/TEST-*.xml | sed 's/.*failures="\([0-9]*\)".*/\1/' | awk '{sum+=$1} END {print sum}')
    ERROR_TESTS=$(grep -h "errors=" target/surefire-reports/TEST-*.xml | sed 's/.*errors="\([0-9]*\)".*/\1/' | awk '{sum+=$1} END {print sum}')
    SKIPPED_TESTS=$(grep -h "skipped=" target/surefire-reports/TEST-*.xml | sed 's/.*skipped="\([0-9]*\)".*/\1/' | awk '{sum+=$1} END {print sum}')
    
    PASSED_TESTS=$((TOTAL_TESTS - FAILED_TESTS - ERROR_TESTS - SKIPPED_TESTS))
    
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    echo "错误测试: $ERROR_TESTS"
    echo "跳过测试: $SKIPPED_TESTS"
    
    if [ $FAILED_TESTS -eq 0 ] && [ $ERROR_TESTS -eq 0 ]; then
        print_message $GREEN "所有测试通过！"
    else
        print_message $RED "存在失败或错误的测试！"
    fi
fi

# 检查覆盖率报告
if [ -f "target/site/jacoco/index.html" ]; then
    print_message $GREEN "代码覆盖率报告已生成: target/site/jacoco/index.html"
    
    # 尝试提取覆盖率数据
    if command -v grep &> /dev/null; then
        INSTRUCTION_COVERAGE=$(grep -o "Total[^%]*%" target/site/jacoco/index.html | head -1 | grep -o "[0-9]*%" | head -1)
        if [ ! -z "$INSTRUCTION_COVERAGE" ]; then
            echo "指令覆盖率: $INSTRUCTION_COVERAGE"
        fi
    fi
fi

# 检查测试报告
if [ -f "target/site/surefire-report.html" ]; then
    print_message $GREEN "测试报告已生成: target/site/surefire-report.html"
fi

echo "----------------------------------------"

# 提供有用的命令提示
print_message $BLUE "有用的命令："
echo "查看详细测试报告: open target/site/surefire-report.html"
echo "查看覆盖率报告: open target/site/jacoco/index.html"
echo "运行特定测试: mvn test -Dtest=ClassName"
echo "运行测试套件: ./run-tests.sh suite"
echo "运行性能测试: ./run-tests.sh performance"
echo "只生成报告: mvn surefire-report:report jacoco:report"

print_message $GREEN "测试运行完成！"
echo "=========================================="
