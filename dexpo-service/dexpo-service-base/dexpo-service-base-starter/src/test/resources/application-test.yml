# 测试环境配置文件
# 使用Mockito进行纯Mock测试，不依赖真实数据库和Redis连接

spring:
  profiles:
    active: test

  # 禁用数据源和Redis自动配置，使用Mock对象
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration
      - org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration

# 日志配置（简化测试日志输出）
logging:
  level:
    com.dexpo.module.base: INFO
    org.springframework: WARN
    org.mockito: DEBUG
    org.junit: INFO
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
      mode: always
      schema-locations: classpath:test-data/schema.sql
      data-locations: classpath:test-data/basic_location.sql
      continue-on-error: false
        
# 测试专用配置
test:
  # 启用Mock模式
  mock-enabled: true
  # 禁用数据库连接
  database-enabled: false
  # 禁用Redis连接
  redis-enabled: false
  # 禁用外部服务调用
  external-services-enabled: false

# 服务发现配置（测试环境禁用）
eureka:
  client:
    enabled: false

# Feign配置（测试环境禁用）
feign:
  client:
    config:
      default:
        connect-timeout: 1000
        read-timeout: 1000

# 管理端点配置（测试环境简化）
management:
  endpoints:
    enabled-by-default: false
    web:
      exposure:
        include: health
  endpoint:
    health:
      enabled: true
      show-details: never
