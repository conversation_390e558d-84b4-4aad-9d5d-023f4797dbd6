-- 测试用基础地域数据
-- 用于单元测试和集成测试

-- 创建基础地域表（如果不存在）
CREATE TABLE IF NOT EXISTS basic_location (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    location_code VARCHAR(50) NOT NULL UNIQUE,
    location_name_cn VARCHAR(100),
    location_name_en VARCHAR(100),
    location_tag VARCHAR(50) NOT NULL,
    creator VARCHAR(64),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updater VARCHAR(64),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0,
    tenant_id BIGINT DEFAULT 0
);

-- 插入测试数据
INSERT INTO basic_location (id, location_code, location_name_cn, location_name_en, location_tag, creator, updater) VALUES
-- 企业类型相关地域
(1, 'CN-BJ', '北京', 'Beijing', 'ENTERPRISE_TYPE', 'test', 'test'),
(2, 'CN-SH', '上海', 'Shanghai', 'ENTERPRISE_TYPE', 'test', 'test'),
(3, 'CN-GZ', '广州', 'Guangzhou', 'ENTERPRISE_TYPE', 'test', 'test'),
(4, 'CN-SZ', '深圳', 'Shenzhen', 'ENTERPRISE_TYPE', 'test', 'test'),
(5, 'CN-HZ', '杭州', 'Hangzhou', 'ENTERPRISE_TYPE', 'test', 'test'),

-- 展会类型相关地域
(6, 'CN-CD', '成都', 'Chengdu', 'EXHIBITION_TYPE', 'test', 'test'),
(7, 'CN-WH', '武汉', 'Wuhan', 'EXHIBITION_TYPE', 'test', 'test'),
(8, 'CN-NJ', '南京', 'Nanjing', 'EXHIBITION_TYPE', 'test', 'test'),
(9, 'CN-XA', '西安', 'Xian', 'EXHIBITION_TYPE', 'test', 'test'),
(10, 'CN-TJ', '天津', 'Tianjin', 'EXHIBITION_TYPE', 'test', 'test'),

-- 国际地域
(11, 'US-NY', '纽约', 'New York', 'INTERNATIONAL', 'test', 'test'),
(12, 'US-LA', '洛杉矶', 'Los Angeles', 'INTERNATIONAL', 'test', 'test'),
(13, 'UK-LON', '伦敦', 'London', 'INTERNATIONAL', 'test', 'test'),
(14, 'JP-TKY', '东京', 'Tokyo', 'INTERNATIONAL', 'test', 'test'),
(15, 'DE-BER', '柏林', 'Berlin', 'INTERNATIONAL', 'test', 'test'),

-- 特殊测试用地域
(16, 'TEST-001', '测试地域1', 'Test Location 1', 'TEST_TAG', 'test', 'test'),
(17, 'TEST-002', '测试地域2', 'Test Location 2', 'TEST_TAG', 'test', 'test'),
(18, 'EMPTY-CN', '', 'Empty Chinese Name', 'TEST_TAG', 'test', 'test'),
(19, 'EMPTY-EN', '空英文名称', '', 'TEST_TAG', 'test', 'test'),
(20, 'BOTH-EMPTY', '', '', 'TEST_TAG', 'test', 'test');

-- 创建展会地域关联表（用于测试展会相关查询）
CREATE TABLE IF NOT EXISTS exhibition_location_mapping (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    exhibition_tag_code VARCHAR(50) NOT NULL,
    location_tag VARCHAR(50) NOT NULL,
    creator VARCHAR(64),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updater VARCHAR(64),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0,
    tenant_id BIGINT DEFAULT 0
);

-- 插入展会地域关联测试数据
INSERT INTO exhibition_location_mapping (exhibition_tag_code, location_tag, creator, updater) VALUES
('TECH_EXPO_2024', 'ENTERPRISE_TYPE', 'test', 'test'),
('TECH_EXPO_2024', 'EXHIBITION_TYPE', 'test', 'test'),
('INTERNATIONAL_FAIR', 'INTERNATIONAL', 'test', 'test'),
('DOMESTIC_EXPO', 'ENTERPRISE_TYPE', 'test', 'test'),
('TEST_EXHIBITION', 'TEST_TAG', 'test', 'test');

-- 创建基础值集表（用于测试值集相关功能）
CREATE TABLE IF NOT EXISTS basic_valueset (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    valueset_code VARCHAR(50) NOT NULL UNIQUE,
    valueset_name VARCHAR(100) NOT NULL,
    valueset_name_en VARCHAR(100),
    description VARCHAR(500),
    status TINYINT DEFAULT 1,
    creator VARCHAR(64),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updater VARCHAR(64),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0,
    tenant_id BIGINT DEFAULT 0
);

-- 插入值集测试数据
INSERT INTO basic_valueset (valueset_code, valueset_name, valueset_name_en, description, creator, updater) VALUES
('USER_TYPE', '用户类型', 'User Type', '系统用户类型分类', 'test', 'test'),
('STATUS_TYPE', '状态类型', 'Status Type', '通用状态类型', 'test', 'test'),
('ENTERPRISE_TYPE', '企业类型', 'Enterprise Type', '企业分类类型', 'test', 'test'),
('EXHIBITION_TYPE', '展会类型', 'Exhibition Type', '展会分类类型', 'test', 'test'),
('REGION_TYPE', '区域类型', 'Region Type', '地理区域分类', 'test', 'test');

-- 创建基础值集选项表
CREATE TABLE IF NOT EXISTS basic_valueset_option (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    valueset_code VARCHAR(50) NOT NULL,
    option_code VARCHAR(50) NOT NULL,
    option_name VARCHAR(100) NOT NULL,
    option_name_en VARCHAR(100),
    option_value VARCHAR(200),
    sort_order INT DEFAULT 0,
    status TINYINT DEFAULT 1,
    creator VARCHAR(64),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updater VARCHAR(64),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0,
    tenant_id BIGINT DEFAULT 0,
    UNIQUE KEY uk_valueset_option (valueset_code, option_code)
);

-- 插入值集选项测试数据
INSERT INTO basic_valueset_option (valueset_code, option_code, option_name, option_name_en, option_value, sort_order, creator, updater) VALUES
-- 用户类型选项
('USER_TYPE', 'ADMIN', '管理员', 'Administrator', 'admin', 1, 'test', 'test'),
('USER_TYPE', 'USER', '普通用户', 'Regular User', 'user', 2, 'test', 'test'),
('USER_TYPE', 'GUEST', '访客', 'Guest', 'guest', 3, 'test', 'test'),

-- 状态类型选项
('STATUS_TYPE', 'ACTIVE', '激活', 'Active', '1', 1, 'test', 'test'),
('STATUS_TYPE', 'INACTIVE', '未激活', 'Inactive', '0', 2, 'test', 'test'),
('STATUS_TYPE', 'PENDING', '待处理', 'Pending', '2', 3, 'test', 'test'),
('STATUS_TYPE', 'DELETED', '已删除', 'Deleted', '-1', 4, 'test', 'test'),

-- 企业类型选项
('ENTERPRISE_TYPE', 'LARGE', '大型企业', 'Large Enterprise', 'large', 1, 'test', 'test'),
('ENTERPRISE_TYPE', 'MEDIUM', '中型企业', 'Medium Enterprise', 'medium', 2, 'test', 'test'),
('ENTERPRISE_TYPE', 'SMALL', '小型企业', 'Small Enterprise', 'small', 3, 'test', 'test'),
('ENTERPRISE_TYPE', 'STARTUP', '初创企业', 'Startup', 'startup', 4, 'test', 'test');

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_basic_location_tag ON basic_location(location_tag);
CREATE INDEX IF NOT EXISTS idx_basic_location_code ON basic_location(location_code);
CREATE INDEX IF NOT EXISTS idx_exhibition_mapping_tag ON exhibition_location_mapping(exhibition_tag_code);
CREATE INDEX IF NOT EXISTS idx_valueset_code ON basic_valueset_option(valueset_code);
CREATE INDEX IF NOT EXISTS idx_option_code ON basic_valueset_option(option_code);
