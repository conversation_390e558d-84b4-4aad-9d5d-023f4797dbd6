# JUnit Platform????
# ????JUnit 5???????

# ??????
junit.jupiter.execution.parallel.enabled=true
junit.jupiter.execution.parallel.mode.default=concurrent
junit.jupiter.execution.parallel.mode.classes.default=concurrent

# ??????
junit.jupiter.testinstance.lifecycle.default=per_class

# ????????
junit.jupiter.displayname.generator.default=org.junit.jupiter.api.DisplayNameGenerator$ReplaceUnderscores

# ??????
junit.jupiter.conditions.deactivate=org.junit.*DisabledCondition

# ??????
junit.jupiter.extensions.autodetection.enabled=true

# ??????
junit.jupiter.execution.timeout.default=5m
junit.jupiter.execution.timeout.testable.method.default=1m
junit.jupiter.execution.timeout.testtemplate.method.default=1m

# ??????
junit.jupiter.params.displayname.default={displayName} [{index}] {arguments}

# ??????
junit.platform.output.capture.stdout=true
junit.platform.output.capture.stderr=true

# ??????
junit.jupiter.testmethod.order.default=org.junit.jupiter.api.MethodOrderer$OrderAnnotation
