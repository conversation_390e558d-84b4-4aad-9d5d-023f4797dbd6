package com.dexpo.module.base.entry.controller;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.base.api.basic.dto.BasicExhibitionLocationDTO;
import com.dexpo.module.base.api.basic.dto.BasicLocationDTO;
import com.dexpo.module.base.api.basic.vo.BasicLocationVO;
import com.dexpo.module.base.app.api.BasicLocationAppService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BasicLocationControllerTest {
    @Mock
    private BasicLocationAppService basicLocationAppService;
    @InjectMocks
    private BasicLocationController controller;

    @Test
    void getLocationList_shouldReturnSuccess() {
        BasicLocationDTO dto = new BasicLocationDTO();
        BasicLocationVO vo = new BasicLocationVO();
        when(basicLocationAppService.getLocationList(any())).thenReturn(List.of(vo));
        CommonResult<List<BasicLocationVO>> result = controller.getLocationList(dto);
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(1, result.getData().size());
        assertSame(vo, result.getData().get(0));
    }

    @Test
    void getExhibitionLocationList_shouldReturnSuccess() {
        BasicExhibitionLocationDTO dto = new BasicExhibitionLocationDTO();
        BasicLocationVO vo = new BasicLocationVO();
        when(basicLocationAppService.getExhibitionLocationList(any())).thenReturn(List.of(vo));
        CommonResult<List<BasicLocationVO>> result = controller.getExhibitionLocationList(dto);
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(1, result.getData().size());
        assertSame(vo, result.getData().get(0));
    }
} 