package com.dexpo.module.base.infrastructure.repository;

import com.dexpo.module.base.domain.model.agg.BasicRegion;
import com.dexpo.module.base.infrastructure.converter.BasicRegionConvert;
import com.dexpo.module.base.infrastructure.tunnel.database.BasicRegionMapper;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.BasicRegionDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BasicRegionRepositoryImplTest {
    @Mock
    private BasicRegionMapper basicRegionMapper;
    @InjectMocks
    private BasicRegionRepositoryImpl repository;

    @BeforeEach
    void setUp() {
        BasicRegionConvert mockConvert = mock(BasicRegionConvert.class, Mockito.RETURNS_DEEP_STUBS);
        setFinalStatic(BasicRegionConvert.class, "INSTANCE", mockConvert);
    }

    private static void setFinalStatic(Class<?> clazz, String fieldName, Object newValue) {
        try {
            java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            java.lang.reflect.Field modifiersField = java.lang.reflect.Field.class.getDeclaredField("modifiers");
            modifiersField.setAccessible(true);
            modifiersField.setInt(field, field.getModifiers() & ~java.lang.reflect.Modifier.FINAL);
            field.set(null, newValue);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void getRegionList_shouldReturnList() {
        BasicRegionDO regionDO = new BasicRegionDO();
        when(basicRegionMapper.selectByLevelAndParentAdcode(anyString(), anyString())).thenReturn(List.of(regionDO));
        BasicRegion region = new BasicRegion();
        when(BasicRegionConvert.INSTANCE.e2dList(anyList())).thenReturn(List.of(region));
        BasicRegion param = new BasicRegion();
        param.setLevel("province");
        param.setParentAdcode("100000");
        List<BasicRegion> result = repository.getRegionList(param);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertSame(region, result.get(0));
    }

    @Test
    void selectList_shouldReturnList() {
        BasicRegionDO regionDO = new BasicRegionDO();
        when(basicRegionMapper.selectList()).thenReturn(List.of(regionDO));
        BasicRegion region = new BasicRegion();
        when(BasicRegionConvert.INSTANCE.e2dList(anyList())).thenReturn(List.of(region));
        List<BasicRegion> result = repository.selectList();
        assertNotNull(result);
        assertEquals(1, result.size());
        assertSame(region, result.get(0));
    }
} 