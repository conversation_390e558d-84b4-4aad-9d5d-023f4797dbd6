package com.dexpo.module.base.enums;

import com.dexpo.module.member.enums.RegisterStatusEnum;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link CommonTodoMediaRegisterActionEnum} 的单元测试类
 * 
 * <p>测试媒体注册动作枚举的所有功能，确保100%代码覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class CommonTodoMediaRegisterActionEnumTest {

    /**
     * 测试枚举值的基本属性
     */
    @Test
    void testEnumValues() {
        CommonTodoMediaRegisterActionEnum[] values = CommonTodoMediaRegisterActionEnum.values();
        
        // 验证枚举数量
        assertEquals(1, values.length, "当前应该只有一个枚举值");
        
        // 验证SUBMIT枚举值
        CommonTodoMediaRegisterActionEnum submit = CommonTodoMediaRegisterActionEnum.SUBMIT;
        assertNotNull(submit, "SUBMIT枚举值不能为空");
    }

    /**
     * 测试SUBMIT枚举值的属性
     */
    @Test
    void testSubmitEnumProperties() {
        CommonTodoMediaRegisterActionEnum submit = CommonTodoMediaRegisterActionEnum.SUBMIT;
        
        // 验证注册状态枚举
        assertEquals(RegisterStatusEnum.PENDING_REVIEW, submit.getRegisterStatusEnum(),
                     "SUBMIT的注册状态应该是PENDING_REVIEW");
        
        // 验证动作描述
        assertEquals("媒体注册报名", submit.getActionDesc(),
                     "SUBMIT的动作描述应该是'媒体注册报名'");
        
        // 验证类型描述
        assertEquals("申请待审核", submit.getTypeDesc(),
                     "SUBMIT的类型描述应该是'申请待审核'");
    }

    /**
     * 测试枚举的getter方法
     */
    @Test
    void testGetterMethods() {
        CommonTodoMediaRegisterActionEnum submit = CommonTodoMediaRegisterActionEnum.SUBMIT;
        
        // 测试getRegisterStatusEnum方法
        RegisterStatusEnum registerStatus = submit.getRegisterStatusEnum();
        assertNotNull(registerStatus, "注册状态枚举不能为空");
        assertEquals(RegisterStatusEnum.PENDING_REVIEW, registerStatus);
        
        // 测试getActionDesc方法
        String actionDesc = submit.getActionDesc();
        assertNotNull(actionDesc, "动作描述不能为空");
        assertFalse(actionDesc.isEmpty(), "动作描述不能为空字符串");
        assertEquals("媒体注册报名", actionDesc);
        
        // 测试getTypeDesc方法
        String typeDesc = submit.getTypeDesc();
        assertNotNull(typeDesc, "类型描述不能为空");
        assertFalse(typeDesc.isEmpty(), "类型描述不能为空字符串");
        assertEquals("申请待审核", typeDesc);
    }

    /**
     * 测试枚举的基本方法
     */
    @Test
    void testBasicEnumMethods() {
        CommonTodoMediaRegisterActionEnum submit = CommonTodoMediaRegisterActionEnum.SUBMIT;
        
        // 测试name方法
        assertEquals("SUBMIT", submit.name());
        
        // 测试toString方法
        assertEquals("SUBMIT", submit.toString());
        
        // 测试ordinal方法
        assertEquals(0, submit.ordinal());
    }

    /**
     * 测试valueOf方法
     */
    @Test
    void testValueOf() {
        // 测试有效值
        CommonTodoMediaRegisterActionEnum submit = 
                CommonTodoMediaRegisterActionEnum.valueOf("SUBMIT");
        assertEquals(CommonTodoMediaRegisterActionEnum.SUBMIT, submit);
        
        // 测试无效值
        assertThrows(IllegalArgumentException.class, 
                     () -> CommonTodoMediaRegisterActionEnum.valueOf("INVALID"));
        
        assertThrows(IllegalArgumentException.class, 
                     () -> CommonTodoMediaRegisterActionEnum.valueOf(""));
        
        assertThrows(NullPointerException.class, 
                     () -> CommonTodoMediaRegisterActionEnum.valueOf(null));
    }

    /**
     * 测试values方法
     */
    @Test
    void testValues() {
        CommonTodoMediaRegisterActionEnum[] values = CommonTodoMediaRegisterActionEnum.values();
        
        assertNotNull(values, "values数组不能为空");
        assertEquals(1, values.length, "当前应该只有一个枚举值");
        assertEquals(CommonTodoMediaRegisterActionEnum.SUBMIT, values[0]);
        
        // 验证返回的是副本，修改不会影响原枚举
        CommonTodoMediaRegisterActionEnum[] values1 = CommonTodoMediaRegisterActionEnum.values();
        CommonTodoMediaRegisterActionEnum[] values2 = CommonTodoMediaRegisterActionEnum.values();
        assertNotSame(values1, values2, "每次调用values()应该返回新的数组");
    }

    /**
     * 测试枚举的不变性
     */
    @Test
    void testEnumImmutability() {
        CommonTodoMediaRegisterActionEnum submit = CommonTodoMediaRegisterActionEnum.SUBMIT;
        
        // 验证枚举属性的不变性
        RegisterStatusEnum originalStatus = submit.getRegisterStatusEnum();
        String originalActionDesc = submit.getActionDesc();
        String originalTypeDesc = submit.getTypeDesc();
        
        // 多次调用getter方法，验证返回值一致
        assertEquals(originalStatus, submit.getRegisterStatusEnum());
        assertEquals(originalActionDesc, submit.getActionDesc());
        assertEquals(originalTypeDesc, submit.getTypeDesc());
    }

    /**
     * 测试枚举的equals和hashCode
     */
    @Test
    void testEqualsAndHashCode() {
        CommonTodoMediaRegisterActionEnum submit1 = CommonTodoMediaRegisterActionEnum.SUBMIT;
        CommonTodoMediaRegisterActionEnum submit2 = CommonTodoMediaRegisterActionEnum.valueOf("SUBMIT");
        
        // 测试equals
        assertEquals(submit1, submit2);
        assertEquals(submit1, submit1); // 自反性
        
        // 测试hashCode
        assertEquals(submit1.hashCode(), submit2.hashCode());
        
        // 测试与null的比较
        assertNotEquals(submit1, null);
        
        // 测试与其他类型的比较
        assertNotEquals(submit1, "SUBMIT");
    }

    /**
     * 测试枚举构造函数的覆盖
     */
    @Test
    void testConstructorCoverage() {
        // 通过访问枚举值来确保构造函数被调用
        CommonTodoMediaRegisterActionEnum submit = CommonTodoMediaRegisterActionEnum.SUBMIT;
        
        // 验证构造函数正确设置了所有字段
        assertNotNull(submit.getRegisterStatusEnum());
        assertNotNull(submit.getActionDesc());
        assertNotNull(submit.getTypeDesc());
        
        // 验证字段值符合预期
        assertEquals(RegisterStatusEnum.PENDING_REVIEW, submit.getRegisterStatusEnum());
        assertEquals("媒体注册报名", submit.getActionDesc());
        assertEquals("申请待审核", submit.getTypeDesc());
    }

    /**
     * 测试枚举的业务逻辑一致性
     */
    @Test
    void testBusinessLogicConsistency() {
        CommonTodoMediaRegisterActionEnum submit = CommonTodoMediaRegisterActionEnum.SUBMIT;
        
        // 验证业务逻辑的一致性：SUBMIT动作对应PENDING_REVIEW状态
        assertEquals(RegisterStatusEnum.PENDING_REVIEW, submit.getRegisterStatusEnum(),
                     "SUBMIT动作应该对应待审核状态");
        
        // 验证描述的一致性
        assertTrue(submit.getActionDesc().contains("注册"),
                   "动作描述应该包含'注册'关键词");
        assertTrue(submit.getTypeDesc().contains("审核"),
                   "类型描述应该包含'审核'关键词");
    }

    /**
     * 测试枚举的Lombok注解功能
     */
    @Test
    void testLombokAnnotations() {
        CommonTodoMediaRegisterActionEnum submit = CommonTodoMediaRegisterActionEnum.SUBMIT;
        
        // 验证@Getter注解生成的getter方法存在且可用
        assertDoesNotThrow(() -> {
            submit.getRegisterStatusEnum();
            submit.getActionDesc();
            submit.getTypeDesc();
        }, "Lombok生成的getter方法应该可以正常调用");
        
        // 验证@AllArgsConstructor注解生成的构造函数功能
        // 通过枚举值的正确初始化来验证构造函数的正确性
        assertNotNull(submit.getRegisterStatusEnum());
        assertNotNull(submit.getActionDesc());
        assertNotNull(submit.getTypeDesc());
    }
}
