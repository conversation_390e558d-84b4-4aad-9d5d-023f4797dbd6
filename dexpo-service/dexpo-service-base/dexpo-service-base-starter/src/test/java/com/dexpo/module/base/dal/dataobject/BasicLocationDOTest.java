package com.dexpo.module.base.dal.dataobject;

import com.dexpo.module.base.BaseUnitTest;
import com.dexpo.module.base.dal.dataobject.basic.BasicLocationDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link BasicLocationDO} 的单元测试类
 * 
 * <p>测试业务地域数据对象的所有功能，确保100%代码覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class BasicLocationDOTest extends BaseUnitTest {

    private BasicLocationDO basicLocationDO;

    @BeforeEach
    void setUp() {
        basicLocationDO = new BasicLocationDO();
    }

    /**
     * 测试默认构造函数
     */
    @Test
    void testDefaultConstructor() {
        BasicLocationDO locationDO = new BasicLocationDO();
        
        assertNotNull(locationDO);
        assertNull(locationDO.getId());
        assertNull(locationDO.getLocationCode());
        assertNull(locationDO.getLocationNameCn());
        assertNull(locationDO.getLocationNameEn());
        assertNull(locationDO.getLocationTag());
    }

    /**
     * 测试Builder构造函数
     */
    @Test
    void testBuilderConstructor() {
        LocalDateTime now = LocalDateTime.now();
        
        BasicLocationDO locationDO = BasicLocationDO.builder()
                .id(1L)
                .locationCode("CN-BJ")
                .locationNameCn("北京")
                .locationNameEn("Beijing")
                .locationTag("ENTERPRISE_TYPE")
                .build();
        
        assertNotNull(locationDO);
        assertEquals(1L, locationDO.getId());
        assertEquals("CN-BJ", locationDO.getLocationCode());
        assertEquals("北京", locationDO.getLocationNameCn());
        assertEquals("Beijing", locationDO.getLocationNameEn());
        assertEquals("ENTERPRISE_TYPE", locationDO.getLocationTag());
    }

    /**
     * 测试全参构造函数
     */
    @Test
    void testAllArgsConstructor() {
        LocalDateTime now = LocalDateTime.now();
        
        BasicLocationDO locationDO = new BasicLocationDO(
                1L, "CN-BJ", "北京", "Beijing", "ENTERPRISE_TYPE"
        );
        
        assertNotNull(locationDO);
        assertEquals(1L, locationDO.getId());
        assertEquals("CN-BJ", locationDO.getLocationCode());
        assertEquals("北京", locationDO.getLocationNameCn());
        assertEquals("Beijing", locationDO.getLocationNameEn());
        assertEquals("ENTERPRISE_TYPE", locationDO.getLocationTag());
    }

    /**
     * 测试ID字段的getter和setter
     */
    @Test
    void testIdGetterSetter() {
        assertNull(basicLocationDO.getId());
        
        basicLocationDO.setId(1L);
        assertEquals(1L, basicLocationDO.getId());
        
        basicLocationDO.setId(null);
        assertNull(basicLocationDO.getId());
    }

    /**
     * 测试locationCode字段的getter和setter
     */
    @Test
    void testLocationCodeGetterSetter() {
        assertNull(basicLocationDO.getLocationCode());
        
        basicLocationDO.setLocationCode("CN-BJ");
        assertEquals("CN-BJ", basicLocationDO.getLocationCode());
        
        basicLocationDO.setLocationCode("");
        assertEquals("", basicLocationDO.getLocationCode());
        
        basicLocationDO.setLocationCode(null);
        assertNull(basicLocationDO.getLocationCode());
    }

    /**
     * 测试locationNameCn字段的getter和setter
     */
    @Test
    void testLocationNameCnGetterSetter() {
        assertNull(basicLocationDO.getLocationNameCn());
        
        basicLocationDO.setLocationNameCn("北京");
        assertEquals("北京", basicLocationDO.getLocationNameCn());
        
        basicLocationDO.setLocationNameCn("");
        assertEquals("", basicLocationDO.getLocationNameCn());
        
        basicLocationDO.setLocationNameCn(null);
        assertNull(basicLocationDO.getLocationNameCn());
    }

    /**
     * 测试locationNameEn字段的getter和setter
     */
    @Test
    void testLocationNameEnGetterSetter() {
        assertNull(basicLocationDO.getLocationNameEn());
        
        basicLocationDO.setLocationNameEn("Beijing");
        assertEquals("Beijing", basicLocationDO.getLocationNameEn());
        
        basicLocationDO.setLocationNameEn("");
        assertEquals("", basicLocationDO.getLocationNameEn());
        
        basicLocationDO.setLocationNameEn(null);
        assertNull(basicLocationDO.getLocationNameEn());
    }

    /**
     * 测试locationTag字段的getter和setter
     */
    @Test
    void testLocationTagGetterSetter() {
        assertNull(basicLocationDO.getLocationTag());
        
        basicLocationDO.setLocationTag("ENTERPRISE_TYPE");
        assertEquals("ENTERPRISE_TYPE", basicLocationDO.getLocationTag());
        
        basicLocationDO.setLocationTag("");
        assertEquals("", basicLocationDO.getLocationTag());
        
        basicLocationDO.setLocationTag(null);
        assertNull(basicLocationDO.getLocationTag());
    }

    /**
     * 测试equals方法
     */
    @Test
    void testEquals() {
        BasicLocationDO locationDO1 = BasicLocationDO.builder()
                .id(1L)
                .locationCode("CN-BJ")
                .locationNameCn("北京")
                .locationNameEn("Beijing")
                .locationTag("ENTERPRISE_TYPE")
                .build();
        
        BasicLocationDO locationDO2 = BasicLocationDO.builder()
                .id(1L)
                .locationCode("CN-BJ")
                .locationNameCn("北京")
                .locationNameEn("Beijing")
                .locationTag("ENTERPRISE_TYPE")
                .build();
        
        BasicLocationDO locationDO3 = BasicLocationDO.builder()
                .id(2L)
                .locationCode("CN-SH")
                .locationNameCn("上海")
                .locationNameEn("Shanghai")
                .locationTag("ENTERPRISE_TYPE")
                .build();
        
        // 测试相等性
        assertEquals(locationDO1, locationDO2);
        assertEquals(locationDO1, locationDO1); // 自反性
        
        // 测试不相等性
        assertNotEquals(locationDO1, locationDO3);
        assertNotEquals(locationDO1, null);
        assertNotEquals(locationDO1, "string");
    }

    /**
     * 测试hashCode方法
     */
    @Test
    void testHashCode() {
        BasicLocationDO locationDO1 = BasicLocationDO.builder()
                .id(1L)
                .locationCode("CN-BJ")
                .locationNameCn("北京")
                .locationNameEn("Beijing")
                .locationTag("ENTERPRISE_TYPE")
                .build();
        
        BasicLocationDO locationDO2 = BasicLocationDO.builder()
                .id(1L)
                .locationCode("CN-BJ")
                .locationNameCn("北京")
                .locationNameEn("Beijing")
                .locationTag("ENTERPRISE_TYPE")
                .build();
        
        // 相等的对象应该有相同的hashCode
        assertEquals(locationDO1.hashCode(), locationDO2.hashCode());
        
        // 测试hashCode的一致性
        int hashCode1 = locationDO1.hashCode();
        int hashCode2 = locationDO1.hashCode();
        assertEquals(hashCode1, hashCode2);
    }

    /**
     * 测试toString方法
     */
    @Test
    void testToString() {
        basicLocationDO.setId(1L);
        basicLocationDO.setLocationCode("CN-BJ");
        basicLocationDO.setLocationNameCn("北京");
        basicLocationDO.setLocationNameEn("Beijing");
        basicLocationDO.setLocationTag("ENTERPRISE_TYPE");
        
        String toString = basicLocationDO.toString();
        
        assertNotNull(toString);
        assertTrue(toString.contains("BasicLocationDO"));
        assertTrue(toString.contains("id=1"));
        assertTrue(toString.contains("locationCode=CN-BJ"));
        assertTrue(toString.contains("locationNameCn=北京"));
        assertTrue(toString.contains("locationNameEn=Beijing"));
        assertTrue(toString.contains("locationTag=ENTERPRISE_TYPE"));
    }

    /**
     * 测试Builder模式的链式调用
     */
    @Test
    void testBuilderChaining() {
        BasicLocationDO locationDO = BasicLocationDO.builder()
                .id(1L)
                .locationCode("CN-BJ")
                .locationNameCn("北京")
                .locationNameEn("Beijing")
                .locationTag("ENTERPRISE_TYPE")
                .build();
        
        assertNotNull(locationDO);
        assertEquals(1L, locationDO.getId());
        assertEquals("CN-BJ", locationDO.getLocationCode());
        assertEquals("北京", locationDO.getLocationNameCn());
        assertEquals("Beijing", locationDO.getLocationNameEn());
        assertEquals("ENTERPRISE_TYPE", locationDO.getLocationTag());
    }

    /**
     * 测试Builder的toBuilder方法
     */
    @Test
    void testToBuilder() {
        BasicLocationDO original = BasicLocationDO.builder()
                .id(1L)
                .locationCode("CN-BJ")
                .locationNameCn("北京")
                .locationNameEn("Beijing")
                .locationTag("ENTERPRISE_TYPE")
                .build();
        
        BasicLocationDO modified = original.toBuilder()
                .locationCode("CN-SH")
                .locationNameCn("上海")
                .locationNameEn("Shanghai")
                .build();
        
        // 验证原对象未被修改
        assertEquals("CN-BJ", original.getLocationCode());
        assertEquals("北京", original.getLocationNameCn());
        assertEquals("Beijing", original.getLocationNameEn());
        
        // 验证新对象的修改
        assertEquals(1L, modified.getId()); // 继承的字段
        assertEquals("CN-SH", modified.getLocationCode()); // 修改的字段
        assertEquals("上海", modified.getLocationNameCn()); // 修改的字段
        assertEquals("Shanghai", modified.getLocationNameEn()); // 修改的字段
        assertEquals("ENTERPRISE_TYPE", modified.getLocationTag()); // 继承的字段
    }

    /**
     * 测试类的注解
     */
    @Test
    void testClassAnnotations() {
        // 验证类上的注解
        assertTrue(BasicLocationDO.class.isAnnotationPresent(lombok.Data.class));
        assertTrue(BasicLocationDO.class.isAnnotationPresent(lombok.Builder.class));
        assertTrue(BasicLocationDO.class.isAnnotationPresent(lombok.EqualsAndHashCode.class));
        assertTrue(BasicLocationDO.class.isAnnotationPresent(lombok.NoArgsConstructor.class));
        assertTrue(BasicLocationDO.class.isAnnotationPresent(lombok.AllArgsConstructor.class));
        assertTrue(BasicLocationDO.class.isAnnotationPresent(
                com.baomidou.mybatisplus.annotation.TableName.class));
    }

    /**
     * 测试字段的注解
     */
    @Test
    void testFieldAnnotations() {
        try {
            // 测试ID字段的注解
            var idField = BasicLocationDO.class.getDeclaredField("id");
            assertTrue(idField.isAnnotationPresent(
                    com.baomidou.mybatisplus.annotation.TableId.class));
            
            // 测试其他字段的注解
            var locationCodeField = BasicLocationDO.class.getDeclaredField("locationCode");
            assertTrue(locationCodeField.isAnnotationPresent(
                    com.baomidou.mybatisplus.annotation.TableField.class));
            
            var locationNameCnField = BasicLocationDO.class.getDeclaredField("locationNameCn");
            assertTrue(locationNameCnField.isAnnotationPresent(
                    com.baomidou.mybatisplus.annotation.TableField.class));
            
            var locationNameEnField = BasicLocationDO.class.getDeclaredField("locationNameEn");
            assertTrue(locationNameEnField.isAnnotationPresent(
                    com.baomidou.mybatisplus.annotation.TableField.class));
            
            var locationTagField = BasicLocationDO.class.getDeclaredField("locationTag");
            assertTrue(locationTagField.isAnnotationPresent(
                    com.baomidou.mybatisplus.annotation.TableField.class));
            
        } catch (NoSuchFieldException e) {
            fail("字段应该存在: " + e.getMessage());
        }
    }
}
