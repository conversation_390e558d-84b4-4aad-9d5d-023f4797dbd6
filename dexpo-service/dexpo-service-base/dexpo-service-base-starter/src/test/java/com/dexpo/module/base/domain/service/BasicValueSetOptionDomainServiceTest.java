package com.dexpo.module.base.domain.service;

import com.dexpo.module.base.domain.model.agg.BasicValueSetOption;
import com.dexpo.module.base.domain.repository.BasicValueSetOptionRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BasicValueSetOptionDomainServiceTest {
    @Mock
    private BasicValueSetOptionRepository basicValueSetOptionRepository;
    @InjectMocks
    private BasicValueSetOptionDomainService service;

    @Test
    void list_shouldReturnList() {
        BasicValueSetOption option = new BasicValueSetOption();
        when(basicValueSetOptionRepository.list()).thenReturn(List.of(option));
        List<BasicValueSetOption> result = service.list();
        assertNotNull(result);
        assertEquals(1, result.size());
        assertSame(option, result.get(0));
    }

    @Test
    void selectByOptionCode_shouldReturnOption() {
        BasicValueSetOption option = new BasicValueSetOption();
        String optionCode = "code1";
        when(basicValueSetOptionRepository.selectByOptionCode(optionCode)).thenReturn(option);
        BasicValueSetOption result = service.selectByOptionCode(optionCode);
        assertNotNull(result);
        assertSame(option, result);
    }
} 