package com.dexpo.module.base.app.service;

import com.dexpo.framework.cache.redis.entity.sponsor.SponsorProfileCache;
import com.dexpo.framework.cache.redis.operate.member.MemberBaseInfoOpt;
import com.dexpo.framework.common.enums.ValueSetCommonTodoBusinessTypeEnum;
import com.dexpo.framework.common.enums.ValueSetTodoStatusEnum;
import com.dexpo.framework.common.enums.ValueSetUserTypeEnum;
import com.dexpo.framework.common.pojo.PageParam;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.framework.security.core.util.SecurityFrameworkUtils;
import com.dexpo.module.base.api.basic.dto.CommenTodoPageQueryDTO;
import com.dexpo.module.base.api.basic.vo.CommonTodoVO;
import com.dexpo.module.base.app.converter.CommonTodoDTOConvert;
import com.dexpo.module.base.domain.model.agg.CommonTodo;
import com.dexpo.module.base.domain.service.CommonTodoDomainService;
import com.dexpo.module.base.infrastructure.enums.CommonTodoMediaRegisterActionEnum;
import com.dexpo.module.base.infrastructure.enums.RegisterStatusEnum;
import com.dexpo.module.base.infrastructure.integration.exhibition.ExhibitionExternalService;
import com.dexpo.module.exhibition.api.dto.ExhibitionQueryDTO;
import com.dexpo.module.exhibition.api.vo.ExhibitionVO;
import com.dexpo.module.member.api.dto.message.MediaRegisterEventDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CommonTodoAppServiceImplTest {
    @Mock
    private CommonTodoDomainService commonTodoDomainService;
    @Mock
    private ExhibitionExternalService exhibitionExternalService;
    @Mock
    private MemberBaseInfoOpt memberBaseInfoOpt;

    @InjectMocks
    private CommonTodoAppServiceImpl service;

    @BeforeEach
    void setUp() {
        CommonTodoDTOConvert mockConvert = mock(CommonTodoDTOConvert.class, Mockito.RETURNS_DEEP_STUBS);
        setFinalStatic(CommonTodoDTOConvert.class, "INSTANCE", mockConvert);
    }

    private static void setFinalStatic(Class<?> clazz, String fieldName, Object newValue) {
        try {
            java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            java.lang.reflect.Field modifiersField = java.lang.reflect.Field.class.getDeclaredField("modifiers");
            modifiersField.setAccessible(true);
            modifiersField.setInt(field, field.getModifiers() & ~java.lang.reflect.Modifier.FINAL);
            field.set(null, newValue);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void getPage_shouldReturnEmpty_whenNoExhibitionIds() {
        CommenTodoPageQueryDTO req = new CommenTodoPageQueryDTO();
        SponsorProfileCache profileCache = new SponsorProfileCache();
        profileCache.setExhibitionTagCode("tag");
        when(memberBaseInfoOpt.sponsorProfile(anyLong())).thenReturn(profileCache);
        when(exhibitionExternalService.getExhibitionIds(any(ExhibitionQueryDTO.class))).thenReturn(Collections.emptyList());
        PageResult<CommonTodoVO> result = service.getPage(req);
        assertNotNull(result);
        assertEquals(0, result.getTotal());
    }

    @Test
    void getPage_shouldReturnPageResult() {
        CommenTodoPageQueryDTO req = new CommenTodoPageQueryDTO();
        req.setPageNo(1);
        req.setPageSize(10);
        req.setStatus(null);
        SponsorProfileCache profileCache = new SponsorProfileCache();
        profileCache.setExhibitionTagCode("tag");
        when(memberBaseInfoOpt.sponsorProfile(anyLong())).thenReturn(profileCache);
        when(exhibitionExternalService.getExhibitionIds(any(ExhibitionQueryDTO.class))).thenReturn(List.of(1L));
        PageResult<CommonTodo> pageResult = new PageResult<>(List.of(new CommonTodo()), 1L);
        when(commonTodoDomainService.getPage(any(CommonTodo.class), any(PageParam.class))).thenReturn(pageResult);
        when(CommonTodoDTOConvert.INSTANCE.e2v(anyList())).thenReturn(List.of(new CommonTodoVO()));
        PageResult<CommonTodoVO> result = service.getPage(req);
        assertNotNull(result);
        assertEquals(1, result.getTotal());
    }

    @Test
    void createMediaRegisterCommonTodo_shouldCallSave() {
        MediaRegisterEventDTO eventDTO = new MediaRegisterEventDTO();
        eventDTO.setExhibitionId(1L);
        eventDTO.setEnterpriseName("企业");
        eventDTO.setMemberId(2L);
        eventDTO.setOperatorId(3L);
        eventDTO.setOperatorName("操作人");
        eventDTO.setEventTime("2024-06-24 12:00:00");
        ExhibitionVO exhibitionVO = new ExhibitionVO();
        exhibitionVO.setExhibitionNameCn("展会");
        when(exhibitionExternalService.getExhibition(anyLong())).thenReturn(exhibitionVO);
        doNothing().when(commonTodoDomainService).save(any(CommonTodo.class));
        service.createMediaRegisterCommonTodo(eventDTO);
        verify(commonTodoDomainService, times(1)).save(any(CommonTodo.class));
    }

    @Test
    void updateMediaRegisterCommonTodo_shouldUpdateStatus() {
        MediaRegisterEventDTO eventDTO = new MediaRegisterEventDTO();
        eventDTO.setExhibitionId(1L);
        eventDTO.setMemberId(2L);
        eventDTO.setOperatorId(3L);
        eventDTO.setOperatorName("操作人");
        eventDTO.setRegisterStatus(RegisterStatusEnum.APPROVED.getCode());
        CommonTodo one = new CommonTodo();
        one.setId(10L);
        when(commonTodoDomainService.selectOneByCommonTodo(any(CommonTodo.class))).thenReturn(one);
        doNothing().when(commonTodoDomainService).updateById(any(CommonTodo.class));
        service.updateMediaRegisterCommonTodo(eventDTO);
        verify(commonTodoDomainService, times(1)).updateById(any(CommonTodo.class));
    }

    @Test
    void updateMediaRegisterCommonTodo_shouldReturnWhenNotFound() {
        MediaRegisterEventDTO eventDTO = new MediaRegisterEventDTO();
        eventDTO.setExhibitionId(1L);
        eventDTO.setMemberId(2L);
        when(commonTodoDomainService.selectOneByCommonTodo(any(CommonTodo.class))).thenReturn(null);
        service.updateMediaRegisterCommonTodo(eventDTO);
        verify(commonTodoDomainService, never()).updateById(any(CommonTodo.class));
    }

    @Test
    void updateMediaRegisterCommonTodo_shouldReturnWhenUnknownStatus() {
        MediaRegisterEventDTO eventDTO = new MediaRegisterEventDTO();
        eventDTO.setExhibitionId(1L);
        eventDTO.setMemberId(2L);
        eventDTO.setRegisterStatus("UNKNOWN_CODE");
        CommonTodo one = new CommonTodo();
        one.setId(10L);
        when(commonTodoDomainService.selectOneByCommonTodo(any(CommonTodo.class))).thenReturn(one);
        service.updateMediaRegisterCommonTodo(eventDTO);
        verify(commonTodoDomainService, never()).updateById(any(CommonTodo.class));
    }
} 