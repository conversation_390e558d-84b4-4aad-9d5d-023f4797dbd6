package com.dexpo.module.base.entry.controller;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.security.core.LoginUser;
import com.dexpo.framework.security.core.util.SecurityFrameworkUtils;
import com.dexpo.module.base.BaseUnitTest;
import com.dexpo.module.base.api.attachment.dto.AttachmentInfoDTO;
import com.dexpo.module.base.api.attachment.vo.AttachmentInfoVO;
import com.dexpo.module.base.app.api.AttachmentInfoAppService;
import com.dexpo.module.base.util.MockitoTestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.mockito.Mock;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * {@link AttachmentController} 的单元测试类
 * 
 * <p>使用Mockito进行纯Mock测试，测试附件控制器的所有功能。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@DisplayName("附件控制器测试")
class AttachmentControllerTest extends BaseUnitTest {

    @Mock
    private AttachmentInfoAppService attachmentInfoAppService;

    @InjectMocks
    private AttachmentController attachmentController;

    private AttachmentInfoVO attachmentInfoVO;
    private AttachmentInfoDTO attachmentInfoDTO;
    private LoginUser loginUser;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        attachmentInfoVO = new AttachmentInfoVO();
        attachmentInfoVO.setId(createTestId());
        attachmentInfoVO.setFileName("test.pdf");
        attachmentInfoVO.setFileSize(1024L);
        attachmentInfoVO.setBusinessType("CONTRACT");

        attachmentInfoDTO = new AttachmentInfoDTO();
        attachmentInfoDTO.setFileName("test.pdf");
        attachmentInfoDTO.setFileSize(1024L);
        attachmentInfoDTO.setBusinessType("CONTRACT");

        loginUser = new LoginUser();
        loginUser.setId(createTestId());
        loginUser.setUsername("testuser");
    }

    @Nested
    @DisplayName("根据ID查找文件测试")
    class FindFileByIdTests {

        @Test
        @DisplayName("根据ID查找文件 - 成功场景")
        void testFindFileById_Success() {
            // 准备测试数据
            Long fileId = createTestId();

            // Mock应用服务调用
            when(attachmentInfoAppService.findById(fileId)).thenReturn(attachmentInfoVO);

            // Mock静态方法
            try (MockedStatic<SecurityFrameworkUtils> mockedSecurity = 
                 MockitoTestUtils.mockStatic(SecurityFrameworkUtils.class)) {
                
                mockedSecurity.when(SecurityFrameworkUtils::getLoginUser).thenReturn(loginUser);

                // 执行测试
                CommonResult<AttachmentInfoVO> result = attachmentController.findFileById(fileId);

                // 验证结果
                assertNotNull(result);
                assertTrue(result.isSuccess());
                assertEquals(0, result.getCode());
                assertNotNull(result.getData());
                assertEquals(attachmentInfoVO, result.getData());

                // 验证方法调用
                verify(attachmentInfoAppService, times(1)).findById(fileId);
                mockedSecurity.verify(SecurityFrameworkUtils::getLoginUser, times(1));
            }
        }

        @Test
        @DisplayName("根据ID查找文件 - 文件不存在")
        void testFindFileById_NotFound() {
            // 准备测试数据
            Long fileId = createTestId();

            // Mock应用服务返回null
            when(attachmentInfoAppService.findById(fileId)).thenReturn(null);

            // Mock静态方法
            try (MockedStatic<SecurityFrameworkUtils> mockedSecurity = 
                 MockitoTestUtils.mockStatic(SecurityFrameworkUtils.class)) {
                
                mockedSecurity.when(SecurityFrameworkUtils::getLoginUser).thenReturn(loginUser);

                // 执行测试
                CommonResult<AttachmentInfoVO> result = attachmentController.findFileById(fileId);

                // 验证结果
                assertNotNull(result);
                assertTrue(result.isSuccess());
                assertNull(result.getData());

                // 验证方法调用
                verify(attachmentInfoAppService, times(1)).findById(fileId);
            }
        }

        @Test
        @DisplayName("根据ID查找文件 - 服务异常")
        void testFindFileById_ServiceException() {
            // 准备测试数据
            Long fileId = createTestId();

            // Mock应用服务抛出异常
            when(attachmentInfoAppService.findById(fileId))
                    .thenThrow(new RuntimeException("Service error"));

            // Mock静态方法
            try (MockedStatic<SecurityFrameworkUtils> mockedSecurity = 
                 MockitoTestUtils.mockStatic(SecurityFrameworkUtils.class)) {
                
                mockedSecurity.when(SecurityFrameworkUtils::getLoginUser).thenReturn(loginUser);

                // 执行测试并验证异常传播
                assertThrows(RuntimeException.class, () -> {
                    attachmentController.findFileById(fileId);
                });

                // 验证方法调用
                verify(attachmentInfoAppService, times(1)).findById(fileId);
            }
        }
    }

    @Nested
    @DisplayName("创建附件测试")
    class CreateAttachmentTests {

        @Test
        @DisplayName("创建附件 - 成功场景")
        void testCreateAttachment_Success() {
            // Mock应用服务调用
            when(attachmentInfoAppService.createAttachment(attachmentInfoDTO, loginUser))
                    .thenReturn(attachmentInfoVO);

            // Mock静态方法
            try (MockedStatic<SecurityFrameworkUtils> mockedSecurity = 
                 MockitoTestUtils.mockStatic(SecurityFrameworkUtils.class)) {
                
                mockedSecurity.when(SecurityFrameworkUtils::getLoginUser).thenReturn(loginUser);

                // 执行测试
                CommonResult<AttachmentInfoVO> result = 
                        attachmentController.createAttachment(attachmentInfoDTO);

                // 验证结果
                assertNotNull(result);
                assertTrue(result.isSuccess());
                assertEquals(0, result.getCode());
                assertNotNull(result.getData());
                assertEquals(attachmentInfoVO, result.getData());

                // 验证方法调用
                verify(attachmentInfoAppService, times(1))
                        .createAttachment(attachmentInfoDTO, loginUser);
                mockedSecurity.verify(SecurityFrameworkUtils::getLoginUser, times(1));
            }
        }

        @Test
        @DisplayName("创建附件 - 用户未登录")
        void testCreateAttachment_UserNotLoggedIn() {
            // Mock静态方法返回null
            try (MockedStatic<SecurityFrameworkUtils> mockedSecurity = 
                 MockitoTestUtils.mockStatic(SecurityFrameworkUtils.class)) {
                
                mockedSecurity.when(SecurityFrameworkUtils::getLoginUser).thenReturn(null);

                // Mock应用服务调用
                when(attachmentInfoAppService.createAttachment(attachmentInfoDTO, null))
                        .thenReturn(attachmentInfoVO);

                // 执行测试
                CommonResult<AttachmentInfoVO> result = 
                        attachmentController.createAttachment(attachmentInfoDTO);

                // 验证结果
                assertNotNull(result);
                assertTrue(result.isSuccess());

                // 验证方法调用
                verify(attachmentInfoAppService, times(1))
                        .createAttachment(attachmentInfoDTO, null);
            }
        }
    }

    @Nested
    @DisplayName("批量查找文件测试")
    class FindFileByIdsTests {

        @Test
        @DisplayName("批量查找文件 - 成功场景")
        void testFindFileByIds_Success() {
            // 准备测试数据
            List<Long> idList = Arrays.asList(1L, 2L, 3L);
            List<AttachmentInfoVO> expectedList = Arrays.asList(attachmentInfoVO);

            // Mock应用服务调用
            when(attachmentInfoAppService.findByIdList(idList)).thenReturn(expectedList);

            // 执行测试
            CommonResult<List<AttachmentInfoVO>> result = 
                    attachmentController.findFileByIds(idList);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertEquals(0, result.getCode());
            assertNotNull(result.getData());
            assertEquals(expectedList, result.getData());

            // 验证方法调用
            verify(attachmentInfoAppService, times(1)).findByIdList(idList);
        }

        @Test
        @DisplayName("批量查找文件 - 空列表")
        void testFindFileByIds_EmptyList() {
            // 准备测试数据
            List<Long> emptyIdList = Collections.emptyList();
            List<AttachmentInfoVO> emptyResultList = Collections.emptyList();

            // Mock应用服务调用
            when(attachmentInfoAppService.findByIdList(emptyIdList)).thenReturn(emptyResultList);

            // 执行测试
            CommonResult<List<AttachmentInfoVO>> result = 
                    attachmentController.findFileByIds(emptyIdList);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertNotNull(result.getData());
            assertTrue(result.getData().isEmpty());

            // 验证方法调用
            verify(attachmentInfoAppService, times(1)).findByIdList(emptyIdList);
        }
    }

    @Nested
    @DisplayName("根据业务类型查找文件测试")
    class FindFileByBusinessTypeTests {

        @Test
        @DisplayName("根据业务类型查找文件 - 成功场景")
        void testFindFileByBusinessType_Success() {
            // 准备测试数据
            String businessType = "CONTRACT";
            CommonResult<AttachmentInfoVO> expectedResult = CommonResult.success(attachmentInfoVO);

            // Mock应用服务调用
            when(attachmentInfoAppService.findFileByBusinessType(businessType))
                    .thenReturn(expectedResult);

            // 执行测试
            CommonResult<AttachmentInfoVO> result = 
                    attachmentController.findFileByBusinessType(businessType);

            // 验证结果
            assertNotNull(result);
            assertEquals(expectedResult, result);

            // 验证方法调用
            verify(attachmentInfoAppService, times(1)).findFileByBusinessType(businessType);
        }

        @Test
        @DisplayName("根据业务类型查找文件 - 业务类型不存在")
        void testFindFileByBusinessType_TypeNotFound() {
            // 准备测试数据
            String businessType = "NON_EXISTENT";
            CommonResult<AttachmentInfoVO> expectedResult = CommonResult.success(null);

            // Mock应用服务调用
            when(attachmentInfoAppService.findFileByBusinessType(businessType))
                    .thenReturn(expectedResult);

            // 执行测试
            CommonResult<AttachmentInfoVO> result = 
                    attachmentController.findFileByBusinessType(businessType);

            // 验证结果
            assertNotNull(result);
            assertEquals(expectedResult, result);

            // 验证方法调用
            verify(attachmentInfoAppService, times(1)).findFileByBusinessType(businessType);
        }
    }

    @Test
    @DisplayName("验证控制器依赖注入")
    void testDependencyInjection() {
        // 验证控制器实例不为空
        assertNotNull(attachmentController);
        
        // 验证Mock对象已正确创建
        assertTrue(MockitoTestUtils.isMockitoMock(attachmentInfoAppService));
    }

    @Test
    @DisplayName("验证控制器注解")
    void testControllerAnnotations() {
        // 验证类上的注解
        assertTrue(AttachmentController.class.isAnnotationPresent(
                org.springframework.web.bind.annotation.RestController.class));
        assertTrue(AttachmentController.class.isAnnotationPresent(
                org.springframework.validation.annotation.Validated.class));
        assertTrue(AttachmentController.class.isAnnotationPresent(
                lombok.extern.slf4j.Slf4j.class));
    }
}
