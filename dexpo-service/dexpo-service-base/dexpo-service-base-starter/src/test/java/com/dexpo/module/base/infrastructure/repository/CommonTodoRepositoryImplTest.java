package com.dexpo.module.base.infrastructure.repository;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dexpo.framework.common.pojo.PageParam;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.base.domain.model.agg.CommonTodo;
import com.dexpo.module.base.infrastructure.converter.CommonTodoConvert;
import com.dexpo.module.base.infrastructure.tunnel.database.CommonTodoMapper;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.CommonTodoDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CommonTodoRepositoryImplTest {
    @Mock
    private CommonTodoMapper commonTodoMapper;
    @InjectMocks
    private CommonTodoRepositoryImpl repository;

    @BeforeEach
    void setUp() {
        CommonTodoConvert mockConvert = mock(CommonTodoConvert.class, Mockito.RETURNS_DEEP_STUBS);
        setFinalStatic(CommonTodoConvert.class, "INSTANCE", mockConvert);
    }

    private static void setFinalStatic(Class<?> clazz, String fieldName, Object newValue) {
        try {
            java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            java.lang.reflect.Field modifiersField = java.lang.reflect.Field.class.getDeclaredField("modifiers");
            modifiersField.setAccessible(true);
            modifiersField.setInt(field, field.getModifiers() & ~java.lang.reflect.Modifier.FINAL);
            field.set(null, newValue);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void getPage_shouldReturnPageResult() {
        CommonTodo todo = new CommonTodo();
        todo.setStatus("TODO");
        PageParam param = new PageParam();
        param.setPageNo(1);
        param.setPageSize(10);
        IPage<CommonTodoDO> page = new Page<>(1, 10);
        IPage<CommonTodoDO> pageResult = mock(IPage.class);
        when(commonTodoMapper.selectPage(any(), any())).thenReturn(pageResult);
        when(pageResult.getRecords()).thenReturn(List.of(new CommonTodoDO()));
        when(page.getTotal()).thenReturn(1L);
        when(CommonTodoConvert.INSTANCE.e2dList(anyList())).thenReturn(List.of(new CommonTodo()));
        PageResult<CommonTodo> result = repository.getPage(todo, param);
        assertNotNull(result);
        assertEquals(1, result.getTotal());
    }

    @Test
    void save_shouldReturnCommonTodo() {
        CommonTodo todo = new CommonTodo();
        CommonTodoDO todoDO = new CommonTodoDO();
        todoDO.setId(123L);
        try (var beanUtilMocked = Mockito.mockStatic(BeanUtil.class)) {
            beanUtilMocked.when(() -> BeanUtil.copyProperties(any(), eq(CommonTodoDO.class))).thenReturn(todoDO);
            doNothing().when(commonTodoMapper).insert(todoDO);
            CommonTodo result = repository.save(todo);
            assertNotNull(result);
            assertEquals(123L, result.getId());
        }
    }

    @Test
    void selectOneByCommonTodo_shouldReturnOne() {
        CommonTodo todo = new CommonTodo();
        CommonTodoDO todoDO = new CommonTodoDO();
        when(commonTodoMapper.selectOne(any(), eq(false))).thenReturn(todoDO);
        CommonTodo agg = new CommonTodo();
        when(CommonTodoConvert.INSTANCE.e2d(todoDO)).thenReturn(agg);
        CommonTodo result = repository.selectOneByCommonTodo(todo);
        assertNotNull(result);
        assertSame(agg, result);
    }

    @Test
    void updateById_shouldCallMapper() {
        CommonTodo todo = new CommonTodo();
        CommonTodoDO todoDO = new CommonTodoDO();
        try (var beanUtilMocked = Mockito.mockStatic(BeanUtil.class)) {
            beanUtilMocked.when(() -> BeanUtil.copyProperties(any(), eq(CommonTodoDO.class))).thenReturn(todoDO);
            doNothing().when(commonTodoMapper).updateById(todoDO);
            repository.updateById(todo);
            verify(commonTodoMapper, times(1)).updateById(todoDO);
        }
    }
}
