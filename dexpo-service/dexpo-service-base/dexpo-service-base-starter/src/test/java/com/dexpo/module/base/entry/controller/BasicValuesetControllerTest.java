package com.dexpo.module.base.entry.controller;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.base.api.basic.dto.BasicValuesetOptionDTO;
import com.dexpo.module.base.api.basic.dto.ExhibitionTagValuesetDTO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetOptionVO;
import com.dexpo.module.base.app.api.BasicValueSetAppService;
import com.dexpo.module.base.app.api.BasicValueSetOptionAppService;
import com.dexpo.module.base.app.api.ExhibitionOptionRelationAppService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BasicValuesetControllerTest {
    @Mock
    private BasicValueSetAppService basicValueSetAppService;
    @Mock
    private ExhibitionOptionRelationAppService exposureOptionRelationAppService;
    @Mock
    private BasicValueSetOptionAppService basicValuesetOptionAppService;
    @InjectMocks
    private BasicValuesetController controller;

    @Test
    void getValuesetListByCodes_shouldReturnSuccess() {
        List<String> codes = List.of("code1");
        BasicValuesetInfoVO vo = new BasicValuesetInfoVO();
        when(basicValueSetAppService.getValuesetListByCodes(codes)).thenReturn(List.of(vo));
        CommonResult<List<BasicValuesetInfoVO>> result = controller.getValuesetListByCodes(codes);
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(1, result.getData().size());
        assertSame(vo, result.getData().get(0));
    }

    @Test
    void getExhibitionValuesetListByCodes_shouldReturnSuccess() {
        ExhibitionTagValuesetDTO dto = new ExhibitionTagValuesetDTO();
        BasicValuesetInfoVO vo = new BasicValuesetInfoVO();
        when(exposureOptionRelationAppService.getExhibitionValuesetListByCodes(any())).thenReturn(List.of(vo));
        CommonResult<List<BasicValuesetInfoVO>> result = controller.getExhibitionValuesetListByCodes(dto);
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(1, result.getData().size());
        assertSame(vo, result.getData().get(0));
    }

    @Test
    void getValuesetOption_shouldReturnSuccess() {
        BasicValuesetOptionDTO dto = new BasicValuesetOptionDTO();
        BasicValuesetOptionVO vo = new BasicValuesetOptionVO();
        when(basicValuesetOptionAppService.getValuesetOption(any())).thenReturn(vo);
        CommonResult<BasicValuesetOptionVO> result = controller.getValuesetOption(dto);
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertSame(vo, result.getData());
    }

    @Test
    void getOptionListByCodes_shouldReturnSuccess() {
        List<String> codes = List.of("opt1");
        BasicValuesetOptionVO vo = new BasicValuesetOptionVO();
        when(basicValuesetOptionAppService.getOptionListByCodes(codes)).thenReturn(List.of(vo));
        CommonResult<List<BasicValuesetOptionVO>> result = controller.getOptionListByCodes(codes);
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(1, result.getData().size());
        assertSame(vo, result.getData().get(0));
    }
} 