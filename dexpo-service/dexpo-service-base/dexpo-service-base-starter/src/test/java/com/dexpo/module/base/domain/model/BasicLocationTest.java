package com.dexpo.module.base.domain.model;

import com.dexpo.module.base.BaseUnitTest;
import com.dexpo.module.base.domain.model.agg.BasicLocation;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link BasicLocation} 的单元测试类
 * 
 * <p>测试业务地域领域模型的所有功能，确保100%代码覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class BasicLocationTest extends BaseUnitTest {

    private BasicLocation basicLocation;

    @BeforeEach
    void setUp() {
        basicLocation = new BasicLocation();
    }

    /**
     * 测试默认构造函数
     */
    @Test
    void testDefaultConstructor() {
        BasicLocation location = new BasicLocation();
        
        assertNotNull(location);
        assertNull(location.getId());
        assertNull(location.getLocationCode());
        assertNull(location.getLocationNameCn());
        assertNull(location.getLocationNameEn());
        assertNull(location.getLocationTag());
    }

    /**
     * 测试ID字段的getter和setter
     */
    @Test
    void testIdGetterSetter() {
        assertNull(basicLocation.getId());
        
        basicLocation.setId(1L);
        assertEquals(1L, basicLocation.getId());
        
        basicLocation.setId(null);
        assertNull(basicLocation.getId());
    }

    /**
     * 测试locationCode字段的getter和setter
     */
    @Test
    void testLocationCodeGetterSetter() {
        assertNull(basicLocation.getLocationCode());
        
        basicLocation.setLocationCode("CN-BJ");
        assertEquals("CN-BJ", basicLocation.getLocationCode());
        
        basicLocation.setLocationCode("");
        assertEquals("", basicLocation.getLocationCode());
        
        basicLocation.setLocationCode(null);
        assertNull(basicLocation.getLocationCode());
    }

    /**
     * 测试locationNameCn字段的getter和setter
     */
    @Test
    void testLocationNameCnGetterSetter() {
        assertNull(basicLocation.getLocationNameCn());
        
        basicLocation.setLocationNameCn("北京");
        assertEquals("北京", basicLocation.getLocationNameCn());
        
        basicLocation.setLocationNameCn("");
        assertEquals("", basicLocation.getLocationNameCn());
        
        basicLocation.setLocationNameCn(null);
        assertNull(basicLocation.getLocationNameCn());
    }

    /**
     * 测试locationNameEn字段的getter和setter
     */
    @Test
    void testLocationNameEnGetterSetter() {
        assertNull(basicLocation.getLocationNameEn());
        
        basicLocation.setLocationNameEn("Beijing");
        assertEquals("Beijing", basicLocation.getLocationNameEn());
        
        basicLocation.setLocationNameEn("");
        assertEquals("", basicLocation.getLocationNameEn());
        
        basicLocation.setLocationNameEn(null);
        assertNull(basicLocation.getLocationNameEn());
    }

    /**
     * 测试locationTag字段的getter和setter
     */
    @Test
    void testLocationTagGetterSetter() {
        assertNull(basicLocation.getLocationTag());
        
        basicLocation.setLocationTag("ENTERPRISE_TYPE");
        assertEquals("ENTERPRISE_TYPE", basicLocation.getLocationTag());
        
        basicLocation.setLocationTag("");
        assertEquals("", basicLocation.getLocationTag());
        
        basicLocation.setLocationTag(null);
        assertNull(basicLocation.getLocationTag());
    }

    /**
     * 测试isValid方法 - 有效场景
     */
    @Test
    void testIsValid_ValidLocation() {
        // 设置有效的地域信息
        basicLocation.setLocationCode("CN-BJ");
        basicLocation.setLocationTag("ENTERPRISE_TYPE");
        basicLocation.setLocationNameCn("北京");
        
        assertTrue(basicLocation.isValid(), "完整的地域信息应该是有效的");
    }

    /**
     * 测试isValid方法 - 只有英文名称
     */
    @Test
    void testIsValid_OnlyEnglishName() {
        basicLocation.setLocationCode("CN-BJ");
        basicLocation.setLocationTag("ENTERPRISE_TYPE");
        basicLocation.setLocationNameEn("Beijing");
        
        assertTrue(basicLocation.isValid(), "只有英文名称的地域信息应该是有效的");
    }

    /**
     * 测试isValid方法 - 中英文名称都有
     */
    @Test
    void testIsValid_BothNames() {
        basicLocation.setLocationCode("CN-BJ");
        basicLocation.setLocationTag("ENTERPRISE_TYPE");
        basicLocation.setLocationNameCn("北京");
        basicLocation.setLocationNameEn("Beijing");
        
        assertTrue(basicLocation.isValid(), "中英文名称都有的地域信息应该是有效的");
    }

    /**
     * 测试isValid方法 - 缺少locationCode
     */
    @Test
    void testIsValid_MissingLocationCode() {
        basicLocation.setLocationTag("ENTERPRISE_TYPE");
        basicLocation.setLocationNameCn("北京");
        
        assertFalse(basicLocation.isValid(), "缺少地域编码的信息应该是无效的");
    }

    /**
     * 测试isValid方法 - locationCode为空字符串
     */
    @Test
    void testIsValid_EmptyLocationCode() {
        basicLocation.setLocationCode("");
        basicLocation.setLocationTag("ENTERPRISE_TYPE");
        basicLocation.setLocationNameCn("北京");
        
        assertFalse(basicLocation.isValid(), "地域编码为空字符串的信息应该是无效的");
    }

    /**
     * 测试isValid方法 - locationCode为空白字符串
     */
    @Test
    void testIsValid_BlankLocationCode() {
        basicLocation.setLocationCode("   ");
        basicLocation.setLocationTag("ENTERPRISE_TYPE");
        basicLocation.setLocationNameCn("北京");
        
        assertFalse(basicLocation.isValid(), "地域编码为空白字符串的信息应该是无效的");
    }

    /**
     * 测试isValid方法 - 缺少locationTag
     */
    @Test
    void testIsValid_MissingLocationTag() {
        basicLocation.setLocationCode("CN-BJ");
        basicLocation.setLocationNameCn("北京");
        
        assertFalse(basicLocation.isValid(), "缺少地域标签的信息应该是无效的");
    }

    /**
     * 测试isValid方法 - locationTag为空字符串
     */
    @Test
    void testIsValid_EmptyLocationTag() {
        basicLocation.setLocationCode("CN-BJ");
        basicLocation.setLocationTag("");
        basicLocation.setLocationNameCn("北京");
        
        assertFalse(basicLocation.isValid(), "地域标签为空字符串的信息应该是无效的");
    }

    /**
     * 测试isValid方法 - locationTag为空白字符串
     */
    @Test
    void testIsValid_BlankLocationTag() {
        basicLocation.setLocationCode("CN-BJ");
        basicLocation.setLocationTag("   ");
        basicLocation.setLocationNameCn("北京");
        
        assertFalse(basicLocation.isValid(), "地域标签为空白字符串的信息应该是无效的");
    }

    /**
     * 测试isValid方法 - 缺少名称
     */
    @Test
    void testIsValid_MissingNames() {
        basicLocation.setLocationCode("CN-BJ");
        basicLocation.setLocationTag("ENTERPRISE_TYPE");
        
        assertFalse(basicLocation.isValid(), "缺少中英文名称的信息应该是无效的");
    }

    /**
     * 测试isValid方法 - 名称为空字符串
     */
    @Test
    void testIsValid_EmptyNames() {
        basicLocation.setLocationCode("CN-BJ");
        basicLocation.setLocationTag("ENTERPRISE_TYPE");
        basicLocation.setLocationNameCn("");
        basicLocation.setLocationNameEn("");
        
        assertFalse(basicLocation.isValid(), "中英文名称都为空字符串的信息应该是无效的");
    }

    /**
     * 测试getDisplayName方法 - 有中文名称
     */
    @Test
    void testGetDisplayName_WithChineseName() {
        basicLocation.setLocationNameCn("北京");
        basicLocation.setLocationNameEn("Beijing");
        
        assertEquals("北京", basicLocation.getDisplayName(), 
                     "有中文名称时应该返回中文名称");
    }

    /**
     * 测试getDisplayName方法 - 只有英文名称
     */
    @Test
    void testGetDisplayName_OnlyEnglishName() {
        basicLocation.setLocationNameEn("Beijing");
        
        assertEquals("Beijing", basicLocation.getDisplayName(), 
                     "只有英文名称时应该返回英文名称");
    }

    /**
     * 测试getDisplayName方法 - 中文名称为空字符串
     */
    @Test
    void testGetDisplayName_EmptyChineseName() {
        basicLocation.setLocationNameCn("");
        basicLocation.setLocationNameEn("Beijing");
        
        assertEquals("Beijing", basicLocation.getDisplayName(), 
                     "中文名称为空字符串时应该返回英文名称");
    }

    /**
     * 测试getDisplayName方法 - 中文名称为空白字符串
     */
    @Test
    void testGetDisplayName_BlankChineseName() {
        basicLocation.setLocationNameCn("   ");
        basicLocation.setLocationNameEn("Beijing");
        
        assertEquals("Beijing", basicLocation.getDisplayName(), 
                     "中文名称为空白字符串时应该返回英文名称");
    }

    /**
     * 测试getDisplayName方法 - 都为空
     */
    @Test
    void testGetDisplayName_BothEmpty() {
        assertEquals("", basicLocation.getDisplayName(), 
                     "中英文名称都为空时应该返回空字符串");
    }

    /**
     * 测试getDisplayName方法 - 英文名称为null
     */
    @Test
    void testGetDisplayName_EnglishNameNull() {
        basicLocation.setLocationNameCn(null);
        basicLocation.setLocationNameEn(null);
        
        assertEquals("", basicLocation.getDisplayName(), 
                     "英文名称为null时应该返回空字符串");
    }

    /**
     * 测试equals方法
     */
    @Test
    void testEquals() {
        BasicLocation location1 = new BasicLocation();
        location1.setId(1L);
        location1.setLocationCode("CN-BJ");
        location1.setLocationNameCn("北京");
        location1.setLocationNameEn("Beijing");
        location1.setLocationTag("ENTERPRISE_TYPE");
        
        BasicLocation location2 = new BasicLocation();
        location2.setId(1L);
        location2.setLocationCode("CN-BJ");
        location2.setLocationNameCn("北京");
        location2.setLocationNameEn("Beijing");
        location2.setLocationTag("ENTERPRISE_TYPE");
        
        BasicLocation location3 = new BasicLocation();
        location3.setId(2L);
        location3.setLocationCode("CN-SH");
        location3.setLocationNameCn("上海");
        location3.setLocationNameEn("Shanghai");
        location3.setLocationTag("ENTERPRISE_TYPE");
        
        // 测试相等性
        assertEquals(location1, location2);
        assertEquals(location1, location1); // 自反性
        
        // 测试不相等性
        assertNotEquals(location1, location3);
        assertNotEquals(location1, null);
        assertNotEquals(location1, "string");
    }

    /**
     * 测试hashCode方法
     */
    @Test
    void testHashCode() {
        BasicLocation location1 = new BasicLocation();
        location1.setId(1L);
        location1.setLocationCode("CN-BJ");
        location1.setLocationNameCn("北京");
        location1.setLocationNameEn("Beijing");
        location1.setLocationTag("ENTERPRISE_TYPE");
        
        BasicLocation location2 = new BasicLocation();
        location2.setId(1L);
        location2.setLocationCode("CN-BJ");
        location2.setLocationNameCn("北京");
        location2.setLocationNameEn("Beijing");
        location2.setLocationTag("ENTERPRISE_TYPE");
        
        // 相等的对象应该有相同的hashCode
        assertEquals(location1.hashCode(), location2.hashCode());
        
        // 测试hashCode的一致性
        int hashCode1 = location1.hashCode();
        int hashCode2 = location1.hashCode();
        assertEquals(hashCode1, hashCode2);
    }

    /**
     * 测试toString方法
     */
    @Test
    void testToString() {
        basicLocation.setId(1L);
        basicLocation.setLocationCode("CN-BJ");
        basicLocation.setLocationNameCn("北京");
        basicLocation.setLocationNameEn("Beijing");
        basicLocation.setLocationTag("ENTERPRISE_TYPE");
        
        String toString = basicLocation.toString();
        
        assertNotNull(toString);
        assertTrue(toString.contains("BasicLocation"));
        assertTrue(toString.contains("id=1"));
        assertTrue(toString.contains("locationCode=CN-BJ"));
        assertTrue(toString.contains("locationNameCn=北京"));
        assertTrue(toString.contains("locationNameEn=Beijing"));
        assertTrue(toString.contains("locationTag=ENTERPRISE_TYPE"));
    }

    /**
     * 测试类的注解
     */
    @Test
    void testClassAnnotations() {
        // 验证类上的注解
        assertTrue(BasicLocation.class.isAnnotationPresent(lombok.Data.class));
    }
}
