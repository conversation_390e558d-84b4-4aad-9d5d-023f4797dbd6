package com.dexpo.module.base.controller;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.base.BaseUnitTest;
import com.dexpo.module.base.api.basic.dto.BasicExhibitionLocationDTO;
import com.dexpo.module.base.api.basic.dto.BasicLocationDTO;
import com.dexpo.module.base.api.basic.vo.BasicLocationVO;
import com.dexpo.module.base.controller.basic.BasicLocationController;
import com.dexpo.module.base.service.basic.BasicLocationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * {@link BasicLocationController} 的单元测试类
 * 
 * <p>测试业务地域控制器的所有功能，确保100%代码覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class BasicLocationControllerTest extends BaseUnitTest {

    @Mock
    private BasicLocationService basicLocationService;

    @InjectMocks
    private BasicLocationController basicLocationController;

    private BasicLocationDTO locationDTO;
    private BasicExhibitionLocationDTO exhibitionLocationDTO;
    private BasicLocationVO locationVO;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        locationDTO = new BasicLocationDTO();
        locationDTO.setLocationTag("ENTERPRISE_TYPE");

        exhibitionLocationDTO = new BasicExhibitionLocationDTO();
        exhibitionLocationDTO.setExhibitionTagCode("TECH_EXPO_2024");

        locationVO = new BasicLocationVO();
        locationVO.setId(1L);
        locationVO.setLocationCode("CN-BJ");
        locationVO.setLocationNameCn("北京");
        locationVO.setLocationNameEn("Beijing");
        locationVO.setLocationTag("ENTERPRISE_TYPE");
    }

    /**
     * 测试根据地域标签获取地域列表 - 成功场景
     */
    @Test
    void testGetLocationList_Success() {
        // 准备测试数据
        List<BasicLocationVO> expectedList = Arrays.asList(locationVO);

        // Mock服务调用
        when(basicLocationService.getLocationList(any(BasicLocationDTO.class)))
                .thenReturn(expectedList);

        // 执行测试
        CommonResult<List<BasicLocationVO>> result = basicLocationController.getLocationList(locationDTO);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode()); // 成功状态码
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());
        assertEquals(expectedList, result.getData());

        // 验证服务方法调用
        verify(basicLocationService, times(1)).getLocationList(locationDTO);
    }

    /**
     * 测试根据地域标签获取地域列表 - 空结果
     */
    @Test
    void testGetLocationList_EmptyResult() {
        // 准备测试数据
        List<BasicLocationVO> emptyList = Collections.emptyList();

        // Mock服务调用
        when(basicLocationService.getLocationList(any(BasicLocationDTO.class)))
                .thenReturn(emptyList);

        // 执行测试
        CommonResult<List<BasicLocationVO>> result = basicLocationController.getLocationList(locationDTO);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());

        // 验证服务方法调用
        verify(basicLocationService, times(1)).getLocationList(locationDTO);
    }

    /**
     * 测试根据地域标签获取地域列表 - 多个结果
     */
    @Test
    void testGetLocationList_MultipleResults() {
        // 准备测试数据
        BasicLocationVO locationVO2 = new BasicLocationVO();
        locationVO2.setId(2L);
        locationVO2.setLocationCode("CN-SH");
        locationVO2.setLocationNameCn("上海");
        locationVO2.setLocationNameEn("Shanghai");
        locationVO2.setLocationTag("ENTERPRISE_TYPE");

        List<BasicLocationVO> expectedList = Arrays.asList(locationVO, locationVO2);

        // Mock服务调用
        when(basicLocationService.getLocationList(any(BasicLocationDTO.class)))
                .thenReturn(expectedList);

        // 执行测试
        CommonResult<List<BasicLocationVO>> result = basicLocationController.getLocationList(locationDTO);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertEquals(2, result.getData().size());
        assertEquals(expectedList, result.getData());

        // 验证服务方法调用
        verify(basicLocationService, times(1)).getLocationList(locationDTO);
    }

    /**
     * 测试根据展会标签获取地域列表 - 成功场景
     */
    @Test
    void testGetExhibitionLocationList_Success() {
        // 准备测试数据
        List<BasicLocationVO> expectedList = Arrays.asList(locationVO);

        // Mock服务调用
        when(basicLocationService.getExhibitionLocationList(any(BasicExhibitionLocationDTO.class)))
                .thenReturn(expectedList);

        // 执行测试
        CommonResult<List<BasicLocationVO>> result = 
                basicLocationController.getExhibitionLocationList(exhibitionLocationDTO);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());
        assertEquals(expectedList, result.getData());

        // 验证服务方法调用
        verify(basicLocationService, times(1)).getExhibitionLocationList(exhibitionLocationDTO);
    }

    /**
     * 测试根据展会标签获取地域列表 - 空结果
     */
    @Test
    void testGetExhibitionLocationList_EmptyResult() {
        // 准备测试数据
        List<BasicLocationVO> emptyList = Collections.emptyList();

        // Mock服务调用
        when(basicLocationService.getExhibitionLocationList(any(BasicExhibitionLocationDTO.class)))
                .thenReturn(emptyList);

        // 执行测试
        CommonResult<List<BasicLocationVO>> result = 
                basicLocationController.getExhibitionLocationList(exhibitionLocationDTO);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());

        // 验证服务方法调用
        verify(basicLocationService, times(1)).getExhibitionLocationList(exhibitionLocationDTO);
    }

    /**
     * 测试服务异常处理
     */
    @Test
    void testGetLocationList_ServiceException() {
        // Mock服务抛出异常
        when(basicLocationService.getLocationList(any(BasicLocationDTO.class)))
                .thenThrow(new RuntimeException("Service error"));

        // 执行测试并验证异常传播
        assertThrows(RuntimeException.class, () -> {
            basicLocationController.getLocationList(locationDTO);
        });

        // 验证服务方法调用
        verify(basicLocationService, times(1)).getLocationList(locationDTO);
    }

    /**
     * 测试展会地域查询的服务异常处理
     */
    @Test
    void testGetExhibitionLocationList_ServiceException() {
        // Mock服务抛出异常
        when(basicLocationService.getExhibitionLocationList(any(BasicExhibitionLocationDTO.class)))
                .thenThrow(new RuntimeException("Service error"));

        // 执行测试并验证异常传播
        assertThrows(RuntimeException.class, () -> {
            basicLocationController.getExhibitionLocationList(exhibitionLocationDTO);
        });

        // 验证服务方法调用
        verify(basicLocationService, times(1)).getExhibitionLocationList(exhibitionLocationDTO);
    }

    /**
     * 测试控制器的依赖注入
     */
    @Test
    void testDependencyInjection() {
        // 验证控制器实例不为空
        assertNotNull(basicLocationController);
        
        // 验证服务依赖已正确注入（通过反射或间接验证）
        // 这里通过调用方法来间接验证依赖注入是否正确
        when(basicLocationService.getLocationList(any(BasicLocationDTO.class)))
                .thenReturn(Collections.emptyList());
        
        assertDoesNotThrow(() -> {
            basicLocationController.getLocationList(locationDTO);
        });
    }

    /**
     * 测试CommonResult的成功响应格式
     */
    @Test
    void testCommonResultSuccessFormat() {
        // 准备测试数据
        List<BasicLocationVO> expectedList = Arrays.asList(locationVO);

        // Mock服务调用
        when(basicLocationService.getLocationList(any(BasicLocationDTO.class)))
                .thenReturn(expectedList);

        // 执行测试
        CommonResult<List<BasicLocationVO>> result = basicLocationController.getLocationList(locationDTO);

        // 验证CommonResult的格式
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertNull(result.getMsg()); // 成功时消息通常为空
    }

    /**
     * 测试参数验证注解的存在性
     */
    @Test
    void testValidationAnnotations() {
        // 验证控制器类上的注解
        assertTrue(BasicLocationController.class.isAnnotationPresent(
                org.springframework.web.bind.annotation.RestController.class));
        assertTrue(BasicLocationController.class.isAnnotationPresent(
                org.springframework.validation.annotation.Validated.class));

        // 验证方法参数上的验证注解
        try {
            var method = BasicLocationController.class.getMethod("getLocationList", BasicLocationDTO.class);
            var parameters = method.getParameters();
            assertTrue(parameters[0].isAnnotationPresent(
                    jakarta.validation.Valid.class));
            assertTrue(parameters[0].isAnnotationPresent(
                    org.springframework.web.bind.annotation.RequestBody.class));
        } catch (NoSuchMethodException e) {
            fail("getLocationList方法应该存在");
        }
    }

    /**
     * 测试API接口实现
     */
    @Test
    void testApiInterfaceImplementation() {
        // 验证控制器实现了正确的API接口
        assertTrue(com.dexpo.module.base.api.basic.BasicLocationApi.class
                .isAssignableFrom(BasicLocationController.class));
    }

    /**
     * 测试方法返回值类型
     */
    @Test
    void testMethodReturnTypes() {
        // 验证方法返回正确的类型
        try {
            var getLocationListMethod = BasicLocationController.class
                    .getMethod("getLocationList", BasicLocationDTO.class);
            assertEquals(CommonResult.class, getLocationListMethod.getReturnType());

            var getExhibitionLocationListMethod = BasicLocationController.class
                    .getMethod("getExhibitionLocationList", BasicExhibitionLocationDTO.class);
            assertEquals(CommonResult.class, getExhibitionLocationListMethod.getReturnType());
        } catch (NoSuchMethodException e) {
            fail("方法应该存在");
        }
    }
}
