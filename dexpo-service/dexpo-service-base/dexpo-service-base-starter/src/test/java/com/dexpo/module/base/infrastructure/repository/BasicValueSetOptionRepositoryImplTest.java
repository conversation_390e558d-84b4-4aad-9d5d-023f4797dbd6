package com.dexpo.module.base.infrastructure.repository;

import com.dexpo.module.base.domain.model.agg.BasicValueSetOption;
import com.dexpo.module.base.infrastructure.converter.BasicValueSetOptionConvert;
import com.dexpo.module.base.infrastructure.tunnel.database.BasicValuesetOptionMapper;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.BasicValuesetOptionDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BasicValueSetOptionRepositoryImplTest {
    @Mock
    private BasicValuesetOptionMapper basicValuesetOptionMapper;
    @InjectMocks
    private BasicValueSetOptionRepositoryImpl repository;

    @BeforeEach
    void setUp() {
        BasicValueSetOptionConvert mockConvert = mock(BasicValueSetOptionConvert.class, Mockito.RETURNS_DEEP_STUBS);
        setFinalStatic(BasicValueSetOptionConvert.class, "INSTANCE", mockConvert);
    }

    private static void setFinalStatic(Class<?> clazz, String fieldName, Object newValue) {
        try {
            java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            java.lang.reflect.Field modifiersField = java.lang.reflect.Field.class.getDeclaredField("modifiers");
            modifiersField.setAccessible(true);
            modifiersField.setInt(field, field.getModifiers() & ~java.lang.reflect.Modifier.FINAL);
            field.set(null, newValue);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void list_shouldReturnList() {
        BasicValuesetOptionDO optionDO = new BasicValuesetOptionDO();
        when(basicValuesetOptionMapper.selectList()).thenReturn(List.of(optionDO));
        BasicValueSetOption option = new BasicValueSetOption();
        when(BasicValueSetOptionConvert.INSTANCE.e2dList(anyList())).thenReturn(List.of(option));
        List<BasicValueSetOption> result = repository.list();
        assertNotNull(result);
        assertEquals(1, result.size());
        assertSame(option, result.get(0));
    }

    @Test
    void selectByOptionCode_shouldReturnOption() {
        BasicValuesetOptionDO optionDO = new BasicValuesetOptionDO();
        when(basicValuesetOptionMapper.selectOne(any(), eq(false))).thenReturn(optionDO);
        BasicValueSetOption option = new BasicValueSetOption();
        when(BasicValueSetOptionConvert.INSTANCE.e2d(optionDO)).thenReturn(option);
        BasicValueSetOption result = repository.selectByOptionCode("code1");
        assertNotNull(result);
        assertSame(option, result);
    }
} 