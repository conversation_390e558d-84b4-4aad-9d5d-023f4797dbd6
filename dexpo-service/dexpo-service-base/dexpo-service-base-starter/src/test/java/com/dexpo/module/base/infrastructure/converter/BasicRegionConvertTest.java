package com.dexpo.module.base.infrastructure.converter;

import com.dexpo.module.base.domain.model.agg.BasicRegion;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.BasicRegionDO;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class BasicRegionConvertTest {
    @Test
    void d2e_shouldMapFields() {
        BasicRegion region = new BasicRegion();
        region.setId(1L);
        region.setAdcode("110000");
        BasicRegionDO regionDO = BasicRegionConvert.INSTANCE.d2e(region);
        assertNotNull(regionDO);
        assertEquals(region.getId(), regionDO.getId());
        assertEquals(region.getAdcode(), regionDO.getAdcode());
    }

    @Test
    void e2d_shouldMapFields() {
        BasicRegionDO regionDO = new BasicRegionDO();
        regionDO.setId(2L);
        regionDO.setAdcode("120000");
        BasicRegion region = BasicRegionConvert.INSTANCE.e2d(regionDO);
        assertNotNull(region);
        assertEquals(regionDO.getId(), region.getId());
        assertEquals(regionDO.getAdcode(), region.getAdcode());
    }

    @Test
    void e2dList_shouldMapList() {
        BasicRegionDO regionDO = new BasicRegionDO();
        regionDO.setId(3L);
        regionDO.setAdcode("130000");
        List<BasicRegion> list = BasicRegionConvert.INSTANCE.e2dList(List.of(regionDO));
        assertNotNull(list);
        assertEquals(1, list.size());
        assertEquals(regionDO.getId(), list.get(0).getId());
        assertEquals(regionDO.getAdcode(), list.get(0).getAdcode());
    }
} 