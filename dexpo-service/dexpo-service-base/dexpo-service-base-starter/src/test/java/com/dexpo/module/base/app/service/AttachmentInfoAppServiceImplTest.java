package com.dexpo.module.base.app.service;

import com.dexpo.framework.common.exception.enums.BaseServiceErrorCodeEnum;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.security.core.LoginUser;
import com.dexpo.module.base.api.attachment.dto.AttachmentInfoDTO;
import com.dexpo.module.base.api.attachment.vo.AttachmentInfoVO;
import com.dexpo.module.base.app.converter.AttachmentInfoDTOConvert;
import com.dexpo.module.base.domain.model.agg.AttachmentInfo;
import com.dexpo.module.base.domain.service.AttachmentInfoDomainService;
import com.dexpo.module.integration.api.flie.FileApi;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AttachmentInfoAppServiceImplTest {
    @Mock
    private AttachmentInfoDomainService attachmentInfoDomainService;
    @Mock
    private FileApi fileApi;

    @InjectMocks
    private AttachmentInfoAppServiceImpl service;

    @BeforeEach
    void setUp() throws Exception {
        AttachmentInfoDTOConvert mockConvert = mock(AttachmentInfoDTOConvert.class, Mockito.RETURNS_DEEP_STUBS);
        setFinalStatic(AttachmentInfoDTOConvert.class, "INSTANCE", mockConvert);
    }

    private static void setFinalStatic(Class<?> clazz, String fieldName, Object newValue) throws Exception {

    }

    @Test
    void findById_shouldReturnVO() {
        AttachmentInfo info = new AttachmentInfo();
        when(attachmentInfoDomainService.findById(anyLong())).thenReturn(info);
        AttachmentInfoVO result = service.findById(1L);
        assertNotNull(result);
    }

    @Test
    void createAttachment_shouldReturnVO() {
        AttachmentInfoDTO dto = new AttachmentInfoDTO();
        LoginUser user = new LoginUser();
        AttachmentInfo resultInfo = new AttachmentInfo();
        when(attachmentInfoDomainService.createAttachment(any(AttachmentInfo.class))).thenReturn(resultInfo);
        AttachmentInfoVO result = service.createAttachment(dto, user);
        assertNotNull(result);
    }

    @Test
    void findByIdList_shouldReturnVOList() {
        List<Long> ids = List.of(1L, 2L);
        List<AttachmentInfo> infoList = List.of(new AttachmentInfo(), new AttachmentInfo());
        when(attachmentInfoDomainService.findByIdList(ids)).thenReturn(infoList);
        List<AttachmentInfoVO> result = service.findByIdList(ids);
        assertNotNull(result);
        assertEquals(2, result.size());
    }

    @Test
    void findFileByBusinessType_shouldReturnError_whenNull() {
        when(attachmentInfoDomainService.findFileByBusinessType(anyString())).thenReturn(null);
        CommonResult<AttachmentInfoVO> result = service.findFileByBusinessType("type");
        assertNotNull(result);
        assertEquals(BaseServiceErrorCodeEnum.ATTACHMENT_NOT_FIND.getCode(), result.getCode());
    }

    @Test
    void findFileByBusinessType_shouldReturnSuccess() {
        AttachmentInfo info = new AttachmentInfo();
        when(attachmentInfoDomainService.findFileByBusinessType(anyString())).thenReturn(info);
        AttachmentInfoVO vo = new AttachmentInfoVO();
        CommonResult<AttachmentInfoVO> result = service.findFileByBusinessType("type");
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(vo, result.getData());
    }
} 