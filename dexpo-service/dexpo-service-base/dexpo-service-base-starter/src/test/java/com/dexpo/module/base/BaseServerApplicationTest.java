package com.dexpo.module.base;

import com.dexpo.module.base.enums.ApiConstants;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link BaseServerApplication} 的单元测试类
 * 
 * <p>测试基础服务启动类的所有功能，确保100%代码覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class BaseServerApplicationTest extends BaseUnitTest {

    /**
     * 测试应用类的注解配置
     */
    @Test
    void testApplicationAnnotations() {
        Class<BaseServerApplication> applicationClass = BaseServerApplication.class;
        
        // 验证@SpringBootApplication注解
        assertTrue(applicationClass.isAnnotationPresent(SpringBootApplication.class));
        
        SpringBootApplication springBootApp = applicationClass.getAnnotation(SpringBootApplication.class);
        assertNotNull(springBootApp);
        
        // 验证排除的自动配置类
        Class<?>[] excludeClasses = springBootApp.exclude();
        assertEquals(2, excludeClasses.length);
        
        boolean hasMetricsAutoConfiguration = false;
        boolean hasDataSourceAutoConfiguration = false;
        
        for (Class<?> excludeClass : excludeClasses) {
            if (excludeClass.getSimpleName().equals("MetricsAutoConfiguration")) {
                hasMetricsAutoConfiguration = true;
            }
            if (excludeClass.getSimpleName().equals("DataSourceAutoConfiguration")) {
                hasDataSourceAutoConfiguration = true;
            }
        }
        
        assertTrue(hasMetricsAutoConfiguration, "应该排除MetricsAutoConfiguration");
        assertTrue(hasDataSourceAutoConfiguration, "应该排除DataSourceAutoConfiguration");
    }

    /**
     * 测试@EnableDiscoveryClient注解
     */
    @Test
    void testEnableDiscoveryClientAnnotation() {
        assertTrue(BaseServerApplication.class.isAnnotationPresent(EnableDiscoveryClient.class));
    }

    /**
     * 测试@ComponentScan注解
     */
    @Test
    void testComponentScanAnnotation() {
        assertTrue(BaseServerApplication.class.isAnnotationPresent(ComponentScan.class));
        
        ComponentScan componentScan = BaseServerApplication.class.getAnnotation(ComponentScan.class);
        assertNotNull(componentScan);
        
        String[] basePackages = componentScan.value();
        assertEquals(5, basePackages.length);
        
        // 验证扫描的包
        assertTrue(containsPackage(basePackages, "com.dexpo.framework.security"));
        assertTrue(containsPackage(basePackages, "com.dexpo.module.base"));
        assertTrue(containsPackage(basePackages, "com.dexpo.framework.cache"));
        assertTrue(containsPackage(basePackages, "com.dexpo.framework.web"));
        assertTrue(containsPackage(basePackages, "com.dexpo.module.exhibition"));
    }

    /**
     * 测试@EnableFeignClients注解
     */
    @Test
    void testEnableFeignClientsAnnotation() {
        assertTrue(BaseServerApplication.class.isAnnotationPresent(EnableFeignClients.class));
        
        EnableFeignClients enableFeignClients = BaseServerApplication.class.getAnnotation(EnableFeignClients.class);
        assertNotNull(enableFeignClients);
        
        String[] feignPackages = enableFeignClients.value();
        assertNotEquals(0, feignPackages.length);
        
        // 验证Feign客户端扫描的包
        assertTrue(containsPackage(feignPackages, "com.dexpo.module.integration"));
        assertTrue(containsPackage(feignPackages, "com.dexpo.module.exhibition"));
    }

    /**
     * 测试main方法的存在性
     */
    @Test
    void testMainMethodExists() {
        try {
            var mainMethod = BaseServerApplication.class.getMethod("main", String[].class);
            assertNotNull(mainMethod);
            
            // 验证方法是public static
            assertTrue(java.lang.reflect.Modifier.isPublic(mainMethod.getModifiers()));
            assertTrue(java.lang.reflect.Modifier.isStatic(mainMethod.getModifiers()));
            
            // 验证返回类型是void
            assertEquals(void.class, mainMethod.getReturnType());
        } catch (NoSuchMethodException e) {
            fail("main方法应该存在");
        }
    }

    /**
     * 测试应用类可以实例化
     */
    @Test
    void testApplicationInstantiation() {
        assertDoesNotThrow(() -> {
            BaseServerApplication app = new BaseServerApplication();
            assertNotNull(app);
        });
    }

    /**
     * 测试类的包结构
     */
    @Test
    void testPackageStructure() {
        String packageName = BaseServerApplication.class.getPackage().getName();
        assertEquals("com.dexpo.module.base", packageName);
    }

    /**
     * 测试类的修饰符
     */
    @Test
    void testClassModifiers() {
        Class<BaseServerApplication> applicationClass = BaseServerApplication.class;
        
        // 验证类是public的
        assertTrue(java.lang.reflect.Modifier.isPublic(applicationClass.getModifiers()));
        
        // 验证类不是abstract的
        assertFalse(java.lang.reflect.Modifier.isAbstract(applicationClass.getModifiers()));
        
        // 验证类不是final的（通常Spring Boot应用类不应该是final的）
        assertFalse(java.lang.reflect.Modifier.isFinal(applicationClass.getModifiers()));
    }

    /**
     * 测试类的继承关系
     */
    @Test
    void testClassInheritance() {
        Class<BaseServerApplication> applicationClass = BaseServerApplication.class;
        
        // 验证继承自Object
        assertEquals(Object.class, applicationClass.getSuperclass());
        
        // 验证没有实现特殊接口（通常Spring Boot应用类不需要实现接口）
        assertEquals(0, applicationClass.getInterfaces().length);
    }

    /**
     * 测试类的构造函数
     */
    @Test
    void testConstructors() {
        var constructors = BaseServerApplication.class.getConstructors();
        
        // 应该有一个public的无参构造函数
        assertEquals(1, constructors.length);
        
        var constructor = constructors[0];
        assertTrue(java.lang.reflect.Modifier.isPublic(constructor.getModifiers()));
        assertEquals(0, constructor.getParameterCount());
    }

    /**
     * 测试类的方法数量
     */
    @Test
    void testMethodCount() {
        var methods = BaseServerApplication.class.getDeclaredMethods();
        
        // 应该只有main方法
        assertNotEquals(0, methods.length);
        assertEquals("main", methods[0].getName());
    }

    /**
     * 测试类的字段
     */
    @Test
    void testFields() {
        var fields = BaseServerApplication.class.getDeclaredFields();
        
        // 通常启动类不应该有实例字段
        // 可能有一些编译器生成的字段，但不应该有业务字段
        for (var field : fields) {
            // 如果有字段，应该是编译器生成的或者是常量
            assertTrue(field.isSynthetic() || 
                      java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                      java.lang.reflect.Modifier.isFinal(field.getModifiers()));
        }
    }


    /**
     * 测试应用配置的一致性
     */
    @Test
    void testConfigurationConsistency() {
        // 验证组件扫描包含了应用自身的包
        ComponentScan componentScan = BaseServerApplication.class.getAnnotation(ComponentScan.class);
        String[] basePackages = componentScan.value();
        
        boolean containsOwnPackage = false;
        String ownPackage = BaseServerApplication.class.getPackage().getName();
        
        for (String pkg : basePackages) {
            if (ownPackage.startsWith(pkg)) {
                containsOwnPackage = true;
                break;
            }
        }
        
        assertTrue(containsOwnPackage, "组件扫描应该包含应用自身的包");
    }

    /**
     * 辅助方法：检查包数组是否包含指定包
     */
    private boolean containsPackage(String[] packages, String targetPackage) {
        for (String pkg : packages) {
            if (pkg.equals(targetPackage)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 测试应用名称与常量的一致性
     */
    @Test
    void testApplicationNameConsistency() {
        // 这个测试确保应用名称与ApiConstants中定义的服务名称一致
        String expectedServiceName = "dexpo-service-base";
        // 验证与ApiConstants的一致性
        assertEquals(ApiConstants.NAME, expectedServiceName,
                "应用名称应该与ApiConstants.NAME保持一致");
    }

    /**
     * 测试Spring Boot版本兼容性
     */
    @Test
    void testSpringBootCompatibility() {
        // 验证使用的是正确的Spring Boot注解
        SpringBootApplication springBootApp = BaseServerApplication.class
                .getAnnotation(SpringBootApplication.class);
        
        assertNotNull(springBootApp);
        
        // 验证exclude属性的使用
        assertTrue(springBootApp.exclude().length > 0, 
                   "应该排除一些自动配置类");
    }

    /**
     * 测试微服务相关注解的完整性
     */
    @Test
    void testMicroserviceAnnotations() {
        Class<BaseServerApplication> appClass = BaseServerApplication.class;
        
        // 验证必要的微服务注解都存在
        assertTrue(appClass.isAnnotationPresent(SpringBootApplication.class), 
                   "应该有@SpringBootApplication注解");
        assertTrue(appClass.isAnnotationPresent(EnableDiscoveryClient.class), 
                   "应该有@EnableDiscoveryClient注解");
        assertTrue(appClass.isAnnotationPresent(EnableFeignClients.class), 
                   "应该有@EnableFeignClients注解");
        assertTrue(appClass.isAnnotationPresent(ComponentScan.class), 
                   "应该有@ComponentScan注解");
    }
}
