package com.dexpo.module.base.infrastructure.repository;

import com.dexpo.module.base.domain.model.agg.ExhibitionValuesetOptionRelation;
import com.dexpo.module.base.infrastructure.converter.ExhibitionValueSetOptionConvert;
import com.dexpo.module.base.infrastructure.tunnel.database.ExhibitionValuesetOptionRelationMapper;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.ExhibitionValuesetOptionRelationDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ExhibitionOptionRelationRepositoryImplTest {
    @Mock
    private ExhibitionValuesetOptionRelationMapper exhibitionValuesetOptionRelationMapper;
    @InjectMocks
    private ExhibitionOptionRelationRepositoryImpl repository;

    @BeforeEach
    void setUp() {
        ExhibitionValueSetOptionConvert mockConvert = mock(ExhibitionValueSetOptionConvert.class, Mockito.RETURNS_DEEP_STUBS);
        setFinalStatic(ExhibitionValueSetOptionConvert.class, "INSTANCE", mockConvert);
    }

    private static void setFinalStatic(Class<?> clazz, String fieldName, Object newValue) {
        try {
            java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            java.lang.reflect.Field modifiersField = java.lang.reflect.Field.class.getDeclaredField("modifiers");
            modifiersField.setAccessible(true);
            modifiersField.setInt(field, field.getModifiers() & ~java.lang.reflect.Modifier.FINAL);
            field.set(null, newValue);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void list_shouldReturnList() {
        ExhibitionValuesetOptionRelationDO relationDO = new ExhibitionValuesetOptionRelationDO();
        when(exhibitionValuesetOptionRelationMapper.selectList()).thenReturn(List.of(relationDO));
        ExhibitionValuesetOptionRelation relation = new ExhibitionValuesetOptionRelation();
        when(ExhibitionValueSetOptionConvert.INSTANCE.e2dList(anyList())).thenReturn(List.of(relation));
        List<ExhibitionValuesetOptionRelation> result = repository.list();
        assertNotNull(result);
        assertEquals(1, result.size());
        assertSame(relation, result.get(0));
    }
} 