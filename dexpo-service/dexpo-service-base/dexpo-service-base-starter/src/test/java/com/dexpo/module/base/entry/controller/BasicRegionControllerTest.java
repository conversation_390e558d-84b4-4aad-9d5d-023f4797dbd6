package com.dexpo.module.base.entry.controller;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.base.api.basic.dto.BasicRegionDTO;
import com.dexpo.module.base.api.basic.vo.BasicRegionTreeVO;
import com.dexpo.module.base.api.basic.vo.BasicRegionVO;
import com.dexpo.module.base.app.api.BasicRegionAppService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BasicRegionControllerTest {
    @Mock
    private BasicRegionAppService baseRegionAppService;
    @InjectMocks
    private BasicRegionController controller;

    @Test
    void getRegionList_shouldReturnSuccess() {
        BasicRegionDTO dto = new BasicRegionDTO();
        BasicRegionVO vo = new BasicRegionVO();
        when(baseRegionAppService.getRegionList(any())).thenReturn(List.of(vo));
        CommonResult<List<BasicRegionVO>> result = controller.getRegionList(dto);
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(1, result.getData().size());
        assertSame(vo, result.getData().get(0));
    }

    @Test
    void getRegionListAll_shouldReturnList() {
        BasicRegionTreeVO treeVO = new BasicRegionTreeVO();
        when(baseRegionAppService.getRegionListAll()).thenReturn(List.of(treeVO));
        List<BasicRegionTreeVO> result = controller.getRegionListAll();
        assertNotNull(result);
        assertEquals(1, result.size());
        assertSame(treeVO, result.get(0));
    }

    @Test
    void getRegionListByLevel_shouldReturnMap() {
        BasicRegionVO vo = new BasicRegionVO();
        Map<String, Map<String, BasicRegionVO>> map = Map.of("level", Map.of("name", vo));
        when(baseRegionAppService.getRegionListByLevel()).thenReturn(map);
        Map<String, Map<String, BasicRegionVO>> result = controller.getRegionListByLevel();
        assertNotNull(result);
        assertTrue(result.containsKey("level"));
        assertTrue(result.get("level").containsKey("name"));
        assertSame(vo, result.get("level").get("name"));
    }
} 