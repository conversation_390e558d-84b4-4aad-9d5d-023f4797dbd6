package com.dexpo.module.base.infrastructure.repository;

import com.dexpo.module.base.BaseUnitTest;
import com.dexpo.module.base.domain.model.agg.BasicLocation;
import com.dexpo.module.base.infrastructure.converter.BasicLocationConvert;
import com.dexpo.module.base.infrastructure.tunnel.database.BasicLocationMapper;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.BasicLocationDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * {@link BasicLocationRepositoryImpl} 的单元测试类
 * 
 * <p>测试业务地域仓储实现类的所有功能，确保100%代码覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class BasicLocationRepositoryImplTest extends BaseUnitTest {

    @Mock
    private BasicLocationMapper basicLocationMapper;

    @InjectMocks
    private BasicLocationRepositoryImpl basicLocationRepository;

    private BasicLocation basicLocation;
    private BasicLocationDO basicLocationDO;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        basicLocation = new BasicLocation();
        basicLocation.setId(1L);
        basicLocation.setLocationCode("CN-BJ");
        basicLocation.setLocationNameCn("北京");
        basicLocation.setLocationNameEn("Beijing");
        basicLocation.setLocationTag("ENTERPRISE_TYPE");

        basicLocationDO = new BasicLocationDO();
        basicLocationDO.setId(1L);
        basicLocationDO.setLocationCode("CN-BJ");
        basicLocationDO.setLocationNameCn("北京");
        basicLocationDO.setLocationNameEn("Beijing");
        basicLocationDO.setLocationTag("ENTERPRISE_TYPE");

        BasicLocationConvert mockConvert = mock(BasicLocationConvert.class, Mockito.RETURNS_DEEP_STUBS);
        setFinalStatic(BasicLocationConvert.class, "INSTANCE", mockConvert);
    }

    private static void setFinalStatic(Class<?> clazz, String fieldName, Object newValue) {
        try {
            java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            java.lang.reflect.Field modifiersField = java.lang.reflect.Field.class.getDeclaredField("modifiers");
            modifiersField.setAccessible(true);
            modifiersField.setInt(field, field.getModifiers() & ~java.lang.reflect.Modifier.FINAL);
            field.set(null, newValue);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试根据地域信息获取地域列表 - 成功场景
     */
    @Test
    void testGetLocationList_Success() {
        // 准备测试数据
        List<BasicLocationDO> doList = Arrays.asList(basicLocationDO);
        List<BasicLocation> expectedList = Arrays.asList(basicLocation);

        // Mock mapper调用
        when(basicLocationMapper.selectList(any())).thenReturn(doList);

        // Mock转换器
        try (MockedStatic<BasicLocationConvert> mockedConvert = mockStatic(BasicLocationConvert.class)) {
            when(BasicLocationConvert.INSTANCE.e2dList(doList)).thenReturn(expectedList);

            // 执行测试
            List<BasicLocation> result = basicLocationRepository.getLocationList(basicLocation);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(expectedList, result);

            // 验证方法调用
            verify(basicLocationMapper, times(1)).selectList(any());
        }
    }

    /**
     * 测试根据地域信息获取地域列表 - 空结果
     */
    @Test
    void testGetLocationList_EmptyResult() {
        // 准备测试数据
        List<BasicLocationDO> emptyDoList = Collections.emptyList();
        List<BasicLocation> emptyList = Collections.emptyList();

        // Mock mapper调用
        when(basicLocationMapper.selectList(any())).thenReturn(emptyDoList);

        // Mock转换器
        try (MockedStatic<BasicLocationConvert> mockedConvert = mockStatic(BasicLocationConvert.class)) {
            when(BasicLocationConvert.INSTANCE.e2dList(emptyDoList)).thenReturn(emptyList);

            // 执行测试
            List<BasicLocation> result = basicLocationRepository.getLocationList(basicLocation);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());

            // 验证方法调用
            verify(basicLocationMapper, times(1)).selectList(any());
        }
    }

    /**
     * 测试根据地域信息获取地域列表 - 多个结果
     */
    @Test
    void testGetLocationList_MultipleResults() {
        // 准备测试数据
        BasicLocationDO locationDO2 = new BasicLocationDO();
        locationDO2.setId(2L);
        locationDO2.setLocationCode("CN-SH");
        locationDO2.setLocationNameCn("上海");
        locationDO2.setLocationNameEn("Shanghai");
        locationDO2.setLocationTag("ENTERPRISE_TYPE");

        BasicLocation location2 = new BasicLocation();
        location2.setId(2L);
        location2.setLocationCode("CN-SH");
        location2.setLocationNameCn("上海");
        location2.setLocationNameEn("Shanghai");
        location2.setLocationTag("ENTERPRISE_TYPE");

        List<BasicLocationDO> doList = Arrays.asList(basicLocationDO, locationDO2);
        List<BasicLocation> expectedList = Arrays.asList(basicLocation, location2);

        // Mock mapper调用
        when(basicLocationMapper.selectList(any())).thenReturn(doList);

        // Mock转换器
        try (MockedStatic<BasicLocationConvert> mockedConvert = mockStatic(BasicLocationConvert.class)) {
            when(BasicLocationConvert.INSTANCE.e2dList(doList)).thenReturn(expectedList);

            // 执行测试
            List<BasicLocation> result = basicLocationRepository.getLocationList(basicLocation);

            // 验证结果
            assertNotNull(result);
            assertEquals(2, result.size());
            assertEquals(expectedList, result);

            // 验证方法调用
            verify(basicLocationMapper, times(1)).selectList(any());
        }
    }

    /**
     * 测试根据展会标签代码获取地域列表 - 成功场景
     */
    @Test
    void testGetExhibitionLocationList_Success() {
        // 准备测试数据
        String exhibitionTagCode = "TECH_EXPO_2024";
        List<BasicLocationDO> doList = Arrays.asList(basicLocationDO);
        List<BasicLocation> expectedList = Arrays.asList(basicLocation);

        // Mock mapper调用
        when(basicLocationMapper.selectByExhibitionTagCode(anyString())).thenReturn(doList);

        // Mock转换器
        try (MockedStatic<BasicLocationConvert> mockedConvert = mockStatic(BasicLocationConvert.class)) {
            when(BasicLocationConvert.INSTANCE.e2dList(doList)).thenReturn(expectedList);

            // 执行测试
            List<BasicLocation> result = basicLocationRepository.getExhibitionLocationList(exhibitionTagCode);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(expectedList, result);

            // 验证方法调用
            verify(basicLocationMapper, times(1)).selectByExhibitionTagCode(exhibitionTagCode);
        }
    }

    /**
     * 测试根据展会标签代码获取地域列表 - 空结果
     */
    @Test
    void testGetExhibitionLocationList_EmptyResult() {
        // 准备测试数据
        String exhibitionTagCode = "NON_EXISTENT_EXPO";
        List<BasicLocationDO> emptyDoList = Collections.emptyList();
        List<BasicLocation> emptyList = Collections.emptyList();

        // Mock mapper调用
        when(basicLocationMapper.selectByExhibitionTagCode(anyString())).thenReturn(emptyDoList);

        // Mock转换器
        try (MockedStatic<BasicLocationConvert> mockedConvert = mockStatic(BasicLocationConvert.class)) {
            when(BasicLocationConvert.INSTANCE.e2dList(emptyDoList)).thenReturn(emptyList);

            // 执行测试
            List<BasicLocation> result = basicLocationRepository.getExhibitionLocationList(exhibitionTagCode);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());

            // 验证方法调用
            verify(basicLocationMapper, times(1)).selectByExhibitionTagCode(exhibitionTagCode);
        }
    }

    /**
     * 测试根据展会标签代码获取地域列表 - null参数
     */
    @Test
    void testGetExhibitionLocationList_NullParameter() {
        // 准备测试数据
        List<BasicLocationDO> emptyDoList = Collections.emptyList();
        List<BasicLocation> emptyList = Collections.emptyList();

        // Mock mapper调用
        when(basicLocationMapper.selectByExhibitionTagCode(null)).thenReturn(emptyDoList);

        // Mock转换器
        try (MockedStatic<BasicLocationConvert> mockedConvert = mockStatic(BasicLocationConvert.class)) {
            when(BasicLocationConvert.INSTANCE.e2dList(emptyDoList)).thenReturn(emptyList);

            // 执行测试
            List<BasicLocation> result = basicLocationRepository.getExhibitionLocationList(null);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());

            // 验证方法调用
            verify(basicLocationMapper, times(1)).selectByExhibitionTagCode(null);
        }
    }

    /**
     * 测试根据展会标签代码获取地域列表 - 空字符串参数
     */
    @Test
    void testGetExhibitionLocationList_EmptyStringParameter() {
        // 准备测试数据
        List<BasicLocationDO> emptyDoList = Collections.emptyList();
        List<BasicLocation> emptyList = Collections.emptyList();

        // Mock mapper调用
        when(basicLocationMapper.selectByExhibitionTagCode("")).thenReturn(emptyDoList);

        // Mock转换器
        try (MockedStatic<BasicLocationConvert> mockedConvert = mockStatic(BasicLocationConvert.class)) {
            when(BasicLocationConvert.INSTANCE.e2dList(emptyDoList)).thenReturn(emptyList);

            // 执行测试
            List<BasicLocation> result = basicLocationRepository.getExhibitionLocationList("");

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());

            // 验证方法调用
            verify(basicLocationMapper, times(1)).selectByExhibitionTagCode("");
        }
    }

    /**
     * 测试异常处理 - getLocationList方法
     */
    @Test
    void testGetLocationList_ExceptionHandling() {
        // Mock mapper抛出异常
        when(basicLocationMapper.selectList(any())).thenThrow(new RuntimeException("Database error"));

        // 执行测试并验证异常传播
        assertThrows(RuntimeException.class, () -> {
            basicLocationRepository.getLocationList(basicLocation);
        });

        // 验证方法调用
        verify(basicLocationMapper, times(1)).selectList(any());
    }

    /**
     * 测试异常处理 - getExhibitionLocationList方法
     */
    @Test
    void testGetExhibitionLocationList_ExceptionHandling() {
        // 准备测试数据
        String exhibitionTagCode = "TECH_EXPO_2024";

        // Mock mapper抛出异常
        when(basicLocationMapper.selectByExhibitionTagCode(anyString()))
                .thenThrow(new RuntimeException("Database error"));

        // 执行测试并验证异常传播
        assertThrows(RuntimeException.class, () -> {
            basicLocationRepository.getExhibitionLocationList(exhibitionTagCode);
        });

        // 验证方法调用
        verify(basicLocationMapper, times(1)).selectByExhibitionTagCode(exhibitionTagCode);
    }

    /**
     * 测试仓储的依赖注入
     */
    @Test
    void testDependencyInjection() {
        // 验证仓储实例不为空
        assertNotNull(basicLocationRepository);
        
        // 验证mapper依赖已正确注入（通过调用方法来间接验证）
        when(basicLocationMapper.selectList(any())).thenReturn(Collections.emptyList());
        
        try (MockedStatic<BasicLocationConvert> mockedConvert = mockStatic(BasicLocationConvert.class)) {
            when(BasicLocationConvert.INSTANCE.e2dList(any())).thenReturn(Collections.emptyList());
            
            assertDoesNotThrow(() -> {
                basicLocationRepository.getLocationList(new BasicLocation());
            });
        }
    }

    /**
     * 测试仓储类的注解
     */
    @Test
    void testRepositoryAnnotations() {
        // 验证类上的注解
        assertTrue(BasicLocationRepositoryImpl.class.isAnnotationPresent(
                org.springframework.stereotype.Service.class));
        assertTrue(BasicLocationRepositoryImpl.class.isAnnotationPresent(
                lombok.AllArgsConstructor.class));
    }

    /**
     * 测试接口实现
     */
    @Test
    void testInterfaceImplementation() {
        // 验证仓储实现了正确的接口
        assertTrue(com.dexpo.module.base.domain.repository.BasicLocationRepository.class
                .isAssignableFrom(BasicLocationRepositoryImpl.class));
    }

    /**
     * 测试转换器异常处理
     */
    @Test
    void testConverterExceptionHandling() {
        // 准备测试数据
        List<BasicLocationDO> doList = Arrays.asList(basicLocationDO);

        // Mock mapper调用
        when(basicLocationMapper.selectList(any())).thenReturn(doList);

        // Mock转换器抛出异常
        try (MockedStatic<BasicLocationConvert> mockedConvert = mockStatic(BasicLocationConvert.class)) {
            when(BasicLocationConvert.INSTANCE.e2dList(doList))
                    .thenThrow(new RuntimeException("Converter error"));

            // 执行测试并验证异常传播
            assertThrows(RuntimeException.class, () -> {
                basicLocationRepository.getLocationList(basicLocation);
            });

            // 验证方法调用
            verify(basicLocationMapper, times(1)).selectList(any());
        }
    }
}
