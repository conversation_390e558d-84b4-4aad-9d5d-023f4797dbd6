package com.dexpo.module.base.util;

import com.dexpo.module.base.api.basic.dto.BasicExhibitionLocationDTO;
import com.dexpo.module.base.api.basic.dto.BasicLocationDTO;
import com.dexpo.module.base.api.basic.vo.BasicLocationVO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetOptionVO;
import com.dexpo.module.base.dal.dataobject.basic.BasicLocationDO;
import com.dexpo.module.base.domain.model.agg.BasicLocation;
import com.dexpo.module.base.enums.BasicRegionLevelEnums;
import com.dexpo.module.base.enums.CommonTodoMediaRegisterActionEnum;
import com.dexpo.module.member.enums.RegisterStatusEnum;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 测试数据构建器
 * 
 * <p>该工具类提供了创建各种测试数据对象的便捷方法，
 * 用于单元测试中快速构建所需的测试数据。</p>
 * 
 * <p>主要功能：</p>
 * <ul>
 *   <li>创建标准的测试数据对象</li>
 *   <li>创建边界条件的测试数据</li>
 *   <li>创建异常情况的测试数据</li>
 *   <li>批量创建测试数据集合</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class TestDataBuilder {

    /**
     * 创建标准的BasicLocationDO对象
     */
    public static BasicLocationDO createBasicLocationDO() {
        return createBasicLocationDO(1L, "CN-BJ", "北京", "Beijing", "ENTERPRISE_TYPE");
    }

    /**
     * 创建指定参数的BasicLocationDO对象
     */
    public static BasicLocationDO createBasicLocationDO(Long id, String locationCode, 
                                                       String locationNameCn, String locationNameEn, 
                                                       String locationTag) {
        BasicLocationDO locationDO = new BasicLocationDO();
        locationDO.setId(id);
        locationDO.setLocationCode(locationCode);
        locationDO.setLocationNameCn(locationNameCn);
        locationDO.setLocationNameEn(locationNameEn);
        locationDO.setLocationTag(locationTag);
        return locationDO;
    }

    /**
     * 创建标准的BasicLocation领域对象
     */
    public static BasicLocation createBasicLocation() {
        return createBasicLocation(1L, "CN-BJ", "北京", "Beijing", "ENTERPRISE_TYPE");
    }

    /**
     * 创建指定参数的BasicLocation领域对象
     */
    public static BasicLocation createBasicLocation(Long id, String locationCode, 
                                                   String locationNameCn, String locationNameEn, 
                                                   String locationTag) {
        BasicLocation location = new BasicLocation();
        location.setId(id);
        location.setLocationCode(locationCode);
        location.setLocationNameCn(locationNameCn);
        location.setLocationNameEn(locationNameEn);
        location.setLocationTag(locationTag);
        return location;
    }

    /**
     * 创建标准的BasicLocationVO对象
     */
    public static BasicLocationVO createBasicLocationVO() {
        return createBasicLocationVO(1L, "CN-BJ", "北京", "Beijing", "ENTERPRISE_TYPE");
    }

    /**
     * 创建指定参数的BasicLocationVO对象
     */
    public static BasicLocationVO createBasicLocationVO(Long id, String locationCode, 
                                                       String locationNameCn, String locationNameEn, 
                                                       String locationTag) {
        BasicLocationVO locationVO = new BasicLocationVO();
        locationVO.setId(id);
        locationVO.setLocationCode(locationCode);
        locationVO.setLocationNameCn(locationNameCn);
        locationVO.setLocationNameEn(locationNameEn);
        locationVO.setLocationTag(locationTag);
        return locationVO;
    }

    /**
     * 创建标准的BasicLocationDTO对象
     */
    public static BasicLocationDTO createBasicLocationDTO() {
        return createBasicLocationDTO("ENTERPRISE_TYPE");
    }

    /**
     * 创建指定地域标签的BasicLocationDTO对象
     */
    public static BasicLocationDTO createBasicLocationDTO(String locationTag) {
        BasicLocationDTO locationDTO = new BasicLocationDTO();
        locationDTO.setLocationTag(locationTag);
        return locationDTO;
    }

    /**
     * 创建标准的BasicExhibitionLocationDTO对象
     */
    public static BasicExhibitionLocationDTO createBasicExhibitionLocationDTO() {
        return createBasicExhibitionLocationDTO("TECH_EXPO_2024");
    }

    /**
     * 创建指定展会标签的BasicExhibitionLocationDTO对象
     */
    public static BasicExhibitionLocationDTO createBasicExhibitionLocationDTO(String exhibitionTagCode) {
        BasicExhibitionLocationDTO exhibitionLocationDTO = new BasicExhibitionLocationDTO();
        exhibitionLocationDTO.setExhibitionTagCode(exhibitionTagCode);
        return exhibitionLocationDTO;
    }

    /**
     * 创建标准的BasicValuesetInfoVO对象
     */
    public static BasicValuesetInfoVO createBasicValuesetInfoVO() {
        return createBasicValuesetInfoVO("USER_TYPE", "用户类型", "User Type", "系统用户类型分类");
    }

    /**
     * 创建指定参数的BasicValuesetInfoVO对象
     */
    public static BasicValuesetInfoVO createBasicValuesetInfoVO(String valuesetCode, String valuesetName, 
                                                               String valuesetNameEn, String description) {
        BasicValuesetInfoVO valuesetInfoVO = new BasicValuesetInfoVO();
        valuesetInfoVO.setValuesetCode(valuesetCode);
        valuesetInfoVO.setValuesetName(valuesetName);
        valuesetInfoVO.setValuesetNameEn(valuesetNameEn);
        valuesetInfoVO.setDescription(description);
        return valuesetInfoVO;
    }

    /**
     * 创建标准的BasicValuesetOptionVO对象
     */
    public static BasicValuesetOptionVO createBasicValuesetOptionVO() {
        return createBasicValuesetOptionVO("USER_TYPE", "ADMIN", "管理员", "Administrator", "admin");
    }

    /**
     * 创建指定参数的BasicValuesetOptionVO对象
     */
    public static BasicValuesetOptionVO createBasicValuesetOptionVO(String valuesetCode, String optionCode, 
                                                                   String optionName, String optionNameEn, 
                                                                   String optionValue) {
        BasicValuesetOptionVO optionVO = new BasicValuesetOptionVO();
        optionVO.setValuesetCode(valuesetCode);
        optionVO.setOptionCode(optionCode);
        optionVO.setOptionName(optionName);
        optionVO.setOptionNameEn(optionNameEn);
        optionVO.setOptionValue(optionValue);
        return optionVO;
    }

    /**
     * 创建BasicLocationDO列表
     */
    public static List<BasicLocationDO> createBasicLocationDOList() {
        return Arrays.asList(
            createBasicLocationDO(1L, "CN-BJ", "北京", "Beijing", "ENTERPRISE_TYPE"),
            createBasicLocationDO(2L, "CN-SH", "上海", "Shanghai", "ENTERPRISE_TYPE"),
            createBasicLocationDO(3L, "CN-GZ", "广州", "Guangzhou", "ENTERPRISE_TYPE")
        );
    }

    /**
     * 创建BasicLocation列表
     */
    public static List<BasicLocation> createBasicLocationList() {
        return Arrays.asList(
            createBasicLocation(1L, "CN-BJ", "北京", "Beijing", "ENTERPRISE_TYPE"),
            createBasicLocation(2L, "CN-SH", "上海", "Shanghai", "ENTERPRISE_TYPE"),
            createBasicLocation(3L, "CN-GZ", "广州", "Guangzhou", "ENTERPRISE_TYPE")
        );
    }

    /**
     * 创建BasicLocationVO列表
     */
    public static List<BasicLocationVO> createBasicLocationVOList() {
        return Arrays.asList(
            createBasicLocationVO(1L, "CN-BJ", "北京", "Beijing", "ENTERPRISE_TYPE"),
            createBasicLocationVO(2L, "CN-SH", "上海", "Shanghai", "ENTERPRISE_TYPE"),
            createBasicLocationVO(3L, "CN-GZ", "广州", "Guangzhou", "ENTERPRISE_TYPE")
        );
    }

    /**
     * 创建BasicValuesetInfoVO列表
     */
    public static List<BasicValuesetInfoVO> createBasicValuesetInfoVOList() {
        return Arrays.asList(
            createBasicValuesetInfoVO("USER_TYPE", "用户类型", "User Type", "系统用户类型分类"),
            createBasicValuesetInfoVO("STATUS_TYPE", "状态类型", "Status Type", "通用状态类型"),
            createBasicValuesetInfoVO("ENTERPRISE_TYPE", "企业类型", "Enterprise Type", "企业分类类型")
        );
    }

    /**
     * 创建值集代码列表
     */
    public static List<String> createValuesetCodeList() {
        return Arrays.asList("USER_TYPE", "STATUS_TYPE", "ENTERPRISE_TYPE");
    }

    /**
     * 创建选项代码列表
     */
    public static List<String> createOptionCodeList() {
        return Arrays.asList("ADMIN", "USER", "GUEST", "ACTIVE", "INACTIVE");
    }

    /**
     * 创建边界条件测试数据 - 空字符串
     */
    public static BasicLocationDO createEmptyStringLocationDO() {
        return createBasicLocationDO(1L, "", "", "", "");
    }

    /**
     * 创建边界条件测试数据 - null值
     */
    public static BasicLocationDO createNullValueLocationDO() {
        return createBasicLocationDO(null, null, null, null, null);
    }

    /**
     * 创建边界条件测试数据 - 只有中文名称
     */
    public static BasicLocation createChineseOnlyLocation() {
        return createBasicLocation(1L, "CN-TEST", "测试地域", null, "TEST_TAG");
    }

    /**
     * 创建边界条件测试数据 - 只有英文名称
     */
    public static BasicLocation createEnglishOnlyLocation() {
        return createBasicLocation(1L, "CN-TEST", null, "Test Location", "TEST_TAG");
    }

    /**
     * 创建边界条件测试数据 - 无效的地域对象
     */
    public static BasicLocation createInvalidLocation() {
        BasicLocation location = new BasicLocation();
        location.setId(1L);
        // 缺少必要的字段，使其无效
        return location;
    }

    /**
     * 创建大数据量测试列表
     */
    public static List<BasicLocationVO> createLargeLocationVOList(int size) {
        List<BasicLocationVO> list = new ArrayList<>();
        for (int i = 1; i <= size; i++) {
            list.add(createBasicLocationVO(
                (long) i, 
                "CN-" + String.format("%03d", i), 
                "测试地域" + i, 
                "Test Location " + i, 
                "TEST_TAG"
            ));
        }
        return list;
    }

    /**
     * 创建特殊字符测试数据
     */
    public static BasicLocationDO createSpecialCharLocationDO() {
        return createBasicLocationDO(
            1L, 
            "CN-SPECIAL", 
            "测试地域!@#$%^&*()", 
            "Test Location!@#$%^&*()", 
            "SPECIAL_TAG"
        );
    }

    /**
     * 创建长字符串测试数据
     */
    public static BasicLocationDO createLongStringLocationDO() {
        String longString = "这是一个非常长的字符串".repeat(10);
        return createBasicLocationDO(1L, "CN-LONG", longString, longString, "LONG_TAG");
    }

    /**
     * 验证测试数据的有效性
     */
    public static boolean isValidTestData(BasicLocation location) {
        return location != null && 
               location.getLocationCode() != null && 
               !location.getLocationCode().trim().isEmpty() &&
               location.getLocationTag() != null && 
               !location.getLocationTag().trim().isEmpty() &&
               (location.getLocationNameCn() != null || location.getLocationNameEn() != null);
    }

    /**
     * 创建测试用的枚举数据
     */
    public static class EnumTestData {
        
        /**
         * 获取所有BasicRegionLevelEnums值
         */
        public static BasicRegionLevelEnums[] getAllRegionLevels() {
            return BasicRegionLevelEnums.values();
        }

        /**
         * 获取所有CommonTodoMediaRegisterActionEnum值
         */
        public static CommonTodoMediaRegisterActionEnum[] getAllMediaRegisterActions() {
            return CommonTodoMediaRegisterActionEnum.values();
        }

        /**
         * 创建测试用的RegisterStatusEnum
         */
        public static RegisterStatusEnum getTestRegisterStatus() {
            return RegisterStatusEnum.PENDING_REVIEW;
        }
    }

    /**
     * 创建测试用的时间数据
     */
    public static class TimeTestData {
        
        /**
         * 获取当前时间
         */
        public static LocalDateTime now() {
            return LocalDateTime.now();
        }

        /**
         * 获取过去的时间
         */
        public static LocalDateTime pastTime() {
            return LocalDateTime.now().minusDays(1);
        }

        /**
         * 获取未来的时间
         */
        public static LocalDateTime futureTime() {
            return LocalDateTime.now().plusDays(1);
        }
    }
}
