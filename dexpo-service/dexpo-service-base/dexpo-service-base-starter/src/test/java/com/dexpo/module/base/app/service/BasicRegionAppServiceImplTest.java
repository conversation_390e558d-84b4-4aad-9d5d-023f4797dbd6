package com.dexpo.module.base.app.service;

import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.exception.enums.BaseServiceErrorCodeEnum;
import com.dexpo.module.base.api.basic.dto.BasicRegionDTO;
import com.dexpo.module.base.api.basic.vo.BasicRegionTreeVO;
import com.dexpo.module.base.api.basic.vo.BasicRegionVO;
import com.dexpo.module.base.app.converter.BasicRegionDTOConvert;
import com.dexpo.module.base.domain.model.agg.BasicRegion;
import com.dexpo.module.base.domain.service.BasicRegionDomainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BasicRegionAppServiceImplTest {
    @Mock
    private BasicRegionDomainService basicRegionDomainService;

    @InjectMocks
    private BasicRegionAppServiceImpl basicRegionAppService;




    @Test
    void getRegionList_shouldThrowException_whenAllBlank() {
        BasicRegionDTO dto = new BasicRegionDTO();
        dto.setLevel("");
        dto.setParentAdcode("");
        ServiceException ex = assertThrows(ServiceException.class, () -> basicRegionAppService.getRegionList(dto));
        assertEquals(BaseServiceErrorCodeEnum.PARAMETER_CANNOT_BE_ALL_EMPTY.getCode(), ex.getCode());
    }

    @Test
    void getRegionList_shouldReturnVOList() {
        BasicRegionDTO dto = new BasicRegionDTO();
        dto.setLevel("province");
        dto.setParentAdcode("100000");
        BasicRegion region = new BasicRegion();
        List<BasicRegion> regionList = Collections.singletonList(region);
        when(basicRegionDomainService.getRegionList(any())).thenReturn(regionList);
        List<BasicRegionVO> result = basicRegionAppService.getRegionList(dto);
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    void getRegionListAll_shouldReturnTreeVOList() {
        BasicRegion region = new BasicRegion();
        List<BasicRegion> regionList = Collections.singletonList(region);
        when(basicRegionDomainService.selectList()).thenReturn(regionList);
        BasicRegionTreeVO treeVO = new BasicRegionTreeVO();
        treeVO.setLevel("province");
        treeVO.setAdcode("100000");
        treeVO.setParentAdcode("");
        when(BasicRegionDTOConvert.INSTANCE.vToTreeList(regionList)).thenReturn(Collections.singletonList(treeVO));
        List<BasicRegionTreeVO> result = basicRegionAppService.getRegionListAll();
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("100000", result.get(0).getAdcode());
    }

    @Test
    void getRegionListByLevel_shouldReturnMap() {
        BasicRegion region = new BasicRegion();
        List<BasicRegion> regionList = Collections.singletonList(region);
        when(basicRegionDomainService.selectList()).thenReturn(regionList);
        BasicRegionVO vo = new BasicRegionVO();
        vo.setLevel("province");
        vo.setName("test");
        when(BasicRegionDTOConvert.INSTANCE.e2vList(regionList)).thenReturn(Collections.singletonList(vo));
        Map<String, Map<String, BasicRegionVO>> result = basicRegionAppService.getRegionListByLevel();
        assertNotNull(result);
        assertTrue(result.containsKey("province"));
        assertTrue(result.get("province").containsKey("test"));
    }
} 