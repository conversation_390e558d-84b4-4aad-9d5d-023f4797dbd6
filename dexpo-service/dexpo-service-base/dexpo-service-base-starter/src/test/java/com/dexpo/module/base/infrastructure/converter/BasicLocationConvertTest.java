package com.dexpo.module.base.infrastructure.converter;

import com.dexpo.module.base.domain.model.agg.BasicLocation;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.BasicLocationDO;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class BasicLocationConvertTest {
    @Test
    void d2e_shouldMapFields() {
        BasicLocation location = new BasicLocation();
        location.setId(1L);
        location.setLocationTag("tag");
        BasicLocationDO locationDO = BasicLocationConvert.INSTANCE.d2e(location);
        assertNotNull(locationDO);
        assertEquals(location.getId(), locationDO.getId());
        assertEquals(location.getLocationTag(), locationDO.getLocationTag());
    }

    @Test
    void e2d_shouldMapFields() {
        BasicLocationDO locationDO = new BasicLocationDO();
        locationDO.setId(2L);
        locationDO.setLocationTag("tag2");
        BasicLocation location = BasicLocationConvert.INSTANCE.e2d(locationDO);
        assertNotNull(location);
        assertEquals(locationDO.getId(), location.getId());
        assertEquals(locationDO.getLocationTag(), location.getLocationTag());
    }

    @Test
    void e2dList_shouldMapList() {
        BasicLocationDO locationDO = new BasicLocationDO();
        locationDO.setId(3L);
        locationDO.setLocationTag("tag3");
        List<BasicLocation> list = BasicLocationConvert.INSTANCE.e2dList(List.of(locationDO));
        assertNotNull(list);
        assertEquals(1, list.size());
        assertEquals(locationDO.getId(), list.get(0).getId());
        assertEquals(locationDO.getLocationTag(), list.get(0).getLocationTag());
    }
} 