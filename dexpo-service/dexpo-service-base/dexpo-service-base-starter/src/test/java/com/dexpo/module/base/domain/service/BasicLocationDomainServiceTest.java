package com.dexpo.module.base.domain.service;

import com.dexpo.module.base.BaseUnitTest;
import com.dexpo.module.base.domain.model.agg.BasicLocation;
import com.dexpo.module.base.domain.repository.BasicLocationRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * {@link BasicLocationDomainService} 的单元测试类
 * 
 * <p>测试业务地域领域服务的所有功能，确保100%代码覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class BasicLocationDomainServiceTest extends BaseUnitTest {

    @Mock
    private BasicLocationRepository basicLocationRepository;

    @InjectMocks
    private BasicLocationDomainService basicLocationDomainService;

    private BasicLocation basicLocation;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        basicLocation = new BasicLocation();
        basicLocation.setId(1L);
        basicLocation.setLocationCode("CN-BJ");
        basicLocation.setLocationNameCn("北京");
        basicLocation.setLocationNameEn("Beijing");
        basicLocation.setLocationTag("ENTERPRISE_TYPE");
    }

    /**
     * 测试根据地域信息获取地域列表 - 成功场景
     */
    @Test
    void testGetLocationList_Success() {
        // 准备测试数据
        BasicLocation queryLocation = new BasicLocation();
        queryLocation.setLocationTag("ENTERPRISE_TYPE");
        
        List<BasicLocation> expectedList = Arrays.asList(basicLocation);

        // Mock仓储调用
        when(basicLocationRepository.getLocationList(any(BasicLocation.class)))
                .thenReturn(expectedList);

        // 执行测试
        List<BasicLocation> result = basicLocationDomainService.getLocationList(queryLocation);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(expectedList, result);
        assertEquals(basicLocation, result.get(0));

        // 验证方法调用
        verify(basicLocationRepository, times(1)).getLocationList(queryLocation);
    }

    /**
     * 测试根据地域信息获取地域列表 - 空结果
     */
    @Test
    void testGetLocationList_EmptyResult() {
        // 准备测试数据
        BasicLocation queryLocation = new BasicLocation();
        queryLocation.setLocationTag("NON_EXISTENT_TAG");
        
        List<BasicLocation> emptyList = Collections.emptyList();

        // Mock仓储调用
        when(basicLocationRepository.getLocationList(any(BasicLocation.class)))
                .thenReturn(emptyList);

        // 执行测试
        List<BasicLocation> result = basicLocationDomainService.getLocationList(queryLocation);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(basicLocationRepository, times(1)).getLocationList(queryLocation);
    }

    /**
     * 测试根据地域信息获取地域列表 - 多个结果
     */
    @Test
    void testGetLocationList_MultipleResults() {
        // 准备测试数据
        BasicLocation queryLocation = new BasicLocation();
        queryLocation.setLocationTag("ENTERPRISE_TYPE");
        
        BasicLocation location2 = new BasicLocation();
        location2.setId(2L);
        location2.setLocationCode("CN-SH");
        location2.setLocationNameCn("上海");
        location2.setLocationNameEn("Shanghai");
        location2.setLocationTag("ENTERPRISE_TYPE");
        
        List<BasicLocation> expectedList = Arrays.asList(basicLocation, location2);

        // Mock仓储调用
        when(basicLocationRepository.getLocationList(any(BasicLocation.class)))
                .thenReturn(expectedList);

        // 执行测试
        List<BasicLocation> result = basicLocationDomainService.getLocationList(queryLocation);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(expectedList, result);

        // 验证方法调用
        verify(basicLocationRepository, times(1)).getLocationList(queryLocation);
    }

    /**
     * 测试根据地域信息获取地域列表 - null参数
     */
    @Test
    void testGetLocationList_NullParameter() {
        // Mock仓储调用
        when(basicLocationRepository.getLocationList(null))
                .thenReturn(Collections.emptyList());

        // 执行测试
        List<BasicLocation> result = basicLocationDomainService.getLocationList(null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(basicLocationRepository, times(1)).getLocationList(null);
    }

    /**
     * 测试根据展会标签代码获取地域列表 - 成功场景
     */
    @Test
    void testGetExhibitionLocationList_Success() {
        // 准备测试数据
        String exhibitionTagCode = "TECH_EXPO_2024";
        List<BasicLocation> expectedList = Arrays.asList(basicLocation);

        // Mock仓储调用
        when(basicLocationRepository.getExhibitionLocationList(anyString()))
                .thenReturn(expectedList);

        // 执行测试
        List<BasicLocation> result = basicLocationDomainService.getExhibitionLocationList(exhibitionTagCode);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(expectedList, result);
        assertEquals(basicLocation, result.get(0));

        // 验证方法调用
        verify(basicLocationRepository, times(1)).getExhibitionLocationList(exhibitionTagCode);
    }

    /**
     * 测试根据展会标签代码获取地域列表 - 空结果
     */
    @Test
    void testGetExhibitionLocationList_EmptyResult() {
        // 准备测试数据
        String exhibitionTagCode = "NON_EXISTENT_EXPO";
        List<BasicLocation> emptyList = Collections.emptyList();

        // Mock仓储调用
        when(basicLocationRepository.getExhibitionLocationList(anyString()))
                .thenReturn(emptyList);

        // 执行测试
        List<BasicLocation> result = basicLocationDomainService.getExhibitionLocationList(exhibitionTagCode);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(basicLocationRepository, times(1)).getExhibitionLocationList(exhibitionTagCode);
    }

    /**
     * 测试根据展会标签代码获取地域列表 - 多个结果
     */
    @Test
    void testGetExhibitionLocationList_MultipleResults() {
        // 准备测试数据
        String exhibitionTagCode = "TECH_EXPO_2024";
        
        BasicLocation location2 = new BasicLocation();
        location2.setId(2L);
        location2.setLocationCode("CN-SZ");
        location2.setLocationNameCn("深圳");
        location2.setLocationNameEn("Shenzhen");
        location2.setLocationTag("ENTERPRISE_TYPE");
        
        List<BasicLocation> expectedList = Arrays.asList(basicLocation, location2);

        // Mock仓储调用
        when(basicLocationRepository.getExhibitionLocationList(anyString()))
                .thenReturn(expectedList);

        // 执行测试
        List<BasicLocation> result = basicLocationDomainService.getExhibitionLocationList(exhibitionTagCode);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(expectedList, result);

        // 验证方法调用
        verify(basicLocationRepository, times(1)).getExhibitionLocationList(exhibitionTagCode);
    }

    /**
     * 测试根据展会标签代码获取地域列表 - null参数
     */
    @Test
    void testGetExhibitionLocationList_NullParameter() {
        // Mock仓储调用
        when(basicLocationRepository.getExhibitionLocationList(null))
                .thenReturn(Collections.emptyList());

        // 执行测试
        List<BasicLocation> result = basicLocationDomainService.getExhibitionLocationList(null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(basicLocationRepository, times(1)).getExhibitionLocationList(null);
    }

    /**
     * 测试根据展会标签代码获取地域列表 - 空字符串参数
     */
    @Test
    void testGetExhibitionLocationList_EmptyStringParameter() {
        // Mock仓储调用
        when(basicLocationRepository.getExhibitionLocationList(""))
                .thenReturn(Collections.emptyList());

        // 执行测试
        List<BasicLocation> result = basicLocationDomainService.getExhibitionLocationList("");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(basicLocationRepository, times(1)).getExhibitionLocationList("");
    }

    /**
     * 测试异常处理 - getLocationList方法
     */
    @Test
    void testGetLocationList_ExceptionHandling() {
        // 准备测试数据
        BasicLocation queryLocation = new BasicLocation();
        queryLocation.setLocationTag("ENTERPRISE_TYPE");

        // Mock仓储抛出异常
        when(basicLocationRepository.getLocationList(any(BasicLocation.class)))
                .thenThrow(new RuntimeException("Repository error"));

        // 执行测试并验证异常传播
        assertThrows(RuntimeException.class, () -> {
            basicLocationDomainService.getLocationList(queryLocation);
        });

        // 验证方法调用
        verify(basicLocationRepository, times(1)).getLocationList(queryLocation);
    }

    /**
     * 测试异常处理 - getExhibitionLocationList方法
     */
    @Test
    void testGetExhibitionLocationList_ExceptionHandling() {
        // 准备测试数据
        String exhibitionTagCode = "TECH_EXPO_2024";

        // Mock仓储抛出异常
        when(basicLocationRepository.getExhibitionLocationList(anyString()))
                .thenThrow(new RuntimeException("Repository error"));

        // 执行测试并验证异常传播
        assertThrows(RuntimeException.class, () -> {
            basicLocationDomainService.getExhibitionLocationList(exhibitionTagCode);
        });

        // 验证方法调用
        verify(basicLocationRepository, times(1)).getExhibitionLocationList(exhibitionTagCode);
    }

    /**
     * 测试服务的依赖注入
     */
    @Test
    void testDependencyInjection() {
        // 验证服务实例不为空
        assertNotNull(basicLocationDomainService);
        
        // 验证仓储依赖已正确注入（通过调用方法来间接验证）
        when(basicLocationRepository.getLocationList(any(BasicLocation.class)))
                .thenReturn(Collections.emptyList());
        
        assertDoesNotThrow(() -> {
            basicLocationDomainService.getLocationList(new BasicLocation());
        });
    }

    /**
     * 测试服务类的注解
     */
    @Test
    void testServiceAnnotations() {
        // 验证类上的注解
        assertTrue(BasicLocationDomainService.class.isAnnotationPresent(
                org.springframework.stereotype.Service.class));
        assertTrue(BasicLocationDomainService.class.isAnnotationPresent(
                lombok.RequiredArgsConstructor.class));
    }

    /**
     * 测试方法的参数传递
     */
    @Test
    void testMethodParameterPassing() {
        // 准备测试数据
        BasicLocation queryLocation = new BasicLocation();
        queryLocation.setLocationTag("TEST_TAG");
        
        String exhibitionTagCode = "TEST_EXPO";

        // Mock仓储调用
        when(basicLocationRepository.getLocationList(queryLocation))
                .thenReturn(Collections.emptyList());
        when(basicLocationRepository.getExhibitionLocationList(exhibitionTagCode))
                .thenReturn(Collections.emptyList());

        // 执行测试
        basicLocationDomainService.getLocationList(queryLocation);
        basicLocationDomainService.getExhibitionLocationList(exhibitionTagCode);

        // 验证参数正确传递
        verify(basicLocationRepository, times(1)).getLocationList(queryLocation);
        verify(basicLocationRepository, times(1)).getExhibitionLocationList(exhibitionTagCode);
    }

    /**
     * 测试方法的返回值传递
     */
    @Test
    void testMethodReturnValuePassing() {
        // 准备测试数据
        BasicLocation queryLocation = new BasicLocation();
        List<BasicLocation> repositoryResult = Arrays.asList(basicLocation);

        // Mock仓储调用
        when(basicLocationRepository.getLocationList(any(BasicLocation.class)))
                .thenReturn(repositoryResult);

        // 执行测试
        List<BasicLocation> serviceResult = basicLocationDomainService.getLocationList(queryLocation);

        // 验证返回值正确传递
        assertSame(repositoryResult, serviceResult, "服务应该直接返回仓储的结果");
    }

    @Test
    void getLocationList_shouldReturnList() {
        BasicLocation location = new BasicLocation();
        when(basicLocationRepository.getLocationList(any())).thenReturn(List.of(location));
        List<BasicLocation> result = basicLocationDomainService.getLocationList(new BasicLocation());
        assertNotNull(result);
        assertEquals(1, result.size());
        assertSame(location, result.get(0));
    }

    @Test
    void getExhibitionLocationList_shouldReturnList() {
        BasicLocation location = new BasicLocation();
        when(basicLocationRepository.getExhibitionLocationList(anyString())).thenReturn(List.of(location));
        List<BasicLocation> result = basicLocationDomainService.getExhibitionLocationList("tag1");
        assertNotNull(result);
        assertEquals(1, result.size());
        assertSame(location, result.get(0));
    }
}
