package com.dexpo.module.base.util;

import org.mockito.ArgumentCaptor;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.stubbing.OngoingStubbing;

import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

import static org.mockito.Mockito.*;

/**
 * Mockito测试工具类
 * 
 * <p>该工具类提供了常用的Mockito操作封装，简化Mock测试的编写。
 * 包含Mock对象创建、行为设置、验证等常用功能。</p>
 * 
 * <p>主要功能：</p>
 * <ul>
 *   <li>Mock对象创建和配置</li>
 *   <li>Mock行为设置</li>
 *   <li>方法调用验证</li>
 *   <li>参数捕获和验证</li>
 *   <li>静态方法Mock</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class MockitoTestUtils {

    /**
     * 创建Mock对象
     */
    public static <T> T createMock(Class<T> classToMock) {
        return mock(classToMock);
    }

    /**
     * 创建Mock对象（带名称）
     */
    public static <T> T createMock(Class<T> classToMock, String name) {
        return mock(classToMock, name);
    }

    /**
     * 创建Spy对象
     */
    public static <T> T createSpy(T object) {
        return spy(object);
    }

    /**
     * 设置Mock方法返回值
     */
    public static <T> OngoingStubbing<T> mockReturn(T methodCall, T returnValue) {
        return when(methodCall).thenReturn(returnValue);
    }

    /**
     * 设置Mock方法抛出异常
     */
    public static <T> OngoingStubbing<T> mockThrow(T methodCall, Throwable throwable) {
        return when(methodCall).thenThrow(throwable);
    }

    /**
     * 设置void方法抛出异常
     */
    public static void mockVoidThrow(Object mock, Runnable methodCall, Throwable throwable) {
        doThrow(throwable).when(mock);
        methodCall.run();
    }

    /**
     * 设置void方法什么都不做
     */
    public static void mockVoidDoNothing(Object mock, Runnable methodCall) {
        doNothing().when(mock);
        methodCall.run();
    }

    /**
     * 验证方法被调用
     */
    public static <T> T verifyCall(T mock) {
        return verify(mock);
    }

    /**
     * 验证方法被调用指定次数
     */
    public static <T> T verifyCall(T mock, int times) {
        return verify(mock, times(times));
    }

    /**
     * 验证方法从未被调用
     */
    public static <T> T verifyNeverCall(T mock) {
        return verify(mock, never());
    }

    /**
     * 验证方法至少被调用一次
     */
    public static <T> T verifyAtLeastOnceCall(T mock) {
        return verify(mock, atLeastOnce());
    }

    /**
     * 验证方法最多被调用指定次数
     */
    public static <T> T verifyAtMostCall(T mock, int maxTimes) {
        return verify(mock, atMost(maxTimes));
    }

    /**
     * 验证没有更多交互
     */
    public static void verifyNoMoreInteractions(Object... mocks) {
        Mockito.verifyNoMoreInteractions(mocks);
    }

    /**
     * 重置Mock对象
     */
    public static void resetMock(Object... mocks) {
        reset(mocks);
    }

    /**
     * 创建参数捕获器
     */
    public static <T> ArgumentCaptor<T> createCaptor(Class<T> clazz) {
        return ArgumentCaptor.forClass(clazz);
    }

    /**
     * 捕获方法参数并验证
     */
    public static <T> T captureArgument(ArgumentCaptor<T> captor) {
        return captor.capture();
    }

    /**
     * 获取捕获的参数值
     */
    public static <T> T getCapturedValue(ArgumentCaptor<T> captor) {
        return captor.getValue();
    }

    /**
     * 获取所有捕获的参数值
     */
    public static <T> List<T> getAllCapturedValues(ArgumentCaptor<T> captor) {
        return captor.getAllValues();
    }

    /**
     * Mock静态方法
     */
    public static <T> MockedStatic<T> mockStatic(Class<T> classToMock) {
        return Mockito.mockStatic(classToMock);
    }

    /**
     * Mock静态方法（带配置）
     */
    public static <T> MockedStatic<T> mockStatic(Class<T> classToMock, String name) {
        return Mockito.mockStatic(classToMock, name);
    }

    /**
     * 执行带静态Mock的测试
     */
    public static <T> void withMockedStatic(Class<T> classToMock, Runnable testCode) {
        try (MockedStatic<T> mockedStatic = mockStatic(classToMock)) {
            testCode.run();
        }
    }

    /**
     * 执行带静态Mock的测试（有返回值）
     */
    public static <T, R> R withMockedStatic(Class<T> classToMock, Supplier<R> testCode) {
        try (MockedStatic<T> mockedStatic = mockStatic(classToMock)) {
            return testCode.get();
        }
    }

    /**
     * 创建Mock数据映射
     */
    public static Map<String, Object> createMockDataMap(String... keyValuePairs) {
        if (keyValuePairs.length % 2 != 0) {
            throw new IllegalArgumentException("参数必须是偶数个，格式为key1, value1, key2, value2...");
        }
        
        Map<String, Object> mockData = new java.util.HashMap<>();
        for (int i = 0; i < keyValuePairs.length; i += 2) {
            mockData.put(keyValuePairs[i], keyValuePairs[i + 1]);
        }
        return mockData;
    }

    /**
     * 验证Mock对象是否为Mockito代理
     */
    public static boolean isMockitoMock(Object object) {
        return object != null && object.getClass().getName().contains("Mockito");
    }

    /**
     * 验证对象是否为Spy
     */
    public static boolean isMockitoSpy(Object object) {
        return object != null && Mockito.mockingDetails(object).isSpy();
    }

    /**
     * 获取Mock对象的详细信息
     */
    public static String getMockDetails(Object mock) {
        if (mock == null) {
            return "对象为null";
        }
        
        var details = Mockito.mockingDetails(mock);
        if (!details.isMock()) {
            return "对象不是Mock对象";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("Mock对象详情:\n");
        sb.append("- 类型: ").append(mock.getClass().getSimpleName()).append("\n");
        sb.append("- 是否为Mock: ").append(details.isMock()).append("\n");
        sb.append("- 是否为Spy: ").append(details.isSpy()).append("\n");
        sb.append("- 调用次数: ").append(details.getInvocations().size()).append("\n");
        
        return sb.toString();
    }

    /**
     * 清理所有Mock对象的调用记录
     */
    public static void clearInvocations(Object... mocks) {
        for (Object mock : mocks) {
            if (mock != null && Mockito.mockingDetails(mock).isMock()) {
                clearInvocations(mock);
            }
        }
    }

    /**
     * 验证Mock对象的基本功能
     */
    public static void validateMockSetup(Object... mocks) {
        for (Object mock : mocks) {
            if (mock == null) {
                throw new IllegalStateException("Mock对象不能为null");
            }
            if (!Mockito.mockingDetails(mock).isMock()) {
                throw new IllegalStateException("对象不是有效的Mock对象: " + mock.getClass());
            }
        }
    }

    /**
     * 创建测试用的异常
     */
    public static RuntimeException createTestException(String message) {
        return new RuntimeException("测试异常: " + message);
    }

    /**
     * 创建测试用的异常（带原因）
     */
    public static RuntimeException createTestException(String message, Throwable cause) {
        return new RuntimeException("测试异常: " + message, cause);
    }
}
