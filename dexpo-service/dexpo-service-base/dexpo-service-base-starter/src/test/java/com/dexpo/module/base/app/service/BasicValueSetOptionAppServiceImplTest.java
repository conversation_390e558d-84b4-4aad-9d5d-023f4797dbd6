package com.dexpo.module.base.app.service;

import com.dexpo.framework.cache.redis.constant.BasicRedisKey;
import com.dexpo.framework.cache.redis.entity.BasicValuesetOptionCache;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.module.base.api.basic.dto.BasicValuesetOptionDTO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetOptionVO;
import com.dexpo.module.base.app.converter.BasicValueSetOptionDTOConvert;
import com.dexpo.module.base.domain.model.agg.BasicValueSetOption;
import com.dexpo.module.base.domain.service.BasicValueSetOptionDomainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BasicValueSetOptionAppServiceImplTest {
    @Mock
    private RedisService redisService;
    @Mock
    private BasicValueSetOptionDomainService basicValueSetOptionDomainService;

    @InjectMocks
    private BasicValueSetOptionAppServiceImpl service;

    @BeforeEach
    void setUp() {
        BasicValueSetOptionDTOConvert mockConvert = mock(BasicValueSetOptionDTOConvert.class, Mockito.RETURNS_DEEP_STUBS);
        setFinalStatic(BasicValueSetOptionDTOConvert.class, "INSTANCE", mockConvert);
    }

    private static void setFinalStatic(Class<?> clazz, String fieldName, Object newValue) {
        try {
            java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            java.lang.reflect.Field modifiersField = java.lang.reflect.Field.class.getDeclaredField("modifiers");
            modifiersField.setAccessible(true);
            modifiersField.setInt(field, field.getModifiers() & ~java.lang.reflect.Modifier.FINAL);
            field.set(null, newValue);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void listByValuesetCodes_shouldReturnList() {
        BasicValuesetOptionCache cache = new BasicValuesetOptionCache();
        cache.setValuesetCode("code1");
        when(redisService.getCacheObject(anyString())).thenReturn(List.of(cache));
        BasicValuesetInfoVO vo = new BasicValuesetInfoVO();
        when(BasicValueSetOptionDTOConvert.INSTANCE.cache2VO(anyList())).thenReturn(List.of(new BasicValuesetOptionVO()));
        List<BasicValuesetInfoVO> result = service.listByValuesetCodes(List.of("code1"));
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    void listByValuesetCodes_shouldReturnEmpty_whenNoCache() {
        when(redisService.getCacheObject(anyString())).thenReturn(Collections.emptyList());
        List<BasicValuesetInfoVO> result = service.listByValuesetCodes(List.of("code2"));
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void listByValuesetOptionCodes_shouldReturnList() {
        BasicValuesetOptionCache cache = new BasicValuesetOptionCache();
        cache.setValuesetCode("code1");
        cache.setOptionCode("opt1");
        when(redisService.getCacheObject(anyString())).thenReturn(List.of(cache));
        BasicValuesetInfoVO vo = new BasicValuesetInfoVO();
        when(BasicValueSetOptionDTOConvert.INSTANCE.cache2VO(anyList())).thenReturn(List.of(new BasicValuesetOptionVO()));
        List<BasicValuesetInfoVO> result = service.listByValuesetOptionCodes(List.of("code1"), List.of("opt1"));
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    void initValueSetOptionCache_shouldReturnCache() {
        BasicValueSetOption option = new BasicValueSetOption();
        List<BasicValueSetOption> optionList = List.of(option);
        when(basicValueSetOptionDomainService.list()).thenReturn(optionList);
        BasicValuesetOptionCache cache = new BasicValuesetOptionCache();
        when(BasicValueSetOptionDTOConvert.INSTANCE.toBasicValuesetOptionCache(optionList)).thenReturn(List.of(cache));
        doNothing().when(redisService).setCacheObject(anyString(), anyList());
        List<BasicValuesetOptionCache> result = service.initValueSetOptionCache();
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    void getValuesetOption_shouldReturnVO() {
        BasicValuesetOptionDTO dto = new BasicValuesetOptionDTO();
        dto.setOptionCode("opt1");
        BasicValueSetOption option = new BasicValueSetOption();
        when(basicValueSetOptionDomainService.selectByOptionCode(anyString())).thenReturn(option);
        BasicValuesetOptionVO vo = new BasicValuesetOptionVO();
        when(BasicValueSetOptionDTOConvert.INSTANCE.e2v(option)).thenReturn(vo);
        BasicValuesetOptionVO result = service.getValuesetOption(dto);
        assertNotNull(result);
        assertSame(vo, result);
    }

    @Test
    void getOptionListByCodes_shouldReturnList() {
        BasicValuesetOptionCache cache = new BasicValuesetOptionCache();
        cache.setOptionCode("opt1");
        when(redisService.getCacheObject(anyString())).thenReturn(List.of(cache));
        BasicValuesetOptionVO vo = new BasicValuesetOptionVO();
        when(BasicValueSetOptionDTOConvert.INSTANCE.cache2VO(any(BasicValuesetOptionCache.class))).thenReturn(vo);
        List<BasicValuesetOptionVO> result = service.getOptionListByCodes(List.of("opt1"));
        assertNotNull(result);
    }
} 