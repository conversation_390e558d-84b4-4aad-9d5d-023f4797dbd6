package com.dexpo.module.base.domain.service;

import com.dexpo.module.base.domain.model.agg.AttachmentInfo;
import com.dexpo.module.base.domain.repository.AttachmentInfoRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * {@link AttachmentInfoDomainService} 的单元测试类
 * 
 * <p>使用Mockito进行纯Mock测试，测试附件信息领域服务的所有功能。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class AttachmentInfoDomainServiceTest {
    @Mock
    private AttachmentInfoRepository attachmentInfoRepository;
    @InjectMocks
    private AttachmentInfoDomainService service;

    @Test
    void findById_shouldReturnInfo() throws Exception {
        AttachmentInfo info = new AttachmentInfo();
        when(attachmentInfoRepository.findById(anyLong())).thenReturn(info);
        AttachmentInfo result = service.findById(1L);
        assertNotNull(result);
        assertSame(info, result);
    }

    @Test
    void createAttachment_shouldReturnInfo() {
        AttachmentInfo info = new AttachmentInfo();
        when(attachmentInfoRepository.save(any())).thenReturn(info);
        AttachmentInfo result = service.createAttachment(info);
        assertNotNull(result);
        assertSame(info, result);
    }

    @Test
    void findByIdList_shouldReturnList() {
        List<AttachmentInfo> infoList = List.of(new AttachmentInfo(), new AttachmentInfo());
        when(attachmentInfoRepository.findByIdList(anyList())).thenReturn(infoList);
        List<AttachmentInfo> result = service.findByIdList(List.of(1L, 2L));
        assertNotNull(result);
    }

    @Test
    void findFileByBusinessType_shouldReturnInfo() {
        AttachmentInfo info = new AttachmentInfo();
        when(attachmentInfoRepository.findFileByBusinessType(anyString())).thenReturn(info);
        AttachmentInfo result = service.findFileByBusinessType("type");
        assertNotNull(result);
        assertSame(info, result);
    }
}
