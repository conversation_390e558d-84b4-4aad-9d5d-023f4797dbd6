package com.dexpo.module.base.domain.service;

import com.dexpo.module.base.BaseUnitTest;
import com.dexpo.module.base.domain.model.agg.AttachmentInfo;
import com.dexpo.module.base.domain.repository.AttachmentInfoRepository;
import com.dexpo.module.base.util.MockitoTestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.mockito.Mock;
import org.mockito.InjectMocks;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * {@link AttachmentInfoDomainService} 的单元测试类
 * 
 * <p>使用Mockito进行纯Mock测试，测试附件信息领域服务的所有功能。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@DisplayName("附件信息领域服务测试")
class AttachmentInfoDomainServiceTest extends BaseUnitTest {

    @Mock
    private AttachmentInfoRepository attachmentInfoRepository;

    @InjectMocks
    private AttachmentInfoDomainService attachmentInfoDomainService;

    private AttachmentInfo attachmentInfo;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        attachmentInfo = new AttachmentInfo();
        attachmentInfo.setId(createTestId());
        attachmentInfo.setFileName("test.pdf");
        attachmentInfo.setFileSize(1024L);
        attachmentInfo.setBusinessType("CONTRACT");
        attachmentInfo.setFilePath("/uploads/test.pdf");
    }

    @Nested
    @DisplayName("根据ID查找附件测试")
    class FindByIdTests {

        @Test
        @DisplayName("根据ID查找附件 - 成功场景")
        void testFindById_Success() {
            // 准备测试数据
            Long attachmentId = createTestId();

            // Mock仓储调用
            when(attachmentInfoRepository.findById(attachmentId)).thenReturn(attachmentInfo);

            // 执行测试
            AttachmentInfo result = attachmentInfoDomainService.findById(attachmentId);

            // 验证结果
            assertNotNull(result);
            assertEquals(attachmentInfo, result);
            assertEquals(attachmentId, result.getId());
            assertEquals("test.pdf", result.getFileName());

            // 验证方法调用
            verify(attachmentInfoRepository, times(1)).findById(attachmentId);
        }

        @Test
        @DisplayName("根据ID查找附件 - 附件不存在")
        void testFindById_NotFound() {
            // 准备测试数据
            Long attachmentId = createTestId();

            // Mock仓储返回null
            when(attachmentInfoRepository.findById(attachmentId)).thenReturn(null);

            // 执行测试
            AttachmentInfo result = attachmentInfoDomainService.findById(attachmentId);

            // 验证结果
            assertNull(result);

            // 验证方法调用
            verify(attachmentInfoRepository, times(1)).findById(attachmentId);
        }

        @Test
        @DisplayName("根据ID查找附件 - null参数")
        void testFindById_NullParameter() {
            // Mock仓储调用
            when(attachmentInfoRepository.findById(null)).thenReturn(null);

            // 执行测试
            AttachmentInfo result = attachmentInfoDomainService.findById(null);

            // 验证结果
            assertNull(result);

            // 验证方法调用
            verify(attachmentInfoRepository, times(1)).findById(null);
        }
    }

    @Nested
    @DisplayName("创建附件测试")
    class CreateAttachmentTests {

        @Test
        @DisplayName("创建附件 - 成功场景")
        void testCreateAttachment_Success() {
            // Mock仓储调用
            when(attachmentInfoRepository.save(attachmentInfo)).thenReturn(attachmentInfo);

            // 执行测试
            AttachmentInfo result = attachmentInfoDomainService.createAttachment(attachmentInfo);

            // 验证结果
            assertNotNull(result);
            assertEquals(attachmentInfo, result);
            assertEquals("test.pdf", result.getFileName());
            assertEquals(1024L, result.getFileSize());

            // 验证方法调用
            verify(attachmentInfoRepository, times(1)).save(attachmentInfo);
        }

        @Test
        @DisplayName("创建附件 - 新附件ID生成")
        void testCreateAttachment_NewIdGenerated() {
            // 准备新附件数据
            AttachmentInfo newAttachment = new AttachmentInfo();
            newAttachment.setFileName("new.pdf");
            newAttachment.setFileSize(2048L);
            newAttachment.setBusinessType("INVOICE");

            AttachmentInfo savedAttachment = new AttachmentInfo();
            savedAttachment.setId(createRandomId());
            savedAttachment.setFileName("new.pdf");
            savedAttachment.setFileSize(2048L);
            savedAttachment.setBusinessType("INVOICE");

            // Mock仓储调用
            when(attachmentInfoRepository.save(newAttachment)).thenReturn(savedAttachment);

            // 执行测试
            AttachmentInfo result = attachmentInfoDomainService.createAttachment(newAttachment);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getId());
            assertEquals("new.pdf", result.getFileName());

            // 验证方法调用
            verify(attachmentInfoRepository, times(1)).save(newAttachment);
        }
    }

    @Nested
    @DisplayName("批量查找附件测试")
    class FindByIdListTests {

        @Test
        @DisplayName("批量查找附件 - 成功场景")
        void testFindByIdList_Success() {
            // 准备测试数据
            List<Long> idList = Arrays.asList(1L, 2L, 3L);
            
            AttachmentInfo attachment2 = new AttachmentInfo();
            attachment2.setId(2L);
            attachment2.setFileName("test2.pdf");
            
            AttachmentInfo attachment3 = new AttachmentInfo();
            attachment3.setId(3L);
            attachment3.setFileName("test3.pdf");
            
            List<AttachmentInfo> expectedList = Arrays.asList(attachmentInfo, attachment2, attachment3);

            // Mock仓储调用
            when(attachmentInfoRepository.findByIdList(idList)).thenReturn(expectedList);

            // 执行测试
            List<AttachmentInfo> result = attachmentInfoDomainService.findByIdList(idList);

            // 验证结果
            assertNotNull(result);
            assertEquals(3, result.size());
            assertEquals(expectedList, result);

            // 验证方法调用
            verify(attachmentInfoRepository, times(1)).findByIdList(idList);
        }

        @Test
        @DisplayName("批量查找附件 - 空列表")
        void testFindByIdList_EmptyList() {
            // 准备测试数据
            List<Long> emptyIdList = Collections.emptyList();
            List<AttachmentInfo> emptyResultList = Collections.emptyList();

            // Mock仓储调用
            when(attachmentInfoRepository.findByIdList(emptyIdList)).thenReturn(emptyResultList);

            // 执行测试
            List<AttachmentInfo> result = attachmentInfoDomainService.findByIdList(emptyIdList);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());

            // 验证方法调用
            verify(attachmentInfoRepository, times(1)).findByIdList(emptyIdList);
        }

        @Test
        @DisplayName("批量查找附件 - 部分存在")
        void testFindByIdList_PartialExists() {
            // 准备测试数据
            List<Long> idList = Arrays.asList(1L, 999L, 3L); // 999L不存在
            List<AttachmentInfo> partialResultList = Arrays.asList(attachmentInfo); // 只返回存在的

            // Mock仓储调用
            when(attachmentInfoRepository.findByIdList(idList)).thenReturn(partialResultList);

            // 执行测试
            List<AttachmentInfo> result = attachmentInfoDomainService.findByIdList(idList);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(partialResultList, result);

            // 验证方法调用
            verify(attachmentInfoRepository, times(1)).findByIdList(idList);
        }
    }

    @Nested
    @DisplayName("根据业务类型查找附件测试")
    class FindFileByBusinessTypeTests {

        @Test
        @DisplayName("根据业务类型查找附件 - 成功场景")
        void testFindFileByBusinessType_Success() {
            // 准备测试数据
            String businessType = "CONTRACT";

            // Mock仓储调用
            when(attachmentInfoRepository.findFileByBusinessType(businessType))
                    .thenReturn(attachmentInfo);

            // 执行测试
            AttachmentInfo result = attachmentInfoDomainService.findFileByBusinessType(businessType);

            // 验证结果
            assertNotNull(result);
            assertEquals(attachmentInfo, result);
            assertEquals(businessType, result.getBusinessType());

            // 验证方法调用
            verify(attachmentInfoRepository, times(1)).findFileByBusinessType(businessType);
        }

        @Test
        @DisplayName("根据业务类型查找附件 - 类型不存在")
        void testFindFileByBusinessType_TypeNotFound() {
            // 准备测试数据
            String businessType = "NON_EXISTENT";

            // Mock仓储返回null
            when(attachmentInfoRepository.findFileByBusinessType(businessType))
                    .thenReturn(null);

            // 执行测试
            AttachmentInfo result = attachmentInfoDomainService.findFileByBusinessType(businessType);

            // 验证结果
            assertNull(result);

            // 验证方法调用
            verify(attachmentInfoRepository, times(1)).findFileByBusinessType(businessType);
        }

        @Test
        @DisplayName("根据业务类型查找附件 - null参数")
        void testFindFileByBusinessType_NullParameter() {
            // Mock仓储调用
            when(attachmentInfoRepository.findFileByBusinessType(null)).thenReturn(null);

            // 执行测试
            AttachmentInfo result = attachmentInfoDomainService.findFileByBusinessType(null);

            // 验证结果
            assertNull(result);

            // 验证方法调用
            verify(attachmentInfoRepository, times(1)).findFileByBusinessType(null);
        }
    }

    @Nested
    @DisplayName("异常处理测试")
    class ExceptionHandlingTests {

        @Test
        @DisplayName("findById方法异常处理")
        void testFindById_ExceptionHandling() {
            // 准备测试数据
            Long attachmentId = createTestId();

            // Mock仓储抛出异常
            when(attachmentInfoRepository.findById(attachmentId))
                    .thenThrow(new RuntimeException("Repository error"));

            // 执行测试并验证异常传播
            assertThrows(RuntimeException.class, () -> {
                attachmentInfoDomainService.findById(attachmentId);
            });

            // 验证方法调用
            verify(attachmentInfoRepository, times(1)).findById(attachmentId);
        }

        @Test
        @DisplayName("createAttachment方法异常处理")
        void testCreateAttachment_ExceptionHandling() {
            // Mock仓储抛出异常
            when(attachmentInfoRepository.save(attachmentInfo))
                    .thenThrow(new RuntimeException("Save error"));

            // 执行测试并验证异常传播
            assertThrows(RuntimeException.class, () -> {
                attachmentInfoDomainService.createAttachment(attachmentInfo);
            });

            // 验证方法调用
            verify(attachmentInfoRepository, times(1)).save(attachmentInfo);
        }
    }

    @Test
    @DisplayName("验证服务依赖注入")
    void testDependencyInjection() {
        // 验证服务实例不为空
        assertNotNull(attachmentInfoDomainService);
        
        // 验证仓储依赖已正确注入
        assertTrue(MockitoTestUtils.isMockitoMock(attachmentInfoRepository));
        
        // 验证服务可以正常调用
        when(attachmentInfoRepository.findById(anyLong())).thenReturn(null);
        
        assertDoesNotThrow(() -> {
            attachmentInfoDomainService.findById(1L);
        });
    }

    @Test
    @DisplayName("验证服务类注解")
    void testServiceAnnotations() {
        // 验证类上的注解
        assertTrue(AttachmentInfoDomainService.class.isAnnotationPresent(
                org.springframework.stereotype.Service.class));
        assertTrue(AttachmentInfoDomainService.class.isAnnotationPresent(
                lombok.AllArgsConstructor.class));
    }

    @Test
    @DisplayName("验证@SneakyThrows注解功能")
    void testSneakyThrowsAnnotation() {
        // 验证@SneakyThrows注解的存在
        try {
            var method = AttachmentInfoDomainService.class.getMethod("findById", Long.class);
            assertTrue(method.isAnnotationPresent(lombok.SneakyThrows.class));
        } catch (NoSuchMethodException e) {
            fail("findById方法应该存在");
        }
    }

    @Test
    @DisplayName("验证方法返回值传递")
    void testMethodReturnValuePassing() {
        // 准备测试数据
        Long testId = createTestId();

        // Mock仓储调用
        when(attachmentInfoRepository.findById(testId)).thenReturn(attachmentInfo);

        // 执行测试
        AttachmentInfo serviceResult = attachmentInfoDomainService.findById(testId);

        // 验证返回值正确传递
        assertSame(attachmentInfo, serviceResult, "服务应该直接返回仓储的结果");
    }
}
