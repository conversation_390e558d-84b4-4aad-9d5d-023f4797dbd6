package com.dexpo.module.base;

import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * 基础单元测试类
 *
 * <p>所有单元测试类的基类，提供通用的测试配置和工具方法。
 * 使用Mockito进行Mock对象管理，确保测试的独立性。</p>
 *
 * <p>主要特性：</p>
 * <ul>
 *   <li>集成Mockito扩展，支持@Mock、@InjectMocks等注解</li>
 *   <li>提供通用的测试数据创建方法</li>
 *   <li>提供测试工具方法</li>
 *   <li>激活test配置文件</li>
 * </ul>
 *
 * <p>使用方式：</p>
 * <pre>
 * {@code
 * @ExtendWith(MockitoExtension.class)
 * class MyServiceTest extends BaseUnitTest {
 *
 *     @Mock
 *     private MyRepository myRepository;
 *
 *     @InjectMocks
 *     private MyService myService;
 *
 *     @Test
 *     void testMethod() {
 *         // 使用基类提供的工具方法
 *         Long testId = createTestId();
 *         String testString = createTestString("prefix");
 *
 *         // Mock行为
 *         when(myRepository.findById(testId)).thenReturn(someObject);
 *
 *         // 执行测试
 *         // ...
 *     }
 * }
 * }
 * </pre>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@ActiveProfiles("test")
public abstract class BaseUnitTest {

    private static final Random RANDOM = new Random();

    // ==================== 基础数据创建方法 ====================

    /**
     * 创建测试用的Long类型ID
     */
    protected Long createTestId() {
        return 1L;
    }

    /**
     * 创建随机的Long类型ID
     */
    protected Long createRandomId() {
        return RANDOM.nextLong(1, 10000);
    }

    /**
     * 创建测试用的字符串
     */
    protected String createTestString(String prefix) {
        return prefix + "_test";
    }

    /**
     * 创建随机字符串
     */
    protected String createRandomString(String prefix) {
        return prefix + "_" + RANDOM.nextInt(1000);
    }

    /**
     * 创建测试用的布尔值
     */
    protected Boolean createTestBoolean() {
        return true;
    }

    /**
     * 创建随机布尔值
     */
    protected Boolean createRandomBoolean() {
        return RANDOM.nextBoolean();
    }

    // ==================== 时间相关方法 ====================

    /**
     * 创建当前时间
     */
    protected LocalDateTime createCurrentTime() {
        return LocalDateTime.now();
    }

    /**
     * 创建过去的时间
     */
    protected LocalDateTime createPastTime() {
        return LocalDateTime.now().minusDays(1);
    }

    /**
     * 创建未来的时间
     */
    protected LocalDateTime createFutureTime() {
        return LocalDateTime.now().plusDays(1);
    }

    // ==================== 集合相关方法 ====================

    /**
     * 创建测试用的字符串列表
     */
    protected List<String> createTestStringList() {
        return Arrays.asList("test1", "test2", "test3");
    }

    /**
     * 创建测试用的ID列表
     */
    protected List<Long> createTestIdList() {
        return Arrays.asList(1L, 2L, 3L);
    }

    // ==================== 验证工具方法 ====================

    /**
     * 验证字符串不为空
     */
    protected boolean isNotBlank(String str) {
        return str != null && !str.trim().isEmpty();
    }

    /**
     * 验证对象不为空
     */
    protected boolean isNotNull(Object obj) {
        return obj != null;
    }

    /**
     * 验证列表不为空
     */
    protected boolean isNotEmpty(List<?> list) {
        return list != null && !list.isEmpty();
    }

    // ==================== 测试数据清理方法 ====================

    /**
     * 清理测试数据（子类可以重写）
     */
    protected void cleanupTestData() {
        // 默认实现为空，子类可以根据需要重写
    }
}
