package com.dexpo.module.base.domain.service;

import com.dexpo.framework.common.pojo.PageParam;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.base.domain.model.agg.CommonTodo;
import com.dexpo.module.base.domain.repository.CommonTodoRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CommonTodoDomainServiceTest {
    @Mock
    private CommonTodoRepository commonTodoRepository;
    @InjectMocks
    private CommonTodoDomainService service;

    @Test
    void getPage_shouldReturnResult() {
        CommonTodo todo = new CommonTodo();
        PageParam param = new PageParam();
        PageResult<CommonTodo> pageResult = new PageResult<>(List.of(todo), 1L);
        when(commonTodoRepository.getPage(any(), any())).thenReturn(pageResult);
        PageResult<CommonTodo> result = service.getPage(todo, param);
        assertNotNull(result);
        assertEquals(1, result.getTotal());
    }

    @Test
    void save_shouldReturnSaved() {
        CommonTodo todo = new CommonTodo();
        when(commonTodoRepository.save(any())).thenReturn(todo);
        CommonTodo result = service.save(todo);
        assertNotNull(result);
        assertSame(todo, result);
    }

    @Test
    void selectOneByCommonTodo_shouldReturnOne() {
        CommonTodo todo = new CommonTodo();
        when(commonTodoRepository.selectOneByCommonTodo(any())).thenReturn(todo);
        CommonTodo result = service.selectOneByCommonTodo(todo);
        assertNotNull(result);
        assertSame(todo, result);
    }

    @Test
    void updateById_shouldCallRepository() {
        CommonTodo todo = new CommonTodo();
        doNothing().when(commonTodoRepository).updateById(any());
        service.updateById(todo);
        verify(commonTodoRepository, times(1)).updateById(todo);
    }
} 