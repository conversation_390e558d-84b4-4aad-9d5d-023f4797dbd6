package com.dexpo.module.base.domain.service;

import com.dexpo.module.base.domain.model.agg.BasicRegion;
import com.dexpo.module.base.domain.repository.BasicRegionRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * {@link BasicRegionDomainService} 的单元测试类
 * 
 * <p>使用Mockito进行纯Mock测试，测试行政区域领域服务的所有功能。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class BasicRegionDomainServiceTest {
    @Mock
    private BasicRegionRepository basicRegionRepository;
    @InjectMocks
    private BasicRegionDomainService service;

    @Test
    void getRegionList_shouldReturnList() {
        BasicRegion region = new BasicRegion();
        when(basicRegionRepository.getRegionList(any())).thenReturn(List.of(region));
        List<BasicRegion> result = service.getRegionList(new BasicRegion());
        assertNotNull(result);
        assertEquals(1, result.size());
        assertSame(region, result.get(0));
    }

    @Test
    void selectList_shouldReturnList() {
        BasicRegion region = new BasicRegion();
        when(basicRegionRepository.selectList()).thenReturn(List.of(region));
        List<BasicRegion> result = service.selectList();
        assertNotNull(result);
        assertEquals(1, result.size());
        assertSame(region, result.get(0));
    }
}
