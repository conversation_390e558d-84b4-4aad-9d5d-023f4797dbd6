package com.dexpo.module.base.domain.service;

import com.dexpo.module.base.BaseUnitTest;
import com.dexpo.module.base.domain.model.agg.BasicRegion;
import com.dexpo.module.base.domain.repository.BasicRegionRepository;
import com.dexpo.module.base.util.MockitoTestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.mockito.Mock;
import org.mockito.InjectMocks;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * {@link BasicRegionDomainService} 的单元测试类
 * 
 * <p>使用Mockito进行纯Mock测试，测试行政区域领域服务的所有功能。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@DisplayName("行政区域领域服务测试")
class BasicRegionDomainServiceTest extends BaseUnitTest {

    @Mock
    private BasicRegionRepository basicRegionRepository;

    @InjectMocks
    private BasicRegionDomainService basicRegionDomainService;

    private BasicRegion basicRegion;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        basicRegion = new BasicRegion();
        basicRegion.setId(createTestId());
        basicRegion.setAdcode("110000");
        basicRegion.setName("北京市");
        basicRegion.setLevel("province");
        basicRegion.setParentAdcode("100000");
    }

    @Nested
    @DisplayName("根据条件查询区域列表测试")
    class GetRegionListTests {

        @Test
        @DisplayName("根据条件查询区域列表 - 成功场景")
        void testGetRegionList_Success() {
            // 准备测试数据
            List<BasicRegion> expectedList = Arrays.asList(basicRegion);

            // Mock仓储调用
            when(basicRegionRepository.getRegionList(any(BasicRegion.class)))
                    .thenReturn(expectedList);

            // 执行测试
            List<BasicRegion> result = basicRegionDomainService.getRegionList(basicRegion);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(expectedList, result);
            assertEquals(basicRegion, result.get(0));

            // 验证方法调用
            verify(basicRegionRepository, times(1)).getRegionList(basicRegion);
        }

        @Test
        @DisplayName("根据条件查询区域列表 - 空结果")
        void testGetRegionList_EmptyResult() {
            // 准备测试数据
            List<BasicRegion> emptyList = Collections.emptyList();

            // Mock仓储调用
            when(basicRegionRepository.getRegionList(any(BasicRegion.class)))
                    .thenReturn(emptyList);

            // 执行测试
            List<BasicRegion> result = basicRegionDomainService.getRegionList(basicRegion);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());

            // 验证方法调用
            verify(basicRegionRepository, times(1)).getRegionList(basicRegion);
        }

        @Test
        @DisplayName("根据条件查询区域列表 - 多个结果")
        void testGetRegionList_MultipleResults() {
            // 准备测试数据
            BasicRegion region2 = new BasicRegion();
            region2.setId(2L);
            region2.setAdcode("120000");
            region2.setName("天津市");
            region2.setLevel("province");
            region2.setParentAdcode("100000");

            BasicRegion region3 = new BasicRegion();
            region3.setId(3L);
            region3.setAdcode("130000");
            region3.setName("河北省");
            region3.setLevel("province");
            region3.setParentAdcode("100000");

            List<BasicRegion> expectedList = Arrays.asList(basicRegion, region2, region3);

            // Mock仓储调用
            when(basicRegionRepository.getRegionList(any(BasicRegion.class)))
                    .thenReturn(expectedList);

            // 执行测试
            List<BasicRegion> result = basicRegionDomainService.getRegionList(basicRegion);

            // 验证结果
            assertNotNull(result);
            assertEquals(3, result.size());
            assertEquals(expectedList, result);

            // 验证方法调用
            verify(basicRegionRepository, times(1)).getRegionList(basicRegion);
        }

        @Test
        @DisplayName("根据条件查询区域列表 - null参数")
        void testGetRegionList_NullParameter() {
            // Mock仓储调用
            when(basicRegionRepository.getRegionList(null))
                    .thenReturn(Collections.emptyList());

            // 执行测试
            List<BasicRegion> result = basicRegionDomainService.getRegionList(null);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());

            // 验证方法调用
            verify(basicRegionRepository, times(1)).getRegionList(null);
        }
    }

    @Nested
    @DisplayName("查询所有区域列表测试")
    class SelectListTests {

        @Test
        @DisplayName("查询所有区域列表 - 成功场景")
        void testSelectList_Success() {
            // 准备测试数据
            BasicRegion region1 = createTestRegion("110000", "北京市", "province");
            BasicRegion region2 = createTestRegion("110100", "北京市市辖区", "city");
            BasicRegion region3 = createTestRegion("110101", "东城区", "district");

            List<BasicRegion> expectedList = Arrays.asList(region1, region2, region3);

            // Mock仓储调用
            when(basicRegionRepository.selectList()).thenReturn(expectedList);

            // 执行测试
            List<BasicRegion> result = basicRegionDomainService.selectList();

            // 验证结果
            assertNotNull(result);
            assertEquals(3, result.size());
            assertEquals(expectedList, result);

            // 验证层级结构
            assertTrue(result.stream().anyMatch(r -> "province".equals(r.getLevel())));
            assertTrue(result.stream().anyMatch(r -> "city".equals(r.getLevel())));
            assertTrue(result.stream().anyMatch(r -> "district".equals(r.getLevel())));

            // 验证方法调用
            verify(basicRegionRepository, times(1)).selectList();
        }

        @Test
        @DisplayName("查询所有区域列表 - 空结果")
        void testSelectList_EmptyResult() {
            // Mock仓储调用
            when(basicRegionRepository.selectList()).thenReturn(Collections.emptyList());

            // 执行测试
            List<BasicRegion> result = basicRegionDomainService.selectList();

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());

            // 验证方法调用
            verify(basicRegionRepository, times(1)).selectList();
        }

        @Test
        @DisplayName("查询所有区域列表 - 大量数据")
        void testSelectList_LargeDataSet() {
            // 准备大量测试数据
            List<BasicRegion> largeList = createLargeRegionList(100);

            // Mock仓储调用
            when(basicRegionRepository.selectList()).thenReturn(largeList);

            // 执行测试
            List<BasicRegion> result = basicRegionDomainService.selectList();

            // 验证结果
            assertNotNull(result);
            assertEquals(100, result.size());
            assertEquals(largeList, result);

            // 验证方法调用
            verify(basicRegionRepository, times(1)).selectList();
        }
    }

    @Nested
    @DisplayName("异常处理测试")
    class ExceptionHandlingTests {

        @Test
        @DisplayName("getRegionList方法异常处理")
        void testGetRegionList_ExceptionHandling() {
            // Mock仓储抛出异常
            when(basicRegionRepository.getRegionList(any(BasicRegion.class)))
                    .thenThrow(new RuntimeException("Repository error"));

            // 执行测试并验证异常传播
            assertThrows(RuntimeException.class, () -> {
                basicRegionDomainService.getRegionList(basicRegion);
            });

            // 验证方法调用
            verify(basicRegionRepository, times(1)).getRegionList(basicRegion);
        }

        @Test
        @DisplayName("selectList方法异常处理")
        void testSelectList_ExceptionHandling() {
            // Mock仓储抛出异常
            when(basicRegionRepository.selectList())
                    .thenThrow(new RuntimeException("Repository error"));

            // 执行测试并验证异常传播
            assertThrows(RuntimeException.class, () -> {
                basicRegionDomainService.selectList();
            });

            // 验证方法调用
            verify(basicRegionRepository, times(1)).selectList();
        }
    }

    @Test
    @DisplayName("验证服务依赖注入")
    void testDependencyInjection() {
        // 验证服务实例不为空
        assertNotNull(basicRegionDomainService);
        
        // 验证仓储依赖已正确注入
        assertTrue(MockitoTestUtils.isMockitoMock(basicRegionRepository));
        
        // 验证服务可以正常调用
        when(basicRegionRepository.selectList()).thenReturn(Collections.emptyList());
        
        assertDoesNotThrow(() -> {
            basicRegionDomainService.selectList();
        });
    }

    @Test
    @DisplayName("验证服务类注解")
    void testServiceAnnotations() {
        // 验证类上的注解
        assertTrue(BasicRegionDomainService.class.isAnnotationPresent(
                org.springframework.stereotype.Service.class));
        assertTrue(BasicRegionDomainService.class.isAnnotationPresent(
                lombok.RequiredArgsConstructor.class));
    }

    @Test
    @DisplayName("验证方法参数传递")
    void testMethodParameterPassing() {
        // 准备测试数据
        BasicRegion testRegion = createTestRegion("test", "测试区域", "test");

        // Mock仓储调用
        when(basicRegionRepository.getRegionList(testRegion))
                .thenReturn(Collections.emptyList());

        // 执行测试
        basicRegionDomainService.getRegionList(testRegion);

        // 验证参数正确传递
        verify(basicRegionRepository, times(1)).getRegionList(testRegion);
    }

    @Test
    @DisplayName("验证方法返回值传递")
    void testMethodReturnValuePassing() {
        // 准备测试数据
        List<BasicRegion> repositoryResult = Arrays.asList(basicRegion);

        // Mock仓储调用
        when(basicRegionRepository.getRegionList(any(BasicRegion.class)))
                .thenReturn(repositoryResult);

        // 执行测试
        List<BasicRegion> serviceResult = basicRegionDomainService.getRegionList(basicRegion);

        // 验证返回值正确传递
        assertSame(repositoryResult, serviceResult, "服务应该直接返回仓储的结果");
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建测试用的区域对象
     */
    private BasicRegion createTestRegion(String adcode, String name, String level) {
        BasicRegion region = new BasicRegion();
        region.setId(createRandomId());
        region.setAdcode(adcode);
        region.setName(name);
        region.setLevel(level);
        return region;
    }

    /**
     * 创建大量测试区域数据
     */
    private List<BasicRegion> createLargeRegionList(int size) {
        List<BasicRegion> list = new java.util.ArrayList<>();
        for (int i = 1; i <= size; i++) {
            BasicRegion region = createTestRegion(
                String.format("%06d", i),
                "测试区域" + i,
                i % 3 == 0 ? "province" : (i % 3 == 1 ? "city" : "district")
            );
            list.add(region);
        }
        return list;
    }
}
