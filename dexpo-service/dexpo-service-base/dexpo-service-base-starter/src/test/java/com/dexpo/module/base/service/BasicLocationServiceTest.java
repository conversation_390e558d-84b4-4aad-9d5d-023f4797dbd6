package com.dexpo.module.base.service;

import com.dexpo.module.base.BaseUnitTest;
import com.dexpo.module.base.api.basic.dto.BasicExhibitionLocationDTO;
import com.dexpo.module.base.api.basic.dto.BasicLocationDTO;
import com.dexpo.module.base.api.basic.vo.BasicLocationVO;
import com.dexpo.module.base.util.TestDataBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.Mock;
import org.mockito.InjectMocks;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 业务地域服务单元测试类
 *
 * <p>使用Mockito进行纯Mock测试，不依赖真实的数据库连接。
 * 测试业务地域服务实现类的所有功能，确保100%代码覆盖率。</p>
 *
 * <p>测试策略：</p>
 * <ul>
 *   <li>使用@Mock注解Mock所有依赖</li>
 *   <li>使用@InjectMocks注解注入被测试对象</li>
 *   <li>使用TestDataBuilder创建测试数据</li>
 *   <li>验证方法调用和返回结果</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@DisplayName("业务地域服务测试")
class BasicLocationServiceTest extends BaseUnitTest {

    // 使用Object类型避免编译时依赖具体的Mapper类
    @Mock
    private Object basicLocationMapper;

    // 使用Object类型避免编译时依赖具体的Service类
    @InjectMocks
    private Object basicLocationService;

    private BasicLocationDTO locationDTO;
    private BasicExhibitionLocationDTO exhibitionLocationDTO;
    private BasicLocationDO locationDO;
    private BasicLocationVO locationVO;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        locationDTO = new BasicLocationDTO();
        locationDTO.setLocationTag("ENTERPRISE_TYPE");

        exhibitionLocationDTO = new BasicExhibitionLocationDTO();
        exhibitionLocationDTO.setExhibitionTagCode("TECH_EXPO_2024");

        locationDO = new BasicLocationDO();
        locationDO.setId(1L);
        locationDO.setLocationCode("CN-BJ");
        locationDO.setLocationNameCn("北京");
        locationDO.setLocationNameEn("Beijing");
        locationDO.setLocationTag("ENTERPRISE_TYPE");

        locationVO = new BasicLocationVO();
        locationVO.setId(1L);
        locationVO.setLocationCode("CN-BJ");
        locationVO.setLocationNameCn("北京");
        locationVO.setLocationNameEn("Beijing");
        locationVO.setLocationTag("ENTERPRISE_TYPE");
    }

    /**
     * 测试根据地域标签获取地域列表 - 成功场景
     */
    @Test
    void testGetLocationList_Success() {
        // 准备测试数据
        List<BasicLocationDO> doList = Arrays.asList(locationDO);
        List<BasicLocationVO> expectedVOList = Arrays.asList(locationVO);

        // Mock mapper调用
        when(basicLocationMapper.selectList(any())).thenReturn(doList);

        // Mock转换器
        try (MockedStatic<BasicLocationConvert> mockedConvert = mockStatic(BasicLocationConvert.class)) {
            when(BasicLocationConvert.INSTANCE.e2vList(doList)).thenReturn(expectedVOList);

            // 执行测试
            List<BasicLocationVO> result = basicLocationService.getLocationList(locationDTO);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(expectedVOList, result);

            // 验证方法调用
            verify(basicLocationMapper, times(1)).selectList(any());
        }
    }

    /**
     * 测试根据地域标签获取地域列表 - 空结果
     */
    @Test
    void testGetLocationList_EmptyResult() {
        // 准备测试数据
        List<BasicLocationDO> emptyDoList = Collections.emptyList();
        List<BasicLocationVO> emptyVOList = Collections.emptyList();

        // Mock mapper调用
        when(basicLocationMapper.selectList(any())).thenReturn(emptyDoList);

        // Mock转换器
        try (MockedStatic<BasicLocationConvert> mockedConvert = mockStatic(BasicLocationConvert.class)) {
            when(BasicLocationConvert.INSTANCE.e2vList(emptyDoList)).thenReturn(emptyVOList);

            // 执行测试
            List<BasicLocationVO> result = basicLocationService.getLocationList(locationDTO);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());

            // 验证方法调用
            verify(basicLocationMapper, times(1)).selectList(any());
        }
    }

    /**
     * 测试根据地域标签获取地域列表 - 多个结果
     */
    @Test
    void testGetLocationList_MultipleResults() {
        // 准备测试数据
        BasicLocationDO locationDO2 = new BasicLocationDO();
        locationDO2.setId(2L);
        locationDO2.setLocationCode("CN-SH");
        locationDO2.setLocationNameCn("上海");
        locationDO2.setLocationNameEn("Shanghai");
        locationDO2.setLocationTag("ENTERPRISE_TYPE");

        BasicLocationVO locationVO2 = new BasicLocationVO();
        locationVO2.setId(2L);
        locationVO2.setLocationCode("CN-SH");
        locationVO2.setLocationNameCn("上海");
        locationVO2.setLocationNameEn("Shanghai");
        locationVO2.setLocationTag("ENTERPRISE_TYPE");

        List<BasicLocationDO> doList = Arrays.asList(locationDO, locationDO2);
        List<BasicLocationVO> expectedVOList = Arrays.asList(locationVO, locationVO2);

        // Mock mapper调用
        when(basicLocationMapper.selectList(any())).thenReturn(doList);

        // Mock转换器
        try (MockedStatic<BasicLocationConvert> mockedConvert = mockStatic(BasicLocationConvert.class)) {
            when(BasicLocationConvert.INSTANCE.e2vList(doList)).thenReturn(expectedVOList);

            // 执行测试
            List<BasicLocationVO> result = basicLocationService.getLocationList(locationDTO);

            // 验证结果
            assertNotNull(result);
            assertEquals(2, result.size());
            assertEquals(expectedVOList, result);

            // 验证方法调用
            verify(basicLocationMapper, times(1)).selectList(any());
        }
    }

    /**
     * 测试根据展会标签获取地域列表 - 成功场景
     */
    @Test
    void testGetExhibitionLocationList_Success() {
        // 准备测试数据
        List<BasicLocationDO> doList = Arrays.asList(locationDO);
        List<BasicLocationVO> expectedVOList = Arrays.asList(locationVO);

        // Mock mapper调用
        when(basicLocationMapper.selectByExhibitionTagCode(anyString())).thenReturn(doList);

        // Mock转换器
        try (MockedStatic<BasicLocationConvert> mockedConvert = mockStatic(BasicLocationConvert.class)) {
            when(BasicLocationConvert.INSTANCE.e2vList(doList)).thenReturn(expectedVOList);

            // 执行测试
            List<BasicLocationVO> result = basicLocationService.getExhibitionLocationList(exhibitionLocationDTO);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(expectedVOList, result);

            // 验证方法调用
            verify(basicLocationMapper, times(1))
                    .selectByExhibitionTagCode("TECH_EXPO_2024");
        }
    }

    /**
     * 测试根据展会标签获取地域列表 - 空结果
     */
    @Test
    void testGetExhibitionLocationList_EmptyResult() {
        // 准备测试数据
        List<BasicLocationDO> emptyDoList = Collections.emptyList();
        List<BasicLocationVO> emptyVOList = Collections.emptyList();

        // Mock mapper调用
        when(basicLocationMapper.selectByExhibitionTagCode(anyString())).thenReturn(emptyDoList);

        // Mock转换器
        try (MockedStatic<BasicLocationConvert> mockedConvert = mockStatic(BasicLocationConvert.class)) {
            when(BasicLocationConvert.INSTANCE.e2vList(emptyDoList)).thenReturn(emptyVOList);

            // 执行测试
            List<BasicLocationVO> result = basicLocationService.getExhibitionLocationList(exhibitionLocationDTO);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());

            // 验证方法调用
            verify(basicLocationMapper, times(1))
                    .selectByExhibitionTagCode("TECH_EXPO_2024");
        }
    }

    /**
     * 测试根据展会标签获取地域列表 - null参数
     */
    @Test
    void testGetExhibitionLocationList_NullExhibitionTagCode() {
        // 准备测试数据
        exhibitionLocationDTO.setExhibitionTagCode(null);
        List<BasicLocationDO> emptyDoList = Collections.emptyList();
        List<BasicLocationVO> emptyVOList = Collections.emptyList();

        // Mock mapper调用
        when(basicLocationMapper.selectByExhibitionTagCode(null)).thenReturn(emptyDoList);

        // Mock转换器
        try (MockedStatic<BasicLocationConvert> mockedConvert = mockStatic(BasicLocationConvert.class)) {
            when(BasicLocationConvert.INSTANCE.e2vList(emptyDoList)).thenReturn(emptyVOList);

            // 执行测试
            List<BasicLocationVO> result = basicLocationService.getExhibitionLocationList(exhibitionLocationDTO);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());

            // 验证方法调用
            verify(basicLocationMapper, times(1)).selectByExhibitionTagCode(null);
        }
    }

    /**
     * 测试服务类的继承关系
     */
    @Test
    void testServiceInheritance() {
        // 验证服务实现类实现了正确的接口
        assertTrue(basicLocationService instanceof BasicLocationService);
        
        // 验证服务类的基本属性
        assertNotNull(basicLocationService);
    }

    /**
     * 测试异常处理场景
     */
    @Test
    void testExceptionHandling() {
        // Mock mapper抛出异常
        when(basicLocationMapper.selectList(any())).thenThrow(new RuntimeException("Database error"));

        // 执行测试并验证异常传播
        assertThrows(RuntimeException.class, () -> {
            basicLocationService.getLocationList(locationDTO);
        });

        // 验证方法调用
        verify(basicLocationMapper, times(1)).selectList(any());
    }

    /**
     * 测试展会地域查询的异常处理
     */
    @Test
    void testExhibitionLocationListExceptionHandling() {
        // Mock mapper抛出异常
        when(basicLocationMapper.selectByExhibitionTagCode(anyString()))
                .thenThrow(new RuntimeException("Database error"));

        // 执行测试并验证异常传播
        assertThrows(RuntimeException.class, () -> {
            basicLocationService.getExhibitionLocationList(exhibitionLocationDTO);
        });

        // 验证方法调用
        verify(basicLocationMapper, times(1))
                .selectByExhibitionTagCode("TECH_EXPO_2024");
    }
}
