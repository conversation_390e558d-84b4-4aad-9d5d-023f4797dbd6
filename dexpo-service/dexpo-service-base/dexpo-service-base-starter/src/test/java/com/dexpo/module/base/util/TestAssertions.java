package com.dexpo.module.base.util;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.base.api.basic.vo.BasicLocationVO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;
import com.dexpo.module.base.dal.dataobject.basic.BasicLocationDO;
import com.dexpo.module.base.domain.model.agg.BasicLocation;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试断言工具类
 * 
 * <p>该工具类提供了专门用于测试的断言方法，
 * 简化测试代码的编写，提高测试的可读性和可维护性。</p>
 * 
 * <p>主要功能：</p>
 * <ul>
 *   <li>对象属性的深度比较</li>
 *   <li>集合数据的批量验证</li>
 *   <li>业务规则的专项验证</li>
 *   <li>通用响应格式的验证</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class TestAssertions {

    /**
     * 验证BasicLocationDO对象的完整性
     */
    public static void assertBasicLocationDO(BasicLocationDO expected, BasicLocationDO actual) {
        assertNotNull(actual, "BasicLocationDO对象不能为空");
        assertEquals(expected.getId(), actual.getId(), "ID不匹配");
        assertEquals(expected.getLocationCode(), actual.getLocationCode(), "地域编码不匹配");
        assertEquals(expected.getLocationNameCn(), actual.getLocationNameCn(), "中文名称不匹配");
        assertEquals(expected.getLocationNameEn(), actual.getLocationNameEn(), "英文名称不匹配");
        assertEquals(expected.getLocationTag(), actual.getLocationTag(), "地域标签不匹配");
    }

    /**
     * 验证BasicLocation领域对象的完整性
     */
    public static void assertBasicLocation(BasicLocation expected, BasicLocation actual) {
        assertNotNull(actual, "BasicLocation对象不能为空");
        assertEquals(expected.getId(), actual.getId(), "ID不匹配");
        assertEquals(expected.getLocationCode(), actual.getLocationCode(), "地域编码不匹配");
        assertEquals(expected.getLocationNameCn(), actual.getLocationNameCn(), "中文名称不匹配");
        assertEquals(expected.getLocationNameEn(), actual.getLocationNameEn(), "英文名称不匹配");
        assertEquals(expected.getLocationTag(), actual.getLocationTag(), "地域标签不匹配");
    }

    /**
     * 验证BasicLocationVO对象的完整性
     */
    public static void assertBasicLocationVO(BasicLocationVO expected, BasicLocationVO actual) {
        assertNotNull(actual, "BasicLocationVO对象不能为空");
        assertEquals(expected.getId(), actual.getId(), "ID不匹配");
        assertEquals(expected.getLocationCode(), actual.getLocationCode(), "地域编码不匹配");
        assertEquals(expected.getLocationNameCn(), actual.getLocationNameCn(), "中文名称不匹配");
        assertEquals(expected.getLocationNameEn(), actual.getLocationNameEn(), "英文名称不匹配");
        assertEquals(expected.getLocationTag(), actual.getLocationTag(), "地域标签不匹配");
    }

    /**
     * 验证BasicValuesetInfoVO对象的完整性
     */
    public static void assertBasicValuesetInfoVO(BasicValuesetInfoVO expected, BasicValuesetInfoVO actual) {
        assertNotNull(actual, "BasicValuesetInfoVO对象不能为空");
        assertEquals(expected.getValuesetCode(), actual.getValuesetCode(), "值集编码不匹配");
        assertEquals(expected.getValuesetName(), actual.getValuesetName(), "值集名称不匹配");
        assertEquals(expected.getValuesetNameEn(), actual.getValuesetNameEn(), "英文名称不匹配");
        assertEquals(expected.getDescription(), actual.getDescription(), "描述不匹配");
    }

    /**
     * 验证BasicLocationDO列表的完整性
     */
    public static void assertBasicLocationDOList(List<BasicLocationDO> expected, List<BasicLocationDO> actual) {
        assertNotNull(actual, "BasicLocationDO列表不能为空");
        assertEquals(expected.size(), actual.size(), "列表大小不匹配");
        
        for (int i = 0; i < expected.size(); i++) {
            assertBasicLocationDO(expected.get(i), actual.get(i));
        }
    }

    /**
     * 验证BasicLocation列表的完整性
     */
    public static void assertBasicLocationList(List<BasicLocation> expected, List<BasicLocation> actual) {
        assertNotNull(actual, "BasicLocation列表不能为空");
        assertEquals(expected.size(), actual.size(), "列表大小不匹配");
        
        for (int i = 0; i < expected.size(); i++) {
            assertBasicLocation(expected.get(i), actual.get(i));
        }
    }

    /**
     * 验证BasicLocationVO列表的完整性
     */
    public static void assertBasicLocationVOList(List<BasicLocationVO> expected, List<BasicLocationVO> actual) {
        assertNotNull(actual, "BasicLocationVO列表不能为空");
        assertEquals(expected.size(), actual.size(), "列表大小不匹配");
        
        for (int i = 0; i < expected.size(); i++) {
            assertBasicLocationVO(expected.get(i), actual.get(i));
        }
    }

    /**
     * 验证BasicValuesetInfoVO列表的完整性
     */
    public static void assertBasicValuesetInfoVOList(List<BasicValuesetInfoVO> expected, List<BasicValuesetInfoVO> actual) {
        assertNotNull(actual, "BasicValuesetInfoVO列表不能为空");
        assertEquals(expected.size(), actual.size(), "列表大小不匹配");
        
        for (int i = 0; i < expected.size(); i++) {
            assertBasicValuesetInfoVO(expected.get(i), actual.get(i));
        }
    }

    /**
     * 验证CommonResult的成功响应格式
     */
    public static <T> void assertSuccessResult(CommonResult<T> result) {
        assertNotNull(result, "响应结果不能为空");
        assertTrue(result.isSuccess(), "响应应该是成功的");
        assertEquals(0, result.getCode(), "成功响应的状态码应该是0");
        assertNotNull(result.getData(), "成功响应的数据不能为空");
    }

    /**
     * 验证CommonResult的成功响应格式（允许数据为空）
     */
    public static <T> void assertSuccessResultAllowNullData(CommonResult<T> result) {
        assertNotNull(result, "响应结果不能为空");
        assertTrue(result.isSuccess(), "响应应该是成功的");
        assertEquals(0, result.getCode(), "成功响应的状态码应该是0");
    }

    /**
     * 验证CommonResult的失败响应格式
     */
    public static <T> void assertFailureResult(CommonResult<T> result) {
        assertNotNull(result, "响应结果不能为空");
        assertFalse(result.isSuccess(), "响应应该是失败的");
        assertNotEquals(0, result.getCode(), "失败响应的状态码不应该是0");
        assertNotNull(result.getMsg(), "失败响应应该有错误消息");
    }

    /**
     * 验证列表不为空且包含指定数量的元素
     */
    public static <T> void assertListNotEmptyAndSize(List<T> list, int expectedSize) {
        assertNotNull(list, "列表不能为空");
        assertFalse(list.isEmpty(), "列表不能为空列表");
        assertEquals(expectedSize, list.size(), "列表大小不匹配");
    }

    /**
     * 验证列表为空
     */
    public static <T> void assertListEmpty(List<T> list) {
        assertNotNull(list, "列表不能为null");
        assertTrue(list.isEmpty(), "列表应该为空");
    }

    /**
     * 验证字符串不为空且不为空白
     */
    public static void assertStringNotBlank(String str, String message) {
        assertNotNull(str, message + " - 字符串不能为null");
        assertFalse(str.trim().isEmpty(), message + " - 字符串不能为空白");
    }

    /**
     * 验证字符串为空或空白
     */
    public static void assertStringBlank(String str, String message) {
        assertTrue(str == null || str.trim().isEmpty(), message + " - 字符串应该为空或空白");
    }

    /**
     * 验证BasicLocation对象的有效性
     */
    public static void assertBasicLocationValid(BasicLocation location) {
        assertNotNull(location, "BasicLocation对象不能为空");
        assertTrue(location.isValid(), "BasicLocation对象应该是有效的");
        assertStringNotBlank(location.getLocationCode(), "地域编码");
        assertStringNotBlank(location.getLocationTag(), "地域标签");
        assertTrue(location.getLocationNameCn() != null || location.getLocationNameEn() != null,
                   "中文名称或英文名称至少有一个不能为空");
    }

    /**
     * 验证BasicLocation对象的无效性
     */
    public static void assertBasicLocationInvalid(BasicLocation location) {
        assertNotNull(location, "BasicLocation对象不能为空");
        assertFalse(location.isValid(), "BasicLocation对象应该是无效的");
    }

    /**
     * 验证两个对象相等（使用equals方法）
     */
    public static <T> void assertObjectEquals(T expected, T actual, String message) {
        assertNotNull(expected, message + " - 期望对象不能为空");
        assertNotNull(actual, message + " - 实际对象不能为空");
        assertEquals(expected, actual, message);
    }

    /**
     * 验证两个对象不相等
     */
    public static <T> void assertObjectNotEquals(T expected, T actual, String message) {
        assertNotEquals(expected, actual, message);
    }

    /**
     * 验证对象的hashCode一致性
     */
    public static <T> void assertHashCodeConsistency(T obj1, T obj2) {
        if (obj1.equals(obj2)) {
            assertEquals(obj1.hashCode(), obj2.hashCode(), 
                         "相等的对象应该有相同的hashCode");
        }
    }

    /**
     * 验证对象的toString方法不为空
     */
    public static void assertToStringNotNull(Object obj) {
        assertNotNull(obj, "对象不能为空");
        String toString = obj.toString();
        assertNotNull(toString, "toString方法不能返回null");
        assertFalse(toString.isEmpty(), "toString方法不能返回空字符串");
    }

    /**
     * 验证枚举的基本属性
     */
    public static <E extends Enum<E>> void assertEnumBasicProperties(E enumValue) {
        assertNotNull(enumValue, "枚举值不能为空");
        assertNotNull(enumValue.name(), "枚举名称不能为空");
        assertFalse(enumValue.name().isEmpty(), "枚举名称不能为空字符串");
        assertTrue(enumValue.ordinal() >= 0, "枚举序号应该大于等于0");
    }

    /**
     * 验证异常的抛出
     */
    public static <T extends Throwable> void assertThrowsWithMessage(
            Class<T> expectedType, String expectedMessage, Runnable executable) {
        T exception = assertThrows(expectedType, executable::run);
        assertTrue(exception.getMessage().contains(expectedMessage),
                   "异常消息应该包含: " + expectedMessage);
    }

    /**
     * 验证方法执行不抛出异常
     */
    public static void assertDoesNotThrowAny(Runnable executable) {
        assertDoesNotThrow(executable::run, "方法执行不应该抛出任何异常");
    }

    /**
     * 验证集合包含指定元素
     */
    public static <T> void assertContains(List<T> list, T element, String message) {
        assertNotNull(list, message + " - 列表不能为空");
        assertTrue(list.contains(element), message + " - 列表应该包含指定元素");
    }

    /**
     * 验证集合不包含指定元素
     */
    public static <T> void assertNotContains(List<T> list, T element, String message) {
        assertNotNull(list, message + " - 列表不能为空");
        assertFalse(list.contains(element), message + " - 列表不应该包含指定元素");
    }

    /**
     * 验证数值在指定范围内
     */
    public static void assertInRange(long value, long min, long max, String message) {
        assertTrue(value >= min && value <= max, 
                   message + " - 值应该在范围[" + min + ", " + max + "]内，实际值: " + value);
    }

    /**
     * 验证布尔值为真
     */
    public static void assertIsTrue(boolean condition, String message) {
        assertTrue(condition, message);
    }

    /**
     * 验证布尔值为假
     */
    public static void assertIsFalse(boolean condition, String message) {
        assertFalse(condition, message);
    }
}
