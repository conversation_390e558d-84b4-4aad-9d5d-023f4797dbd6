package com.dexpo.module.base.config;

import com.dexpo.module.base.BaseUnitTest;
import org.junit.jupiter.api.Test;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link RestTemplateConfig} 的单元测试类
 * 
 * <p>测试RestTemplate配置类的Bean创建和配置功能。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class RestTemplateConfigTest extends BaseUnitTest {

    private final RestTemplateConfig restTemplateConfig = new RestTemplateConfig();

    /**
     * 测试RestTemplate Bean的创建
     */
    @Test
    void testRestTemplateBeanCreation() {
        // 调用配置方法
        RestTemplate restTemplate = restTemplateConfig.restTemplate();
        
        // 验证Bean创建成功
        assertNotNull(restTemplate, "RestTemplate Bean不能为空");
        assertTrue(restTemplate instanceof RestTemplate, "返回的对象应该是RestTemplate实例");
    }

    /**
     * 测试RestTemplate的基本配置
     */
    @Test
    void testRestTemplateBasicConfiguration() {
        RestTemplate restTemplate = restTemplateConfig.restTemplate();
        
        // 验证基本配置
        assertNotNull(restTemplate.getMessageConverters(), "消息转换器列表不能为空");
        assertFalse(restTemplate.getMessageConverters().isEmpty(), "应该有默认的消息转换器");
        
        // 验证错误处理器
        assertNotNull(restTemplate.getErrorHandler(), "错误处理器不能为空");
        
        // 验证请求工厂
        assertNotNull(restTemplate.getRequestFactory(), "请求工厂不能为空");
    }

    /**
     * 测试多次调用返回不同实例
     */
    @Test
    void testMultipleCallsReturnDifferentInstances() {
        RestTemplate restTemplate1 = restTemplateConfig.restTemplate();
        RestTemplate restTemplate2 = restTemplateConfig.restTemplate();
        
        // 验证每次调用都返回新实例（因为没有@Scope("singleton")）
        assertNotSame(restTemplate1, restTemplate2, "每次调用应该返回新的RestTemplate实例");
    }

    /**
     * 测试RestTemplate的可用性
     */
    @Test
    void testRestTemplateUsability() {
        RestTemplate restTemplate = restTemplateConfig.restTemplate();
        
        // 验证RestTemplate可以正常使用
        assertDoesNotThrow(() -> {
            // 测试基本方法调用不会抛出异常
            restTemplate.getMessageConverters();
            restTemplate.getErrorHandler();
            restTemplate.getRequestFactory();
        }, "RestTemplate的基本方法调用不应该抛出异常");
    }



    /**
     * 测试RestTemplate的默认超时配置
     */
    @Test
    void testRestTemplateDefaultTimeouts() {
        RestTemplate restTemplate = restTemplateConfig.restTemplate();
        
        // 验证默认配置存在
        assertNotNull(restTemplate.getRequestFactory(), "请求工厂应该存在");
        
        // 注意：这里只测试配置的存在性，具体的超时值可能需要根据实际配置调整
        assertDoesNotThrow(() -> {
            // 测试请求工厂的基本功能
            var requestFactory = restTemplate.getRequestFactory();
            assertNotNull(requestFactory);
        });
    }

    /**
     * 测试RestTemplate的消息转换器配置
     */
    @Test
    void testRestTemplateMessageConverters() {
        RestTemplate restTemplate = restTemplateConfig.restTemplate();
        
        var messageConverters = restTemplate.getMessageConverters();
        assertNotNull(messageConverters, "消息转换器列表不能为空");
        assertFalse(messageConverters.isEmpty(), "应该有默认的消息转换器");
        
        // 验证常见的消息转换器类型
        boolean hasJsonConverter = messageConverters.stream()
                .anyMatch(converter -> converter.getClass().getSimpleName().contains("Json"));
        
        // 注意：具体的转换器类型可能因Spring版本而异，这里只做基本验证
        assertTrue(messageConverters.size() > 0, "应该至少有一个消息转换器");
    }

    /**
     * 测试配置类的实例化
     */
    @Test
    void testConfigurationInstantiation() {
        // 验证配置类可以正常实例化
        assertDoesNotThrow(() -> {
            RestTemplateConfig config = new RestTemplateConfig();
            assertNotNull(config);
        }, "配置类应该可以正常实例化");
    }

    /**
     * 测试Bean方法的返回值一致性
     */
    @Test
    void testBeanMethodConsistency() {
        RestTemplate restTemplate1 = restTemplateConfig.restTemplate();
        RestTemplate restTemplate2 = restTemplateConfig.restTemplate();
        
        // 虽然是不同的实例，但配置应该是一致的
        assertEquals(restTemplate1.getMessageConverters().size(), 
                     restTemplate2.getMessageConverters().size(),
                     "不同实例的消息转换器数量应该一致");
        
        assertEquals(restTemplate1.getErrorHandler().getClass(), 
                     restTemplate2.getErrorHandler().getClass(),
                     "不同实例的错误处理器类型应该一致");
    }
}
