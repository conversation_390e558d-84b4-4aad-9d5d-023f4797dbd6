package com.dexpo.module.base.app.service;

import com.dexpo.module.base.api.basic.dto.BasicExhibitionLocationDTO;
import com.dexpo.module.base.api.basic.dto.BasicLocationDTO;
import com.dexpo.module.base.api.basic.vo.BasicLocationVO;
import com.dexpo.module.base.app.converter.BasicLocationDTOConvert;
import com.dexpo.module.base.domain.model.agg.BasicLocation;
import com.dexpo.module.base.domain.service.BasicLocationDomainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BasicLocationAppServiceImplTest {
    @Mock
    private BasicLocationDomainService basicLocationDomainService;

    @InjectMocks
    private BasicLocationAppServiceImpl service;
    @Test
    void getLocationList_shouldReturnList() {
        BasicLocationDTO dto = new BasicLocationDTO();
        BasicLocation location = new BasicLocation();
        List<BasicLocation> locationList = Collections.singletonList(location);
        when(basicLocationDomainService.getLocationList(any())).thenReturn(locationList);
        List<BasicLocationVO> result = service.getLocationList(dto);
        assertNotNull(result);
    }

    @Test
    void getExhibitionLocationList_shouldReturnList() {
        BasicExhibitionLocationDTO dto = new BasicExhibitionLocationDTO();
        dto.setExhibitionTagCode("tag1");
        BasicLocation location = new BasicLocation();
        List<BasicLocation> locationList = Collections.singletonList(location);
        when(basicLocationDomainService.getExhibitionLocationList(anyString())).thenReturn(locationList);
        List<BasicLocationVO> result = service.getExhibitionLocationList(dto);
        assertNotNull(result);
        assertEquals(1, result.size());
    }
} 