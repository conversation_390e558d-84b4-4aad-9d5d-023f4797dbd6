package com.dexpo.module.base.app.service;

import com.dexpo.framework.cache.redis.constant.BasicRedisKey;
import com.dexpo.framework.cache.redis.constant.ICacheKey;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.module.base.api.basic.dto.ExhibitionTagValuesetDTO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;
import com.dexpo.module.base.app.api.BasicValueSetOptionAppService;
import com.dexpo.module.base.app.converter.ExhibitionValueSetOptionDTOConvert;
import com.dexpo.module.base.app.entity.ExhibitionValuesetOptionRelationCache;
import com.dexpo.module.base.domain.model.agg.ExhibitionValuesetOptionRelation;
import com.dexpo.module.base.domain.service.ExhibitionOptionRelationDomainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ExhibitionOptionRelationAppServiceImplTest {
    @Mock
    private ExhibitionOptionRelationDomainService examinationOptionRelationDomainService;
    @Mock
    private BasicValueSetOptionAppService basicValueSetOptionAppService;
    @Mock
    private RedisService redisService;

    @InjectMocks
    private ExhibitionOptionRelationAppServiceImpl service;

    @BeforeEach
    void setUp() {
        ExhibitionValueSetOptionDTOConvert mockConvert = mock(ExhibitionValueSetOptionDTOConvert.class, Mockito.RETURNS_DEEP_STUBS);
        setFinalStatic(ExhibitionValueSetOptionDTOConvert.class, "INSTANCE", mockConvert);
    }

    private static void setFinalStatic(Class<?> clazz, String fieldName, Object newValue) {
        try {
            java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            java.lang.reflect.Field modifiersField = java.lang.reflect.Field.class.getDeclaredField("modifiers");
            modifiersField.setAccessible(true);
            modifiersField.setInt(field, field.getModifiers() & ~java.lang.reflect.Modifier.FINAL);
            field.set(null, newValue);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void getExhibitionValuesetListByCodes_shouldReturnFromCache() {
        ExhibitionTagValuesetDTO dto = new ExhibitionTagValuesetDTO();
        dto.setExhibitionTagCode("tag1");
        dto.setValuesetList(List.of("vs1"));
        ExhibitionValuesetOptionRelationCache cache = new ExhibitionValuesetOptionRelationCache();
        cache.setValuesetOptionCode("opt1");
        when(redisService.getCacheObject(anyString())).thenReturn(List.of(cache));
        BasicValuesetInfoVO vo = new BasicValuesetInfoVO();
        when(basicValueSetOptionAppService.listByValuesetOptionCodes(anyList(), anyList())).thenReturn(List.of(vo));
        List<BasicValuesetInfoVO> result = service.getExhibitionValuesetListByCodes(dto);
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    void getExhibitionValuesetListByCodes_shouldReturnFromInitCache() {
        ExhibitionTagValuesetDTO dto = new ExhibitionTagValuesetDTO();
        dto.setExhibitionTagCode("tag2");
        dto.setValuesetList(List.of("vs2"));
        when(redisService.getCacheObject(anyString())).thenReturn(null);
        ExhibitionValuesetOptionRelationCache cache = new ExhibitionValuesetOptionRelationCache();
        cache.setValuesetOptionCode("opt2");
        Map<String, List<ExhibitionValuesetOptionRelationCache>> map = Map.of("tag2", List.of(cache));
        doReturn(map).when(service).initExhibitionValuesetOptionCache();
        BasicValuesetInfoVO vo = new BasicValuesetInfoVO();
        when(basicValueSetOptionAppService.listByValuesetOptionCodes(anyList(), anyList())).thenReturn(List.of(vo));
        List<BasicValuesetInfoVO> result = service.getExhibitionValuesetListByCodes(dto);
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    void getExhibitionValuesetListByCodes_shouldReturnEmpty() {
        ExhibitionTagValuesetDTO dto = new ExhibitionTagValuesetDTO();
        dto.setExhibitionTagCode("tag3");
        dto.setValuesetList(List.of("vs3"));
        when(redisService.getCacheObject(anyString())).thenReturn(Collections.emptyList());
        doReturn(Collections.emptyMap()).when(service).initExhibitionValuesetOptionCache();
        List<BasicValuesetInfoVO> result = service.getExhibitionValuesetListByCodes(dto);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void initExhibitionValuesetOptionCache_shouldReturnMap() {
        ExhibitionValuesetOptionRelation relation = new ExhibitionValuesetOptionRelation();
        relation.setExhibitionTagCode("tag4");
        List<ExhibitionValuesetOptionRelation> relationList = List.of(relation);
        when(examinationOptionRelationDomainService.list()).thenReturn(relationList);
        ExhibitionValuesetOptionRelationCache cache = new ExhibitionValuesetOptionRelationCache();
        when(ExhibitionValueSetOptionDTOConvert.INSTANCE.toCacheList(anyList())).thenReturn(List.of(cache));
        doNothing().when(redisService).setCacheObject(anyString(), anyList());
        Map<String, List<ExhibitionValuesetOptionRelationCache>> result = service.initExhibitionValuesetOptionCache();
        assertNotNull(result);
        assertTrue(result.containsKey("tag4"));
    }
} 