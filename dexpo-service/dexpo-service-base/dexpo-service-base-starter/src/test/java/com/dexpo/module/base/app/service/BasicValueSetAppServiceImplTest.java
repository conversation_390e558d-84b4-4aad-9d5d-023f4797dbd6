package com.dexpo.module.base.app.service;
import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;
import com.dexpo.module.base.app.api.BasicValueSetOptionAppService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class BasicValueSetAppServiceImplTest {

    @Mock
    private BasicValueSetOptionAppService basicValueSetOptionAppService;

    @InjectMocks
    private BasicValueSetAppServiceImpl basicValueSetAppService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getValuesetListByCodes_shouldReturnVOList_whenCodesProvided() {
        // Arrange
        List<String> codes = Arrays.asList("CODE1", "CODE2");
        List<BasicValuesetInfoVO> expectedList = Arrays.asList(new BasicValuesetInfoVO(), new BasicValuesetInfoVO());

        when(basicValueSetOptionAppService.listByValuesetCodes(codes)).thenReturn(expectedList);

        // Act
        List<BasicValuesetInfoVO> result = basicValueSetAppService.getValuesetListByCodes(codes);

        assertEquals(expectedList, result);
    }

    @Test
    void getValuesetListByCodes_shouldReturnEmptyList_whenNoCodesProvided() {
        // Arrange
        List<String> codes = Collections.emptyList();
        List<BasicValuesetInfoVO> expectedList = Collections.emptyList();

        when(basicValueSetOptionAppService.listByValuesetCodes(codes)).thenReturn(expectedList);

        // Act
        List<BasicValuesetInfoVO> result = basicValueSetAppService.getValuesetListByCodes(codes);

        // Assert
        verify(basicValueSetOptionAppService, times(1)).listByValuesetCodes(codes);
        assertEquals(expectedList, result);
    }
}

