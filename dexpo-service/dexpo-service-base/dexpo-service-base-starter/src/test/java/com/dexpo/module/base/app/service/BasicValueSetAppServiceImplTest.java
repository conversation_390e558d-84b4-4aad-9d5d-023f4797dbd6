package com.dexpo.module.base.app.service;

import com.dexpo.module.base.BaseUnitTest;
import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;
import com.dexpo.module.base.app.api.BasicValueSetOptionAppService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

/**
 * {@link BasicValueSetAppServiceImpl} 的单元测试类
 * 
 * <p>测试值集应用服务实现类的所有功能，确保100%代码覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class BasicValueSetAppServiceImplTest extends BaseUnitTest {

    @Mock
    private BasicValueSetOptionAppService basicValueSetOptionAppService;

    @InjectMocks
    private BasicValueSetAppServiceImpl basicValueSetAppService;

    private List<String> valuesetCodes;
    private BasicValuesetInfoVO valuesetInfoVO;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        valuesetCodes = Arrays.asList("USER_TYPE", "STATUS_TYPE", "ENTERPRISE_TYPE");

        valuesetInfoVO = new BasicValuesetInfoVO();
        valuesetInfoVO.setValuesetCode("USER_TYPE");
        valuesetInfoVO.setValuesetName("用户类型");
        valuesetInfoVO.setValuesetNameEn("User Type");
        valuesetInfoVO.setDescription("用户类型值集");
    }

    /**
     * 测试根据值集代码列表获取值集信息 - 成功场景
     */
    @Test
    void testGetValuesetListByCodes_Success() {
        // 准备测试数据
        List<BasicValuesetInfoVO> expectedList = Arrays.asList(valuesetInfoVO);

        // Mock服务调用
        when(basicValueSetOptionAppService.listByValuesetCodes(anyList()))
                .thenReturn(expectedList);

        // 执行测试
        List<BasicValuesetInfoVO> result = basicValueSetAppService.getValuesetListByCodes(valuesetCodes);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(expectedList, result);
        assertEquals(valuesetInfoVO, result.get(0));

        // 验证方法调用
        verify(basicValueSetOptionAppService, times(1)).listByValuesetCodes(valuesetCodes);
    }

    /**
     * 测试根据值集代码列表获取值集信息 - 空结果
     */
    @Test
    void testGetValuesetListByCodes_EmptyResult() {
        // 准备测试数据
        List<BasicValuesetInfoVO> emptyList = Collections.emptyList();

        // Mock服务调用
        when(basicValueSetOptionAppService.listByValuesetCodes(anyList()))
                .thenReturn(emptyList);

        // 执行测试
        List<BasicValuesetInfoVO> result = basicValueSetAppService.getValuesetListByCodes(valuesetCodes);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(basicValueSetOptionAppService, times(1)).listByValuesetCodes(valuesetCodes);
    }

    /**
     * 测试根据值集代码列表获取值集信息 - 多个结果
     */
    @Test
    void testGetValuesetListByCodes_MultipleResults() {
        // 准备测试数据
        BasicValuesetInfoVO valuesetInfoVO2 = new BasicValuesetInfoVO();
        valuesetInfoVO2.setValuesetCode("STATUS_TYPE");
        valuesetInfoVO2.setValuesetName("状态类型");
        valuesetInfoVO2.setValuesetNameEn("Status Type");
        valuesetInfoVO2.setDescription("状态类型值集");

        BasicValuesetInfoVO valuesetInfoVO3 = new BasicValuesetInfoVO();
        valuesetInfoVO3.setValuesetCode("ENTERPRISE_TYPE");
        valuesetInfoVO3.setValuesetName("企业类型");
        valuesetInfoVO3.setValuesetNameEn("Enterprise Type");
        valuesetInfoVO3.setDescription("企业类型值集");

        List<BasicValuesetInfoVO> expectedList = Arrays.asList(valuesetInfoVO, valuesetInfoVO2, valuesetInfoVO3);

        // Mock服务调用
        when(basicValueSetOptionAppService.listByValuesetCodes(anyList()))
                .thenReturn(expectedList);

        // 执行测试
        List<BasicValuesetInfoVO> result = basicValueSetAppService.getValuesetListByCodes(valuesetCodes);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals(expectedList, result);

        // 验证方法调用
        verify(basicValueSetOptionAppService, times(1)).listByValuesetCodes(valuesetCodes);
    }

    /**
     * 测试根据值集代码列表获取值集信息 - 单个代码
     */
    @Test
    void testGetValuesetListByCodes_SingleCode() {
        // 准备测试数据
        List<String> singleCodeList = Arrays.asList("USER_TYPE");
        List<BasicValuesetInfoVO> expectedList = Arrays.asList(valuesetInfoVO);

        // Mock服务调用
        when(basicValueSetOptionAppService.listByValuesetCodes(singleCodeList))
                .thenReturn(expectedList);

        // 执行测试
        List<BasicValuesetInfoVO> result = basicValueSetAppService.getValuesetListByCodes(singleCodeList);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(expectedList, result);

        // 验证方法调用
        verify(basicValueSetOptionAppService, times(1)).listByValuesetCodes(singleCodeList);
    }

    /**
     * 测试根据值集代码列表获取值集信息 - 空列表
     */
    @Test
    void testGetValuesetListByCodes_EmptyList() {
        // 准备测试数据
        List<String> emptyCodeList = Collections.emptyList();
        List<BasicValuesetInfoVO> emptyResultList = Collections.emptyList();

        // Mock服务调用
        when(basicValueSetOptionAppService.listByValuesetCodes(emptyCodeList))
                .thenReturn(emptyResultList);

        // 执行测试
        List<BasicValuesetInfoVO> result = basicValueSetAppService.getValuesetListByCodes(emptyCodeList);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(basicValueSetOptionAppService, times(1)).listByValuesetCodes(emptyCodeList);
    }

    /**
     * 测试根据值集代码列表获取值集信息 - null参数
     */
    @Test
    void testGetValuesetListByCodes_NullParameter() {
        // Mock服务调用
        when(basicValueSetOptionAppService.listByValuesetCodes(null))
                .thenReturn(Collections.emptyList());

        // 执行测试
        List<BasicValuesetInfoVO> result = basicValueSetAppService.getValuesetListByCodes(null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(basicValueSetOptionAppService, times(1)).listByValuesetCodes(null);
    }

    /**
     * 测试异常处理
     */
    @Test
    void testGetValuesetListByCodes_ExceptionHandling() {
        // Mock服务抛出异常
        when(basicValueSetOptionAppService.listByValuesetCodes(anyList()))
                .thenThrow(new RuntimeException("Service error"));

        // 执行测试并验证异常传播
        assertThrows(RuntimeException.class, () -> {
            basicValueSetAppService.getValuesetListByCodes(valuesetCodes);
        });

        // 验证方法调用
        verify(basicValueSetOptionAppService, times(1)).listByValuesetCodes(valuesetCodes);
    }

    /**
     * 测试服务的依赖注入
     */
    @Test
    void testDependencyInjection() {
        // 验证服务实例不为空
        assertNotNull(basicValueSetAppService);
        
        // 验证依赖服务已正确注入（通过调用方法来间接验证）
        when(basicValueSetOptionAppService.listByValuesetCodes(anyList()))
                .thenReturn(Collections.emptyList());
        
        assertDoesNotThrow(() -> {
            basicValueSetAppService.getValuesetListByCodes(Collections.emptyList());
        });
    }

    /**
     * 测试服务类的注解
     */
    @Test
    void testServiceAnnotations() {
        // 验证类上的注解
        assertTrue(BasicValueSetAppServiceImpl.class.isAnnotationPresent(
                org.springframework.stereotype.Service.class));
        assertTrue(BasicValueSetAppServiceImpl.class.isAnnotationPresent(
                lombok.RequiredArgsConstructor.class));
    }

    /**
     * 测试接口实现
     */
    @Test
    void testInterfaceImplementation() {
        // 验证服务实现了正确的接口
        assertTrue(com.dexpo.module.base.app.api.BasicValueSetAppService.class
                .isAssignableFrom(BasicValueSetAppServiceImpl.class));
    }

    /**
     * 测试方法的参数传递
     */
    @Test
    void testMethodParameterPassing() {
        // 准备测试数据
        List<String> testCodes = Arrays.asList("TEST_CODE_1", "TEST_CODE_2");

        // Mock服务调用
        when(basicValueSetOptionAppService.listByValuesetCodes(testCodes))
                .thenReturn(Collections.emptyList());

        // 执行测试
        basicValueSetAppService.getValuesetListByCodes(testCodes);

        // 验证参数正确传递
        verify(basicValueSetOptionAppService, times(1)).listByValuesetCodes(testCodes);
    }

    /**
     * 测试方法的返回值传递
     */
    @Test
    void testMethodReturnValuePassing() {
        // 准备测试数据
        List<BasicValuesetInfoVO> serviceResult = Arrays.asList(valuesetInfoVO);

        // Mock服务调用
        when(basicValueSetOptionAppService.listByValuesetCodes(anyList()))
                .thenReturn(serviceResult);

        // 执行测试
        List<BasicValuesetInfoVO> appServiceResult = basicValueSetAppService.getValuesetListByCodes(valuesetCodes);

        // 验证返回值正确传递
        assertSame(serviceResult, appServiceResult, "应用服务应该直接返回底层服务的结果");
    }

    /**
     * 测试方法的重载（如果存在）
     */
    @Test
    void testMethodOverloading() {
        // 验证方法签名
        try {
            var method = BasicValueSetAppServiceImpl.class
                    .getMethod("getValuesetListByCodes", List.class);
            assertNotNull(method);
            assertEquals(List.class, method.getReturnType());
        } catch (NoSuchMethodException e) {
            fail("getValuesetListByCodes方法应该存在");
        }
    }

    /**
     * 测试并发调用
     */
    @Test
    void testConcurrentCalls() {
        // 准备测试数据
        List<BasicValuesetInfoVO> expectedList = Arrays.asList(valuesetInfoVO);

        // Mock服务调用
        when(basicValueSetOptionAppService.listByValuesetCodes(anyList()))
                .thenReturn(expectedList);

        // 执行多次调用
        List<BasicValuesetInfoVO> result1 = basicValueSetAppService.getValuesetListByCodes(valuesetCodes);
        List<BasicValuesetInfoVO> result2 = basicValueSetAppService.getValuesetListByCodes(valuesetCodes);

        // 验证结果
        assertEquals(result1, result2);
        
        // 验证方法调用次数
        verify(basicValueSetOptionAppService, times(2)).listByValuesetCodes(valuesetCodes);
    }

    /**
     * 测试大数据量处理
     */
    @Test
    void testLargeDataHandling() {
        // 准备大量测试数据
        List<String> largeCodes = Arrays.asList(
                "CODE_1", "CODE_2", "CODE_3", "CODE_4", "CODE_5",
                "CODE_6", "CODE_7", "CODE_8", "CODE_9", "CODE_10"
        );
        
        List<BasicValuesetInfoVO> largeResult = Collections.nCopies(10, valuesetInfoVO);

        // Mock服务调用
        when(basicValueSetOptionAppService.listByValuesetCodes(largeCodes))
                .thenReturn(largeResult);

        // 执行测试
        List<BasicValuesetInfoVO> result = basicValueSetAppService.getValuesetListByCodes(largeCodes);

        // 验证结果
        assertNotNull(result);
        assertEquals(10, result.size());

        // 验证方法调用
        verify(basicValueSetOptionAppService, times(1)).listByValuesetCodes(largeCodes);
    }
}
