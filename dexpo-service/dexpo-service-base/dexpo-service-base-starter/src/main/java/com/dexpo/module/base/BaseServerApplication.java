package com.dexpo.module.base;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * 项目的启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {MetricsAutoConfiguration.class, DataSourceAutoConfiguration.class})
@Slf4j
@EnableDiscoveryClient
@ComponentScan({"com.dexpo.framework.security", "com.dexpo.module.base", "com.dexpo.framework.cache", "com.dexpo.framework.web","com.dexpo.module.exhibition"})
@EnableFeignClients(value = {"com.dexpo.module.integration","com.dexpo.module.exhibition","com.dexpo.module.member"})
public class BaseServerApplication {

    public static void main(String[] args) {

        SpringApplication.run(BaseServerApplication.class, args);
        log.info("启动成功！");
    }

}
