--- #################### 数据库相关配置 ####################
spring:
  cloud:
    function:
      # 消费者Bean方法命 mailChannel-in-0 中 mailChannel一致
      definition: mediaRegisterStatusEventChannel
    stream:
      rocketmq:
        binder:
          name-server: rmq-cn-btz49fcqh07.cn-shanghai.rmq.aliyuncs.com:8080
          access-key: ${ACCESS_KEY}
          secret-key: ${SECRET_KEY}
      bindings:
        # 消费者
        mediaRegisterStatusEventChannel-in-0:
          binder: rocketmq
          destination: MEDIA-REGISTER-STATUS-EVENT-CHANNEL
          group: dev-consumer-group
  # 数据源配置项
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: false # 暂时禁用web监控
      stat-view-servlet:
        enabled: false # 暂时禁用监控页面
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 5 # 初始连接数
        min-idle: 10 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 FROM DUAL # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master # 设置默认的数据源或者数据源组,默认值即为 master
      datasource:
        master: # 主数据源
          url: ********************************************************************************************************************************************************************************************
          username: ${DATASOURCE_USERNAME}
          password: ${DATASOURCE_PASSWORD}
          driver-class-name: com.mysql.cj.jdbc.Driver
  data:
    redis:
      host: ${REDIS_HOST}
      port: 6379
      ssl:
        enabled: false
      password: ${REDIS_PASSWORD}
      database: 0

# 日志文件配置
logging:
  level:
    # 配置自己写的 MyBatis Mapper 打印日志
    com.dexpo.module.base.dal.mysql: debug

--- #################### RR低代码相关配置 ####################

# RR低代码配置项，设置当前项目所有自定义的配置
dexpo:
  info:
    base-package: com.dexpo # 基础包路径
  env: # 多环境的配置项
    tag: ${HOSTNAME}
  security:
    mock-enable: true
  web:
    admin-ui:
      url: http://localhost:48080 # Admin UI 地址
      title: DEXPO Admin # Admin UI 标题
  xss:
    enable: false
    exclude-urls: # 如下两个 url，仅仅是为了演示，去掉配置也没关系
      - ${spring.boot.admin.context-path}/** # 不处理 Spring Boot Admin 的请求
      - ${management.endpoints.web.base-path}/** # 不处理 Actuator 的请求
  access-log: # 访问日志的配置项
    enable: false
  error-code: # 错误码相关配置项
    enable: false
  demo: false # 关闭演示模式

#################### 接口文档配置 ####################

springdoc:
  api-docs:
    enabled: true # 1. 是否开启 Swagger 接文档的元数据
    path: /v3/api-docs
  swagger-ui:
    enabled: true # 2.1 是否开启 Swagger 文档的官方 UI 界面
    path: /swagger-ui.html
