package ${basePackage}.module.${table.moduleName}.controller.${sceneEnum.basePackage}.${table.businessName}.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import ${PageParamClassName};
## 处理 Date 字段的引入
#foreach ($column in $columns)
#if (${column.listOperation} && ${column.javaType} == "LocalDateTime")## 时间类型
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static ${DateUtilsClassName}.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
#break
#end
#end
## 字段模板
#macro(columnTpl $prefix $prefixStr)
    @Schema(description = "${prefixStr}${column.columnComment}"#if ("$!column.example" != ""), example = "${column.example}"#end)
    private ${column.javaType}#if ("$!prefix" != "") ${prefix}${JavaField}#else ${column.javaField}#end;
#end

@Schema(description = "${sceneEnum.name} - ${table.classComment} Excel 导出 Request VO，参数和 ${table.className}PageReqVO 是一致的")
@Data
public class ${sceneEnum.prefixClass}${table.className}ExportReqVO {

#foreach ($column in $columns)
#if (${column.listOperation})##查询操作
#if (${column.listOperationCondition} == "BETWEEN")## 情况一，Between 的时候
    @Schema(description = "${column.columnComment}"#if ("$!column.example" != ""), example = "${column.example}"#end)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private ${column.javaType}[] ${column.javaField};
#else##情况二，非 Between 的时间
    #columnTpl('', '')
#end

#end
#end
}
