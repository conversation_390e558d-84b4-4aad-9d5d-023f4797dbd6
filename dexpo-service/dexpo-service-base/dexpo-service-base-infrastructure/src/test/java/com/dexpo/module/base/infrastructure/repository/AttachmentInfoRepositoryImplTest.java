package com.dexpo.module.base.infrastructure.repository;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.dexpo.module.base.domain.model.agg.AttachmentInfo;
import com.dexpo.module.base.infrastructure.converter.AttachmentInfoConverter;
import com.dexpo.module.base.infrastructure.tunnel.database.AttachmentInfoMapper;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.AttachmentInfoDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AttachmentInfoRepositoryImplTest {
    @Mock
    private AttachmentInfoMapper attachmentInfoMapper;

    @InjectMocks
    private AttachmentInfoRepositoryImpl repository;

    @Test
    void save_shouldReturnAttachmentInfo() {
        AttachmentInfo info = new AttachmentInfo();
        AttachmentInfoDO infoDO = new AttachmentInfoDO();
        infoDO.setId(123L);

        try (var beanUtilMocked = Mockito.mockStatic(BeanUtil.class)) {
            beanUtilMocked.when(() -> BeanUtil.copyProperties(any(), eq(AttachmentInfoDO.class))).thenReturn(infoDO);

            // 使用 when(...).thenReturn(...) 替代 doNothing()
            when(attachmentInfoMapper.insert(any(AttachmentInfoDO.class))).thenReturn(1);

            AttachmentInfo result = repository.save(info);

            assertNotNull(result);
            assertEquals(123L, result.getId());
        }
    }

    @Test
    void findById_shouldReturnEntity() {
        AttachmentInfoDO infoDO = new AttachmentInfoDO();
        infoDO.setId(1L);
        infoDO.setAttachmentType("");
        when(attachmentInfoMapper.selectById(anyLong())).thenReturn(infoDO);
        AttachmentInfo result = repository.findById(1L);
        assertNotNull(result);
    }

    @Test
    void findByIdList_shouldReturnEntityList() {
        List<Long> ids = List.of(1L, 2L);
        AttachmentInfoDO infoDO = new AttachmentInfoDO();
        when(attachmentInfoMapper.selectList(any())).thenReturn(List.of(infoDO));
        List<AttachmentInfo> result = repository.findByIdList(ids);
        assertNotNull(result);
    }

    @Test
    void findFileByBusinessType_shouldReturnFirst_whenNotEmpty() {
        AttachmentInfoDO infoDO = new AttachmentInfoDO();
        when(attachmentInfoMapper.selectList(any())).thenReturn(List.of(infoDO));
        AttachmentInfo result = repository.findFileByBusinessType("type");
        assertNotNull(result);
    }

    @Test
    void findFileByBusinessType_shouldReturnNull_whenEmpty() {
        when(attachmentInfoMapper.selectList(any())).thenReturn(Collections.emptyList());
        AttachmentInfo result = repository.findFileByBusinessType("type");
        assertNull(result);
    }
} 