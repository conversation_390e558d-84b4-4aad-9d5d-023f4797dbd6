package com.dexpo.module.base.infrastructure.repository;

import com.dexpo.module.base.domain.model.agg.BasicRegion;
import com.dexpo.module.base.infrastructure.tunnel.database.BasicRegionMapper;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.BasicRegionDO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BasicRegionRepositoryImplTest {
    @Mock
    private BasicRegionMapper basicRegionMapper;
    @InjectMocks
    private BasicRegionRepositoryImpl repository;


    @Test
    void getRegionList_shouldReturnList() {
        BasicRegionDO regionDO = new BasicRegionDO();
        when(basicRegionMapper.selectByLevelAndParentAdcode(anyString(), anyString())).thenReturn(List.of(regionDO));
        BasicRegion param = new BasicRegion();
        param.setLevel("province");
        param.setParentAdcode("100000");
        List<BasicRegion> result = repository.getRegionList(param);
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    void selectList_shouldReturnList() {
        BasicRegionDO regionDO = new BasicRegionDO();
        when(basicRegionMapper.selectList()).thenReturn(List.of(regionDO));
        List<BasicRegion> result = repository.selectList();
        assertNotNull(result);
        assertEquals(1, result.size());
    }
} 