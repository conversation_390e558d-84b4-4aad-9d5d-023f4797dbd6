package com.dexpo.module.base.infrastructure.integration.exhibition;

import com.dexpo.framework.cache.redis.operate.exhibition.ExhibitionInfoCacheOpt;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.exhibition.api.ExhibitionApi;
import com.dexpo.module.exhibition.api.dto.ExhibitionQueryDTO;
import com.dexpo.module.exhibition.api.vo.ExhibitionVO;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ExhibitionExternalServiceTest {
    @Mock
    private ExhibitionApi exhibitionApi;
    @Mock
    private ExhibitionInfoCacheOpt exhibitionInfoCacheOpt;
    @Mock
    private RedisService redisService;

    @InjectMocks
    private ExhibitionExternalService service;



    @Test
    void getExhibitionIds_shouldReturnIdList() {
        ExhibitionQueryDTO queryDTO = new ExhibitionQueryDTO();
        ExhibitionVO vo1 = new ExhibitionVO(); vo1.setId(1L);
        ExhibitionVO vo2 = new ExhibitionVO(); vo2.setId(2L);
        when(exhibitionApi.getExhibitionList(any())).thenReturn(CommonResult.success(List.of(vo1, vo2)));
        List<Long> ids = service.getExhibitionIds(queryDTO);
        assertEquals(List.of(1L, 2L), ids);
    }

    @Test
    void getExhibitionIds_shouldReturnEmpty_whenNoData() {
        ExhibitionQueryDTO queryDTO = new ExhibitionQueryDTO();
        when(exhibitionApi.getExhibitionList(any())).thenReturn(CommonResult.success(Collections.emptyList()));
        List<Long> ids = service.getExhibitionIds(queryDTO);
        assertTrue(ids.isEmpty());
    }

    @Test
    void getExhibitionMap_shouldReturnMap() {
        ExhibitionQueryDTO queryDTO = new ExhibitionQueryDTO();
        ExhibitionVO vo1 = new ExhibitionVO(); vo1.setId(1L);
        ExhibitionVO vo2 = new ExhibitionVO(); vo2.setId(2L);
        when(exhibitionApi.getExhibitionList(any())).thenReturn(CommonResult.success(List.of(vo1, vo2)));
        Map<Long, ExhibitionVO> map = service.getExhibitionMap(queryDTO);
        assertEquals(2, map.size());
        assertSame(vo1, map.get(1L));
        assertSame(vo2, map.get(2L));
    }

    @Test
    void getExhibitionList_shouldReturnList() {
        ExhibitionQueryDTO queryDTO = new ExhibitionQueryDTO();
        ExhibitionVO vo = new ExhibitionVO();
        when(exhibitionApi.getExhibitionList(any())).thenReturn(CommonResult.success(List.of(vo)));
        List<ExhibitionVO> list = service.getExhibitionList(queryDTO);
        assertEquals(1, list.size());
        assertSame(vo, list.get(0));
    }


    @Test
    void getExhibitionList_shouldReturnEmpty_whenResultDataNull() {
        ExhibitionQueryDTO queryDTO = new ExhibitionQueryDTO();
        List<ExhibitionVO> voList = Lists.newArrayList();
        when(exhibitionApi.getExhibitionList(any())).thenReturn(CommonResult.success(voList));
        List<ExhibitionVO> list = service.getExhibitionList(queryDTO);
        assertTrue(list.isEmpty());
    }

    @Test
    void getExhibition_shouldDelegateToCacheQueryService() {
        // 由于ExhibitionCacheQueryService是new出来的，只能间接测试
        ExhibitionVO vo = new ExhibitionVO();
        ExhibitionExternalService spyService = spy(service);
        doReturn(vo).when(spyService).getExhibition(anyLong());
        ExhibitionVO result = spyService.getExhibition(123L);
        assertSame(vo, result);
    }
    @Test
    void testGetExhibition_shouldThrowException_whenCalled() {
        try {
             service.getExhibition(1l);
        } catch (Exception e) {
            assertNotNull(e);
        }
    }
} 