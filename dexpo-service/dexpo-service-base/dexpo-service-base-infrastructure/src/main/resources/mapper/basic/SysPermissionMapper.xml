<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dexpo.module.base.infrastructure.tunnel.database.SysPermissionMapper">

    <sql id="Base_Column_List">
        id
        , permission_code, permission_name, permission_type, parent_permission_code,
          role_category, app_type,del_flg, create_user, create_user_name, create_time,
        update_user, update_user_name, update_time
    </sql>

    <resultMap id="BaseResultMap" type="com.dexpo.module.base.infrastructure.tunnel.dataobject.SysPermissionDO">
        <id column="id" property="id"/>
        <result column="permission_code" property="permissionCode"/>
        <result column="permission_name" property="permissionName"/>
        <result column="permission_type" property="permissionType"/>
        <result column="parent_permission_code" property="parentPermissionCode"/>
        <result column="role_category" property="roleCategory"/>
        <result column="app_type" property="appType"/>
        <result column="del_flg" property="delFlg"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_name" property="updateUserName"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="permissionByOrganizationList" resultMap="BaseResultMap">
        SELECT sp.*
        from sys_organization_role_relation sorr,
             sys_role sr,
             sys_permission_role_relation sprr,
             sys_permission sp
        where sorr.del_flg = 0
          and sr.role_code = sorr.role_code
          and sr.del_flg = 0
          and sr.role_type = 'VO_ROLE_TYPE_1'
          and sr.role_code = sprr.role_code
          and sp.permission_code = sprr.permission_code
          and sp.del_flg = 0
          and sprr.del_flg = 0
          and sorr.organization_code = #{organizationCode}
    </select>

    <select id="permissionByUserCode" resultMap="BaseResultMap">
        SELECT sp.*
        from sys_user_role_relation surr,
             sys_role sr,
             sys_permission_role_relation sprr,
             sys_permission sp
        where surr.del_flg = 0
          and sr.role_code = surr.role_code
          and sr.del_flg = 0
          and sr.role_code = sprr.role_code
          and sp.permission_code = sprr.permission_code
          and sp.del_flg = 0
          and sprr.del_flg = 0
          and sr.user_type = #{userType}
          and sorr.organization_code = #{userCode}
    </select>
    <select id="findAllPermissionInterfaceRelation"
            resultType="com.dexpo.module.base.domain.model.valueobject.SysPermissionInterfaceRelation">
        select spir.id,
               spir.permission_code,
               spir.interface_url,
               spir.is_public,
               spir.del_flg,
               spir.create_user,
               spir.create_user_name,
               spir.create_time,
               spir.update_user,
               spir.update_user_name,
               spir.update_time
        from sys_permission_interface_relation spir
        where spir.del_flg = 0
    </select>
</mapper>