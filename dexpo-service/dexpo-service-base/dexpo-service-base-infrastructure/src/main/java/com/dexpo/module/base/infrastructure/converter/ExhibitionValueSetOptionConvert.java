package com.dexpo.module.base.infrastructure.converter;

import com.dexpo.module.base.domain.model.agg.ExhibitionValuesetOptionRelation;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.ExhibitionValuesetOptionRelationDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 会展tag关联值集信息转换接口
 */
@Mapper
public interface ExhibitionValueSetOptionConvert {

    ExhibitionValueSetOptionConvert INSTANCE = Mappers.getMapper(ExhibitionValueSetOptionConvert.class);

    ExhibitionValuesetOptionRelationDO d2e(ExhibitionValuesetOptionRelation relation);

    ExhibitionValuesetOptionRelation e2d(ExhibitionValuesetOptionRelationDO entity);

    List<ExhibitionValuesetOptionRelation> e2dList(List<ExhibitionValuesetOptionRelationDO> doList);
}