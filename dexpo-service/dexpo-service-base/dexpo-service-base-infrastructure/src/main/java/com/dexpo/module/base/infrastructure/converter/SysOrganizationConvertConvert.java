package com.dexpo.module.base.infrastructure.converter;

import com.dexpo.module.base.domain.model.valueobject.SysOrganizationRoleRelation;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.SysOrganizationRoleRelationDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface SysOrganizationConvertConvert {
    SysOrganizationConvertConvert INSTANCE = Mappers.getMapper(SysOrganizationConvertConvert.class);

    SysOrganizationRoleRelationDO e2d(SysOrganizationRoleRelation sysOrganizationRoleRelation);

    SysOrganizationRoleRelation d2e(SysOrganizationRoleRelationDO sysOrganizationRoleRelation);

    List<SysOrganizationRoleRelation> d2eList(List<SysOrganizationRoleRelationDO> sysOrganizationRoleRelationDOS);
} 