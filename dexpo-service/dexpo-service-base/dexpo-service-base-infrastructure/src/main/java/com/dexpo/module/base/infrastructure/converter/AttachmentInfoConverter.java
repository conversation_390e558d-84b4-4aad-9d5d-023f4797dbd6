package com.dexpo.module.base.infrastructure.converter;

import com.dexpo.module.base.domain.model.agg.AttachmentInfo;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.AttachmentInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * attachmentInfo 数据库模型 聚合根转换
 *
 * <AUTHOR> Xiaohua 18/06/2025 15:21
 **/
@Mapper
public interface AttachmentInfoConverter  {


    AttachmentInfoConverter INSTANCE = Mappers.getMapper(AttachmentInfoConverter.class);

    /**
     * 单个数据库模型转领域模型
     */
    AttachmentInfo toEntity(AttachmentInfoDO attachmentInfoDO);

    /**
     * 列表数据库模型转领域模型列表
     */
    List<AttachmentInfo> toEntityList(List<AttachmentInfoDO> attachmentInfoDOList);

    /**
     * 领域模型转数据库模型
     */
    AttachmentInfoDO toDO(AttachmentInfo attachmentInfo);

}
