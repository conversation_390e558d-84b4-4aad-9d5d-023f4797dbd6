package com.dexpo.module.base.infrastructure.tunnel.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 值集信息数据对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("basic_valueset")
public class BasicValuesetDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 值集代码
     */
    @TableField("valueset_code")
    private String valuesetCode;

    /**
     * 值集描述
     */
    @TableField("valueset_description")
    private String valuesetDescription;

    /**
     * 是否可编辑：0否 1是
     */
    @TableField("is_edit_able")
    private Boolean isEditAble;

} 