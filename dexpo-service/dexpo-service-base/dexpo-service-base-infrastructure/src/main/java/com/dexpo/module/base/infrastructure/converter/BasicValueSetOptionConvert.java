package com.dexpo.module.base.infrastructure.converter;

import com.dexpo.framework.cache.redis.entity.BasicValuesetOptionCache;
import com.dexpo.module.base.domain.model.agg.BasicValueSetOption;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.BasicValuesetOptionDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 值集选项转换接口
 */
@Mapper
public interface BasicValueSetOptionConvert{

    BasicValueSetOptionConvert INSTANCE = Mappers.getMapper(BasicValueSetOptionConvert.class);

    BasicValuesetOptionDO d2e(BasicValueSetOption baseValueSetOption);

    BasicValueSetOption e2d(BasicValuesetOptionDO entity);

    List<BasicValueSetOption> e2dList(List<BasicValuesetOptionDO> doList);

    /**
     * 转换为cache 对象
     *
     * @param optionDO do
     * @return cache
     */
    BasicValuesetOptionCache toBasicValuesetOptionCache(BasicValuesetOptionDO optionDO);

    /**
     * 转换为cache 对象
     *
     * @param optionDOs do
     * @return cache
     */
    List<BasicValuesetOptionCache> toBasicValuesetOptionCache(List<BasicValuesetOptionDO> optionDOs);

} 