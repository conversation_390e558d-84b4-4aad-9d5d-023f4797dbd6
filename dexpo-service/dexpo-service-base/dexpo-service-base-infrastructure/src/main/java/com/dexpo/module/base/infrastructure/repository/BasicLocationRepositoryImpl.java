package com.dexpo.module.base.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dexpo.module.base.domain.model.agg.BasicLocation;
import com.dexpo.module.base.domain.repository.BasicLocationRepository;
import com.dexpo.module.base.infrastructure.converter.BasicLocationConvert;
import com.dexpo.module.base.infrastructure.tunnel.database.BasicLocationMapper;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.BasicLocationDO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class BasicLocationRepositoryImpl implements BasicLocationRepository {

    private final BasicLocationMapper basicLocationMapper;
    @Override
    public List<BasicLocation> getLocationList(BasicLocation basicLocation) {
        LambdaQueryWrapper<BasicLocationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BasicLocationDO::getLocationTag, basicLocation.getLocationTag());
        List<BasicLocationDO> doList = basicLocationMapper.selectList(queryWrapper);
        return BasicLocationConvert.INSTANCE.e2dList(doList);
    }

    @Override
    public List<BasicLocation> getExhibitionLocationList(String exhibitionTagCode) {
        List<BasicLocationDO> doList = basicLocationMapper.selectByExhibitionTagCode(exhibitionTagCode);
        return BasicLocationConvert.INSTANCE.e2dList(doList);
    }
}