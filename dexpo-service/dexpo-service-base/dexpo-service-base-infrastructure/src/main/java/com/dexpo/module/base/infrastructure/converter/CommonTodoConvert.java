package com.dexpo.module.base.infrastructure.converter;

import com.dexpo.module.base.domain.model.agg.CommonTodo;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.CommonTodoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CommonTodoConvert {

    CommonTodoConvert INSTANCE = Mappers.getMapper(CommonTodoConvert.class);

    CommonTodoDO d2e(CommonTodo commonTodo);

    CommonTodo e2d(CommonTodoDO entity);

    List<CommonTodo> e2dList(List<CommonTodoDO> doList);
}
