package com.dexpo.module.base.infrastructure.tunnel.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 业务地域信息数据对象
 */

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("basic_location")
public class BasicLocationDO extends BaseDO {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 地域编码
     */
    @TableField("location_code")
    private String locationCode;
    
    /**
     * 地域名称-中文
     */
    @TableField("location_name_cn")
    private String locationNameCn;
    
    /**
     * 地域名称-英文
     */
    @TableField("location_name_en")
    private String locationNameEn;
    
    /**
     * 地域标签：值集VS_ACTION_ENTERPRISE_TYPE
     */
    @TableField("location_tag")
    private String locationTag;
    
} 