package com.dexpo.module.base.infrastructure.repository;

import com.dexpo.module.base.domain.model.agg.ExhibitionValuesetOptionRelation;
import com.dexpo.module.base.domain.repository.ExhibitionOptionRelationRepository;
import com.dexpo.module.base.infrastructure.converter.ExhibitionValueSetOptionConvert;
import com.dexpo.module.base.infrastructure.tunnel.database.ExhibitionValuesetOptionRelationMapper;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.ExhibitionValuesetOptionRelationDO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class ExhibitionOptionRelationRepositoryImpl implements ExhibitionOptionRelationRepository {
    private final ExhibitionValuesetOptionRelationMapper exhibitionValuesetOptionRelationMapper;
    @Override
    public List<ExhibitionValuesetOptionRelation> list() {
        List<ExhibitionValuesetOptionRelationDO>  doList = exhibitionValuesetOptionRelationMapper.selectList();
        return ExhibitionValueSetOptionConvert.INSTANCE.e2dList(doList);
    }
}