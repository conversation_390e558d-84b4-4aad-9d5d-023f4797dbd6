package com.dexpo.module.base.infrastructure.tunnel.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName("sys_organization")
public class SysOrganizationDO extends BaseDO {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 组织编码
     */
    @TableField("organization_code")
    private String organizationCode;
    /**
     * 组织名称
     */
    @TableField("organization_name")
    private String organizationName;
    /**
     * 组织级别
     */
    @TableField("organization_level")
    private Integer organizationLevel;
    /**
     * 父组织编码
     */
    @TableField("parent_organization_code")
    private String parentOrganizationCode;
    /**
     * 创建人
     */
    @TableField("create_user")
    private Long createUser;
    /**
     * 创建人名称
     */
    @TableField("create_user_name")
    private String createUserName;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
    /**
     * 更新人
     */
    @TableField("update_user")
    private Long updateUser;
    /**
     * 更新人名称
     */
    @TableField("update_user_name")
    private String updateUserName;
    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
} 