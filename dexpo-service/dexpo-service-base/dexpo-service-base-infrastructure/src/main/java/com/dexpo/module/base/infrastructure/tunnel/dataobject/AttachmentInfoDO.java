package com.dexpo.module.base.infrastructure.tunnel.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * 值集信息数据对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("common_attachment")
public class AttachmentInfoDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @TableField(value = "id", fill = FieldFill.INSERT)
    private Long id;

    /**
     * 附件名
     */
    @TableField(value = "business_type", jdbcType = JdbcType.VARCHAR)
    private String BusinessType;

    /**
     * 附件名
     */
    @TableField(value = "attachment_name", jdbcType = JdbcType.VARCHAR)
    private String attachmentName;

    /**
     * 附件类型
     */
    @TableField(value = "attachment_type", jdbcType = JdbcType.VARCHAR)
    private String attachmentType;

    /**
     * 附件大小
     */
    @TableField(value = "attachment_size", jdbcType = JdbcType.BIGINT)
    private Long attachmentSize;

    /**
     * 附件存储地址
     * /日期20250521/用户编码/uuid_文件名称.文件类型
     */
    @TableField(value = "attachment_path", jdbcType = JdbcType.VARCHAR)
    private String attachmentPath;

} 