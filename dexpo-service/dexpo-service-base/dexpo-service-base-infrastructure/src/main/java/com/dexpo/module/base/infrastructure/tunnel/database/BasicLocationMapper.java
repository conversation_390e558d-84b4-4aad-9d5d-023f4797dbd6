package com.dexpo.module.base.infrastructure.tunnel.database;

import com.dexpo.framework.mybatis.core.mapper.BaseMapperX;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.BasicLocationDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业务地域信息Mapper接口
 */
@Mapper
public interface BasicLocationMapper extends BaseMapperX<BasicLocationDO> {

    List<BasicLocationDO> selectByExhibitionTagCode(@Param("exhibitionTagCode") String exhibitionTagCode);
}