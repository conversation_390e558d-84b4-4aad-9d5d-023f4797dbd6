package com.dexpo.module.base.infrastructure.tunnel.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("common_todo")
public class CommonTodoDO extends BaseDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 会展id
     */
    private Long exhibitionId;

    /**
     * 待办标题
     */
    private String todoTitle;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 业务单号
     */
    private String businessNo;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 待办状态
     */
    private String status;

    /**
     * 待办生成时间
     */
    private LocalDateTime generationTime;

    /**
     * 完成时间
     */
    private LocalDateTime doneTime;

}
