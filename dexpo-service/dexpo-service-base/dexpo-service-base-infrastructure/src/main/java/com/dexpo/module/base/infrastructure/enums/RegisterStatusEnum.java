package com.dexpo.module.base.infrastructure.enums;

import com.dexpo.module.base.infrastructure.constants.EnumConstants;
import lombok.Getter;

@Getter
public enum RegisterStatusEnum {

    DRAFT(
            EnumConstants.VS_REGISTER_STATUS,
            "VO_REGISTER_STATUS_1",
            "草稿",
            "Draft"
    ),
    PENDING_REVIEW(
            EnumConstants.VS_REGISTER_STATUS,
            "VO_REGISTER_STATUS_2",
            "待审核",
            "Pending Review"
    ),
    APPROVED(
            EnumConstants.VS_REGISTER_STATUS,
            "VO_REGISTER_STATUS_3",
            "审核通过",
            "Approved"
    ),
    REJECTED(
            EnumConstants.VS_REGISTER_STATUS,
            "VO_REGISTER_STATUS_4",
            "审核驳回",
            "Rejected"
    ),
    RECALL(
            EnumConstants.VS_REGISTER_STATUS,
            "VO_REGISTER_STATUS_5",
            "已撤回",
            "Recall"
    )
    ;

    /**
     * 值集分类编码
     */
    private final String valuesetCode;

    /**
     * 枚举项编码
     */
    private final String code;

    /**
     * 中文描述
     */
    private final String descriptionCN;

    /**
     * 英文描述（预留字段）
     */
    private final String descriptionEN;

    RegisterStatusEnum(String valuesetCode, String code, String descriptionCN, String descriptionEN) {
        this.valuesetCode = valuesetCode;
        this.code = code;
        this.descriptionCN = descriptionCN;
        this.descriptionEN = descriptionEN;
    }

    /**
     * 根据枚举编码获取枚举实例
     * @param code VO_REGISTER_STATUS_1 格式的编码
     * @return 匹配的枚举实例，未找到返回null
     */
    public static RegisterStatusEnum getByCode(String code) {
        for (RegisterStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查注册状态
     * 保存草稿或提交时,状态只能为空 草稿 撤回 驳回其中之一
     */
    public static boolean checkDraftOrRecallStatus(String code) {
        return code==null ||"".equals( code)||DRAFT.code.equals(code) || RECALL.code.equals(code)||REJECTED.code.equals(code);
    }


}
