package com.dexpo.module.base.infrastructure.tunnel.database;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dexpo.module.base.domain.model.valueobject.SysPermissionInterfaceRelation;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.SysPermissionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysPermissionMapper extends BaseMapper<SysPermissionDO> {

    List<SysPermissionDO> permissionByOrganizationList(@Param("organizationCode") String organizationCode);

    List<SysPermissionDO> permissionByUserCode(@Param("userCode") String userCode, @Param("userType") String userType);

    List<SysPermissionInterfaceRelation> findAllPermissionInterfaceRelation();
}
