package com.dexpo.module.base.infrastructure.converter;


import com.dexpo.module.base.domain.model.agg.BasicRegion;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.BasicRegionDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper
public interface BasicRegionConvert {

    BasicRegionConvert INSTANCE = Mappers.getMapper(BasicRegionConvert.class);

    BasicRegionDO d2e(BasicRegion baseRegion);

    BasicRegion e2d(BasicRegionDO entity);

    List<BasicRegion> e2dList(List<BasicRegionDO> doList);
}
