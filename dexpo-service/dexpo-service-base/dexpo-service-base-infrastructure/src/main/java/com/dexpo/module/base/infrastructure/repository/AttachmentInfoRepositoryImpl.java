package com.dexpo.module.base.infrastructure.repository;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dexpo.module.base.domain.model.agg.AttachmentInfo;
import com.dexpo.module.base.domain.repository.AttachmentInfoRepository;
import com.dexpo.module.base.infrastructure.converter.AttachmentInfoConverter;
import com.dexpo.module.base.infrastructure.tunnel.database.AttachmentInfoMapper;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.AttachmentInfoDO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * AttachmentInfo 仓储接口
 *attachmentInfoDO
 * <AUTHOR> Xiaohua 18/06/2025 15:10
 **/
@Service
@AllArgsConstructor
public class AttachmentInfoRepositoryImpl implements AttachmentInfoRepository {
    private final AttachmentInfoMapper attachmentInfoMapper;

    @Override
    public AttachmentInfo save(AttachmentInfo attachmentInfo) {
        AttachmentInfoDO attachmentInfoDO = BeanUtil.copyProperties(attachmentInfo,AttachmentInfoDO.class);
        attachmentInfoMapper.insert(attachmentInfoDO);
        attachmentInfo.setId(attachmentInfoDO.getId());
        return attachmentInfo;
    }

    @Override
    public AttachmentInfo findById(Long id) {
        AttachmentInfoDO attachmentInfoDO = attachmentInfoMapper.selectById(id);
        return AttachmentInfoConverter.INSTANCE.toEntity(attachmentInfoDO);
    }

    @Override
    public List<AttachmentInfo> findByIdList(List<Long> idList) {
        LambdaQueryWrapper<AttachmentInfoDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(AttachmentInfoDO::getId, idList);
        wrapper.eq(AttachmentInfoDO::getDelFlg, false);
        List<AttachmentInfoDO> doList = attachmentInfoMapper.selectList(wrapper);
        return AttachmentInfoConverter.INSTANCE.toEntityList(doList);
    }

    @Override
    public AttachmentInfo findFileByBusinessType(String businessType) {
        LambdaQueryWrapper<AttachmentInfoDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AttachmentInfoDO::getBusinessType, businessType);
        wrapper.eq(AttachmentInfoDO::getDelFlg, false);
        List<AttachmentInfoDO> attachmentInfoDOS = attachmentInfoMapper.selectList(wrapper);
        List<AttachmentInfo> list = AttachmentInfoConverter.INSTANCE.toEntityList(attachmentInfoDOS);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.getFirst();
    }
}
