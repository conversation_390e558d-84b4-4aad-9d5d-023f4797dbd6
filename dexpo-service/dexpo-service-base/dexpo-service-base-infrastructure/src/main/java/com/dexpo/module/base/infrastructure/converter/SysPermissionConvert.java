package com.dexpo.module.base.infrastructure.converter;

import com.dexpo.module.base.domain.model.valueobject.SysPermission;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.SysPermissionDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface SysPermissionConvert {

    SysPermissionConvert INSTANCE = Mappers.getMapper(SysPermissionConvert.class);

    List<SysPermission> d2eList(List<SysPermissionDO> list);
}
