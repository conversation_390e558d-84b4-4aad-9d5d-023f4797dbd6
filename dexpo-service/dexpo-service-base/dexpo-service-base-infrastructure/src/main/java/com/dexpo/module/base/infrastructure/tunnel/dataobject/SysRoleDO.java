package com.dexpo.module.base.infrastructure.tunnel.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

@Data
@TableName("sys_role")
public class SysRoleDO extends BaseDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色分类：值集VS_ACTION_USER_TYPE
     */
    private String roleCategory;

    /**
     * 角色类型：值集VS_ROLE_TYPE
     */
    private String roleType;

    /**
     * 角色描述
     */
    private String roleDescription;

    /**
     * 启用状态，1启用，0禁用
     */
    private Boolean isUseAble;


}
