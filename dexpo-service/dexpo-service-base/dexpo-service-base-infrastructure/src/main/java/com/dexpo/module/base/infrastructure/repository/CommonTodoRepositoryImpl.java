package com.dexpo.module.base.infrastructure.repository;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dexpo.framework.common.pojo.PageParam;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.base.domain.model.agg.CommonTodo;
import com.dexpo.module.base.domain.repository.CommonTodoRepository;
import com.dexpo.module.base.infrastructure.converter.CommonTodoConvert;
import com.dexpo.module.base.infrastructure.tunnel.database.CommonTodoMapper;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.CommonTodoDO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class CommonTodoRepositoryImpl implements CommonTodoRepository {
    private final CommonTodoMapper commonTodoMapper;

    @Override
    public PageResult<CommonTodo> getPage(CommonTodo commonTodo, PageParam pageParam) {
        IPage<CommonTodoDO> page = new Page<>(pageParam.getPageNo(), pageParam.getPageSize());
        IPage<CommonTodoDO> pageResult = commonTodoMapper.selectPage(page, Wrappers.<CommonTodoDO>lambdaQuery()
                .eq(CommonTodoDO::getStatus, commonTodo.getStatus())
                .orderByDesc(CommonTodoDO::getGenerationTime));
        List<CommonTodo> voList = CommonTodoConvert.INSTANCE.e2dList(pageResult.getRecords());
        return new PageResult<>(voList, page.getTotal());
    }

    @Override
    public CommonTodo save(CommonTodo commonTodo) {
        CommonTodoDO commonTodoDO = BeanUtil.copyProperties(commonTodo, CommonTodoDO.class);
        commonTodoMapper.insert(commonTodoDO);
        commonTodo.setId(commonTodoDO.getId());
        return commonTodo;
    }

    @Override
    public CommonTodo selectOneByCommonTodo(CommonTodo commonTodo) {
        CommonTodoDO commonTodoDO = commonTodoMapper.selectOne(Wrappers.<CommonTodoDO>lambdaQuery()
                .eq(CommonTodoDO::getBusinessNo, commonTodo.getBusinessNo())
                .eq(CommonTodoDO::getBusinessType, commonTodo.getBusinessType())
                .eq(CommonTodoDO::getExhibitionId, commonTodo.getExhibitionId())
                .eq(CommonTodoDO::getStatus, commonTodo.getStatus())
                .orderByDesc(CommonTodoDO::getGenerationTime), false);
        return  CommonTodoConvert.INSTANCE.e2d(commonTodoDO);
    }

    @Override
    public void updateById(CommonTodo commonTodo) {
        CommonTodoDO commonTodoDO = BeanUtil.copyProperties(commonTodo, CommonTodoDO.class);
        commonTodoMapper.updateById(commonTodoDO);
    }

}