package com.dexpo.module.base.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dexpo.module.base.domain.model.agg.BasicValueSetOption;
import com.dexpo.module.base.domain.repository.BasicValueSetOptionRepository;
import com.dexpo.module.base.infrastructure.converter.BasicValueSetOptionConvert;
import com.dexpo.module.base.infrastructure.tunnel.database.BasicValuesetOptionMapper;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.BasicValuesetOptionDO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 值集选项 Repository 实现
 */
@Service
@AllArgsConstructor
public class BasicValueSetOptionRepositoryImpl implements BasicValueSetOptionRepository {
    private final BasicValuesetOptionMapper basicValuesetOptionMapper;

    @Override
    public List<BasicValueSetOption> list() {
        List<BasicValuesetOptionDO> doList = basicValuesetOptionMapper.selectList();
        return BasicValueSetOptionConvert.INSTANCE.e2dList(doList);
    }

    @Override
    public BasicValueSetOption selectByOptionCode(String optionCode) {
        LambdaQueryWrapper<BasicValuesetOptionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BasicValuesetOptionDO::getOptionCode, optionCode)
                .eq(BasicValuesetOptionDO::getDelFlg, Boolean.FALSE);
        BasicValuesetOptionDO optionDO = basicValuesetOptionMapper.selectOne(queryWrapper,false);
        return BasicValueSetOptionConvert.INSTANCE.e2d(optionDO);
    }


}