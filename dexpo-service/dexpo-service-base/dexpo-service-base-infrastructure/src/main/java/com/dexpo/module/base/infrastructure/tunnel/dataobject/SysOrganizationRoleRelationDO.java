package com.dexpo.module.base.infrastructure.tunnel.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

@Data
@TableName("sys_organization_role_relation")
public class SysOrganizationRoleRelationDO extends BaseDO {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 项目组织code
     */
    private String organizationCode;

    /**
     * 角色编码
     */
    private String roleCode;

} 