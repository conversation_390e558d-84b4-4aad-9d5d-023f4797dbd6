<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dexpo</groupId>
        <artifactId>dexpo-service-base</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>dexpo-service-base-app</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-base-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-base-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-base-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>