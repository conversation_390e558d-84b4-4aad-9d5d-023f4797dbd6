package com.dexpo.module.base.app.converter;

import com.dexpo.module.base.BaseUnitTest;
import com.dexpo.module.base.api.role.dto.SysRoleDTO;
import com.dexpo.module.base.api.role.vo.SysPermissionRoleRelationVO;
import com.dexpo.module.base.api.role.vo.SysRoleVO;
import com.dexpo.module.base.domain.model.valueobject.SysPermissionRoleRelation;
import com.dexpo.module.base.domain.model.valueobject.SysRole;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link SysRoleDTOConvert} 的单元测试类
 * 
 * <p>测试系统角色转换器的所有转换方法，确保100%分支覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class SysRoleDTOConvertTest extends BaseUnitTest {

    /**
     * 测试实体转VO - 正常情况
     */
    @Test
    void testE2v_Success() {
        // Arrange
        SysRole entity = new SysRole();
        entity.setId(1L);
        entity.setRoleName("系统管理员");
        entity.setRoleCode("SYSTEM_ADMIN");
        entity.setRoleType("SYSTEM");
        entity.setRoleDescription("系统管理员角色");
        entity.setRoleCategory("ADMIN");
        entity.setOrganizationCode("ORG001");
        entity.setCreateTime(LocalDateTime.of(2024, 1, 1, 0, 0, 0));
        entity.setUpdateTime(LocalDateTime.of(2024, 6, 15, 12, 0, 0));

        // Act
        SysRoleVO vo = SysRoleDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(1L, vo.getId());
        assertEquals("系统管理员", vo.getRoleName());
        assertEquals("SYSTEM_ADMIN", vo.getRoleCode());
        assertEquals("SYSTEM", vo.getRoleType());
        assertEquals("系统管理员角色", vo.getRoleDescription());
        assertEquals("ADMIN", vo.getRoleCategory());
        assertEquals("ORG001", vo.getOrganizationCode());
        assertEquals(LocalDateTime.of(2024, 1, 1, 0, 0, 0), vo.getCreateTime());
        assertEquals(LocalDateTime.of(2024, 6, 15, 12, 0, 0), vo.getUpdateTime());
    }

    /**
     * 测试实体转VO - null输入
     */
    @Test
    void testE2v_WithNull() {
        // Act
        SysRoleVO vo = SysRoleDTOConvert.INSTANCE.e2v((SysRole) null);

        // Assert
        assertNull(vo);
    }

    /**
     * 测试实体转VO - 空字段
     */
    @Test
    void testE2v_WithEmptyFields() {
        // Arrange
        SysRole entity = new SysRole();
        entity.setId(null);
        entity.setRoleName("");
        entity.setRoleCode("");
        entity.setRoleType("");
        entity.setRoleDescription("");
        entity.setRoleCategory("");
        entity.setOrganizationCode("");
        entity.setCreateTime(null);
        entity.setUpdateTime(null);

        // Act
        SysRoleVO vo = SysRoleDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertNull(vo.getId());
        assertEquals("", vo.getRoleName());
        assertEquals("", vo.getRoleCode());
        assertEquals("", vo.getRoleType());
        assertEquals("", vo.getRoleDescription());
        assertEquals("", vo.getRoleCategory());
        assertEquals("", vo.getOrganizationCode());
        assertNull(vo.getCreateTime());
        assertNull(vo.getUpdateTime());
    }

    /**
     * 测试实体列表转VO列表 - 正常情况
     */
    @Test
    void testE2vList_Success() {
        // Arrange
        SysRole entity1 = new SysRole();
        entity1.setId(1L);
        entity1.setRoleName("管理员");
        entity1.setRoleCode("ADMIN");
        entity1.setRoleType("SYSTEM");
        entity1.setRoleCategory("ADMIN");

        SysRole entity2 = new SysRole();
        entity2.setId(2L);
        entity2.setRoleName("普通用户");
        entity2.setRoleCode("USER");
        entity2.setRoleType("BUSINESS");
        entity2.setRoleCategory("USER");

        List<SysRole> entityList = Arrays.asList(entity1, entity2);

        // Act
        List<SysRoleVO> voList = SysRoleDTOConvert.INSTANCE.e2v(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        
        SysRoleVO vo1 = voList.get(0);
        assertEquals(1L, vo1.getId());
        assertEquals("管理员", vo1.getRoleName());
        assertEquals("ADMIN", vo1.getRoleCode());
        assertEquals("SYSTEM", vo1.getRoleType());
        assertEquals("ADMIN", vo1.getRoleCategory());

        SysRoleVO vo2 = voList.get(1);
        assertEquals(2L, vo2.getId());
        assertEquals("普通用户", vo2.getRoleName());
        assertEquals("USER", vo2.getRoleCode());
        assertEquals("BUSINESS", vo2.getRoleType());
        assertEquals("USER", vo2.getRoleCategory());
    }

    /**
     * 测试实体列表转VO列表 - 空列表
     */
    @Test
    void testE2vList_WithEmptyList() {
        // Act
        List<SysRoleVO> voList = SysRoleDTOConvert.INSTANCE.e2v(Collections.emptyList());

        // Assert
        assertNotNull(voList);
        assertTrue(voList.isEmpty());
    }

    /**
     * 测试实体列表转VO列表 - null输入
     */
    @Test
    void testE2vList_WithNull() {
        // Act
        List<SysRoleVO> voList = SysRoleDTOConvert.INSTANCE.e2v((List<SysRole>) null);

        // Assert
        assertNull(voList);
    }

    /**
     * 测试DTO转实体 - 正常情况
     */
    @Test
    void testDte_Success() {
        // Arrange
        SysRoleDTO dto = new SysRoleDTO();
        dto.setId(1L);
        dto.setRoleName("业务管理员");
        dto.setRoleCode("BUSINESS_ADMIN");
        dto.setRoleType("BUSINESS");
        dto.setRoleDescription("业务管理员角色");
        dto.setRoleCategory("BUSINESS");
        dto.setOrganizationCode("ORG002");

        // Act
        SysRole entity = SysRoleDTOConvert.INSTANCE.dte(dto);

        // Assert
        assertNotNull(entity);
        assertEquals(1L, entity.getId());
        assertEquals("业务管理员", entity.getRoleName());
        assertEquals("BUSINESS_ADMIN", entity.getRoleCode());
        assertEquals("BUSINESS", entity.getRoleType());
        assertEquals("业务管理员角色", entity.getRoleDescription());
        assertEquals("BUSINESS", entity.getRoleCategory());
        assertEquals("ORG002", entity.getOrganizationCode());
    }

    /**
     * 测试DTO转实体 - null输入
     */
    @Test
    void testDte_WithNull() {
        // Act
        SysRole entity = SysRoleDTOConvert.INSTANCE.dte(null);

        // Assert
        assertNull(entity);
    }

    /**
     * 测试DTO转实体 - 空字段
     */
    @Test
    void testDte_WithEmptyFields() {
        // Arrange
        SysRoleDTO dto = new SysRoleDTO();
        dto.setId(null);
        dto.setRoleName("");
        dto.setRoleCode("");
        dto.setRoleType("");

        // Act
        SysRole entity = SysRoleDTOConvert.INSTANCE.dte(dto);

        // Assert
        assertNotNull(entity);
        assertNull(entity.getId());
        assertEquals("", entity.getRoleName());
        assertEquals("", entity.getRoleCode());
        assertEquals("", entity.getRoleType());
    }

    /**
     * 测试权限角色关系列表转VO列表 - 正常情况
     */
    @Test
    void testE2vListPermissionRoleRelation_Success() {
        // Arrange
        SysPermissionRoleRelation relation1 = new SysPermissionRoleRelation();
        relation1.setId(1L);
        relation1.setPermissionId(100L);
        relation1.setRoleId(200L);
        relation1.setPermissionName("用户管理");
        relation1.setRoleName("管理员");

        SysPermissionRoleRelation relation2 = new SysPermissionRoleRelation();
        relation2.setId(2L);
        relation2.setPermissionId(101L);
        relation2.setRoleId(201L);
        relation2.setPermissionName("角色管理");
        relation2.setRoleName("超级管理员");

        List<SysPermissionRoleRelation> relationList = Arrays.asList(relation1, relation2);

        // Act
        List<SysPermissionRoleRelationVO> voList = SysRoleDTOConvert.INSTANCE.e2vList(relationList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        
        SysPermissionRoleRelationVO vo1 = voList.get(0);
        assertEquals(1L, vo1.getId());
        assertEquals(100L, vo1.getPermissionId());
        assertEquals(200L, vo1.getRoleId());
        assertEquals("用户管理", vo1.getPermissionName());
        assertEquals("管理员", vo1.getRoleName());

        SysPermissionRoleRelationVO vo2 = voList.get(1);
        assertEquals(2L, vo2.getId());
        assertEquals(101L, vo2.getPermissionId());
        assertEquals(201L, vo2.getRoleId());
        assertEquals("角色管理", vo2.getPermissionName());
        assertEquals("超级管理员", vo2.getRoleName());
    }

    /**
     * 测试权限角色关系列表转VO列表 - 空列表
     */
    @Test
    void testE2vListPermissionRoleRelation_WithEmptyList() {
        // Act
        List<SysPermissionRoleRelationVO> voList = SysRoleDTOConvert.INSTANCE.e2vList(Collections.emptyList());

        // Assert
        assertNotNull(voList);
        assertTrue(voList.isEmpty());
    }

    /**
     * 测试权限角色关系列表转VO列表 - null输入
     */
    @Test
    void testE2vListPermissionRoleRelation_WithNull() {
        // Act
        List<SysPermissionRoleRelationVO> voList = SysRoleDTOConvert.INSTANCE.e2vList(null);

        // Assert
        assertNull(voList);
    }

    /**
     * 测试转换器实例不为null
     */
    @Test
    void testInstanceNotNull() {
        assertNotNull(SysRoleDTOConvert.INSTANCE);
    }

    /**
     * 测试包含null元素的列表
     */
    @Test
    void testE2vList_WithNullElements() {
        // Arrange
        SysRole entity = new SysRole();
        entity.setId(1L);
        entity.setRoleName("有效角色");

        List<SysRole> entityList = Arrays.asList(entity, null);

        // Act
        List<SysRoleVO> voList = SysRoleDTOConvert.INSTANCE.e2v(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        assertNotNull(voList.get(0));
        assertEquals(1L, voList.get(0).getId());
        assertEquals("有效角色", voList.get(0).getRoleName());
        assertNull(voList.get(1));
    }

    /**
     * 测试特殊字符处理
     */
    @Test
    void testE2v_WithSpecialCharacters() {
        // Arrange
        SysRole entity = new SysRole();
        entity.setId(999L);
        entity.setRoleName("特殊角色@#$%");
        entity.setRoleCode("SPECIAL_ROLE@#$");
        entity.setRoleType("SPECIAL_TYPE");
        entity.setDescription("描述包含特殊字符：<>&\"'");
        entity.setStatus("SPECIAL_STATUS");

        // Act
        SysRoleVO vo = SysRoleDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(999L, vo.getId());
        assertEquals("特殊角色@#$%", vo.getRoleName());
        assertEquals("SPECIAL_ROLE@#$", vo.getRoleCode());
        assertEquals("SPECIAL_TYPE", vo.getRoleType());
        assertEquals("描述包含特殊字符：<>&\"'", vo.getDescription());
        assertEquals("SPECIAL_STATUS", vo.getStatus());
    }

    /**
     * 测试边界值
     */
    @Test
    void testE2v_WithBoundaryValues() {
        // Arrange
        SysRole entity = new SysRole();
        entity.setId(0L);
        entity.setRoleName("0");
        entity.setRoleCode("0");
        entity.setRoleType("0");
        entity.setDescription("0");
        entity.setStatus("0");
        entity.setSort(0);
        entity.setCreateTime(LocalDateTime.MIN);
        entity.setUpdateTime(LocalDateTime.MIN);

        // Act
        SysRoleVO vo = SysRoleDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(0L, vo.getId());
        assertEquals("0", vo.getRoleName());
        assertEquals("0", vo.getRoleCode());
        assertEquals("0", vo.getRoleType());
        assertEquals("0", vo.getDescription());
        assertEquals("0", vo.getStatus());
        assertEquals(0, vo.getSort());
        assertEquals(LocalDateTime.MIN, vo.getCreateTime());
        assertEquals(LocalDateTime.MIN, vo.getUpdateTime());
    }

    /**
     * 测试最大值边界
     */
    @Test
    void testE2v_WithMaxValues() {
        // Arrange
        SysRole entity = new SysRole();
        entity.setId(Long.MAX_VALUE);
        entity.setSort(Integer.MAX_VALUE);
        entity.setCreateTime(LocalDateTime.MAX);
        entity.setUpdateTime(LocalDateTime.MAX);

        // Act
        SysRoleVO vo = SysRoleDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(Long.MAX_VALUE, vo.getId());
        assertEquals(Integer.MAX_VALUE, vo.getSort());
        assertEquals(LocalDateTime.MAX, vo.getCreateTime());
        assertEquals(LocalDateTime.MAX, vo.getUpdateTime());
    }

    /**
     * 测试长字符串处理
     */
    @Test
    void testE2v_WithLongStrings() {
        // Arrange
        String longString = "A".repeat(500);
        SysRole entity = new SysRole();
        entity.setId(1L);
        entity.setRoleName(longString);
        entity.setRoleCode(longString);
        entity.setRoleType(longString);
        entity.setDescription(longString);
        entity.setStatus(longString);

        // Act
        SysRoleVO vo = SysRoleDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(1L, vo.getId());
        assertEquals(longString, vo.getRoleName());
        assertEquals(longString, vo.getRoleCode());
        assertEquals(longString, vo.getRoleType());
        assertEquals(longString, vo.getDescription());
        assertEquals(longString, vo.getStatus());
    }

    /**
     * 测试角色类型的业务场景
     */
    @Test
    void testE2v_RoleTypeScenarios() {
        // Arrange - 测试不同类型的角色
        SysRole systemRole = new SysRole();
        systemRole.setId(1L);
        systemRole.setRoleName("系统管理员");
        systemRole.setRoleCode("SYSTEM_ADMIN");
        systemRole.setRoleType("SYSTEM");
        systemRole.setStatus("ACTIVE");

        SysRole businessRole = new SysRole();
        businessRole.setId(2L);
        businessRole.setRoleName("业务管理员");
        businessRole.setRoleCode("BUSINESS_ADMIN");
        businessRole.setRoleType("BUSINESS");
        businessRole.setStatus("ACTIVE");

        // Act
        SysRoleVO systemVO = SysRoleDTOConvert.INSTANCE.e2v(systemRole);
        SysRoleVO businessVO = SysRoleDTOConvert.INSTANCE.e2v(businessRole);

        // Assert
        assertNotNull(systemVO);
        assertEquals("SYSTEM", systemVO.getRoleType());
        assertEquals("系统管理员", systemVO.getRoleName());

        assertNotNull(businessVO);
        assertEquals("BUSINESS", businessVO.getRoleType());
        assertEquals("业务管理员", businessVO.getRoleName());
    }
}
