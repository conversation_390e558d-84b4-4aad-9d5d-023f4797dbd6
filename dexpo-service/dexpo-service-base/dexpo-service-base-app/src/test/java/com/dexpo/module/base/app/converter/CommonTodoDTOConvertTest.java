package com.dexpo.module.base.app.converter;

import com.dexpo.module.base.api.basic.vo.CommonTodoVO;
import com.dexpo.module.base.domain.model.agg.CommonTodo;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link CommonTodoDTOConvert} 的单元测试类
 * 
 * <p>测试通用待办事项转换器的所有转换方法，确保100%分支覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class CommonTodoDTOConvertTest  {

    /**
     * 测试实体转VO - 正常情况
     */
    @Test
    void testE2v_Success() {
        // Arrange
        CommonTodo entity = new CommonTodo();
        entity.setId(1L);
        entity.setTodoTitle("测试待办事项");
        entity.setBusinessType("MEDIA_USER_AUDIT");
        entity.setBusinessNo("BIZ-001");
        entity.setUserType("MEDIA");
        entity.setUserId(100L);
        entity.setExhibitionId(200L);
        entity.setStatus("TODO");
        entity.setGenerationTime(LocalDateTime.of(2024, 1, 1, 0, 0, 0));
        entity.setDoneTime(LocalDateTime.of(2024, 6, 15, 12, 0, 0));

        // Act
        CommonTodoVO vo = CommonTodoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(1L, vo.getId());
        assertEquals("测试待办事项", vo.getTodoTitle());
        assertEquals("MEDIA_USER_AUDIT", vo.getBusinessType());
        assertEquals("BIZ-001", vo.getBusinessNo());
        assertEquals("MEDIA", vo.getUserType());
        assertEquals(100L, vo.getUserId());
        assertEquals(200L, vo.getExhibitionId());
        assertEquals("TODO", vo.getStatus());
        assertEquals(LocalDateTime.of(2024, 1, 1, 0, 0, 0), vo.getGenerationTime());
        assertEquals(LocalDateTime.of(2024, 6, 15, 12, 0, 0), vo.getDoneTime());
    }

    /**
     * 测试实体转VO - null输入
     */
    @Test
    void testE2v_WithNull() {
        // Act
        CommonTodoVO vo = CommonTodoDTOConvert.INSTANCE.e2v((CommonTodo) null);

        // Assert
        assertNull(vo);
    }

    /**
     * 测试实体转VO - 空字段
     */
    @Test
    void testE2v_WithEmptyFields() {
        // Arrange
        CommonTodo entity = new CommonTodo();
        entity.setId(null);
        entity.setTodoTitle("");
        entity.setBusinessType("");
        entity.setBusinessNo("");
        entity.setUserType("");
        entity.setUserId(null);
        entity.setExhibitionId(null);
        entity.setStatus("");
        entity.setGenerationTime(null);
        entity.setDoneTime(null);

        // Act
        CommonTodoVO vo = CommonTodoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertNull(vo.getId());
        assertEquals("", vo.getTodoTitle());
        assertEquals("", vo.getBusinessType());
        assertEquals("", vo.getBusinessNo());
        assertEquals("", vo.getUserType());
        assertNull(vo.getUserId());
        assertNull(vo.getExhibitionId());
        assertEquals("", vo.getStatus());
        assertNull(vo.getGenerationTime());
        assertNull(vo.getDoneTime());
    }

    /**
     * 测试实体转VO - null字段
     */
    @Test
    void testE2v_WithNullFields() {
        // Arrange
        CommonTodo entity = new CommonTodo();
        entity.setId(1L);
        entity.setTodoTitle(null);
        entity.setBusinessType(null);
        entity.setBusinessNo(null);
        entity.setUserType(null);
        entity.setUserId(null);
        entity.setExhibitionId(null);
        entity.setStatus(null);
        entity.setGenerationTime(null);
        entity.setDoneTime(null);

        // Act
        CommonTodoVO vo = CommonTodoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(1L, vo.getId());
        assertNull(vo.getTodoTitle());
        assertNull(vo.getBusinessType());
        assertNull(vo.getBusinessNo());
        assertNull(vo.getUserType());
        assertNull(vo.getUserId());
        assertNull(vo.getExhibitionId());
        assertNull(vo.getStatus());
        assertNull(vo.getGenerationTime());
        assertNull(vo.getDoneTime());
    }

    /**
     * 测试实体列表转VO列表 - 正常情况
     */
    @Test
    void testE2vList_Success() {
        // Arrange
        CommonTodo entity1 = new CommonTodo();
        entity1.setId(1L);
        entity1.setTodoTitle("待办事项1");
        entity1.setBusinessType("MEDIA_USER_AUDIT");
        entity1.setBusinessNo("BIZ-001");
        entity1.setStatus("TODO");
        entity1.setUserType("MEDIA");
        entity1.setUserId(100L);
        entity1.setExhibitionId(300L);

        CommonTodo entity2 = new CommonTodo();
        entity2.setId(2L);
        entity2.setTodoTitle("待办事项2");
        entity2.setBusinessType("MEDIA_USER_AUDIT");
        entity2.setBusinessNo("BIZ-002");
        entity2.setStatus("DONE");
        entity2.setUserType("SPONSOR");
        entity2.setUserId(200L);
        entity2.setExhibitionId(400L);

        List<CommonTodo> entityList = Arrays.asList(entity1, entity2);

        // Act
        List<CommonTodoVO> voList = CommonTodoDTOConvert.INSTANCE.e2v(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        
        CommonTodoVO vo1 = voList.get(0);
        assertEquals(1L, vo1.getId());
        assertEquals("待办事项1", vo1.getTodoTitle());
        assertEquals("MEDIA_USER_AUDIT", vo1.getBusinessType());
        assertEquals("BIZ-001", vo1.getBusinessNo());
        assertEquals("TODO", vo1.getStatus());
        assertEquals("MEDIA", vo1.getUserType());
        assertEquals(100L, vo1.getUserId());
        assertEquals(300L, vo1.getExhibitionId());

        CommonTodoVO vo2 = voList.get(1);
        assertEquals(2L, vo2.getId());
        assertEquals("待办事项2", vo2.getTodoTitle());
        assertEquals("MEDIA_USER_AUDIT", vo2.getBusinessType());
        assertEquals("BIZ-002", vo2.getBusinessNo());
        assertEquals("DONE", vo2.getStatus());
        assertEquals("SPONSOR", vo2.getUserType());
        assertEquals(200L, vo2.getUserId());
        assertEquals(400L, vo2.getExhibitionId());
    }

    /**
     * 测试实体列表转VO列表 - 空列表
     */
    @Test
    void testE2vList_WithEmptyList() {
        // Act
        List<CommonTodoVO> voList = CommonTodoDTOConvert.INSTANCE.e2v(Collections.emptyList());

        // Assert
        assertNotNull(voList);
        assertTrue(voList.isEmpty());
    }

    /**
     * 测试实体列表转VO列表 - null输入
     */
    @Test
    void testE2vList_WithNull() {
        // Act
        List<CommonTodoVO> voList = CommonTodoDTOConvert.INSTANCE.e2v((List<CommonTodo>) null);

        // Assert
        assertNull(voList);
    }

    /**
     * 测试实体列表转VO列表 - 包含null元素
     */
    @Test
    void testE2vList_WithNullElements() {
        // Arrange
        CommonTodo entity = new CommonTodo();
        entity.setId(1L);
        entity.setTodoTitle("有效待办事项");
        entity.setStatus("TODO");

        List<CommonTodo> entityList = Arrays.asList(entity, null);

        // Act
        List<CommonTodoVO> voList = CommonTodoDTOConvert.INSTANCE.e2v(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        assertNotNull(voList.get(0));
        assertEquals(1L, voList.get(0).getId());
        assertEquals("有效待办事项", voList.get(0).getTodoTitle());
        assertNull(voList.get(1));
    }

    /**
     * 测试转换器实例不为null
     */
    @Test
    void testInstanceNotNull() {
        assertNotNull(CommonTodoDTOConvert.INSTANCE);
    }

    /**
     * 测试特殊字符处理
     */
    @Test
    void testE2v_WithSpecialCharacters() {
        // Arrange
        CommonTodo entity = new CommonTodo();
        entity.setId(999L);
        entity.setTodoTitle("特殊字符测试@#$%^&*()");
        entity.setBusinessType("SPECIAL_TYPE@#$");
        entity.setBusinessNo("BIZ@#$%");
        entity.setStatus("SPECIAL_STATUS");
        entity.setUserType("SPECIAL_USER");
        entity.setUserId(999L);
        entity.setExhibitionId(888L);

        // Act
        CommonTodoVO vo = CommonTodoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(999L, vo.getId());
        assertEquals("特殊字符测试@#$%^&*()", vo.getTodoTitle());
        assertEquals("SPECIAL_TYPE@#$", vo.getBusinessType());
        assertEquals("BIZ@#$%", vo.getBusinessNo());
        assertEquals("SPECIAL_STATUS", vo.getStatus());
        assertEquals("SPECIAL_USER", vo.getUserType());
        assertEquals(999L, vo.getUserId());
        assertEquals(888L, vo.getExhibitionId());
    }

    /**
     * 测试长字符串处理
     */
    @Test
    void testE2v_WithLongStrings() {
        // Arrange
        String longString = "A".repeat(1000);
        CommonTodo entity = new CommonTodo();
        entity.setId(Long.MAX_VALUE);
        entity.setTodoTitle(longString);
        entity.setBusinessType(longString);
        entity.setBusinessNo(longString);
        entity.setStatus(longString);
        entity.setUserType(longString);
        entity.setUserId(Long.MAX_VALUE);
        entity.setExhibitionId(Long.MAX_VALUE);

        // Act
        CommonTodoVO vo = CommonTodoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(Long.MAX_VALUE, vo.getId());
        assertEquals(longString, vo.getTodoTitle());
        assertEquals(longString, vo.getBusinessType());
        assertEquals(longString, vo.getBusinessNo());
        assertEquals(longString, vo.getStatus());
        assertEquals(longString, vo.getUserType());
        assertEquals(Long.MAX_VALUE, vo.getUserId());
        assertEquals(Long.MAX_VALUE, vo.getExhibitionId());
    }

    /**
     * 测试边界值
     */
    @Test
    void testE2v_WithBoundaryValues() {
        // Arrange
        CommonTodo entity = new CommonTodo();
        entity.setId(0L);
        entity.setTodoTitle("0");
        entity.setBusinessType("0");
        entity.setBusinessNo("0");
        entity.setStatus("0");
        entity.setUserType("0");
        entity.setUserId(0L);
        entity.setExhibitionId(0L);
        entity.setGenerationTime(LocalDateTime.MIN);
        entity.setDoneTime(LocalDateTime.MIN);

        // Act
        CommonTodoVO vo = CommonTodoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(0L, vo.getId());
        assertEquals("0", vo.getTodoTitle());
        assertEquals("0", vo.getBusinessType());
        assertEquals("0", vo.getBusinessNo());
        assertEquals("0", vo.getStatus());
        assertEquals("0", vo.getUserType());
        assertEquals(0L, vo.getUserId());
        assertEquals(0L, vo.getExhibitionId());
        assertEquals(LocalDateTime.MIN, vo.getGenerationTime());
        assertEquals(LocalDateTime.MIN, vo.getDoneTime());
    }

    /**
     * 测试最大值边界
     */
    @Test
    void testE2v_WithMaxValues() {
        // Arrange
        CommonTodo entity = new CommonTodo();
        entity.setId(Long.MAX_VALUE);
        entity.setUserId(Long.MAX_VALUE);
        entity.setExhibitionId(Long.MAX_VALUE);
        entity.setGenerationTime(LocalDateTime.MAX);
        entity.setDoneTime(LocalDateTime.MAX);

        // Act
        CommonTodoVO vo = CommonTodoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(Long.MAX_VALUE, vo.getId());
        assertEquals(Long.MAX_VALUE, vo.getUserId());
        assertEquals(Long.MAX_VALUE, vo.getExhibitionId());
        assertEquals(LocalDateTime.MAX, vo.getGenerationTime());
        assertEquals(LocalDateTime.MAX, vo.getDoneTime());
    }

    /**
     * 测试业务场景 - 媒体用户审核待办
     */
    @Test
    void testE2v_MediaUserAuditScenario() {
        // Arrange
        CommonTodo entity = new CommonTodo();
        entity.setId(1L);
        entity.setTodoTitle("媒体用户审核");
        entity.setBusinessType("MEDIA_USER_AUDIT");
        entity.setBusinessNo("MEDIA-2024-001");
        entity.setUserType("MEDIA");
        entity.setUserId(1001L);
        entity.setExhibitionId(2001L);
        entity.setStatus("TODO");
        entity.setGenerationTime(LocalDateTime.of(2024, 1, 1, 9, 0, 0));

        // Act
        CommonTodoVO vo = CommonTodoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(1L, vo.getId());
        assertEquals("媒体用户审核", vo.getTodoTitle());
        assertEquals("MEDIA_USER_AUDIT", vo.getBusinessType());
        assertEquals("MEDIA-2024-001", vo.getBusinessNo());
        assertEquals("MEDIA", vo.getUserType());
        assertEquals(1001L, vo.getUserId());
        assertEquals(2001L, vo.getExhibitionId());
        assertEquals("TODO", vo.getStatus());
        assertEquals(LocalDateTime.of(2024, 1, 1, 9, 0, 0), vo.getGenerationTime());
    }
}
