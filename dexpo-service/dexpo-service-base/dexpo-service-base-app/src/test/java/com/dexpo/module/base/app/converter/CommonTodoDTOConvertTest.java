package com.dexpo.module.base.app.converter;

import com.dexpo.module.base.api.basic.vo.CommonTodoVO;
import com.dexpo.module.base.domain.model.agg.CommonTodo;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link CommonTodoDTOConvert} 的单元测试类
 * 
 * <p>测试通用待办事项转换器的所有转换方法，确保100%分支覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class CommonTodoDTOConvertTest {

    /**
     * 测试实体转VO - 正常情况
     */
    @Test
    void testE2v_Success() {
        // Arrange
        CommonTodo entity = new CommonTodo();
        entity.setId(1L);
        entity.setTitle("测试待办事项");
        entity.setContent("这是一个测试待办事项的内容");
        entity.setStatus("PENDING");
        entity.setPriority("HIGH");
        entity.setAssigneeId(100L);
        entity.setAssigneeName("张三");
        entity.setCreatorId(200L);
        entity.setCreatorName("李四");
        entity.setDueDate(LocalDateTime.of(2024, 12, 31, 23, 59, 59));
        entity.setCreateTime(LocalDateTime.of(2024, 1, 1, 0, 0, 0));
        entity.setUpdateTime(LocalDateTime.of(2024, 6, 15, 12, 0, 0));

        // Act
        CommonTodoVO vo = CommonTodoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(1L, vo.getId());
        assertEquals("测试待办事项", vo.getTitle());
        assertEquals("这是一个测试待办事项的内容", vo.getContent());
        assertEquals("PENDING", vo.getStatus());
        assertEquals("HIGH", vo.getPriority());
        assertEquals(100L, vo.getAssigneeId());
        assertEquals("张三", vo.getAssigneeName());
        assertEquals(200L, vo.getCreatorId());
        assertEquals("李四", vo.getCreatorName());
        assertEquals(LocalDateTime.of(2024, 12, 31, 23, 59, 59), vo.getDueDate());
        assertEquals(LocalDateTime.of(2024, 1, 1, 0, 0, 0), vo.getCreateTime());
        assertEquals(LocalDateTime.of(2024, 6, 15, 12, 0, 0), vo.getUpdateTime());
    }

    /**
     * 测试实体转VO - null输入
     */
    @Test
    void testE2v_WithNull() {
        // Act
        CommonTodoVO vo = CommonTodoDTOConvert.INSTANCE.e2v((CommonTodo) null);

        // Assert
        assertNull(vo);
    }

    /**
     * 测试实体转VO - 空字段
     */
    @Test
    void testE2v_WithEmptyFields() {
        // Arrange
        CommonTodo entity = new CommonTodo();
        entity.setId(null);
        entity.setTitle("");
        entity.setContent("");
        entity.setStatus("");
        entity.setPriority("");
        entity.setAssigneeId(null);
        entity.setAssigneeName("");
        entity.setCreatorId(null);
        entity.setCreatorName("");
        entity.setDueDate(null);
        entity.setCreateTime(null);
        entity.setUpdateTime(null);

        // Act
        CommonTodoVO vo = CommonTodoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertNull(vo.getId());
        assertEquals("", vo.getTitle());
        assertEquals("", vo.getContent());
        assertEquals("", vo.getStatus());
        assertEquals("", vo.getPriority());
        assertNull(vo.getAssigneeId());
        assertEquals("", vo.getAssigneeName());
        assertNull(vo.getCreatorId());
        assertEquals("", vo.getCreatorName());
        assertNull(vo.getDueDate());
        assertNull(vo.getCreateTime());
        assertNull(vo.getUpdateTime());
    }

    /**
     * 测试实体转VO - null字段
     */
    @Test
    void testE2v_WithNullFields() {
        // Arrange
        CommonTodo entity = new CommonTodo();
        entity.setId(1L);
        entity.setTitle(null);
        entity.setContent(null);
        entity.setStatus(null);
        entity.setPriority(null);
        entity.setAssigneeId(null);
        entity.setAssigneeName(null);
        entity.setCreatorId(null);
        entity.setCreatorName(null);
        entity.setDueDate(null);
        entity.setCreateTime(null);
        entity.setUpdateTime(null);

        // Act
        CommonTodoVO vo = CommonTodoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(1L, vo.getId());
        assertNull(vo.getTitle());
        assertNull(vo.getContent());
        assertNull(vo.getStatus());
        assertNull(vo.getPriority());
        assertNull(vo.getAssigneeId());
        assertNull(vo.getAssigneeName());
        assertNull(vo.getCreatorId());
        assertNull(vo.getCreatorName());
        assertNull(vo.getDueDate());
        assertNull(vo.getCreateTime());
        assertNull(vo.getUpdateTime());
    }

    /**
     * 测试实体列表转VO列表 - 正常情况
     */
    @Test
    void testE2vList_Success() {
        // Arrange
        CommonTodo entity1 = new CommonTodo();
        entity1.setId(1L);
        entity1.setTitle("待办事项1");
        entity1.setContent("内容1");
        entity1.setStatus("PENDING");
        entity1.setPriority("HIGH");
        entity1.setAssigneeId(100L);
        entity1.setAssigneeName("张三");

        CommonTodo entity2 = new CommonTodo();
        entity2.setId(2L);
        entity2.setTitle("待办事项2");
        entity2.setContent("内容2");
        entity2.setStatus("COMPLETED");
        entity2.setPriority("LOW");
        entity2.setAssigneeId(200L);
        entity2.setAssigneeName("李四");

        List<CommonTodo> entityList = Arrays.asList(entity1, entity2);

        // Act
        List<CommonTodoVO> voList = CommonTodoDTOConvert.INSTANCE.e2v(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        
        CommonTodoVO vo1 = voList.get(0);
        assertEquals(1L, vo1.getId());
        assertEquals("待办事项1", vo1.getTitle());
        assertEquals("内容1", vo1.getContent());
        assertEquals("PENDING", vo1.getStatus());
        assertEquals("HIGH", vo1.getPriority());
        assertEquals(100L, vo1.getAssigneeId());
        assertEquals("张三", vo1.getAssigneeName());

        CommonTodoVO vo2 = voList.get(1);
        assertEquals(2L, vo2.getId());
        assertEquals("待办事项2", vo2.getTitle());
        assertEquals("内容2", vo2.getContent());
        assertEquals("COMPLETED", vo2.getStatus());
        assertEquals("LOW", vo2.getPriority());
        assertEquals(200L, vo2.getAssigneeId());
        assertEquals("李四", vo2.getAssigneeName());
    }

    /**
     * 测试实体列表转VO列表 - 空列表
     */
    @Test
    void testE2vList_WithEmptyList() {
        // Act
        List<CommonTodoVO> voList = CommonTodoDTOConvert.INSTANCE.e2v(Collections.emptyList());

        // Assert
        assertNotNull(voList);
        assertTrue(voList.isEmpty());
    }

    /**
     * 测试实体列表转VO列表 - null输入
     */
    @Test
    void testE2vList_WithNull() {
        // Act
        List<CommonTodoVO> voList = CommonTodoDTOConvert.INSTANCE.e2v((List<CommonTodo>) null);

        // Assert
        assertNull(voList);
    }

    /**
     * 测试实体列表转VO列表 - 包含null元素
     */
    @Test
    void testE2vList_WithNullElements() {
        // Arrange
        CommonTodo entity = new CommonTodo();
        entity.setId(1L);
        entity.setTitle("有效待办事项");
        entity.setStatus("PENDING");

        List<CommonTodo> entityList = Arrays.asList(entity, null);

        // Act
        List<CommonTodoVO> voList = CommonTodoDTOConvert.INSTANCE.e2v(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        assertNotNull(voList.get(0));
        assertEquals(1L, voList.get(0).getId());
        assertEquals("有效待办事项", voList.get(0).getTitle());
        assertNull(voList.get(1));
    }

    /**
     * 测试转换器实例不为null
     */
    @Test
    void testInstanceNotNull() {
        assertNotNull(CommonTodoDTOConvert.INSTANCE);
    }

    /**
     * 测试特殊字符处理
     */
    @Test
    void testE2v_WithSpecialCharacters() {
        // Arrange
        CommonTodo entity = new CommonTodo();
        entity.setId(999L);
        entity.setTitle("特殊字符测试@#$%^&*()");
        entity.setContent("内容包含特殊字符：<>&\"'");
        entity.setStatus("SPECIAL_STATUS");
        entity.setPriority("URGENT!!!");
        entity.setAssigneeName("用户名@domain.com");
        entity.setCreatorName("创建者<script>");

        // Act
        CommonTodoVO vo = CommonTodoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(999L, vo.getId());
        assertEquals("特殊字符测试@#$%^&*()", vo.getTitle());
        assertEquals("内容包含特殊字符：<>&\"'", vo.getContent());
        assertEquals("SPECIAL_STATUS", vo.getStatus());
        assertEquals("URGENT!!!", vo.getPriority());
        assertEquals("用户名@domain.com", vo.getAssigneeName());
        assertEquals("创建者<script>", vo.getCreatorName());
    }

    /**
     * 测试长字符串处理
     */
    @Test
    void testE2v_WithLongStrings() {
        // Arrange
        String longString = "A".repeat(1000);
        CommonTodo entity = new CommonTodo();
        entity.setId(Long.MAX_VALUE);
        entity.setTitle(longString);
        entity.setContent(longString);
        entity.setStatus(longString);
        entity.setPriority(longString);
        entity.setAssigneeName(longString);
        entity.setCreatorName(longString);

        // Act
        CommonTodoVO vo = CommonTodoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(Long.MAX_VALUE, vo.getId());
        assertEquals(longString, vo.getTitle());
        assertEquals(longString, vo.getContent());
        assertEquals(longString, vo.getStatus());
        assertEquals(longString, vo.getPriority());
        assertEquals(longString, vo.getAssigneeName());
        assertEquals(longString, vo.getCreatorName());
    }

    /**
     * 测试边界值
     */
    @Test
    void testE2v_WithBoundaryValues() {
        // Arrange
        CommonTodo entity = new CommonTodo();
        entity.setId(0L);
        entity.setTitle("0");
        entity.setContent("0");
        entity.setStatus("0");
        entity.setPriority("0");
        entity.setAssigneeId(0L);
        entity.setAssigneeName("0");
        entity.setCreatorId(0L);
        entity.setCreatorName("0");
        entity.setDueDate(LocalDateTime.MIN);
        entity.setCreateTime(LocalDateTime.MIN);
        entity.setUpdateTime(LocalDateTime.MIN);

        // Act
        CommonTodoVO vo = CommonTodoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(0L, vo.getId());
        assertEquals("0", vo.getTitle());
        assertEquals("0", vo.getContent());
        assertEquals("0", vo.getStatus());
        assertEquals("0", vo.getPriority());
        assertEquals(0L, vo.getAssigneeId());
        assertEquals("0", vo.getAssigneeName());
        assertEquals(0L, vo.getCreatorId());
        assertEquals("0", vo.getCreatorName());
        assertEquals(LocalDateTime.MIN, vo.getDueDate());
        assertEquals(LocalDateTime.MIN, vo.getCreateTime());
        assertEquals(LocalDateTime.MIN, vo.getUpdateTime());
    }

    /**
     * 测试最大值边界
     */
    @Test
    void testE2v_WithMaxValues() {
        // Arrange
        CommonTodo entity = new CommonTodo();
        entity.setId(Long.MAX_VALUE);
        entity.setAssigneeId(Long.MAX_VALUE);
        entity.setCreatorId(Long.MAX_VALUE);
        entity.setDueDate(LocalDateTime.MAX);
        entity.setCreateTime(LocalDateTime.MAX);
        entity.setUpdateTime(LocalDateTime.MAX);

        // Act
        CommonTodoVO vo = CommonTodoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(Long.MAX_VALUE, vo.getId());
        assertEquals(Long.MAX_VALUE, vo.getAssigneeId());
        assertEquals(Long.MAX_VALUE, vo.getCreatorId());
        assertEquals(LocalDateTime.MAX, vo.getDueDate());
        assertEquals(LocalDateTime.MAX, vo.getCreateTime());
        assertEquals(LocalDateTime.MAX, vo.getUpdateTime());
    }
}
