package com.dexpo.module.base.app.service;

import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.module.base.api.basic.dto.ExhibitionTagValuesetDTO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;
import com.dexpo.module.base.app.api.BasicValueSetOptionAppService;
import com.dexpo.module.base.app.entity.ExhibitionValuesetOptionRelationCache;
import com.dexpo.module.base.domain.model.agg.ExhibitionValuesetOptionRelation;
import com.dexpo.module.base.domain.service.ExhibitionOptionRelationDomainService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ExhibitionOptionRelationAppServiceImplTest {

    @Mock
    private ExhibitionOptionRelationDomainService examinationOptionRelationDomainService;

    @Mock
    private BasicValueSetOptionAppService basicValueSetOptionAppService;

    @Mock
    private RedisService redisService;

    @InjectMocks
    private ExhibitionOptionRelationAppServiceImpl service;

    @Test
    void getExhibitionValuesetListByCodes_shouldReturnFromCache() {
        // Arrange
        ExhibitionTagValuesetDTO request = new ExhibitionTagValuesetDTO();
        request.setExhibitionTagCode("tag1");
        request.setValuesetList(Collections.singletonList("vs1"));

        ExhibitionValuesetOptionRelationCache cacheItem = new ExhibitionValuesetOptionRelationCache();
        cacheItem.setValuesetOptionCode("opt1");
        List<ExhibitionValuesetOptionRelationCache> cacheList = Collections.singletonList(cacheItem);
        when(redisService.getCacheObject(anyString())).thenReturn(cacheList);

        List<BasicValuesetInfoVO> expectedVOs = Collections.singletonList(new BasicValuesetInfoVO());
        when(basicValueSetOptionAppService.listByValuesetOptionCodes(anyList(), anyList())).thenReturn(expectedVOs);

        // Act
        List<BasicValuesetInfoVO> result = service.getExhibitionValuesetListByCodes(request);

        // Assert
        assertNotNull(result);
        assertEquals(expectedVOs, result);
        verify(basicValueSetOptionAppService).listByValuesetOptionCodes(request.getValuesetList(), List.of("opt1"));
    }

    @Test
    void getExhibitionValuesetListByCodes_shouldReturnFromInit_whenCacheEmpty() {
        // Arrange
        ExhibitionTagValuesetDTO request = new ExhibitionTagValuesetDTO();
        request.setExhibitionTagCode("tag1");
        request.setValuesetList(Collections.singletonList("vs1"));

        when(redisService.getCacheObject(anyString())).thenReturn(Collections.emptyList());

        ExhibitionValuesetOptionRelation relation = new ExhibitionValuesetOptionRelation();
        relation.setExhibitionTagCode("tag1");
        relation.setValuesetOptionCode("opt1");
        when(examinationOptionRelationDomainService.list()).thenReturn(Collections.singletonList(relation));

        List<BasicValuesetInfoVO> expectedVOs = Collections.singletonList(new BasicValuesetInfoVO());
        when(basicValueSetOptionAppService.listByValuesetOptionCodes(anyList(), anyList())).thenReturn(expectedVOs);

        // Act
        List<BasicValuesetInfoVO> result = service.getExhibitionValuesetListByCodes(request);

        // Assert
        assertNotNull(result);
        assertEquals(expectedVOs, result);
        verify(redisService).setCacheObject(anyString(), anyList());
    }

    @Test
    void getExhibitionValuesetListByCodes_shouldReturnEmptyList_whenNoData() {
        // Arrange
        ExhibitionTagValuesetDTO request = new ExhibitionTagValuesetDTO();
        request.setExhibitionTagCode("tag1");
        when(redisService.getCacheObject(anyString())).thenReturn(null);
        when(examinationOptionRelationDomainService.list()).thenReturn(Collections.emptyList());

        // Act
        List<BasicValuesetInfoVO> result = service.getExhibitionValuesetListByCodes(request);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.size());
    }


    @Test
    void initExhibitionValuesetOptionCache_shouldGroupAndCache() {
        // Arrange
        ExhibitionValuesetOptionRelation relation1 = new ExhibitionValuesetOptionRelation();
        relation1.setExhibitionTagCode("tag1");
        relation1.setValuesetOptionCode("opt1");
        ExhibitionValuesetOptionRelation relation2 = new ExhibitionValuesetOptionRelation();
        relation2.setExhibitionTagCode("tag2");
        relation2.setValuesetOptionCode("opt2");
        List<ExhibitionValuesetOptionRelation> relations = List.of(relation1, relation2);
        when(examinationOptionRelationDomainService.list()).thenReturn(relations);
        doNothing().when(redisService).setCacheObject(anyString(), anyList());

        // Act
        Map<String, List<ExhibitionValuesetOptionRelationCache>> result = service.initExhibitionValuesetOptionCache();

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(1, result.get("tag1").size());
        assertEquals("opt1", result.get("tag1").get(0).getValuesetOptionCode());
        assertEquals(1, result.get("tag2").size());
        assertEquals("opt2", result.get("tag2").get(0).getValuesetOptionCode());
        verify(redisService, times(2)).setCacheObject(anyString(), anyList());
    }
} 