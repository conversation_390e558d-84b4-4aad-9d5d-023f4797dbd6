# 转换器单元测试修复指南

## 编译错误原因

生成的转换器测试代码存在以下编译错误：

### 1. 字段名不匹配
- 测试代码中使用的字段名与实际实体类的字段名不一致
- 例如：使用了 `setDescription()` 但实际字段是 `setRoleDescription()`

### 2. 类结构差异
- 实际的DTO和VO类结构与假设的结构不同
- 某些字段在实际类中不存在（如 `setSort()`, `setStatus()` 等）

### 3. 缺少基类继承
- 测试类没有继承 `BaseUnitTest` 基类

## 修复步骤

### 步骤1：查看实际类结构
在修复测试之前，需要查看实际的类结构：

```bash
# 查看实体类
cat dexpo-service/dexpo-service-base/dexpo-service-base-domain/src/main/java/com/dexpo/module/base/domain/model/valueobject/SysPermission.java

# 查看DTO类
cat dexpo-service/dexpo-service-base/dexpo-service-base-api/src/main/java/com/dexpo/module/base/api/permission/dto/SysPermissionDTO.java

# 查看VO类
cat dexpo-service/dexpo-service-base/dexpo-service-base-api/src/main/java/com/dexpo/module/base/api/permission/vo/SysPermissionVO.java
```

### 步骤2：修复字段映射
根据实际类结构修复字段映射：

#### SysPermission 实际字段：
- `permissionCode` - 权限代码
- `permissionName` - 权限名称
- `permissionType` - 权限类型
- `parentPermissionCode` - 父权限代码
- `roleCategory` - 角色类别
- `appType` - 应用类型
- `createTime` - 创建时间
- `updateTime` - 更新时间

#### SysRole 实际字段：
- `roleCode` - 角色代码
- `roleName` - 角色名称
- `roleType` - 角色类型
- `roleDescription` - 角色描述
- `roleCategory` - 角色类别
- `organizationCode` - 组织代码
- `createTime` - 创建时间
- `updateTime` - 更新时间

### 步骤3：使用模板修复测试
使用 `ConverterTestTemplate.java` 作为模板，按以下步骤修复：

1. 复制模板内容
2. 替换类名和字段名
3. 根据实际转换器方法调整测试方法
4. 确保继承 `BaseUnitTest`

### 步骤4：验证修复
运行测试验证修复结果：

```bash
cd dexpo-service/dexpo-service-base/dexpo-service-base-starter
mvn test -Dtest=*DTOConvertTest
```

## 已修复的文件

### 部分修复的文件：
- `SysPermissionDTOConvertTest.java` - 已修复基本结构和字段映射
- `SysRoleDTOConvertTest.java` - 已修复部分方法

### 需要继续修复的文件：
- `AttachmentInfoDTOConvertTest.java`
- `BasicLocationDTOConvertTest.java`
- `BasicRegionDTOConvertTest.java`
- `BasicValueSetOptionDTOConvertTest.java`
- `CommonTodoDTOConvertTest.java`
- `ManageOrganizationDTOConvertTest.java`
- `SysOrganizationDTOConvertTest.java`
- `SysPermissionInterfaceRelationDTOConvertTest.java`
- `SysPermissionRoleRelationDTOConvertTest.java`

## 修复示例

### 修复前（错误）：
```java
@Test
void testE2v_Success() {
    SysPermission entity = new SysPermission();
    entity.setId(1L);
    entity.setPath("/user");  // 错误：该字段不存在
    entity.setSort(1);        // 错误：该字段不存在
    
    SysPermissionVO vo = SysPermissionDTOConvert.INSTANCE.e2v(entity);
    assertEquals("/user", vo.getPath());  // 错误：该字段不存在
}
```

### 修复后（正确）：
```java
@Test
void testE2v_Success() {
    SysPermission entity = new SysPermission();
    entity.setId(1L);
    entity.setPermissionCode("USER_MANAGE");     // 正确：使用实际字段
    entity.setPermissionName("用户管理");         // 正确：使用实际字段
    entity.setParentPermissionCode("SYSTEM");    // 正确：使用实际字段
    
    SysPermissionVO vo = SysPermissionDTOConvert.INSTANCE.e2v(entity);
    assertEquals("USER_MANAGE", vo.getPermissionCode());  // 正确：使用实际字段
}
```

## 注意事项

1. **字段验证**：在编写测试前，务必验证字段是否存在于实际类中
2. **方法验证**：确认转换器接口中的方法签名
3. **继承关系**：确保测试类继承 `BaseUnitTest`
4. **导入语句**：检查所有必要的导入语句是否正确
5. **覆盖率目标**：确保测试覆盖所有分支和边界情况

## 测试覆盖要求

每个转换器测试应包含：
- ✅ 正常转换测试
- ✅ null输入测试
- ✅ 空字段测试
- ✅ 列表转换测试
- ✅ 边界值测试
- ✅ 特殊字符测试
- ✅ 实例验证测试
- ✅ 业务场景测试

确保达到100%分支覆盖率。
