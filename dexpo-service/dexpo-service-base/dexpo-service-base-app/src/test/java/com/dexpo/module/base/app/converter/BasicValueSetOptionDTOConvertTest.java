package com.dexpo.module.base.app.converter;

import com.dexpo.framework.cache.redis.entity.BasicValuesetOptionCache;
import com.dexpo.module.base.api.basic.dto.BasicValuesetOptionDTO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetOptionVO;
import com.dexpo.module.base.domain.model.agg.BasicValueSetOption;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link BasicValueSetOptionDTOConvert} 的单元测试类
 * 
 * <p>测试值集选项转换器的所有转换方法，确保100%分支覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class BasicValueSetOptionDTOConvertTest {

    /**
     * 测试实体转VO - 正常情况
     */
    @Test
    void testE2v_Success() {
        // Arrange
        BasicValueSetOption entity = new BasicValueSetOption();
        entity.setId(1L);
        entity.setValuesetCode("VS_TEST");
        entity.setOptionCode("OPT_001");
        entity.setOptionDescriptionCn("测试选项");
        entity.setOptionDescriptionEn("Test Option");
        entity.setOptionOrder(1);
        entity.setParentCode("PARENT_001");

        // Act
        BasicValuesetOptionVO vo = BasicValueSetOptionDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(1L, vo.getId());
        assertEquals("VS_TEST", vo.getValuesetCode());
        assertEquals("OPT_001", vo.getOptionCode());
        assertEquals("测试选项", vo.getOptionDescriptionCn());
        assertEquals("Test Option", vo.getOptionDescriptionEn());
        assertEquals(1, vo.getOptionOrder());
        assertEquals("PARENT_001", vo.getParentCode());
    }

    /**
     * 测试实体转VO - null输入
     */
    @Test
    void testE2v_WithNull() {
        // Act
        BasicValuesetOptionVO vo = BasicValueSetOptionDTOConvert.INSTANCE.e2v(null);

        // Assert
        assertNull(vo);
    }

    /**
     * 测试DTO转实体 - 正常情况
     */
    @Test
    void testD2e_Success() {
        // Arrange
        BasicValuesetOptionDTO dto = new BasicValuesetOptionDTO();
        dto.setValuesetCode("VS_DTO");
        dto.setOptionCode("OPT_DTO");
        dto.setOptionDescriptionCn("DTO选项");
        dto.setOptionDescriptionEn("DTO Option");
        dto.setOptionOrder(2);
        dto.setParentCode("PARENT_DTO");

        // Act
        BasicValueSetOption entity = BasicValueSetOptionDTOConvert.INSTANCE.d2e(dto);

        // Assert
        assertNotNull(entity);
        assertEquals("VS_DTO", entity.getValuesetCode());
        assertEquals("OPT_DTO", entity.getOptionCode());
        assertEquals("DTO选项", entity.getOptionDescriptionCn());
        assertEquals("DTO Option", entity.getOptionDescriptionEn());
        assertEquals(2, entity.getOptionOrder());
        assertEquals("PARENT_DTO", entity.getParentCode());
    }

    /**
     * 测试DTO转实体 - null输入
     */
    @Test
    void testD2e_WithNull() {
        // Act
        BasicValueSetOption entity = BasicValueSetOptionDTOConvert.INSTANCE.d2e(null);

        // Assert
        assertNull(entity);
    }

    /**
     * 测试实体转缓存对象 - 正常情况
     */
    @Test
    void testToBasicValuesetOptionCache_Success() {
        // Arrange
        BasicValueSetOption entity = new BasicValueSetOption();
        entity.setId(1L);
        entity.setValuesetCode("VS_CACHE");
        entity.setOptionCode("OPT_CACHE");
        entity.setOptionDescriptionCn("缓存选项");
        entity.setOptionDescriptionEn("Cache Option");
        entity.setOptionOrder(3);
        entity.setParentCode("PARENT_CACHE");

        // Act
        BasicValuesetOptionCache cache = BasicValueSetOptionDTOConvert.INSTANCE.toBasicValuesetOptionCache(entity);

        // Assert
        assertNotNull(cache);
        assertEquals(1L, cache.getId());
        assertEquals("VS_CACHE", cache.getValuesetCode());
        assertEquals("OPT_CACHE", cache.getOptionCode());
        assertEquals("缓存选项", cache.getOptionDescriptionCn());
        assertEquals("Cache Option", cache.getOptionDescriptionEn());
        assertEquals(3, cache.getOptionOrder());
        assertEquals("PARENT_CACHE", cache.getParentCode());
    }

    /**
     * 测试实体转缓存对象 - null输入
     */
    @Test
    void testToBasicValuesetOptionCache_WithNull() {
        // Act
        BasicValuesetOptionCache cache = BasicValueSetOptionDTOConvert.INSTANCE.toBasicValuesetOptionCache((BasicValueSetOption) null);

        // Assert
        assertNull(cache);
    }

    /**
     * 测试实体列表转缓存对象列表 - 正常情况
     */
    @Test
    void testToBasicValuesetOptionCacheList_Success() {
        // Arrange
        BasicValueSetOption entity1 = new BasicValueSetOption();
        entity1.setId(1L);
        entity1.setValuesetCode("VS_001");
        entity1.setOptionCode("OPT_001");

        BasicValueSetOption entity2 = new BasicValueSetOption();
        entity2.setId(2L);
        entity2.setValuesetCode("VS_002");
        entity2.setOptionCode("OPT_002");

        List<BasicValueSetOption> entityList = Arrays.asList(entity1, entity2);

        // Act
        List<BasicValuesetOptionCache> cacheList = BasicValueSetOptionDTOConvert.INSTANCE.toBasicValuesetOptionCache(entityList);

        // Assert
        assertNotNull(cacheList);
        assertEquals(2, cacheList.size());
        
        BasicValuesetOptionCache cache1 = cacheList.get(0);
        assertEquals(1L, cache1.getId());
        assertEquals("VS_001", cache1.getValuesetCode());
        assertEquals("OPT_001", cache1.getOptionCode());

        BasicValuesetOptionCache cache2 = cacheList.get(1);
        assertEquals(2L, cache2.getId());
        assertEquals("VS_002", cache2.getValuesetCode());
        assertEquals("OPT_002", cache2.getOptionCode());
    }

    /**
     * 测试实体列表转缓存对象列表 - 空列表
     */
    @Test
    void testToBasicValuesetOptionCacheList_WithEmptyList() {
        // Act
        List<BasicValuesetOptionCache> cacheList = BasicValueSetOptionDTOConvert.INSTANCE.toBasicValuesetOptionCache(Collections.emptyList());

        // Assert
        assertNotNull(cacheList);
        assertTrue(cacheList.isEmpty());
    }

    /**
     * 测试实体列表转缓存对象列表 - null输入
     */
    @Test
    void testToBasicValuesetOptionCacheList_WithNull() {
        // Act
        List<BasicValuesetOptionCache> cacheList = BasicValueSetOptionDTOConvert.INSTANCE.toBasicValuesetOptionCache((List<BasicValueSetOption>) null);

        // Assert
        assertNull(cacheList);
    }

    /**
     * 测试缓存对象转VO - 正常情况
     */
    @Test
    void testCache2VO_Success() {
        // Arrange
        BasicValuesetOptionCache cache = new BasicValuesetOptionCache();
        cache.setId(1L);
        cache.setValuesetCode("VS_CACHE_VO");
        cache.setOptionCode("OPT_CACHE_VO");
        cache.setOptionDescriptionCn("缓存转VO");
        cache.setOptionDescriptionEn("Cache to VO");
        cache.setOptionOrder(4);
        cache.setParentCode("PARENT_CACHE_VO");

        // Act
        BasicValuesetOptionVO vo = BasicValueSetOptionDTOConvert.INSTANCE.cache2VO(cache);

        // Assert
        assertNotNull(vo);
        assertEquals(1L, vo.getId());
        assertEquals("VS_CACHE_VO", vo.getValuesetCode());
        assertEquals("OPT_CACHE_VO", vo.getOptionCode());
        assertEquals("缓存转VO", vo.getOptionDescriptionCn());
        assertEquals("Cache to VO", vo.getOptionDescriptionEn());
        assertEquals(4, vo.getOptionOrder());
        assertEquals("PARENT_CACHE_VO", vo.getParentCode());
    }

    /**
     * 测试缓存对象转VO - null输入
     */
    @Test
    void testCache2VO_WithNull() {
        // Act
        BasicValuesetOptionVO vo = BasicValueSetOptionDTOConvert.INSTANCE.cache2VO((BasicValuesetOptionCache) null);

        // Assert
        assertNull(vo);
    }

    /**
     * 测试缓存对象列表转VO列表 - 正常情况
     */
    @Test
    void testCache2VOList_Success() {
        // Arrange
        BasicValuesetOptionCache cache1 = new BasicValuesetOptionCache();
        cache1.setId(1L);
        cache1.setValuesetCode("VS_CACHE_1");
        cache1.setOptionCode("OPT_CACHE_1");

        BasicValuesetOptionCache cache2 = new BasicValuesetOptionCache();
        cache2.setId(2L);
        cache2.setValuesetCode("VS_CACHE_2");
        cache2.setOptionCode("OPT_CACHE_2");

        List<BasicValuesetOptionCache> cacheList = Arrays.asList(cache1, cache2);

        // Act
        List<BasicValuesetOptionVO> voList = BasicValueSetOptionDTOConvert.INSTANCE.cache2VO(cacheList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        
        BasicValuesetOptionVO vo1 = voList.get(0);
        assertEquals(1L, vo1.getId());
        assertEquals("VS_CACHE_1", vo1.getValuesetCode());
        assertEquals("OPT_CACHE_1", vo1.getOptionCode());

        BasicValuesetOptionVO vo2 = voList.get(1);
        assertEquals(2L, vo2.getId());
        assertEquals("VS_CACHE_2", vo2.getValuesetCode());
        assertEquals("OPT_CACHE_2", vo2.getOptionCode());
    }

    /**
     * 测试缓存对象列表转VO列表 - 空列表
     */
    @Test
    void testCache2VOList_WithEmptyList() {
        // Act
        List<BasicValuesetOptionVO> voList = BasicValueSetOptionDTOConvert.INSTANCE.cache2VO(Collections.emptyList());

        // Assert
        assertNotNull(voList);
        assertTrue(voList.isEmpty());
    }

    /**
     * 测试缓存对象列表转VO列表 - null输入
     */
    @Test
    void testCache2VOList_WithNull() {
        // Act
        List<BasicValuesetOptionVO> voList = BasicValueSetOptionDTOConvert.INSTANCE.cache2VO((List<BasicValuesetOptionCache>) null);

        // Assert
        assertNull(voList);
    }

    /**
     * 测试实体列表转VO列表 - 正常情况
     */
    @Test
    void testE2vList_Success() {
        // Arrange
        BasicValueSetOption entity1 = new BasicValueSetOption();
        entity1.setId(1L);
        entity1.setValuesetCode("VS_E2V_1");
        entity1.setOptionCode("OPT_E2V_1");

        BasicValueSetOption entity2 = new BasicValueSetOption();
        entity2.setId(2L);
        entity2.setValuesetCode("VS_E2V_2");
        entity2.setOptionCode("OPT_E2V_2");

        List<BasicValueSetOption> entityList = Arrays.asList(entity1, entity2);

        // Act
        List<BasicValuesetOptionVO> voList = BasicValueSetOptionDTOConvert.INSTANCE.e2vList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        
        BasicValuesetOptionVO vo1 = voList.get(0);
        assertEquals(1L, vo1.getId());
        assertEquals("VS_E2V_1", vo1.getValuesetCode());
        assertEquals("OPT_E2V_1", vo1.getOptionCode());

        BasicValuesetOptionVO vo2 = voList.get(1);
        assertEquals(2L, vo2.getId());
        assertEquals("VS_E2V_2", vo2.getValuesetCode());
        assertEquals("OPT_E2V_2", vo2.getOptionCode());
    }

    /**
     * 测试实体列表转VO列表 - null输入
     */
    @Test
    void testE2vList_WithNull() {
        // Act
        List<BasicValuesetOptionVO> voList = BasicValueSetOptionDTOConvert.INSTANCE.e2vList(null);

        // Assert
        assertNull(voList);
    }

    /**
     * 测试转换器实例不为null
     */
    @Test
    void testInstanceNotNull() {
        assertNotNull(BasicValueSetOptionDTOConvert.INSTANCE);
    }

    /**
     * 测试空字段处理
     */
    @Test
    void testE2v_WithEmptyFields() {
        // Arrange
        BasicValueSetOption entity = new BasicValueSetOption();
        entity.setId(null);
        entity.setValuesetCode("");
        entity.setOptionCode("");
        entity.setOptionDescriptionCn("");
        entity.setOptionDescriptionEn("");
        entity.setOptionOrder(null);
        entity.setParentCode("");

        // Act
        BasicValuesetOptionVO vo = BasicValueSetOptionDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertNull(vo.getId());
        assertEquals("", vo.getValuesetCode());
        assertEquals("", vo.getOptionCode());
        assertEquals("", vo.getOptionDescriptionCn());
        assertEquals("", vo.getOptionDescriptionEn());
        assertNull(vo.getOptionOrder());
        assertEquals("", vo.getParentCode());
    }
}
