package com.dexpo.module.base.app.converter;

import com.dexpo.module.base.BaseUnitTest;
import com.dexpo.module.base.api.attachment.dto.AttachmentInfoDTO;
import com.dexpo.module.base.api.attachment.vo.AttachmentInfoVO;
import com.dexpo.module.base.domain.model.agg.AttachmentInfo;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link AttachmentInfoDTOConvert} 的单元测试类
 * 
 * <p>测试附件信息转换器的所有转换方法，确保100%分支覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class AttachmentInfoDTOConvertTest extends BaseUnitTest {

    /**
     * 测试实体转VO - 正常情况
     */
    @Test
    void testE2v_Success() {
        // Arrange
        AttachmentInfo entity = new AttachmentInfo();
        entity.setId(1L);
        entity.setAttachmentName("test.jpg");
        entity.setAttachmentSize(1024L);
        entity.setAttachmentType("image/jpeg");
        entity.setAttachmentPath("/uploads/test.jpg");
        entity.setCreateUserName("testUser");
        entity.setCreateUser(100L);

        // Act
        AttachmentInfoVO vo = AttachmentInfoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(1L, vo.getId());
        assertEquals("test.jpg", vo.getAttachmentName());
        assertEquals(1024L, vo.getAttachmentSize());
        assertEquals("image/jpeg", vo.getAttachmentType());
        assertEquals("/uploads/test.jpg", vo.getAttachmentPath());
    }

    /**
     * 测试实体转VO - null输入
     */
    @Test
    void testE2v_WithNull() {
        // Act
        AttachmentInfoVO vo = AttachmentInfoDTOConvert.INSTANCE.e2v(null);

        // Assert
        assertNull(vo);
    }

    /**
     * 测试实体转VO - 空字段
     */
    @Test
    void testE2v_WithEmptyFields() {
        // Arrange
        AttachmentInfo entity = new AttachmentInfo();
        entity.setId(null);
        entity.setAttachmentName("");
        entity.setAttachmentSize(0L);
        entity.setAttachmentType(null);
        entity.setAttachmentPath("");
        entity.setCreateUserName("");
        entity.setCreateUser(null);

        // Act
        AttachmentInfoVO vo = AttachmentInfoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertNull(vo.getId());
        assertEquals("", vo.getAttachmentName());
        assertEquals(0L, vo.getAttachmentSize());
        assertNull(vo.getAttachmentType());
        assertEquals("", vo.getAttachmentPath());
    }

    /**
     * 测试DTO转实体 - 正常情况
     */
    @Test
    void testD2e_Success() {
        // Arrange
        AttachmentInfoDTO dto = new AttachmentInfoDTO();
        dto.setId(2L);
        dto.setAttachmentName("document.pdf");
        dto.setAttachmentSize(2048L);
        dto.setAttachmentType("application/pdf");
        dto.setAttachmentPath("/uploads/document.pdf");

        // Act
        AttachmentInfo entity = AttachmentInfoDTOConvert.INSTANCE.d2e(dto);

        // Assert
        assertNotNull(entity);
        assertEquals(2L, entity.getId());
        assertEquals("document.pdf", entity.getAttachmentName());
        assertEquals(2048L, entity.getAttachmentSize());
        assertEquals("application/pdf", entity.getAttachmentType());
        assertEquals("/uploads/document.pdf", entity.getAttachmentPath());
    }

    /**
     * 测试DTO转实体 - null输入
     */
    @Test
    void testD2e_WithNull() {
        // Act
        AttachmentInfo entity = AttachmentInfoDTOConvert.INSTANCE.d2e(null);

        // Assert
        assertNull(entity);
    }

    /**
     * 测试DTO转实体 - 空字段
     */
    @Test
    void testD2e_WithEmptyFields() {
        // Arrange
        AttachmentInfoDTO dto = new AttachmentInfoDTO();
        dto.setId(null);
        dto.setAttachmentName("");
        dto.setAttachmentSize(null);
        dto.setAttachmentType("");
        dto.setAttachmentPath(null);

        // Act
        AttachmentInfo entity = AttachmentInfoDTOConvert.INSTANCE.d2e(dto);

        // Assert
        assertNotNull(entity);
        assertNull(entity.getId());
        assertEquals("", entity.getAttachmentName());
        assertNull(entity.getAttachmentSize());
        assertEquals("", entity.getAttachmentType());
        assertNull(entity.getAttachmentPath());
    }

    /**
     * 测试实体列表转VO列表 - 正常情况
     */
    @Test
    void testE2vList_Success() {
        // Arrange
        AttachmentInfo entity1 = new AttachmentInfo();
        entity1.setId(1L);
        entity1.setAttachmentName("file1.jpg");
        entity1.setAttachmentType("image/jpeg");

        AttachmentInfo entity2 = new AttachmentInfo();
        entity2.setId(2L);
        entity2.setAttachmentName("file2.pdf");
        entity2.setAttachmentType("application/pdf");

        List<AttachmentInfo> entityList = Arrays.asList(entity1, entity2);

        // Act
        List<AttachmentInfoVO> voList = AttachmentInfoDTOConvert.INSTANCE.e2vList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        
        AttachmentInfoVO vo1 = voList.get(0);
        assertEquals(1L, vo1.getId());
        assertEquals("file1.jpg", vo1.getAttachmentName());
        assertEquals("image/jpeg", vo1.getAttachmentType());

        AttachmentInfoVO vo2 = voList.get(1);
        assertEquals(2L, vo2.getId());
        assertEquals("file2.pdf", vo2.getAttachmentName());
        assertEquals("application/pdf", vo2.getAttachmentType());
    }

    /**
     * 测试实体列表转VO列表 - 空列表
     */
    @Test
    void testE2vList_WithEmptyList() {
        // Act
        List<AttachmentInfoVO> voList = AttachmentInfoDTOConvert.INSTANCE.e2vList(Collections.emptyList());

        // Assert
        assertNotNull(voList);
        assertTrue(voList.isEmpty());
    }

    /**
     * 测试实体列表转VO列表 - null输入
     */
    @Test
    void testE2vList_WithNull() {
        // Act
        List<AttachmentInfoVO> voList = AttachmentInfoDTOConvert.INSTANCE.e2vList(null);

        // Assert
        assertNull(voList);
    }

    /**
     * 测试实体列表转VO列表 - 包含null元素
     */
    @Test
    void testE2vList_WithNullElements() {
        // Arrange
        AttachmentInfo entity = new AttachmentInfo();
        entity.setId(1L);
        entity.setAttachmentName("file.jpg");

        List<AttachmentInfo> entityList = Arrays.asList(entity, null);

        // Act
        List<AttachmentInfoVO> voList = AttachmentInfoDTOConvert.INSTANCE.e2vList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        assertNotNull(voList.get(0));
        assertEquals(1L, voList.get(0).getId());
        assertNull(voList.get(1));
    }

    /**
     * 测试DTO列表转实体列表 - 正常情况
     */
    @Test
    void testD2eList_Success() {
        // Arrange
        AttachmentInfoDTO dto1 = new AttachmentInfoDTO();
        dto1.setId(1L);
        dto1.setAttachmentName("file1.jpg");
        dto1.setAttachmentType("image/jpeg");

        AttachmentInfoDTO dto2 = new AttachmentInfoDTO();
        dto2.setId(2L);
        dto2.setAttachmentName("file2.pdf");
        dto2.setAttachmentType("application/pdf");

        List<AttachmentInfoDTO> dtoList = Arrays.asList(dto1, dto2);

        // Act
        List<AttachmentInfo> entityList = AttachmentInfoDTOConvert.INSTANCE.d2eList(dtoList);

        // Assert
        assertNotNull(entityList);
        assertEquals(2, entityList.size());
        
        AttachmentInfo entity1 = entityList.get(0);
        assertEquals(1L, entity1.getId());
        assertEquals("file1.jpg", entity1.getAttachmentName());
        assertEquals("image/jpeg", entity1.getAttachmentType());

        AttachmentInfo entity2 = entityList.get(1);
        assertEquals(2L, entity2.getId());
        assertEquals("file2.pdf", entity2.getAttachmentName());
        assertEquals("application/pdf", entity2.getAttachmentType());
    }

    /**
     * 测试DTO列表转实体列表 - 空列表
     */
    @Test
    void testD2eList_WithEmptyList() {
        // Act
        List<AttachmentInfo> entityList = AttachmentInfoDTOConvert.INSTANCE.d2eList(Collections.emptyList());

        // Assert
        assertNotNull(entityList);
        assertTrue(entityList.isEmpty());
    }

    /**
     * 测试DTO列表转实体列表 - null输入
     */
    @Test
    void testD2eList_WithNull() {
        // Act
        List<AttachmentInfo> entityList = AttachmentInfoDTOConvert.INSTANCE.d2eList(null);

        // Assert
        assertNull(entityList);
    }

    /**
     * 测试转换器实例不为null
     */
    @Test
    void testInstanceNotNull() {
        assertNotNull(AttachmentInfoDTOConvert.INSTANCE);
    }

    /**
     * 测试大文件转换
     */
    @Test
    void testE2v_WithLargeFile() {
        // Arrange
        AttachmentInfo entity = new AttachmentInfo();
        entity.setId(Long.MAX_VALUE);
        entity.setAttachmentName("large_file_with_very_long_name_that_exceeds_normal_limits.zip");
        entity.setAttachmentSize(Long.MAX_VALUE);
        entity.setAttachmentType("application/zip");
        entity.setAttachmentPath("/uploads/very/deep/nested/path/large_file_with_very_long_name_that_exceeds_normal_limits.zip");
        entity.setCreateUserName("admin");
        entity.setCreateUser(Long.MAX_VALUE);

        // Act
        AttachmentInfoVO vo = AttachmentInfoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(Long.MAX_VALUE, vo.getId());
        assertEquals("large_file_with_very_long_name_that_exceeds_normal_limits.zip", vo.getAttachmentName());
        assertEquals(Long.MAX_VALUE, vo.getAttachmentSize());
        assertEquals("application/zip", vo.getAttachmentType());
        assertEquals("/uploads/very/deep/nested/path/large_file_with_very_long_name_that_exceeds_normal_limits.zip", vo.getAttachmentPath());
    }

    /**
     * 测试特殊字符处理
     */
    @Test
    void testE2v_WithSpecialCharacters() {
        // Arrange
        AttachmentInfo entity = new AttachmentInfo();
        entity.setId(999L);
        entity.setAttachmentName("特殊文件@#$%.txt");
        entity.setAttachmentSize(512L);
        entity.setAttachmentType("text/plain");
        entity.setAttachmentPath("/uploads/special@#$%/特殊文件@#$%.txt");

        // Act
        AttachmentInfoVO vo = AttachmentInfoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(999L, vo.getId());
        assertEquals("特殊文件@#$%.txt", vo.getAttachmentName());
        assertEquals(512L, vo.getAttachmentSize());
        assertEquals("text/plain", vo.getAttachmentType());
        assertEquals("/uploads/special@#$%/特殊文件@#$%.txt", vo.getAttachmentPath());
    }

    /**
     * 测试边界值
     */
    @Test
    void testE2v_WithBoundaryValues() {
        // Arrange
        AttachmentInfo entity = new AttachmentInfo();
        entity.setId(0L);
        entity.setAttachmentName("0");
        entity.setAttachmentSize(0L);
        entity.setAttachmentType("0");
        entity.setAttachmentPath("0");

        // Act
        AttachmentInfoVO vo = AttachmentInfoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(0L, vo.getId());
        assertEquals("0", vo.getAttachmentName());
        assertEquals(0L, vo.getAttachmentSize());
        assertEquals("0", vo.getAttachmentType());
        assertEquals("0", vo.getAttachmentPath());
    }
}
