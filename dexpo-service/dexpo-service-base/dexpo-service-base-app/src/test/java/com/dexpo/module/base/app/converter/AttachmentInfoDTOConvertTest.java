package com.dexpo.module.base.app.converter;

import com.dexpo.module.base.api.attachment.dto.AttachmentInfoDTO;
import com.dexpo.module.base.api.attachment.vo.AttachmentInfoVO;
import com.dexpo.module.base.domain.model.agg.AttachmentInfo;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link AttachmentInfoDTOConvert} 的单元测试类
 * 
 * <p>测试附件信息转换器的所有转换方法，确保100%分支覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class AttachmentInfoDTOConvertTest {

    /**
     * 测试实体转VO - 正常情况
     */
    @Test
    void testE2v_Success() {
        // Arrange
        AttachmentInfo entity = new AttachmentInfo();
        entity.setId(1L);
        entity.setFileName("test.jpg");
        entity.setFileSize(1024L);
        entity.setFileType("image/jpeg");
        entity.setFilePath("/uploads/test.jpg");
        entity.setBusinessType("USER_AVATAR");
        entity.setBusinessId("user123");

        // Act
        AttachmentInfoVO vo = AttachmentInfoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(1L, vo.getId());
        assertEquals("test.jpg", vo.getFileName());
        assertEquals(1024L, vo.getFileSize());
        assertEquals("image/jpeg", vo.getFileType());
        assertEquals("/uploads/test.jpg", vo.getFilePath());
        assertEquals("USER_AVATAR", vo.getBusinessType());
        assertEquals("user123", vo.getBusinessId());
    }

    /**
     * 测试实体转VO - null输入
     */
    @Test
    void testE2v_WithNull() {
        // Act
        AttachmentInfoVO vo = AttachmentInfoDTOConvert.INSTANCE.e2v(null);

        // Assert
        assertNull(vo);
    }

    /**
     * 测试实体转VO - 空字段
     */
    @Test
    void testE2v_WithEmptyFields() {
        // Arrange
        AttachmentInfo entity = new AttachmentInfo();
        entity.setId(null);
        entity.setFileName("");
        entity.setFileSize(0L);
        entity.setFileType(null);
        entity.setFilePath("");
        entity.setBusinessType("");
        entity.setBusinessId(null);

        // Act
        AttachmentInfoVO vo = AttachmentInfoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertNull(vo.getId());
        assertEquals("", vo.getFileName());
        assertEquals(0L, vo.getFileSize());
        assertNull(vo.getFileType());
        assertEquals("", vo.getFilePath());
        assertEquals("", vo.getBusinessType());
        assertNull(vo.getBusinessId());
    }

    /**
     * 测试DTO转实体 - 正常情况
     */
    @Test
    void testD2e_Success() {
        // Arrange
        AttachmentInfoDTO dto = new AttachmentInfoDTO();
        dto.setId(2L);
        dto.setFileName("document.pdf");
        dto.setFileSize(2048L);
        dto.setFileType("application/pdf");
        dto.setFilePath("/uploads/document.pdf");
        dto.setBusinessType("CONTRACT");
        dto.setBusinessId("contract456");

        // Act
        AttachmentInfo entity = AttachmentInfoDTOConvert.INSTANCE.d2e(dto);

        // Assert
        assertNotNull(entity);
        assertEquals(2L, entity.getId());
        assertEquals("document.pdf", entity.getFileName());
        assertEquals(2048L, entity.getFileSize());
        assertEquals("application/pdf", entity.getFileType());
        assertEquals("/uploads/document.pdf", entity.getFilePath());
        assertEquals("CONTRACT", entity.getBusinessType());
        assertEquals("contract456", entity.getBusinessId());
    }

    /**
     * 测试DTO转实体 - null输入
     */
    @Test
    void testD2e_WithNull() {
        // Act
        AttachmentInfo entity = AttachmentInfoDTOConvert.INSTANCE.d2e(null);

        // Assert
        assertNull(entity);
    }

    /**
     * 测试DTO转实体 - 空字段
     */
    @Test
    void testD2e_WithEmptyFields() {
        // Arrange
        AttachmentInfoDTO dto = new AttachmentInfoDTO();
        dto.setId(null);
        dto.setFileName("");
        dto.setFileSize(null);
        dto.setFileType("");
        dto.setFilePath(null);
        dto.setBusinessType("");
        dto.setBusinessId("");

        // Act
        AttachmentInfo entity = AttachmentInfoDTOConvert.INSTANCE.d2e(dto);

        // Assert
        assertNotNull(entity);
        assertNull(entity.getId());
        assertEquals("", entity.getFileName());
        assertNull(entity.getFileSize());
        assertEquals("", entity.getFileType());
        assertNull(entity.getFilePath());
        assertEquals("", entity.getBusinessType());
        assertEquals("", entity.getBusinessId());
    }

    /**
     * 测试实体列表转VO列表 - 正常情况
     */
    @Test
    void testE2vList_Success() {
        // Arrange
        AttachmentInfo entity1 = new AttachmentInfo();
        entity1.setId(1L);
        entity1.setFileName("file1.jpg");
        entity1.setBusinessType("TYPE1");

        AttachmentInfo entity2 = new AttachmentInfo();
        entity2.setId(2L);
        entity2.setFileName("file2.pdf");
        entity2.setBusinessType("TYPE2");

        List<AttachmentInfo> entityList = Arrays.asList(entity1, entity2);

        // Act
        List<AttachmentInfoVO> voList = AttachmentInfoDTOConvert.INSTANCE.e2vList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        
        AttachmentInfoVO vo1 = voList.get(0);
        assertEquals(1L, vo1.getId());
        assertEquals("file1.jpg", vo1.getFileName());
        assertEquals("TYPE1", vo1.getBusinessType());

        AttachmentInfoVO vo2 = voList.get(1);
        assertEquals(2L, vo2.getId());
        assertEquals("file2.pdf", vo2.getFileName());
        assertEquals("TYPE2", vo2.getBusinessType());
    }

    /**
     * 测试实体列表转VO列表 - 空列表
     */
    @Test
    void testE2vList_WithEmptyList() {
        // Act
        List<AttachmentInfoVO> voList = AttachmentInfoDTOConvert.INSTANCE.e2vList(Collections.emptyList());

        // Assert
        assertNotNull(voList);
        assertTrue(voList.isEmpty());
    }

    /**
     * 测试实体列表转VO列表 - null输入
     */
    @Test
    void testE2vList_WithNull() {
        // Act
        List<AttachmentInfoVO> voList = AttachmentInfoDTOConvert.INSTANCE.e2vList(null);

        // Assert
        assertNull(voList);
    }

    /**
     * 测试实体列表转VO列表 - 包含null元素
     */
    @Test
    void testE2vList_WithNullElements() {
        // Arrange
        AttachmentInfo entity = new AttachmentInfo();
        entity.setId(1L);
        entity.setFileName("file.jpg");

        List<AttachmentInfo> entityList = Arrays.asList(entity, null);

        // Act
        List<AttachmentInfoVO> voList = AttachmentInfoDTOConvert.INSTANCE.e2vList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        assertNotNull(voList.get(0));
        assertEquals(1L, voList.get(0).getId());
        assertNull(voList.get(1));
    }

    /**
     * 测试DTO列表转实体列表 - 正常情况
     */
    @Test
    void testD2eList_Success() {
        // Arrange
        AttachmentInfoDTO dto1 = new AttachmentInfoDTO();
        dto1.setId(1L);
        dto1.setFileName("file1.jpg");
        dto1.setBusinessType("TYPE1");

        AttachmentInfoDTO dto2 = new AttachmentInfoDTO();
        dto2.setId(2L);
        dto2.setFileName("file2.pdf");
        dto2.setBusinessType("TYPE2");

        List<AttachmentInfoDTO> dtoList = Arrays.asList(dto1, dto2);

        // Act
        List<AttachmentInfo> entityList = AttachmentInfoDTOConvert.INSTANCE.d2eList(dtoList);

        // Assert
        assertNotNull(entityList);
        assertEquals(2, entityList.size());
        
        AttachmentInfo entity1 = entityList.get(0);
        assertEquals(1L, entity1.getId());
        assertEquals("file1.jpg", entity1.getFileName());
        assertEquals("TYPE1", entity1.getBusinessType());

        AttachmentInfo entity2 = entityList.get(1);
        assertEquals(2L, entity2.getId());
        assertEquals("file2.pdf", entity2.getFileName());
        assertEquals("TYPE2", entity2.getBusinessType());
    }

    /**
     * 测试DTO列表转实体列表 - 空列表
     */
    @Test
    void testD2eList_WithEmptyList() {
        // Act
        List<AttachmentInfo> entityList = AttachmentInfoDTOConvert.INSTANCE.d2eList(Collections.emptyList());

        // Assert
        assertNotNull(entityList);
        assertTrue(entityList.isEmpty());
    }

    /**
     * 测试DTO列表转实体列表 - null输入
     */
    @Test
    void testD2eList_WithNull() {
        // Act
        List<AttachmentInfo> entityList = AttachmentInfoDTOConvert.INSTANCE.d2eList(null);

        // Assert
        assertNull(entityList);
    }

    /**
     * 测试转换器实例不为null
     */
    @Test
    void testInstanceNotNull() {
        assertNotNull(AttachmentInfoDTOConvert.INSTANCE);
    }

    /**
     * 测试大文件转换
     */
    @Test
    void testE2v_WithLargeFile() {
        // Arrange
        AttachmentInfo entity = new AttachmentInfo();
        entity.setId(Long.MAX_VALUE);
        entity.setFileName("large_file_with_very_long_name_that_exceeds_normal_limits.zip");
        entity.setFileSize(Long.MAX_VALUE);
        entity.setFileType("application/zip");
        entity.setFilePath("/uploads/very/deep/nested/path/large_file_with_very_long_name_that_exceeds_normal_limits.zip");
        entity.setBusinessType("LARGE_FILE_BACKUP");
        entity.setBusinessId("backup_" + Long.MAX_VALUE);

        // Act
        AttachmentInfoVO vo = AttachmentInfoDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(Long.MAX_VALUE, vo.getId());
        assertEquals("large_file_with_very_long_name_that_exceeds_normal_limits.zip", vo.getFileName());
        assertEquals(Long.MAX_VALUE, vo.getFileSize());
        assertEquals("application/zip", vo.getFileType());
        assertEquals("/uploads/very/deep/nested/path/large_file_with_very_long_name_that_exceeds_normal_limits.zip", vo.getFilePath());
        assertEquals("LARGE_FILE_BACKUP", vo.getBusinessType());
        assertEquals("backup_" + Long.MAX_VALUE, vo.getBusinessId());
    }
}
