package com.dexpo.module.base.app.service;

import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.module.base.api.basic.dto.BasicRegionDTO;
import com.dexpo.module.base.api.basic.vo.BasicRegionTreeVO;
import com.dexpo.module.base.api.basic.vo.BasicRegionVO;
import com.dexpo.module.base.domain.model.agg.BasicRegion;
import com.dexpo.module.base.domain.service.BasicRegionDomainService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BasicRegionAppServiceImplTest {
    @Mock
    private BasicRegionDomainService basicRegionDomainService;

    @InjectMocks
    private BasicRegionAppServiceImpl basicRegionAppService;

    @Test
    void getRegionList_shouldThrowException_whenParamsAreBlank() {
        BasicRegionDTO dto = new BasicRegionDTO();
        assertThrows(ServiceException.class, () -> basicRegionAppService.getRegionList(dto));
    }

    @Test
    void getRegionList_shouldReturnList() {
        BasicRegionDTO dto = new BasicRegionDTO();
        dto.setLevel("city");
        when(basicRegionDomainService.getRegionList(any(BasicRegion.class))).thenReturn(Collections.singletonList(new BasicRegion()));
        List<BasicRegionVO> result = basicRegionAppService.getRegionList(dto);
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    void getRegionListAll_shouldReturnEmptyTree_whenNoRegions() {
        when(basicRegionDomainService.selectList()).thenReturn(Collections.emptyList());
        List<BasicRegionTreeVO> result = basicRegionAppService.getRegionListAll();
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getRegionListAll_shouldBuildTree() {
        // Arrange
        BasicRegion root = new BasicRegion();
        root.setAdcode("100000");
        root.setName("Country");

        BasicRegion child = new BasicRegion();
        child.setAdcode("110000");
        child.setName("City");
        child.setParentAdcode("100000");
        
        BasicRegion street = new BasicRegion();
        street.setAdcode("110101");
        street.setName("Street");
        street.setParentAdcode("110000");
        street.setLevel("street");

        BasicRegion orphan = new BasicRegion();
        orphan.setAdcode("999999");
        orphan.setName("Orphan");
        orphan.setParentAdcode("-1"); // Non-existent parent

        when(basicRegionDomainService.selectList()).thenReturn(List.of(root, child, street, orphan));
        
        // Act
        List<BasicRegionTreeVO> result = basicRegionAppService.getRegionListAll();
        
        // Assert
        assertNotNull(result);
        assertEquals(1, result.size()); // root and orphan
        BasicRegionTreeVO rootNode = result.stream().filter(r -> r.getAdcode().equals("100000")).findFirst().orElse(null);
        assertNotNull(rootNode);
        assertEquals(1, rootNode.getChildrenList().size());
        assertEquals("110000", rootNode.getChildrenList().get(0).getAdcode());
        // street node should be filtered out
        assertTrue(rootNode.getChildrenList().stream().noneMatch(c -> "street".equals(c.getLevel())));
    }

    @Test
    void getRegionListByLevel_shouldReturnEmptyMap_whenNoRegions() {
        when(basicRegionDomainService.selectList()).thenReturn(Collections.emptyList());
        Map<String, Map<String, BasicRegionVO>> result = basicRegionAppService.getRegionListByLevel();
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getRegionListByLevel_shouldBuildMap() {
        // Arrange
        BasicRegion region1 = new BasicRegion();
        region1.setLevel("province");
        region1.setName("Beijing");

        BasicRegion region2 = new BasicRegion();
        region2.setLevel("province");
        region2.setName("Shanghai");
        
        BasicRegion region3 = new BasicRegion();
        region3.setLevel("city");
        region3.setName("ChaoYang");

        when(basicRegionDomainService.selectList()).thenReturn(List.of(region1, region2, region3));

        // Act
        Map<String, Map<String, BasicRegionVO>> result = basicRegionAppService.getRegionListByLevel();

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey("province"));
        assertTrue(result.containsKey("city"));
        assertEquals(2, result.get("province").size());
        assertEquals(1, result.get("city").size());
        assertEquals("Beijing", result.get("province").get("Beijing").getName());
    }
    @Test
    void testGetRegionListAll_buildsCorrectTreeStructure() {
        // Arrange
        BasicRegion province = createRegion("110000", "province", "北京市", null);
        BasicRegion city = createRegion("110100", "city", "市辖区", "110000");
        BasicRegion district = createRegion("110101", "district", "东城区", "110100");
        BasicRegion street = createRegion("110101001", "street", "东华门街道", "110101");

        when(basicRegionDomainService.selectList()).thenReturn(Arrays.asList(province, city, district, street));


        // Act
        List<BasicRegionTreeVO> result = basicRegionAppService.getRegionListAll();

        // Assert
        assertThat(result).hasSize(1);
        BasicRegionTreeVO root = result.getFirst();
        assertThat(root.getAdcode()).isEqualTo("110000");
        assertThat(root.getChildrenList()).hasSize(1);

        BasicRegionTreeVO cityNode = root.getChildrenList().getFirst();
        assertThat(cityNode.getAdcode()).isEqualTo("110100");
        assertThat(cityNode.getChildrenList()).hasSize(1);

        BasicRegionTreeVO districtNode = cityNode.getChildrenList().getFirst();
        assertThat(districtNode.getAdcode()).isEqualTo("110101");
        assertThat(districtNode.getChildrenList()).isNull(); // street 级别被跳过
    }

    private BasicRegion createRegion(String adcode, String level, String name, String parentAdcode) {
        BasicRegion region = new BasicRegion();
        region.setAdcode(adcode);
        region.setLevel(level);
        region.setName(name);
        region.setParentAdcode(parentAdcode);
        return region;
    }
    @Test
    void testBuildMapByLevel_withExistingLevelMap_shouldReuseIt() {
        // Arrange
        BasicRegion region1 = new BasicRegion();
        region1.setLevel("province");
        region1.setName("Beijing");

        BasicRegion region2 = new BasicRegion();
        region2.setLevel("province");
        region2.setName("Shanghai");

        when(basicRegionDomainService.selectList()).thenReturn(Arrays.asList(region1, region2));

        // Act
        Map<String, Map<String, BasicRegionVO>> result = basicRegionAppService.getRegionListByLevel();

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey("province"));
        assertEquals(2, result.get("province").size());
        assertTrue(result.get("province").containsKey("Beijing"));
        assertTrue(result.get("province").containsKey("Shanghai"));
    }
    @Test
    void testBuildTree_withExistingChildrenList_shouldAddToIt() {
        // Arrange
        BasicRegionTreeVO parent = new BasicRegionTreeVO();
        parent.setAdcode("100000");
        parent.setLevel("province");
        parent.setParentAdcode(null);
        parent.setChildrenList(new ArrayList<>());
        BasicRegionTreeVO parent = new BasicRegionTreeVO();
        parent.setAdcode("100000");
        parent.setLevel("province");
        parent.setParentAdcode(null);
        parent.setChildrenList(new ArrayList<>());

        BasicRegionTreeVO child1 = new BasicRegionTreeVO();
        child1.setAdcode("100001");
        child1.setLevel("city");
        child1.setParentAdcode("100000");

        BasicRegionTreeVO child2 = new BasicRegionTreeVO();
        child2.setAdcode("100002");
        child2.setLevel("city");
        child2.setParentAdcode("100000");

        List<BasicRegionTreeVO> regions = Arrays.asList(parent, child1, child2);

        // Act
        List<BasicRegionTreeVO> result = basicRegionAppService.getRegionListAll();

        // Assert
        assertNotNull(result);
    }

} 