package com.dexpo.module.base.app.service;

import com.dexpo.framework.cache.redis.entity.BasicValuesetOptionCache;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.module.base.api.basic.dto.BasicValuesetOptionDTO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetOptionVO;
import com.dexpo.module.base.domain.model.agg.BasicValueSetOption;
import com.dexpo.module.base.domain.service.BasicValueSetOptionDomainService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BasicValueSetOptionAppServiceImplTest {
    @Mock
    private RedisService redisService;
    @Mock
    private BasicValueSetOptionDomainService basicValueSetOptionDomainService;

    @InjectMocks
    private BasicValueSetOptionAppServiceImpl service;

    @Test
    void listByValuesetCodes_shouldReturnList() {
        BasicValuesetOptionCache cache = new BasicValuesetOptionCache();
        cache.setValuesetCode("code1");
        cache.setOptionCode("opt1");
        when(redisService.getCacheObject(anyString())).thenReturn(List.of(cache));
        List<BasicValuesetInfoVO> result = service.listByValuesetCodes(List.of("code1"));
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals("code1", result.get(0).getValuesetCode());
        assertEquals(1, result.get(0).getOptions().size());
        assertEquals("opt1", result.get(0).getOptions().get(0).getOptionCode());
    }

    @Test
    void listByValuesetCodes_shouldReturnEmpty_whenNoCache() {
        when(redisService.getCacheObject(anyString())).thenReturn(Collections.emptyList());
        when(basicValueSetOptionDomainService.list()).thenReturn(Collections.emptyList());
        List<BasicValuesetInfoVO> result = service.listByValuesetCodes(List.of("code2"));
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void listByValuesetOptionCodes_shouldReturnList() {
        BasicValuesetOptionCache cache = new BasicValuesetOptionCache();
        cache.setValuesetCode("code1");
        cache.setOptionCode("opt1");
        when(redisService.getCacheObject(anyString())).thenReturn(List.of(cache));
        List<BasicValuesetInfoVO> result = service.listByValuesetOptionCodes(List.of("code1"), List.of("opt1"));
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }
    @Test
    void listByValuesetOptionCodes_shouldReturnList1() {
        BasicValuesetOptionCache cache = new BasicValuesetOptionCache();
        cache.setValuesetCode("code1");
        cache.setOptionCode("opt1");
        when(redisService.getCacheObject(anyString())).thenReturn(List.of(cache));
        List<BasicValuesetInfoVO> result = service.listByValuesetOptionCodes(List.of("code1"), List.of("opt2"));
        assertNotNull(result);
    }

    @Test
    void listByValuesetOptionCodes_shouldReturnList2() {
        BasicValuesetOptionCache cache = new BasicValuesetOptionCache();
        cache.setValuesetCode("code1");
        cache.setOptionCode("opt1");
        when(redisService.getCacheObject(anyString())).thenReturn(List.of(cache));
        List<BasicValuesetInfoVO> result = service.listByValuesetOptionCodes(List.of("code2"), List.of("opt1"));
        assertNotNull(result);
    }
    @Test
    void listByValuesetOptionCodes_notReturnList() {

        when(redisService.getCacheObject(anyString())).thenReturn(null);
        List<BasicValuesetInfoVO> result = service.listByValuesetOptionCodes(List.of("code1"), List.of("opt1"));
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void initValueSetOptionCache_shouldReturnCache() {
        BasicValueSetOption option = new BasicValueSetOption();
        option.setId(1L);
        option.setOptionCode("opt1");
        List<BasicValueSetOption> optionList = List.of(option);
        when(basicValueSetOptionDomainService.list()).thenReturn(optionList);
        doNothing().when(redisService).setCacheObject(anyString(), anyList());
        List<BasicValuesetOptionCache> result = service.initValueSetOptionCache();
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("opt1", result.get(0).getOptionCode());
    }

    @Test
    void getValuesetOption_shouldReturnVO() {
        BasicValuesetOptionDTO dto = new BasicValuesetOptionDTO();
        dto.setOptionCode("opt1");
        BasicValueSetOption option = new BasicValueSetOption();
        option.setOptionCode("opt1");
        when(basicValueSetOptionDomainService.selectByOptionCode(anyString())).thenReturn(option);
        BasicValuesetOptionVO result = service.getValuesetOption(dto);
        assertNotNull(result);
        assertEquals("opt1", result.getOptionCode());
    }

    @Test
    void getOptionListByCodes_shouldReturnList() {
        BasicValuesetOptionCache cache = new BasicValuesetOptionCache();
        cache.setOptionCode("opt1");
        when(redisService.getCacheObject(anyString())).thenReturn(List.of(cache));
        List<BasicValuesetOptionVO> result = service.getOptionListByCodes(List.of("opt1"));
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("opt1", result.get(0).getOptionCode());
    }
} 