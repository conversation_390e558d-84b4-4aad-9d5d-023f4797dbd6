package com.dexpo.module.base.app.converter;

import com.dexpo.module.base.BaseUnitTest;
import com.dexpo.module.base.api.permission.vo.SysPermissionInterfaceRelationVO;
import com.dexpo.module.base.domain.model.valueobject.SysPermissionInterfaceRelation;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link SysPermissionInterfaceRelationDTOConvert} 的单元测试类
 * 
 * <p>测试系统权限接口关系转换器的所有转换方法，确保100%分支覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class SysPermissionInterfaceRelationDTOConvertTest extends BaseUnitTest {

    /**
     * 测试实体列表转VO列表 - 正常情况
     */
    @Test
    void testE2vList_Success() {
        // Arrange
        SysPermissionInterfaceRelation entity1 = new SysPermissionInterfaceRelation();
        entity1.setId(1L);
        entity1.setPermissionId(100L);
        entity1.setInterfaceId(200L);
        entity1.setInterfaceName("用户查询接口");
        entity1.setInterfaceUrl("/api/user/query");
        entity1.setHttpMethod("GET");
        entity1.setDescription("用户查询接口描述");
        entity1.setStatus("ACTIVE");

        SysPermissionInterfaceRelation entity2 = new SysPermissionInterfaceRelation();
        entity2.setId(2L);
        entity2.setPermissionId(101L);
        entity2.setInterfaceId(201L);
        entity2.setInterfaceName("用户创建接口");
        entity2.setInterfaceUrl("/api/user/create");
        entity2.setHttpMethod("POST");
        entity2.setDescription("用户创建接口描述");
        entity2.setStatus("ACTIVE");

        List<SysPermissionInterfaceRelation> entityList = Arrays.asList(entity1, entity2);

        // Act
        List<SysPermissionInterfaceRelationVO> voList = SysPermissionInterfaceRelationDTOConvert.INSTANCE.e2vList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        
        SysPermissionInterfaceRelationVO vo1 = voList.get(0);
        assertEquals(1L, vo1.getId());
        assertEquals(100L, vo1.getPermissionId());
        assertEquals(200L, vo1.getInterfaceId());
        assertEquals("用户查询接口", vo1.getInterfaceName());
        assertEquals("/api/user/query", vo1.getInterfaceUrl());
        assertEquals("GET", vo1.getHttpMethod());
        assertEquals("用户查询接口描述", vo1.getDescription());
        assertEquals("ACTIVE", vo1.getStatus());

        SysPermissionInterfaceRelationVO vo2 = voList.get(1);
        assertEquals(2L, vo2.getId());
        assertEquals(101L, vo2.getPermissionId());
        assertEquals(201L, vo2.getInterfaceId());
        assertEquals("用户创建接口", vo2.getInterfaceName());
        assertEquals("/api/user/create", vo2.getInterfaceUrl());
        assertEquals("POST", vo2.getHttpMethod());
        assertEquals("用户创建接口描述", vo2.getDescription());
        assertEquals("ACTIVE", vo2.getStatus());
    }

    /**
     * 测试实体列表转VO列表 - 空列表
     */
    @Test
    void testE2vList_WithEmptyList() {
        // Act
        List<SysPermissionInterfaceRelationVO> voList = SysPermissionInterfaceRelationDTOConvert.INSTANCE.e2vList(Collections.emptyList());

        // Assert
        assertNotNull(voList);
        assertTrue(voList.isEmpty());
    }

    /**
     * 测试实体列表转VO列表 - null输入
     */
    @Test
    void testE2vList_WithNull() {
        // Act
        List<SysPermissionInterfaceRelationVO> voList = SysPermissionInterfaceRelationDTOConvert.INSTANCE.e2vList(null);

        // Assert
        assertNull(voList);
    }

    /**
     * 测试实体列表转VO列表 - 包含null元素
     */
    @Test
    void testE2vList_WithNullElements() {
        // Arrange
        SysPermissionInterfaceRelation entity = new SysPermissionInterfaceRelation();
        entity.setId(1L);
        entity.setPermissionId(100L);
        entity.setInterfaceId(200L);
        entity.setInterfaceName("有效接口");

        List<SysPermissionInterfaceRelation> entityList = Arrays.asList(entity, null);

        // Act
        List<SysPermissionInterfaceRelationVO> voList = SysPermissionInterfaceRelationDTOConvert.INSTANCE.e2vList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        assertNotNull(voList.get(0));
        assertEquals(1L, voList.get(0).getId());
        assertEquals("有效接口", voList.get(0).getInterfaceName());
        assertNull(voList.get(1));
    }

    /**
     * 测试实体列表转VO列表 - 空字段
     */
    @Test
    void testE2vList_WithEmptyFields() {
        // Arrange
        SysPermissionInterfaceRelation entity = new SysPermissionInterfaceRelation();
        entity.setId(null);
        entity.setPermissionId(null);
        entity.setInterfaceId(null);
        entity.setInterfaceName("");
        entity.setInterfaceUrl("");
        entity.setHttpMethod("");
        entity.setDescription("");
        entity.setStatus("");

        List<SysPermissionInterfaceRelation> entityList = Collections.singletonList(entity);

        // Act
        List<SysPermissionInterfaceRelationVO> voList = SysPermissionInterfaceRelationDTOConvert.INSTANCE.e2vList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(1, voList.size());
        
        SysPermissionInterfaceRelationVO vo = voList.get(0);
        assertNull(vo.getId());
        assertNull(vo.getPermissionId());
        assertNull(vo.getInterfaceId());
        assertEquals("", vo.getInterfaceName());
        assertEquals("", vo.getInterfaceUrl());
        assertEquals("", vo.getHttpMethod());
        assertEquals("", vo.getDescription());
        assertEquals("", vo.getStatus());
    }

    /**
     * 测试实体列表转VO列表 - null字段
     */
    @Test
    void testE2vList_WithNullFields() {
        // Arrange
        SysPermissionInterfaceRelation entity = new SysPermissionInterfaceRelation();
        entity.setId(1L);
        entity.setPermissionId(null);
        entity.setInterfaceId(null);
        entity.setInterfaceName(null);
        entity.setInterfaceUrl(null);
        entity.setHttpMethod(null);
        entity.setDescription(null);
        entity.setStatus(null);

        List<SysPermissionInterfaceRelation> entityList = Collections.singletonList(entity);

        // Act
        List<SysPermissionInterfaceRelationVO> voList = SysPermissionInterfaceRelationDTOConvert.INSTANCE.e2vList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(1, voList.size());
        
        SysPermissionInterfaceRelationVO vo = voList.get(0);
        assertEquals(1L, vo.getId());
        assertNull(vo.getPermissionId());
        assertNull(vo.getInterfaceId());
        assertNull(vo.getInterfaceName());
        assertNull(vo.getInterfaceUrl());
        assertNull(vo.getHttpMethod());
        assertNull(vo.getDescription());
        assertNull(vo.getStatus());
    }

    /**
     * 测试转换器实例不为null
     */
    @Test
    void testInstanceNotNull() {
        assertNotNull(SysPermissionInterfaceRelationDTOConvert.INSTANCE);
    }

    /**
     * 测试不同HTTP方法的处理
     */
    @Test
    void testE2vList_WithDifferentHttpMethods() {
        // Arrange
        SysPermissionInterfaceRelation getEntity = new SysPermissionInterfaceRelation();
        getEntity.setId(1L);
        getEntity.setHttpMethod("GET");
        getEntity.setInterfaceUrl("/api/user/get");

        SysPermissionInterfaceRelation postEntity = new SysPermissionInterfaceRelation();
        postEntity.setId(2L);
        postEntity.setHttpMethod("POST");
        postEntity.setInterfaceUrl("/api/user/create");

        SysPermissionInterfaceRelation putEntity = new SysPermissionInterfaceRelation();
        putEntity.setId(3L);
        putEntity.setHttpMethod("PUT");
        putEntity.setInterfaceUrl("/api/user/update");

        SysPermissionInterfaceRelation deleteEntity = new SysPermissionInterfaceRelation();
        deleteEntity.setId(4L);
        deleteEntity.setHttpMethod("DELETE");
        deleteEntity.setInterfaceUrl("/api/user/delete");

        List<SysPermissionInterfaceRelation> entityList = Arrays.asList(getEntity, postEntity, putEntity, deleteEntity);

        // Act
        List<SysPermissionInterfaceRelationVO> voList = SysPermissionInterfaceRelationDTOConvert.INSTANCE.e2vList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(4, voList.size());
        
        assertEquals("GET", voList.get(0).getHttpMethod());
        assertEquals("/api/user/get", voList.get(0).getInterfaceUrl());
        
        assertEquals("POST", voList.get(1).getHttpMethod());
        assertEquals("/api/user/create", voList.get(1).getInterfaceUrl());
        
        assertEquals("PUT", voList.get(2).getHttpMethod());
        assertEquals("/api/user/update", voList.get(2).getInterfaceUrl());
        
        assertEquals("DELETE", voList.get(3).getHttpMethod());
        assertEquals("/api/user/delete", voList.get(3).getInterfaceUrl());
    }

    /**
     * 测试特殊字符处理
     */
    @Test
    void testE2vList_WithSpecialCharacters() {
        // Arrange
        SysPermissionInterfaceRelation entity = new SysPermissionInterfaceRelation();
        entity.setId(999L);
        entity.setPermissionId(999L);
        entity.setInterfaceId(999L);
        entity.setInterfaceName("特殊接口@#$%");
        entity.setInterfaceUrl("/api/special@path?param=value&other=test");
        entity.setHttpMethod("POST");
        entity.setDescription("描述包含特殊字符：<>&\"'");
        entity.setStatus("SPECIAL_STATUS");

        List<SysPermissionInterfaceRelation> entityList = Collections.singletonList(entity);

        // Act
        List<SysPermissionInterfaceRelationVO> voList = SysPermissionInterfaceRelationDTOConvert.INSTANCE.e2vList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(1, voList.size());
        
        SysPermissionInterfaceRelationVO vo = voList.get(0);
        assertEquals(999L, vo.getId());
        assertEquals(999L, vo.getPermissionId());
        assertEquals(999L, vo.getInterfaceId());
        assertEquals("特殊接口@#$%", vo.getInterfaceName());
        assertEquals("/api/special@path?param=value&other=test", vo.getInterfaceUrl());
        assertEquals("POST", vo.getHttpMethod());
        assertEquals("描述包含特殊字符：<>&\"'", vo.getDescription());
        assertEquals("SPECIAL_STATUS", vo.getStatus());
    }

    /**
     * 测试长字符串处理
     */
    @Test
    void testE2vList_WithLongStrings() {
        // Arrange
        String longString = "A".repeat(500);
        SysPermissionInterfaceRelation entity = new SysPermissionInterfaceRelation();
        entity.setId(Long.MAX_VALUE);
        entity.setPermissionId(Long.MAX_VALUE);
        entity.setInterfaceId(Long.MAX_VALUE);
        entity.setInterfaceName(longString);
        entity.setInterfaceUrl(longString);
        entity.setHttpMethod(longString);
        entity.setDescription(longString);
        entity.setStatus(longString);

        List<SysPermissionInterfaceRelation> entityList = Collections.singletonList(entity);

        // Act
        List<SysPermissionInterfaceRelationVO> voList = SysPermissionInterfaceRelationDTOConvert.INSTANCE.e2vList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(1, voList.size());
        
        SysPermissionInterfaceRelationVO vo = voList.get(0);
        assertEquals(Long.MAX_VALUE, vo.getId());
        assertEquals(Long.MAX_VALUE, vo.getPermissionId());
        assertEquals(Long.MAX_VALUE, vo.getInterfaceId());
        assertEquals(longString, vo.getInterfaceName());
        assertEquals(longString, vo.getInterfaceUrl());
        assertEquals(longString, vo.getHttpMethod());
        assertEquals(longString, vo.getDescription());
        assertEquals(longString, vo.getStatus());
    }

    /**
     * 测试边界值
     */
    @Test
    void testE2vList_WithBoundaryValues() {
        // Arrange
        SysPermissionInterfaceRelation entity = new SysPermissionInterfaceRelation();
        entity.setId(0L);
        entity.setPermissionId(0L);
        entity.setInterfaceId(0L);
        entity.setInterfaceName("0");
        entity.setInterfaceUrl("0");
        entity.setHttpMethod("0");
        entity.setDescription("0");
        entity.setStatus("0");

        List<SysPermissionInterfaceRelation> entityList = Collections.singletonList(entity);

        // Act
        List<SysPermissionInterfaceRelationVO> voList = SysPermissionInterfaceRelationDTOConvert.INSTANCE.e2vList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(1, voList.size());
        
        SysPermissionInterfaceRelationVO vo = voList.get(0);
        assertEquals(0L, vo.getId());
        assertEquals(0L, vo.getPermissionId());
        assertEquals(0L, vo.getInterfaceId());
        assertEquals("0", vo.getInterfaceName());
        assertEquals("0", vo.getInterfaceUrl());
        assertEquals("0", vo.getHttpMethod());
        assertEquals("0", vo.getDescription());
        assertEquals("0", vo.getStatus());
    }

    /**
     * 测试RESTful API路径
     */
    @Test
    void testE2vList_WithRestfulPaths() {
        // Arrange
        SysPermissionInterfaceRelation entity1 = new SysPermissionInterfaceRelation();
        entity1.setId(1L);
        entity1.setInterfaceUrl("/api/v1/users/{id}");
        entity1.setHttpMethod("GET");

        SysPermissionInterfaceRelation entity2 = new SysPermissionInterfaceRelation();
        entity2.setId(2L);
        entity2.setInterfaceUrl("/api/v1/users/{userId}/roles/{roleId}");
        entity2.setHttpMethod("PUT");

        List<SysPermissionInterfaceRelation> entityList = Arrays.asList(entity1, entity2);

        // Act
        List<SysPermissionInterfaceRelationVO> voList = SysPermissionInterfaceRelationDTOConvert.INSTANCE.e2vList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        
        assertEquals("/api/v1/users/{id}", voList.get(0).getInterfaceUrl());
        assertEquals("GET", voList.get(0).getHttpMethod());
        
        assertEquals("/api/v1/users/{userId}/roles/{roleId}", voList.get(1).getInterfaceUrl());
        assertEquals("PUT", voList.get(1).getHttpMethod());
    }
}
