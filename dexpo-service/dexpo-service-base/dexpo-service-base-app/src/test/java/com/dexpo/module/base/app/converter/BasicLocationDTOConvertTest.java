package com.dexpo.module.base.app.converter;

import com.dexpo.module.base.api.basic.dto.BasicLocationDTO;
import com.dexpo.module.base.api.basic.vo.BasicLocationVO;
import com.dexpo.module.base.domain.model.agg.BasicLocation;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link BasicLocationDTOConvert} 的单元测试类
 * 
 * <p>测试业务地域转换器的所有转换方法，确保100%分支覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class BasicLocationDTOConvertTest {

    /**
     * 测试实体转VO - 正常情况
     */
    @Test
    void testE2v_Success() {
        // Arrange
        BasicLocation entity = new BasicLocation();
        entity.setId(1L);
        entity.setLocationCode("LOC001");
        entity.setLocationNameCn("北京");
        entity.setLocationNameEn("Beijing");
        entity.setLocationTag("CITY");

        // Act
        BasicLocationVO vo = BasicLocationDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(1L, vo.getId());
        assertEquals("LOC001", vo.getLocationCode());
        assertEquals("北京", vo.getLocationNameCn());
        assertEquals("Beijing", vo.getLocationNameEn());
        assertEquals("CITY", vo.getLocationTag());
    }

    /**
     * 测试实体转VO - null输入
     */
    @Test
    void testE2v_WithNull() {
        // Act
        BasicLocationVO vo = BasicLocationDTOConvert.INSTANCE.e2v(null);

        // Assert
        assertNull(vo);
    }

    /**
     * 测试实体转VO - 空字段
     */
    @Test
    void testE2v_WithEmptyFields() {
        // Arrange
        BasicLocation entity = new BasicLocation();
        entity.setId(null);
        entity.setLocationCode("");
        entity.setLocationNameCn("");
        entity.setLocationNameEn("");
        entity.setLocationTag("");

        // Act
        BasicLocationVO vo = BasicLocationDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertNull(vo.getId());
        assertEquals("", vo.getLocationCode());
        assertEquals("", vo.getLocationNameCn());
        assertEquals("", vo.getLocationNameEn());
        assertEquals("", vo.getLocationTag());
    }

    /**
     * 测试DTO转实体 - 正常情况
     */
    @Test
    void testD2e_Success() {
        // Arrange
        BasicLocationDTO dto = new BasicLocationDTO();
        dto.setLocationTag("PROVINCE");

        // Act
        BasicLocation entity = BasicLocationDTOConvert.INSTANCE.d2e(dto);

        // Assert
        assertNotNull(entity);
        assertEquals("PROVINCE", entity.getLocationTag());
    }

    /**
     * 测试DTO转实体 - null输入
     */
    @Test
    void testD2e_WithNull() {
        // Act
        BasicLocation entity = BasicLocationDTOConvert.INSTANCE.d2e(null);

        // Assert
        assertNull(entity);
    }

    /**
     * 测试DTO转实体 - 空字段
     */
    @Test
    void testD2e_WithEmptyFields() {
        // Arrange
        BasicLocationDTO dto = new BasicLocationDTO();
        dto.setLocationTag("");

        // Act
        BasicLocation entity = BasicLocationDTOConvert.INSTANCE.d2e(dto);

        // Assert
        assertNotNull(entity);
        assertEquals("", entity.getLocationTag());
    }

    /**
     * 测试DTO转实体 - null字段
     */
    @Test
    void testD2e_WithNullFields() {
        // Arrange
        BasicLocationDTO dto = new BasicLocationDTO();
        dto.setLocationTag(null);

        // Act
        BasicLocation entity = BasicLocationDTOConvert.INSTANCE.d2e(dto);

        // Assert
        assertNotNull(entity);
        assertNull(entity.getLocationTag());
    }

    /**
     * 测试实体列表转VO列表 - 正常情况
     */
    @Test
    void testE2vList_Success() {
        // Arrange
        BasicLocation entity1 = new BasicLocation();
        entity1.setId(1L);
        entity1.setLocationCode("LOC001");
        entity1.setLocationNameCn("北京");
        entity1.setLocationTag("CITY");

        BasicLocation entity2 = new BasicLocation();
        entity2.setId(2L);
        entity2.setLocationCode("LOC002");
        entity2.setLocationNameCn("上海");
        entity2.setLocationTag("CITY");

        List<BasicLocation> entityList = Arrays.asList(entity1, entity2);

        // Act
        List<BasicLocationVO> voList = BasicLocationDTOConvert.INSTANCE.e2vList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        
        BasicLocationVO vo1 = voList.get(0);
        assertEquals(1L, vo1.getId());
        assertEquals("LOC001", vo1.getLocationCode());
        assertEquals("北京", vo1.getLocationNameCn());
        assertEquals("CITY", vo1.getLocationTag());

        BasicLocationVO vo2 = voList.get(1);
        assertEquals(2L, vo2.getId());
        assertEquals("LOC002", vo2.getLocationCode());
        assertEquals("上海", vo2.getLocationNameCn());
        assertEquals("CITY", vo2.getLocationTag());
    }

    /**
     * 测试实体列表转VO列表 - 空列表
     */
    @Test
    void testE2vList_WithEmptyList() {
        // Act
        List<BasicLocationVO> voList = BasicLocationDTOConvert.INSTANCE.e2vList(Collections.emptyList());

        // Assert
        assertNotNull(voList);
        assertTrue(voList.isEmpty());
    }

    /**
     * 测试实体列表转VO列表 - null输入
     */
    @Test
    void testE2vList_WithNull() {
        // Act
        List<BasicLocationVO> voList = BasicLocationDTOConvert.INSTANCE.e2vList(null);

        // Assert
        assertNull(voList);
    }

    /**
     * 测试实体列表转VO列表 - 包含null元素
     */
    @Test
    void testE2vList_WithNullElements() {
        // Arrange
        BasicLocation entity = new BasicLocation();
        entity.setId(1L);
        entity.setLocationCode("LOC001");

        List<BasicLocation> entityList = Arrays.asList(entity, null);

        // Act
        List<BasicLocationVO> voList = BasicLocationDTOConvert.INSTANCE.e2vList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        assertNotNull(voList.get(0));
        assertEquals(1L, voList.get(0).getId());
        assertNull(voList.get(1));
    }

    /**
     * 测试DTO列表转实体列表 - 正常情况
     */
    @Test
    void testD2eList_Success() {
        // Arrange
        BasicLocationDTO dto1 = new BasicLocationDTO();
        dto1.setLocationTag("CITY");

        BasicLocationDTO dto2 = new BasicLocationDTO();
        dto2.setLocationTag("PROVINCE");

        List<BasicLocationDTO> dtoList = Arrays.asList(dto1, dto2);

        // Act
        List<BasicLocation> entityList = BasicLocationDTOConvert.INSTANCE.d2eList(dtoList);

        // Assert
        assertNotNull(entityList);
        assertEquals(2, entityList.size());
        
        BasicLocation entity1 = entityList.get(0);
        assertEquals("CITY", entity1.getLocationTag());

        BasicLocation entity2 = entityList.get(1);
        assertEquals("PROVINCE", entity2.getLocationTag());
    }

    /**
     * 测试DTO列表转实体列表 - 空列表
     */
    @Test
    void testD2eList_WithEmptyList() {
        // Act
        List<BasicLocation> entityList = BasicLocationDTOConvert.INSTANCE.d2eList(Collections.emptyList());

        // Assert
        assertNotNull(entityList);
        assertTrue(entityList.isEmpty());
    }

    /**
     * 测试DTO列表转实体列表 - null输入
     */
    @Test
    void testD2eList_WithNull() {
        // Act
        List<BasicLocation> entityList = BasicLocationDTOConvert.INSTANCE.d2eList(null);

        // Assert
        assertNull(entityList);
    }

    /**
     * 测试转换器实例不为null
     */
    @Test
    void testInstanceNotNull() {
        assertNotNull(BasicLocationDTOConvert.INSTANCE);
    }

    /**
     * 测试特殊字符处理
     */
    @Test
    void testE2v_WithSpecialCharacters() {
        // Arrange
        BasicLocation entity = new BasicLocation();
        entity.setId(999L);
        entity.setLocationCode("LOC@#$%");
        entity.setLocationNameCn("测试地区（特殊）");
        entity.setLocationNameEn("Test Location & Special");
        entity.setLocationTag("SPECIAL_TAG");

        // Act
        BasicLocationVO vo = BasicLocationDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(999L, vo.getId());
        assertEquals("LOC@#$%", vo.getLocationCode());
        assertEquals("测试地区（特殊）", vo.getLocationNameCn());
        assertEquals("Test Location & Special", vo.getLocationNameEn());
        assertEquals("SPECIAL_TAG", vo.getLocationTag());
    }

    /**
     * 测试长字符串处理
     */
    @Test
    void testE2v_WithLongStrings() {
        // Arrange
        String longString = "A".repeat(1000);
        BasicLocation entity = new BasicLocation();
        entity.setId(Long.MAX_VALUE);
        entity.setLocationCode(longString);
        entity.setLocationNameCn(longString);
        entity.setLocationNameEn(longString);
        entity.setLocationTag(longString);

        // Act
        BasicLocationVO vo = BasicLocationDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(Long.MAX_VALUE, vo.getId());
        assertEquals(longString, vo.getLocationCode());
        assertEquals(longString, vo.getLocationNameCn());
        assertEquals(longString, vo.getLocationNameEn());
        assertEquals(longString, vo.getLocationTag());
    }

    /**
     * 测试边界值
     */
    @Test
    void testE2v_WithBoundaryValues() {
        // Arrange
        BasicLocation entity = new BasicLocation();
        entity.setId(0L);
        entity.setLocationCode("0");
        entity.setLocationNameCn("0");
        entity.setLocationNameEn("0");
        entity.setLocationTag("0");

        // Act
        BasicLocationVO vo = BasicLocationDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(0L, vo.getId());
        assertEquals("0", vo.getLocationCode());
        assertEquals("0", vo.getLocationNameCn());
        assertEquals("0", vo.getLocationNameEn());
        assertEquals("0", vo.getLocationTag());
    }
}
