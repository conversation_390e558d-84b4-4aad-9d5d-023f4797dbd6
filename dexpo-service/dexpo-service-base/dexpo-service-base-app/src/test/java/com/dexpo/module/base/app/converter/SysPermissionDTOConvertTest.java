package com.dexpo.module.base.app.converter;

import com.dexpo.module.base.api.permission.dto.SysPermissionDTO;
import com.dexpo.module.base.api.permission.vo.SysPermissionVO;
import com.dexpo.module.base.domain.model.valueobject.SysPermission;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link SysPermissionDTOConvert} 的单元测试类
 * 
 * <p>测试系统权限转换器的所有转换方法，确保100%分支覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class SysPermissionDTOConvertTest {

    /**
     * 测试实体转VO - 正常情况
     */
    @Test
    void testE2v_Success() {
        // Arrange
        SysPermission entity = new SysPermission();
        entity.setId(1L);
        entity.setPermissionName("用户管理");
        entity.setPermissionCode("USER_MANAGE");
        entity.setPermissionType("MENU");
        entity.setParentId(0L);
        entity.setPath("/user");
        entity.setComponent("UserManage");
        entity.setIcon("user");
        entity.setSort(1);
        entity.setStatus("ACTIVE");
        entity.setDescription("用户管理权限");

        // Act
        SysPermissionVO vo = SysPermissionDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(1L, vo.getId());
        assertEquals("用户管理", vo.getPermissionName());
        assertEquals("USER_MANAGE", vo.getPermissionCode());
        assertEquals("MENU", vo.getPermissionType());
        assertEquals(0L, vo.getParentId());
        assertEquals("/user", vo.getPath());
        assertEquals("UserManage", vo.getComponent());
        assertEquals("user", vo.getIcon());
        assertEquals(1, vo.getSort());
        assertEquals("ACTIVE", vo.getStatus());
        assertEquals("用户管理权限", vo.getDescription());
    }

    /**
     * 测试实体转VO - null输入
     */
    @Test
    void testE2v_WithNull() {
        // Act
        SysPermissionVO vo = SysPermissionDTOConvert.INSTANCE.e2v((SysPermission) null);

        // Assert
        assertNull(vo);
    }

    /**
     * 测试实体转VO - 空字段
     */
    @Test
    void testE2v_WithEmptyFields() {
        // Arrange
        SysPermission entity = new SysPermission();
        entity.setId(null);
        entity.setPermissionName("");
        entity.setPermissionCode("");
        entity.setPermissionType("");
        entity.setParentId(null);
        entity.setPath("");
        entity.setComponent("");
        entity.setIcon("");
        entity.setSort(null);
        entity.setStatus("");
        entity.setDescription("");

        // Act
        SysPermissionVO vo = SysPermissionDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertNull(vo.getId());
        assertEquals("", vo.getPermissionName());
        assertEquals("", vo.getPermissionCode());
        assertEquals("", vo.getPermissionType());
        assertNull(vo.getParentId());
        assertEquals("", vo.getPath());
        assertEquals("", vo.getComponent());
        assertEquals("", vo.getIcon());
        assertNull(vo.getSort());
        assertEquals("", vo.getStatus());
        assertEquals("", vo.getDescription());
    }

    /**
     * 测试实体列表转VO列表 - 正常情况
     */
    @Test
    void testE2vList_Success() {
        // Arrange
        SysPermission entity1 = new SysPermission();
        entity1.setId(1L);
        entity1.setPermissionName("权限1");
        entity1.setPermissionCode("PERM_001");
        entity1.setPermissionType("MENU");

        SysPermission entity2 = new SysPermission();
        entity2.setId(2L);
        entity2.setPermissionName("权限2");
        entity2.setPermissionCode("PERM_002");
        entity2.setPermissionType("BUTTON");

        List<SysPermission> entityList = Arrays.asList(entity1, entity2);

        // Act
        List<SysPermissionVO> voList = SysPermissionDTOConvert.INSTANCE.e2v(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        
        SysPermissionVO vo1 = voList.get(0);
        assertEquals(1L, vo1.getId());
        assertEquals("权限1", vo1.getPermissionName());
        assertEquals("PERM_001", vo1.getPermissionCode());
        assertEquals("MENU", vo1.getPermissionType());

        SysPermissionVO vo2 = voList.get(1);
        assertEquals(2L, vo2.getId());
        assertEquals("权限2", vo2.getPermissionName());
        assertEquals("PERM_002", vo2.getPermissionCode());
        assertEquals("BUTTON", vo2.getPermissionType());
    }

    /**
     * 测试实体列表转VO列表 - 空列表
     */
    @Test
    void testE2vList_WithEmptyList() {
        // Act
        List<SysPermissionVO> voList = SysPermissionDTOConvert.INSTANCE.e2v(Collections.emptyList());

        // Assert
        assertNotNull(voList);
        assertTrue(voList.isEmpty());
    }

    /**
     * 测试实体列表转VO列表 - null输入
     */
    @Test
    void testE2vList_WithNull() {
        // Act
        List<SysPermissionVO> voList = SysPermissionDTOConvert.INSTANCE.e2v((List<SysPermission>) null);

        // Assert
        assertNull(voList);
    }

    /**
     * 测试DTO转实体 - 正常情况
     */
    @Test
    void testDte_Success() {
        // Arrange
        SysPermissionDTO dto = new SysPermissionDTO();
        dto.setId(1L);
        dto.setPermissionName("角色管理");
        dto.setPermissionCode("ROLE_MANAGE");
        dto.setPermissionType("MENU");
        dto.setParentId(0L);
        dto.setPath("/role");
        dto.setComponent("RoleManage");
        dto.setIcon("role");
        dto.setSort(2);
        dto.setStatus("ACTIVE");
        dto.setDescription("角色管理权限");

        // Act
        SysPermission entity = SysPermissionDTOConvert.INSTANCE.dte(dto);

        // Assert
        assertNotNull(entity);
        assertEquals(1L, entity.getId());
        assertEquals("角色管理", entity.getPermissionName());
        assertEquals("ROLE_MANAGE", entity.getPermissionCode());
        assertEquals("MENU", entity.getPermissionType());
        assertEquals(0L, entity.getParentId());
        assertEquals("/role", entity.getPath());
        assertEquals("RoleManage", entity.getComponent());
        assertEquals("role", entity.getIcon());
        assertEquals(2, entity.getSort());
        assertEquals("ACTIVE", entity.getStatus());
        assertEquals("角色管理权限", entity.getDescription());
    }

    /**
     * 测试DTO转实体 - null输入
     */
    @Test
    void testDte_WithNull() {
        // Act
        SysPermission entity = SysPermissionDTOConvert.INSTANCE.dte(null);

        // Assert
        assertNull(entity);
    }

    /**
     * 测试DTO转实体 - 空字段
     */
    @Test
    void testDte_WithEmptyFields() {
        // Arrange
        SysPermissionDTO dto = new SysPermissionDTO();
        dto.setId(null);
        dto.setPermissionName("");
        dto.setPermissionCode("");
        dto.setPermissionType("");
        dto.setParentId(null);
        dto.setPath("");
        dto.setComponent("");
        dto.setIcon("");
        dto.setSort(null);
        dto.setStatus("");
        dto.setDescription("");

        // Act
        SysPermission entity = SysPermissionDTOConvert.INSTANCE.dte(dto);

        // Assert
        assertNotNull(entity);
        assertNull(entity.getId());
        assertEquals("", entity.getPermissionName());
        assertEquals("", entity.getPermissionCode());
        assertEquals("", entity.getPermissionType());
        assertNull(entity.getParentId());
        assertEquals("", entity.getPath());
        assertEquals("", entity.getComponent());
        assertEquals("", entity.getIcon());
        assertNull(entity.getSort());
        assertEquals("", entity.getStatus());
        assertEquals("", entity.getDescription());
    }

    /**
     * 测试e2vList方法 - 正常情况
     */
    @Test
    void testE2vListMethod_Success() {
        // Arrange
        SysPermission entity1 = new SysPermission();
        entity1.setId(1L);
        entity1.setPermissionName("权限A");
        entity1.setPermissionCode("PERM_A");

        SysPermission entity2 = new SysPermission();
        entity2.setId(2L);
        entity2.setPermissionName("权限B");
        entity2.setPermissionCode("PERM_B");

        List<SysPermission> entityList = Arrays.asList(entity1, entity2);

        // Act
        List<SysPermissionVO> voList = SysPermissionDTOConvert.INSTANCE.e2vList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        
        SysPermissionVO vo1 = voList.get(0);
        assertEquals(1L, vo1.getId());
        assertEquals("权限A", vo1.getPermissionName());
        assertEquals("PERM_A", vo1.getPermissionCode());

        SysPermissionVO vo2 = voList.get(1);
        assertEquals(2L, vo2.getId());
        assertEquals("权限B", vo2.getPermissionName());
        assertEquals("PERM_B", vo2.getPermissionCode());
    }

    /**
     * 测试e2vList方法 - null输入
     */
    @Test
    void testE2vListMethod_WithNull() {
        // Act
        List<SysPermissionVO> voList = SysPermissionDTOConvert.INSTANCE.e2vList(null);

        // Assert
        assertNull(voList);
    }

    /**
     * 测试e2vList方法 - 空列表
     */
    @Test
    void testE2vListMethod_WithEmptyList() {
        // Act
        List<SysPermissionVO> voList = SysPermissionDTOConvert.INSTANCE.e2vList(Collections.emptyList());

        // Assert
        assertNotNull(voList);
        assertTrue(voList.isEmpty());
    }

    /**
     * 测试转换器实例不为null
     */
    @Test
    void testInstanceNotNull() {
        assertNotNull(SysPermissionDTOConvert.INSTANCE);
    }

    /**
     * 测试特殊字符处理
     */
    @Test
    void testE2v_WithSpecialCharacters() {
        // Arrange
        SysPermission entity = new SysPermission();
        entity.setId(999L);
        entity.setPermissionName("特殊权限@#$%");
        entity.setPermissionCode("SPECIAL_PERM@#$");
        entity.setPermissionType("SPECIAL_TYPE");
        entity.setPath("/special@path");
        entity.setComponent("Special<Component>");
        entity.setIcon("icon&special");
        entity.setDescription("描述包含特殊字符：<>&\"'");

        // Act
        SysPermissionVO vo = SysPermissionDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(999L, vo.getId());
        assertEquals("特殊权限@#$%", vo.getPermissionName());
        assertEquals("SPECIAL_PERM@#$", vo.getPermissionCode());
        assertEquals("SPECIAL_TYPE", vo.getPermissionType());
        assertEquals("/special@path", vo.getPath());
        assertEquals("Special<Component>", vo.getComponent());
        assertEquals("icon&special", vo.getIcon());
        assertEquals("描述包含特殊字符：<>&\"'", vo.getDescription());
    }

    /**
     * 测试边界值
     */
    @Test
    void testE2v_WithBoundaryValues() {
        // Arrange
        SysPermission entity = new SysPermission();
        entity.setId(0L);
        entity.setPermissionName("0");
        entity.setPermissionCode("0");
        entity.setPermissionType("0");
        entity.setParentId(0L);
        entity.setPath("0");
        entity.setComponent("0");
        entity.setIcon("0");
        entity.setSort(0);
        entity.setStatus("0");
        entity.setDescription("0");

        // Act
        SysPermissionVO vo = SysPermissionDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(0L, vo.getId());
        assertEquals("0", vo.getPermissionName());
        assertEquals("0", vo.getPermissionCode());
        assertEquals("0", vo.getPermissionType());
        assertEquals(0L, vo.getParentId());
        assertEquals("0", vo.getPath());
        assertEquals("0", vo.getComponent());
        assertEquals("0", vo.getIcon());
        assertEquals(0, vo.getSort());
        assertEquals("0", vo.getStatus());
        assertEquals("0", vo.getDescription());
    }

    /**
     * 测试包含null元素的列表
     */
    @Test
    void testE2vList_WithNullElements() {
        // Arrange
        SysPermission entity = new SysPermission();
        entity.setId(1L);
        entity.setPermissionName("有效权限");

        List<SysPermission> entityList = Arrays.asList(entity, null);

        // Act
        List<SysPermissionVO> voList = SysPermissionDTOConvert.INSTANCE.e2v(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        assertNotNull(voList.get(0));
        assertEquals(1L, voList.get(0).getId());
        assertNull(voList.get(1));
    }

    /**
     * 测试权限层级结构
     */
    @Test
    void testE2v_WithHierarchicalPermissions() {
        // Arrange
        SysPermission parentPermission = new SysPermission();
        parentPermission.setId(1L);
        parentPermission.setPermissionName("系统管理");
        parentPermission.setPermissionCode("SYSTEM_MANAGE");
        parentPermission.setPermissionType("MENU");
        parentPermission.setParentId(0L);
        parentPermission.setSort(1);

        SysPermission childPermission = new SysPermission();
        childPermission.setId(2L);
        childPermission.setPermissionName("用户管理");
        childPermission.setPermissionCode("USER_MANAGE");
        childPermission.setPermissionType("MENU");
        childPermission.setParentId(1L);
        childPermission.setSort(1);

        // Act
        SysPermissionVO parentVO = SysPermissionDTOConvert.INSTANCE.e2v(parentPermission);
        SysPermissionVO childVO = SysPermissionDTOConvert.INSTANCE.e2v(childPermission);

        // Assert
        assertNotNull(parentVO);
        assertEquals(1L, parentVO.getId());
        assertEquals(0L, parentVO.getParentId());
        assertEquals("系统管理", parentVO.getPermissionName());

        assertNotNull(childVO);
        assertEquals(2L, childVO.getId());
        assertEquals(1L, childVO.getParentId());
        assertEquals("用户管理", childVO.getPermissionName());
    }
}
