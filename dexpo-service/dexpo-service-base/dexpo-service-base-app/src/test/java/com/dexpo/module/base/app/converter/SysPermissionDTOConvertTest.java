package com.dexpo.module.base.app.converter;

import com.dexpo.module.base.api.permission.dto.SysPermissionDTO;
import com.dexpo.module.base.api.permission.vo.SysPermissionVO;
import com.dexpo.module.base.domain.model.valueobject.SysPermission;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link SysPermissionDTOConvert} 的单元测试类
 * 
 * <p>测试系统权限转换器的所有转换方法，确保100%分支覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class SysPermissionDTOConvertTest  {

    /**
     * 测试实体转VO - 正常情况
     */
    @Test
    void testE2v_Success() {
        // Arrange
        SysPermission entity = new SysPermission();
        entity.setId(1L);
        entity.setPermissionName("用户管理");
        entity.setPermissionCode("USER_MANAGE");
        entity.setPermissionType("MENU");
        entity.setParentPermissionCode("PARENT_CODE");
        entity.setRoleCategory("SYSTEM");
        entity.setAppType("WEB");

        // Act
        SysPermissionVO vo = SysPermissionDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(1L, vo.getId());
        assertEquals("用户管理", vo.getPermissionName());
        assertEquals("USER_MANAGE", vo.getPermissionCode());
        assertEquals("MENU", vo.getPermissionType());
        assertEquals("PARENT_CODE", vo.getParentPermissionCode());
        assertEquals("SYSTEM", vo.getRoleCategory());
        assertEquals("WEB", vo.getAppType());
    }

    /**
     * 测试实体转VO - null输入
     */
    @Test
    void testE2v_WithNull() {
        // Act
        SysPermissionVO vo = SysPermissionDTOConvert.INSTANCE.e2v((SysPermission) null);

        // Assert
        assertNull(vo);
    }

    /**
     * 测试实体转VO - 空字段
     */
    @Test
    void testE2v_WithEmptyFields() {
        // Arrange
        SysPermission entity = new SysPermission();
        entity.setId(null);
        entity.setPermissionName("");
        entity.setPermissionCode("");
        entity.setPermissionType("");
        entity.setParentPermissionCode("");
        entity.setRoleCategory("");
        entity.setAppType("");

        // Act
        SysPermissionVO vo = SysPermissionDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertNull(vo.getId());
        assertEquals("", vo.getPermissionName());
        assertEquals("", vo.getPermissionCode());
        assertEquals("", vo.getPermissionType());
        assertEquals("", vo.getParentPermissionCode());
        assertEquals("", vo.getRoleCategory());
        assertEquals("", vo.getAppType());
    }

    /**
     * 测试实体列表转VO列表 - 正常情况
     */
    @Test
    void testE2vList_Success() {
        // Arrange
        SysPermission entity1 = new SysPermission();
        entity1.setId(1L);
        entity1.setPermissionName("权限1");
        entity1.setPermissionCode("PERM_001");
        entity1.setPermissionType("MENU");

        SysPermission entity2 = new SysPermission();
        entity2.setId(2L);
        entity2.setPermissionName("权限2");
        entity2.setPermissionCode("PERM_002");
        entity2.setPermissionType("BUTTON");

        List<SysPermission> entityList = Arrays.asList(entity1, entity2);

        // Act
        List<SysPermissionVO> voList = SysPermissionDTOConvert.INSTANCE.e2v(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        
        SysPermissionVO vo1 = voList.get(0);
        assertEquals(1L, vo1.getId());
        assertEquals("权限1", vo1.getPermissionName());
        assertEquals("PERM_001", vo1.getPermissionCode());
        assertEquals("MENU", vo1.getPermissionType());

        SysPermissionVO vo2 = voList.get(1);
        assertEquals(2L, vo2.getId());
        assertEquals("权限2", vo2.getPermissionName());
        assertEquals("PERM_002", vo2.getPermissionCode());
        assertEquals("BUTTON", vo2.getPermissionType());
    }

    /**
     * 测试实体列表转VO列表 - 空列表
     */
    @Test
    void testE2vList_WithEmptyList() {
        // Act
        List<SysPermissionVO> voList = SysPermissionDTOConvert.INSTANCE.e2v(Collections.emptyList());

        // Assert
        assertNotNull(voList);
        assertTrue(voList.isEmpty());
    }

    /**
     * 测试实体列表转VO列表 - null输入
     */
    @Test
    void testE2vList_WithNull() {
        // Act
        List<SysPermissionVO> voList = SysPermissionDTOConvert.INSTANCE.e2v((List<SysPermission>) null);

        // Assert
        assertNull(voList);
    }

    /**
     * 测试DTO转实体 - 正常情况
     */
    @Test
    void testDte_Success() {
        // Arrange
        SysPermissionDTO dto = new SysPermissionDTO();
        dto.setOrganizationCode("ORG001");
        dto.setUserCode("USER001");
        dto.setUserType("ADMIN");

        // Act
        SysPermission entity = SysPermissionDTOConvert.INSTANCE.dte(dto);

        // Assert
        assertNotNull(entity);
        assertEquals("ORG001", entity.getOrganizationCode());
        assertEquals("USER001", entity.getUserCode());
        assertEquals("ADMIN", entity.getUserType());
    }

    /**
     * 测试DTO转实体 - null输入
     */
    @Test
    void testDte_WithNull() {
        // Act
        SysPermission entity = SysPermissionDTOConvert.INSTANCE.dte(null);

        // Assert
        assertNull(entity);
    }

    /**
     * 测试DTO转实体 - 空字段
     */
    @Test
    void testDte_WithEmptyFields() {
        // Arrange
        SysPermissionDTO dto = new SysPermissionDTO();
        dto.setOrganizationCode("");
        dto.setUserCode("");
        dto.setUserType("");

        // Act
        SysPermission entity = SysPermissionDTOConvert.INSTANCE.dte(dto);

        // Assert
        assertNotNull(entity);
        assertEquals("", entity.getOrganizationCode());
        assertEquals("", entity.getUserCode());
        assertEquals("", entity.getUserType());
    }

    /**
     * 测试e2vList方法 - 正常情况
     */
    @Test
    void testE2vListMethod_Success() {
        // Arrange
        SysPermission entity1 = new SysPermission();
        entity1.setId(1L);
        entity1.setPermissionName("权限A");
        entity1.setPermissionCode("PERM_A");

        SysPermission entity2 = new SysPermission();
        entity2.setId(2L);
        entity2.setPermissionName("权限B");
        entity2.setPermissionCode("PERM_B");

        List<SysPermission> entityList = Arrays.asList(entity1, entity2);

        // Act
        List<SysPermissionVO> voList = SysPermissionDTOConvert.INSTANCE.e2vList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        
        SysPermissionVO vo1 = voList.get(0);
        assertEquals(1L, vo1.getId());
        assertEquals("权限A", vo1.getPermissionName());
        assertEquals("PERM_A", vo1.getPermissionCode());

        SysPermissionVO vo2 = voList.get(1);
        assertEquals(2L, vo2.getId());
        assertEquals("权限B", vo2.getPermissionName());
        assertEquals("PERM_B", vo2.getPermissionCode());
    }

    /**
     * 测试e2vList方法 - null输入
     */
    @Test
    void testE2vListMethod_WithNull() {
        // Act
        List<SysPermissionVO> voList = SysPermissionDTOConvert.INSTANCE.e2vList(null);

        // Assert
        assertNull(voList);
    }

    /**
     * 测试e2vList方法 - 空列表
     */
    @Test
    void testE2vListMethod_WithEmptyList() {
        // Act
        List<SysPermissionVO> voList = SysPermissionDTOConvert.INSTANCE.e2vList(Collections.emptyList());

        // Assert
        assertNotNull(voList);
        assertTrue(voList.isEmpty());
    }

    /**
     * 测试转换器实例不为null
     */
    @Test
    void testInstanceNotNull() {
        assertNotNull(SysPermissionDTOConvert.INSTANCE);
    }

    /**
     * 测试特殊字符处理
     */
    @Test
    void testE2v_WithSpecialCharacters() {
        // Arrange
        SysPermission entity = new SysPermission();
        entity.setId(999L);
        entity.setPermissionName("特殊权限@#$%");
        entity.setPermissionCode("SPECIAL_PERM@#$");
        entity.setPermissionType("SPECIAL_TYPE");
        entity.setParentPermissionCode("PARENT@#$");
        entity.setRoleCategory("SPECIAL_ROLE");
        entity.setAppType("SPECIAL_APP");

        // Act
        SysPermissionVO vo = SysPermissionDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(999L, vo.getId());
        assertEquals("特殊权限@#$%", vo.getPermissionName());
        assertEquals("SPECIAL_PERM@#$", vo.getPermissionCode());
        assertEquals("SPECIAL_TYPE", vo.getPermissionType());
        assertEquals("PARENT@#$", vo.getParentPermissionCode());
        assertEquals("SPECIAL_ROLE", vo.getRoleCategory());
        assertEquals("SPECIAL_APP", vo.getAppType());
    }

    /**
     * 测试边界值
     */
    @Test
    void testE2v_WithBoundaryValues() {
        // Arrange
        SysPermission entity = new SysPermission();
        entity.setId(0L);
        entity.setPermissionName("0");
        entity.setPermissionCode("0");
        entity.setPermissionType("0");
        entity.setParentPermissionCode("0");
        entity.setRoleCategory("0");
        entity.setAppType("0");

        // Act
        SysPermissionVO vo = SysPermissionDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(0L, vo.getId());
        assertEquals("0", vo.getPermissionName());
        assertEquals("0", vo.getPermissionCode());
        assertEquals("0", vo.getPermissionType());
        assertEquals("0", vo.getParentPermissionCode());
        assertEquals("0", vo.getRoleCategory());
        assertEquals("0", vo.getAppType());
    }

    /**
     * 测试包含null元素的列表
     */
    @Test
    void testE2vList_WithNullElements() {
        // Arrange
        SysPermission entity = new SysPermission();
        entity.setId(1L);
        entity.setPermissionName("有效权限");

        List<SysPermission> entityList = Arrays.asList(entity, null);

        // Act
        List<SysPermissionVO> voList = SysPermissionDTOConvert.INSTANCE.e2v(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        assertNotNull(voList.get(0));
        assertEquals(1L, voList.get(0).getId());
        assertNull(voList.get(1));
    }

    /**
     * 测试权限层级结构
     */
    @Test
    void testE2v_WithHierarchicalPermissions() {
        // Arrange
        SysPermission parentPermission = new SysPermission();
        parentPermission.setId(1L);
        parentPermission.setPermissionName("系统管理");
        parentPermission.setPermissionCode("SYSTEM_MANAGE");
        parentPermission.setPermissionType("MENU");
        parentPermission.setParentPermissionCode("ROOT");

        SysPermission childPermission = new SysPermission();
        childPermission.setId(2L);
        childPermission.setPermissionName("用户管理");
        childPermission.setPermissionCode("USER_MANAGE");
        childPermission.setPermissionType("MENU");
        childPermission.setParentPermissionCode("SYSTEM_MANAGE");

        // Act
        SysPermissionVO parentVO = SysPermissionDTOConvert.INSTANCE.e2v(parentPermission);
        SysPermissionVO childVO = SysPermissionDTOConvert.INSTANCE.e2v(childPermission);

        // Assert
        assertNotNull(parentVO);
        assertEquals(1L, parentVO.getId());
        assertEquals("ROOT", parentVO.getParentPermissionCode());
        assertEquals("系统管理", parentVO.getPermissionName());

        assertNotNull(childVO);
        assertEquals(2L, childVO.getId());
        assertEquals("SYSTEM_MANAGE", childVO.getParentPermissionCode());
        assertEquals("用户管理", childVO.getPermissionName());
    }
}
