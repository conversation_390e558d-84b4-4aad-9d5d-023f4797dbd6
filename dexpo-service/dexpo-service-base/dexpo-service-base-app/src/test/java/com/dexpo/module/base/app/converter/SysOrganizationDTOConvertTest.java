package com.dexpo.module.base.app.converter;

import com.dexpo.module.base.api.basic.vo.SysOrganizationVO;
import com.dexpo.module.base.domain.model.SysOrganization;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link SysOrganizationDTOConvert} 的单元测试类
 * 
 * <p>测试系统组织转换器的所有转换方法，确保100%分支覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class SysOrganizationDTOConvertTest {

    /**
     * 测试实体列表转VO列表 - 正常情况
     */
    @Test
    void testToVOList_Success() {
        // Arrange
        SysOrganization entity1 = new SysOrganization();
        entity1.setId(1L);
        entity1.setOrganizationName("系统组织1");
        entity1.setOrganizationCode("SYS_ORG_001");
        entity1.setParentId(0L);
        entity1.setLevel(1);
        entity1.setSort(1);
        entity1.setStatus("ACTIVE");
        entity1.setDescription("系统组织1描述");

        SysOrganization entity2 = new SysOrganization();
        entity2.setId(2L);
        entity2.setOrganizationName("系统组织2");
        entity2.setOrganizationCode("SYS_ORG_002");
        entity2.setParentId(1L);
        entity2.setLevel(2);
        entity2.setSort(2);
        entity2.setStatus("INACTIVE");
        entity2.setDescription("系统组织2描述");

        List<SysOrganization> entityList = Arrays.asList(entity1, entity2);

        // Act
        List<SysOrganizationVO> voList = SysOrganizationDTOConvert.INSTANCE.toVOList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        
        SysOrganizationVO vo1 = voList.get(0);
        assertEquals(1L, vo1.getId());
        assertEquals("系统组织1", vo1.getOrganizationName());
        assertEquals("SYS_ORG_001", vo1.getOrganizationCode());
        assertEquals(0L, vo1.getParentId());
        assertEquals(1, vo1.getLevel());
        assertEquals(1, vo1.getSort());
        assertEquals("ACTIVE", vo1.getStatus());
        assertEquals("系统组织1描述", vo1.getDescription());

        SysOrganizationVO vo2 = voList.get(1);
        assertEquals(2L, vo2.getId());
        assertEquals("系统组织2", vo2.getOrganizationName());
        assertEquals("SYS_ORG_002", vo2.getOrganizationCode());
        assertEquals(1L, vo2.getParentId());
        assertEquals(2, vo2.getLevel());
        assertEquals(2, vo2.getSort());
        assertEquals("INACTIVE", vo2.getStatus());
        assertEquals("系统组织2描述", vo2.getDescription());
    }

    /**
     * 测试实体列表转VO列表 - 空列表
     */
    @Test
    void testToVOList_WithEmptyList() {
        // Act
        List<SysOrganizationVO> voList = SysOrganizationDTOConvert.INSTANCE.toVOList(Collections.emptyList());

        // Assert
        assertNotNull(voList);
        assertTrue(voList.isEmpty());
    }

    /**
     * 测试实体列表转VO列表 - null输入
     */
    @Test
    void testToVOList_WithNull() {
        // Act
        List<SysOrganizationVO> voList = SysOrganizationDTOConvert.INSTANCE.toVOList(null);

        // Assert
        assertNull(voList);
    }

    /**
     * 测试实体列表转VO列表 - 包含null元素
     */
    @Test
    void testToVOList_WithNullElements() {
        // Arrange
        SysOrganization entity = new SysOrganization();
        entity.setId(1L);
        entity.setOrganizationName("有效组织");
        entity.setOrganizationCode("VALID_ORG");

        List<SysOrganization> entityList = Arrays.asList(entity, null);

        // Act
        List<SysOrganizationVO> voList = SysOrganizationDTOConvert.INSTANCE.toVOList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        assertNotNull(voList.get(0));
        assertEquals(1L, voList.get(0).getId());
        assertEquals("有效组织", voList.get(0).getOrganizationName());
        assertNull(voList.get(1));
    }

    /**
     * 测试实体列表转VO列表 - 空字段
     */
    @Test
    void testToVOList_WithEmptyFields() {
        // Arrange
        SysOrganization entity = new SysOrganization();
        entity.setId(null);
        entity.setOrganizationName("");
        entity.setOrganizationCode("");
        entity.setParentId(null);
        entity.setLevel(null);
        entity.setSort(null);
        entity.setStatus("");
        entity.setDescription("");

        List<SysOrganization> entityList = Collections.singletonList(entity);

        // Act
        List<SysOrganizationVO> voList = SysOrganizationDTOConvert.INSTANCE.toVOList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(1, voList.size());
        
        SysOrganizationVO vo = voList.get(0);
        assertNull(vo.getId());
        assertEquals("", vo.getOrganizationName());
        assertEquals("", vo.getOrganizationCode());
        assertNull(vo.getParentId());
        assertNull(vo.getLevel());
        assertNull(vo.getSort());
        assertEquals("", vo.getStatus());
        assertEquals("", vo.getDescription());
    }

    /**
     * 测试实体列表转VO列表 - null字段
     */
    @Test
    void testToVOList_WithNullFields() {
        // Arrange
        SysOrganization entity = new SysOrganization();
        entity.setId(1L);
        entity.setOrganizationName(null);
        entity.setOrganizationCode(null);
        entity.setParentId(null);
        entity.setLevel(null);
        entity.setSort(null);
        entity.setStatus(null);
        entity.setDescription(null);

        List<SysOrganization> entityList = Collections.singletonList(entity);

        // Act
        List<SysOrganizationVO> voList = SysOrganizationDTOConvert.INSTANCE.toVOList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(1, voList.size());
        
        SysOrganizationVO vo = voList.get(0);
        assertEquals(1L, vo.getId());
        assertNull(vo.getOrganizationName());
        assertNull(vo.getOrganizationCode());
        assertNull(vo.getParentId());
        assertNull(vo.getLevel());
        assertNull(vo.getSort());
        assertNull(vo.getStatus());
        assertNull(vo.getDescription());
    }

    /**
     * 测试转换器实例不为null
     */
    @Test
    void testInstanceNotNull() {
        assertNotNull(SysOrganizationDTOConvert.INSTANCE);
    }

    /**
     * 测试特殊字符处理
     */
    @Test
    void testToVOList_WithSpecialCharacters() {
        // Arrange
        SysOrganization entity = new SysOrganization();
        entity.setId(999L);
        entity.setOrganizationName("特殊组织@#$%");
        entity.setOrganizationCode("ORG@#$");
        entity.setParentId(-1L);
        entity.setLevel(-1);
        entity.setSort(-1);
        entity.setStatus("SPECIAL_STATUS");
        entity.setDescription("描述包含特殊字符：<>&\"'");

        List<SysOrganization> entityList = Collections.singletonList(entity);

        // Act
        List<SysOrganizationVO> voList = SysOrganizationDTOConvert.INSTANCE.toVOList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(1, voList.size());
        
        SysOrganizationVO vo = voList.get(0);
        assertEquals(999L, vo.getId());
        assertEquals("特殊组织@#$%", vo.getOrganizationName());
        assertEquals("ORG@#$", vo.getOrganizationCode());
        assertEquals(-1L, vo.getParentId());
        assertEquals(-1, vo.getLevel());
        assertEquals(-1, vo.getSort());
        assertEquals("SPECIAL_STATUS", vo.getStatus());
        assertEquals("描述包含特殊字符：<>&\"'", vo.getDescription());
    }

    /**
     * 测试长字符串处理
     */
    @Test
    void testToVOList_WithLongStrings() {
        // Arrange
        String longString = "A".repeat(1000);
        SysOrganization entity = new SysOrganization();
        entity.setId(Long.MAX_VALUE);
        entity.setOrganizationName(longString);
        entity.setOrganizationCode(longString);
        entity.setParentId(Long.MAX_VALUE);
        entity.setLevel(Integer.MAX_VALUE);
        entity.setSort(Integer.MAX_VALUE);
        entity.setStatus(longString);
        entity.setDescription(longString);

        List<SysOrganization> entityList = Collections.singletonList(entity);

        // Act
        List<SysOrganizationVO> voList = SysOrganizationDTOConvert.INSTANCE.toVOList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(1, voList.size());
        
        SysOrganizationVO vo = voList.get(0);
        assertEquals(Long.MAX_VALUE, vo.getId());
        assertEquals(longString, vo.getOrganizationName());
        assertEquals(longString, vo.getOrganizationCode());
        assertEquals(Long.MAX_VALUE, vo.getParentId());
        assertEquals(Integer.MAX_VALUE, vo.getLevel());
        assertEquals(Integer.MAX_VALUE, vo.getSort());
        assertEquals(longString, vo.getStatus());
        assertEquals(longString, vo.getDescription());
    }

    /**
     * 测试边界值
     */
    @Test
    void testToVOList_WithBoundaryValues() {
        // Arrange
        SysOrganization entity = new SysOrganization();
        entity.setId(0L);
        entity.setOrganizationName("0");
        entity.setOrganizationCode("0");
        entity.setParentId(0L);
        entity.setLevel(0);
        entity.setSort(0);
        entity.setStatus("0");
        entity.setDescription("0");

        List<SysOrganization> entityList = Collections.singletonList(entity);

        // Act
        List<SysOrganizationVO> voList = SysOrganizationDTOConvert.INSTANCE.toVOList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(1, voList.size());
        
        SysOrganizationVO vo = voList.get(0);
        assertEquals(0L, vo.getId());
        assertEquals("0", vo.getOrganizationName());
        assertEquals("0", vo.getOrganizationCode());
        assertEquals(0L, vo.getParentId());
        assertEquals(0, vo.getLevel());
        assertEquals(0, vo.getSort());
        assertEquals("0", vo.getStatus());
        assertEquals("0", vo.getDescription());
    }

    /**
     * 测试层级结构组织
     */
    @Test
    void testToVOList_WithHierarchicalStructure() {
        // Arrange
        SysOrganization rootOrg = new SysOrganization();
        rootOrg.setId(1L);
        rootOrg.setOrganizationName("根组织");
        rootOrg.setOrganizationCode("ROOT");
        rootOrg.setParentId(0L);
        rootOrg.setLevel(1);
        rootOrg.setSort(1);

        SysOrganization childOrg = new SysOrganization();
        childOrg.setId(2L);
        childOrg.setOrganizationName("子组织");
        childOrg.setOrganizationCode("CHILD");
        childOrg.setParentId(1L);
        childOrg.setLevel(2);
        childOrg.setSort(1);

        SysOrganization grandChildOrg = new SysOrganization();
        grandChildOrg.setId(3L);
        grandChildOrg.setOrganizationName("孙组织");
        grandChildOrg.setOrganizationCode("GRANDCHILD");
        grandChildOrg.setParentId(2L);
        grandChildOrg.setLevel(3);
        grandChildOrg.setSort(1);

        List<SysOrganization> entityList = Arrays.asList(rootOrg, childOrg, grandChildOrg);

        // Act
        List<SysOrganizationVO> voList = SysOrganizationDTOConvert.INSTANCE.toVOList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(3, voList.size());
        
        // 验证层级关系
        SysOrganizationVO rootVO = voList.get(0);
        assertEquals(1L, rootVO.getId());
        assertEquals(0L, rootVO.getParentId());
        assertEquals(1, rootVO.getLevel());

        SysOrganizationVO childVO = voList.get(1);
        assertEquals(2L, childVO.getId());
        assertEquals(1L, childVO.getParentId());
        assertEquals(2, childVO.getLevel());

        SysOrganizationVO grandChildVO = voList.get(2);
        assertEquals(3L, grandChildVO.getId());
        assertEquals(2L, grandChildVO.getParentId());
        assertEquals(3, grandChildVO.getLevel());
    }
}
