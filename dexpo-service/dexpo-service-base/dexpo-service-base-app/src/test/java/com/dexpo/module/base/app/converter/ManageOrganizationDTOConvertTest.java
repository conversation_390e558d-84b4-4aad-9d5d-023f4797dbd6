package com.dexpo.module.base.app.converter;

import com.dexpo.module.base.BaseUnitTest;
import com.dexpo.module.base.api.basic.dto.UserPageQueryDTO;
import com.dexpo.module.base.api.basic.vo.ManageOrganizationVO;
import com.dexpo.module.base.domain.model.ManageOrganization;
import com.dexpo.module.base.domain.model.SponsorPageInfo;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link ManageOrganizationDTOConvert} 的单元测试类
 * 
 * <p>测试管理组织转换器的所有转换方法，确保100%分支覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class ManageOrganizationDTOConvertTest extends BaseUnitTest {

    /**
     * 测试实体列表转VO列表 - 正常情况
     */
    @Test
    void testToVOList_Success() {
        // Arrange
        ManageOrganization entity1 = new ManageOrganization();
        entity1.setId(1L);
        entity1.setOrganizationName("组织1");
        entity1.setOrganizationCode("ORG001");
        entity1.setContactPerson("联系人1");
        entity1.setContactPhone("13800138001");
        entity1.setContactEmail("<EMAIL>");

        ManageOrganization entity2 = new ManageOrganization();
        entity2.setId(2L);
        entity2.setOrganizationName("组织2");
        entity2.setOrganizationCode("ORG002");
        entity2.setContactPerson("联系人2");
        entity2.setContactPhone("13800138002");
        entity2.setContactEmail("<EMAIL>");

        List<ManageOrganization> entityList = Arrays.asList(entity1, entity2);

        // Act
        List<ManageOrganizationVO> voList = ManageOrganizationDTOConvert.INSTANCE.toVOList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        
        ManageOrganizationVO vo1 = voList.get(0);
        assertEquals(1L, vo1.getId());
        assertEquals("组织1", vo1.getOrganizationName());
        assertEquals("ORG001", vo1.getOrganizationCode());
        assertEquals("联系人1", vo1.getContactPerson());
        assertEquals("13800138001", vo1.getContactPhone());
        assertEquals("<EMAIL>", vo1.getContactEmail());

        ManageOrganizationVO vo2 = voList.get(1);
        assertEquals(2L, vo2.getId());
        assertEquals("组织2", vo2.getOrganizationName());
        assertEquals("ORG002", vo2.getOrganizationCode());
        assertEquals("联系人2", vo2.getContactPerson());
        assertEquals("13800138002", vo2.getContactPhone());
        assertEquals("<EMAIL>", vo2.getContactEmail());
    }

    /**
     * 测试实体列表转VO列表 - 空列表
     */
    @Test
    void testToVOList_WithEmptyList() {
        // Act
        List<ManageOrganizationVO> voList = ManageOrganizationDTOConvert.INSTANCE.toVOList(Collections.emptyList());

        // Assert
        assertNotNull(voList);
        assertTrue(voList.isEmpty());
    }

    /**
     * 测试实体列表转VO列表 - null输入
     */
    @Test
    void testToVOList_WithNull() {
        // Act
        List<ManageOrganizationVO> voList = ManageOrganizationDTOConvert.INSTANCE.toVOList(null);

        // Assert
        assertNull(voList);
    }

    /**
     * 测试实体列表转VO列表 - 包含null元素
     */
    @Test
    void testToVOList_WithNullElements() {
        // Arrange
        ManageOrganization entity = new ManageOrganization();
        entity.setId(1L);
        entity.setOrganizationName("有效组织");
        entity.setOrganizationCode("VALID_ORG");

        List<ManageOrganization> entityList = Arrays.asList(entity, null);

        // Act
        List<ManageOrganizationVO> voList = ManageOrganizationDTOConvert.INSTANCE.toVOList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        assertNotNull(voList.get(0));
        assertEquals(1L, voList.get(0).getId());
        assertEquals("有效组织", voList.get(0).getOrganizationName());
        assertNull(voList.get(1));
    }

    /**
     * 测试实体列表转VO列表 - 空字段
     */
    @Test
    void testToVOList_WithEmptyFields() {
        // Arrange
        ManageOrganization entity = new ManageOrganization();
        entity.setId(null);
        entity.setOrganizationName("");
        entity.setOrganizationCode("");
        entity.setContactPerson("");
        entity.setContactPhone("");
        entity.setContactEmail("");

        List<ManageOrganization> entityList = Collections.singletonList(entity);

        // Act
        List<ManageOrganizationVO> voList = ManageOrganizationDTOConvert.INSTANCE.toVOList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(1, voList.size());
        
        ManageOrganizationVO vo = voList.get(0);
        assertNull(vo.getId());
        assertEquals("", vo.getOrganizationName());
        assertEquals("", vo.getOrganizationCode());
        assertEquals("", vo.getContactPerson());
        assertEquals("", vo.getContactPhone());
        assertEquals("", vo.getContactEmail());
    }

    /**
     * 测试UserPageQueryDTO转SponsorPageInfo - 正常情况
     */
    @Test
    void testToSponsor_Success() {
        // Arrange
        UserPageQueryDTO dto = new UserPageQueryDTO();
        dto.setName("赞助商名称");
        dto.setMobile("13800138000");
        dto.setEmail("<EMAIL>");
        dto.setManageOrganizationCode("SPONSOR_ORG");
        dto.setSysOrganization("系统组织");

        // Act
        SponsorPageInfo sponsorPageInfo = ManageOrganizationDTOConvert.INSTANCE.toSponsor(dto);

        // Assert
        assertNotNull(sponsorPageInfo);
        assertEquals("赞助商名称", sponsorPageInfo.getSponsorName());
        assertEquals("13800138000", sponsorPageInfo.getSponsorMobile());
        assertEquals("<EMAIL>", sponsorPageInfo.getSponsorEmail());
        assertEquals("SPONSOR_ORG", sponsorPageInfo.getManageOrganizationCode());
        assertEquals("系统组织", sponsorPageInfo.getSysOrganization());
    }

    /**
     * 测试UserPageQueryDTO转SponsorPageInfo - null输入
     */
    @Test
    void testToSponsor_WithNull() {
        // Act
        SponsorPageInfo sponsorPageInfo = ManageOrganizationDTOConvert.INSTANCE.toSponsor(null);

        // Assert
        assertNull(sponsorPageInfo);
    }

    /**
     * 测试UserPageQueryDTO转SponsorPageInfo - 空字段
     */
    @Test
    void testToSponsor_WithEmptyFields() {
        // Arrange
        UserPageQueryDTO dto = new UserPageQueryDTO();
        dto.setName("");
        dto.setMobile("");
        dto.setEmail("");
        dto.setManageOrganizationCode("");
        dto.setSysOrganization("");

        // Act
        SponsorPageInfo sponsorPageInfo = ManageOrganizationDTOConvert.INSTANCE.toSponsor(dto);

        // Assert
        assertNotNull(sponsorPageInfo);
        assertEquals("", sponsorPageInfo.getSponsorName());
        assertEquals("", sponsorPageInfo.getSponsorMobile());
        assertEquals("", sponsorPageInfo.getSponsorEmail());
        assertEquals("", sponsorPageInfo.getManageOrganizationCode());
        assertEquals("", sponsorPageInfo.getSysOrganization());
    }

    /**
     * 测试UserPageQueryDTO转SponsorPageInfo - null字段
     */
    @Test
    void testToSponsor_WithNullFields() {
        // Arrange
        UserPageQueryDTO dto = new UserPageQueryDTO();
        dto.setName(null);
        dto.setMobile(null);
        dto.setEmail(null);
        dto.setManageOrganizationCode(null);
        dto.setSysOrganization(null);

        // Act
        SponsorPageInfo sponsorPageInfo = ManageOrganizationDTOConvert.INSTANCE.toSponsor(dto);

        // Assert
        assertNotNull(sponsorPageInfo);
        assertNull(sponsorPageInfo.getSponsorName());
        assertNull(sponsorPageInfo.getSponsorMobile());
        assertNull(sponsorPageInfo.getSponsorEmail());
        assertNull(sponsorPageInfo.getManageOrganizationCode());
        assertNull(sponsorPageInfo.getSysOrganization());
    }

    /**
     * 测试转换器实例不为null
     */
    @Test
    void testInstanceNotNull() {
        assertNotNull(ManageOrganizationDTOConvert.INSTANCE);
    }

    /**
     * 测试特殊字符处理
     */
    @Test
    void testToVOList_WithSpecialCharacters() {
        // Arrange
        ManageOrganization entity = new ManageOrganization();
        entity.setId(999L);
        entity.setOrganizationName("特殊组织@#$%");
        entity.setOrganizationCode("ORG@#$");
        entity.setContactPerson("联系人<script>");
        entity.setContactPhone("+86-138-0013-8000");
        entity.setContactEmail("<EMAIL>");

        List<ManageOrganization> entityList = Collections.singletonList(entity);

        // Act
        List<ManageOrganizationVO> voList = ManageOrganizationDTOConvert.INSTANCE.toVOList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(1, voList.size());
        
        ManageOrganizationVO vo = voList.get(0);
        assertEquals(999L, vo.getId());
        assertEquals("特殊组织@#$%", vo.getOrganizationName());
        assertEquals("ORG@#$", vo.getOrganizationCode());
        assertEquals("联系人<script>", vo.getContactPerson());
        assertEquals("+86-138-0013-8000", vo.getContactPhone());
        assertEquals("<EMAIL>", vo.getContactEmail());
    }

    /**
     * 测试长字符串处理
     */
    @Test
    void testToVOList_WithLongStrings() {
        // Arrange
        String longString = "A".repeat(500);
        ManageOrganization entity = new ManageOrganization();
        entity.setId(Long.MAX_VALUE);
        entity.setOrganizationName(longString);
        entity.setOrganizationCode(longString);
        entity.setContactPerson(longString);
        entity.setContactPhone(longString);
        entity.setContactEmail(longString);

        List<ManageOrganization> entityList = Collections.singletonList(entity);

        // Act
        List<ManageOrganizationVO> voList = ManageOrganizationDTOConvert.INSTANCE.toVOList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(1, voList.size());
        
        ManageOrganizationVO vo = voList.get(0);
        assertEquals(Long.MAX_VALUE, vo.getId());
        assertEquals(longString, vo.getOrganizationName());
        assertEquals(longString, vo.getOrganizationCode());
        assertEquals(longString, vo.getContactPerson());
        assertEquals(longString, vo.getContactPhone());
        assertEquals(longString, vo.getContactEmail());
    }

    /**
     * 测试边界值
     */
    @Test
    void testToVOList_WithBoundaryValues() {
        // Arrange
        ManageOrganization entity = new ManageOrganization();
        entity.setId(0L);
        entity.setOrganizationName("0");
        entity.setOrganizationCode("0");
        entity.setContactPerson("0");
        entity.setContactPhone("0");
        entity.setContactEmail("0");

        List<ManageOrganization> entityList = Collections.singletonList(entity);

        // Act
        List<ManageOrganizationVO> voList = ManageOrganizationDTOConvert.INSTANCE.toVOList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(1, voList.size());
        
        ManageOrganizationVO vo = voList.get(0);
        assertEquals(0L, vo.getId());
        assertEquals("0", vo.getOrganizationName());
        assertEquals("0", vo.getOrganizationCode());
        assertEquals("0", vo.getContactPerson());
        assertEquals("0", vo.getContactPhone());
        assertEquals("0", vo.getContactEmail());
    }

    /**
     * 测试映射注解的正确性
     */
    @Test
    void testToSponsor_MappingAnnotations() {
        // Arrange
        UserPageQueryDTO dto = new UserPageQueryDTO();
        dto.setName("测试名称");
        dto.setMobile("测试手机");
        dto.setEmail("测试邮箱");
        dto.setManageOrganizationCode("测试组织代码");
        dto.setSysOrganization("测试系统组织");

        // Act
        SponsorPageInfo sponsorPageInfo = ManageOrganizationDTOConvert.INSTANCE.toSponsor(dto);

        // Assert
        assertNotNull(sponsorPageInfo);
        // 验证@Mapping注解的映射是否正确
        assertEquals("测试名称", sponsorPageInfo.getSponsorName()); // name -> sponsorName
        assertEquals("测试手机", sponsorPageInfo.getSponsorMobile()); // mobile -> sponsorMobile
        assertEquals("测试邮箱", sponsorPageInfo.getSponsorEmail()); // email -> sponsorEmail
        assertEquals("测试组织代码", sponsorPageInfo.getManageOrganizationCode()); // manageOrganizationCode -> manageOrganizationCode
        assertEquals("测试系统组织", sponsorPageInfo.getSysOrganization()); // sysOrganization -> sysOrganization
    }
}
