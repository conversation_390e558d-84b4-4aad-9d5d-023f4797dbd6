package com.dexpo.module.base.app.converter;

import com.dexpo.module.base.BaseUnitTest;
import com.dexpo.module.base.api.role.vo.SysPermissionRoleRelationVO;
import com.dexpo.module.base.domain.model.valueobject.SysPermissionRoleRelation;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link SysPermissionRoleRelationDTOConvert} 的单元测试类
 * 
 * <p>测试系统权限角色关系转换器的所有转换方法，确保100%分支覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class SysPermissionRoleRelationDTOConvertTest extends BaseUnitTest {

    /**
     * 测试实体转VO - 正常情况
     */
    @Test
    void testE2v_Success() {
        // Arrange
        SysPermissionRoleRelation entity = new SysPermissionRoleRelation();
        entity.setId(1L);
        entity.setPermissionId(100L);
        entity.setRoleId(200L);
        entity.setPermissionName("用户管理");
        entity.setPermissionCode("USER_MANAGE");
        entity.setRoleName("管理员");
        entity.setRoleCode("ADMIN");
        entity.setStatus("ACTIVE");
        entity.setCreateTime(LocalDateTime.of(2024, 1, 1, 0, 0, 0));
        entity.setUpdateTime(LocalDateTime.of(2024, 6, 15, 12, 0, 0));

        // Act
        SysPermissionRoleRelationVO vo = SysPermissionRoleRelationDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(1L, vo.getId());
        assertEquals(100L, vo.getPermissionId());
        assertEquals(200L, vo.getRoleId());
        assertEquals("用户管理", vo.getPermissionName());
        assertEquals("USER_MANAGE", vo.getPermissionCode());
        assertEquals("管理员", vo.getRoleName());
        assertEquals("ADMIN", vo.getRoleCode());
        assertEquals("ACTIVE", vo.getStatus());
        assertEquals(LocalDateTime.of(2024, 1, 1, 0, 0, 0), vo.getCreateTime());
        assertEquals(LocalDateTime.of(2024, 6, 15, 12, 0, 0), vo.getUpdateTime());
    }

    /**
     * 测试实体转VO - null输入
     */
    @Test
    void testE2v_WithNull() {
        // Act
        SysPermissionRoleRelationVO vo = SysPermissionRoleRelationDTOConvert.INSTANCE.e2v((SysPermissionRoleRelation) null);

        // Assert
        assertNull(vo);
    }

    /**
     * 测试实体转VO - 空字段
     */
    @Test
    void testE2v_WithEmptyFields() {
        // Arrange
        SysPermissionRoleRelation entity = new SysPermissionRoleRelation();
        entity.setId(null);
        entity.setPermissionId(null);
        entity.setRoleId(null);
        entity.setPermissionName("");
        entity.setPermissionCode("");
        entity.setRoleName("");
        entity.setRoleCode("");
        entity.setStatus("");
        entity.setCreateTime(null);
        entity.setUpdateTime(null);

        // Act
        SysPermissionRoleRelationVO vo = SysPermissionRoleRelationDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertNull(vo.getId());
        assertNull(vo.getPermissionId());
        assertNull(vo.getRoleId());
        assertEquals("", vo.getPermissionName());
        assertEquals("", vo.getPermissionCode());
        assertEquals("", vo.getRoleName());
        assertEquals("", vo.getRoleCode());
        assertEquals("", vo.getStatus());
        assertNull(vo.getCreateTime());
        assertNull(vo.getUpdateTime());
    }

    /**
     * 测试实体列表转VO列表 - 正常情况
     */
    @Test
    void testE2vList_Success() {
        // Arrange
        SysPermissionRoleRelation entity1 = new SysPermissionRoleRelation();
        entity1.setId(1L);
        entity1.setPermissionId(100L);
        entity1.setRoleId(200L);
        entity1.setPermissionName("权限1");
        entity1.setRoleName("角色1");

        SysPermissionRoleRelation entity2 = new SysPermissionRoleRelation();
        entity2.setId(2L);
        entity2.setPermissionId(101L);
        entity2.setRoleId(201L);
        entity2.setPermissionName("权限2");
        entity2.setRoleName("角色2");

        List<SysPermissionRoleRelation> entityList = Arrays.asList(entity1, entity2);

        // Act
        List<SysPermissionRoleRelationVO> voList = SysPermissionRoleRelationDTOConvert.INSTANCE.e2v(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        
        SysPermissionRoleRelationVO vo1 = voList.get(0);
        assertEquals(1L, vo1.getId());
        assertEquals(100L, vo1.getPermissionId());
        assertEquals(200L, vo1.getRoleId());
        assertEquals("权限1", vo1.getPermissionName());
        assertEquals("角色1", vo1.getRoleName());

        SysPermissionRoleRelationVO vo2 = voList.get(1);
        assertEquals(2L, vo2.getId());
        assertEquals(101L, vo2.getPermissionId());
        assertEquals(201L, vo2.getRoleId());
        assertEquals("权限2", vo2.getPermissionName());
        assertEquals("角色2", vo2.getRoleName());
    }

    /**
     * 测试实体列表转VO列表 - 空列表
     */
    @Test
    void testE2vList_WithEmptyList() {
        // Act
        List<SysPermissionRoleRelationVO> voList = SysPermissionRoleRelationDTOConvert.INSTANCE.e2v(Collections.emptyList());

        // Assert
        assertNotNull(voList);
        assertTrue(voList.isEmpty());
    }

    /**
     * 测试实体列表转VO列表 - null输入
     */
    @Test
    void testE2vList_WithNull() {
        // Act
        List<SysPermissionRoleRelationVO> voList = SysPermissionRoleRelationDTOConvert.INSTANCE.e2v((List<SysPermissionRoleRelation>) null);

        // Assert
        assertNull(voList);
    }

    /**
     * 测试e2vList方法 - 正常情况
     */
    @Test
    void testE2vListMethod_Success() {
        // Arrange
        SysPermissionRoleRelation entity1 = new SysPermissionRoleRelation();
        entity1.setId(1L);
        entity1.setPermissionId(100L);
        entity1.setRoleId(200L);
        entity1.setPermissionName("权限A");
        entity1.setRoleName("角色A");

        SysPermissionRoleRelation entity2 = new SysPermissionRoleRelation();
        entity2.setId(2L);
        entity2.setPermissionId(101L);
        entity2.setRoleId(201L);
        entity2.setPermissionName("权限B");
        entity2.setRoleName("角色B");

        List<SysPermissionRoleRelation> entityList = Arrays.asList(entity1, entity2);

        // Act
        List<SysPermissionRoleRelationVO> voList = SysPermissionRoleRelationDTOConvert.INSTANCE.e2vList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        
        SysPermissionRoleRelationVO vo1 = voList.get(0);
        assertEquals(1L, vo1.getId());
        assertEquals(100L, vo1.getPermissionId());
        assertEquals(200L, vo1.getRoleId());
        assertEquals("权限A", vo1.getPermissionName());
        assertEquals("角色A", vo1.getRoleName());

        SysPermissionRoleRelationVO vo2 = voList.get(1);
        assertEquals(2L, vo2.getId());
        assertEquals(101L, vo2.getPermissionId());
        assertEquals(201L, vo2.getRoleId());
        assertEquals("权限B", vo2.getPermissionName());
        assertEquals("角色B", vo2.getRoleName());
    }

    /**
     * 测试e2vList方法 - null输入
     */
    @Test
    void testE2vListMethod_WithNull() {
        // Act
        List<SysPermissionRoleRelationVO> voList = SysPermissionRoleRelationDTOConvert.INSTANCE.e2vList(null);

        // Assert
        assertNull(voList);
    }

    /**
     * 测试e2vList方法 - 空列表
     */
    @Test
    void testE2vListMethod_WithEmptyList() {
        // Act
        List<SysPermissionRoleRelationVO> voList = SysPermissionRoleRelationDTOConvert.INSTANCE.e2vList(Collections.emptyList());

        // Assert
        assertNotNull(voList);
        assertTrue(voList.isEmpty());
    }

    /**
     * 测试转换器实例不为null
     */
    @Test
    void testInstanceNotNull() {
        assertNotNull(SysPermissionRoleRelationDTOConvert.INSTANCE);
    }

    /**
     * 测试包含null元素的列表
     */
    @Test
    void testE2vList_WithNullElements() {
        // Arrange
        SysPermissionRoleRelation entity = new SysPermissionRoleRelation();
        entity.setId(1L);
        entity.setPermissionName("有效权限");
        entity.setRoleName("有效角色");

        List<SysPermissionRoleRelation> entityList = Arrays.asList(entity, null);

        // Act
        List<SysPermissionRoleRelationVO> voList = SysPermissionRoleRelationDTOConvert.INSTANCE.e2v(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        assertNotNull(voList.get(0));
        assertEquals(1L, voList.get(0).getId());
        assertEquals("有效权限", voList.get(0).getPermissionName());
        assertNull(voList.get(1));
    }

    /**
     * 测试特殊字符处理
     */
    @Test
    void testE2v_WithSpecialCharacters() {
        // Arrange
        SysPermissionRoleRelation entity = new SysPermissionRoleRelation();
        entity.setId(999L);
        entity.setPermissionId(999L);
        entity.setRoleId(999L);
        entity.setPermissionName("特殊权限@#$%");
        entity.setPermissionCode("SPECIAL_PERM@#$");
        entity.setRoleName("特殊角色<script>");
        entity.setRoleCode("SPECIAL_ROLE&");
        entity.setStatus("SPECIAL_STATUS");

        // Act
        SysPermissionRoleRelationVO vo = SysPermissionRoleRelationDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(999L, vo.getId());
        assertEquals(999L, vo.getPermissionId());
        assertEquals(999L, vo.getRoleId());
        assertEquals("特殊权限@#$%", vo.getPermissionName());
        assertEquals("SPECIAL_PERM@#$", vo.getPermissionCode());
        assertEquals("特殊角色<script>", vo.getRoleName());
        assertEquals("SPECIAL_ROLE&", vo.getRoleCode());
        assertEquals("SPECIAL_STATUS", vo.getStatus());
    }

    /**
     * 测试边界值
     */
    @Test
    void testE2v_WithBoundaryValues() {
        // Arrange
        SysPermissionRoleRelation entity = new SysPermissionRoleRelation();
        entity.setId(0L);
        entity.setPermissionId(0L);
        entity.setRoleId(0L);
        entity.setPermissionName("0");
        entity.setPermissionCode("0");
        entity.setRoleName("0");
        entity.setRoleCode("0");
        entity.setStatus("0");
        entity.setCreateTime(LocalDateTime.MIN);
        entity.setUpdateTime(LocalDateTime.MIN);

        // Act
        SysPermissionRoleRelationVO vo = SysPermissionRoleRelationDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(0L, vo.getId());
        assertEquals(0L, vo.getPermissionId());
        assertEquals(0L, vo.getRoleId());
        assertEquals("0", vo.getPermissionName());
        assertEquals("0", vo.getPermissionCode());
        assertEquals("0", vo.getRoleName());
        assertEquals("0", vo.getRoleCode());
        assertEquals("0", vo.getStatus());
        assertEquals(LocalDateTime.MIN, vo.getCreateTime());
        assertEquals(LocalDateTime.MIN, vo.getUpdateTime());
    }

    /**
     * 测试最大值边界
     */
    @Test
    void testE2v_WithMaxValues() {
        // Arrange
        SysPermissionRoleRelation entity = new SysPermissionRoleRelation();
        entity.setId(Long.MAX_VALUE);
        entity.setPermissionId(Long.MAX_VALUE);
        entity.setRoleId(Long.MAX_VALUE);
        entity.setCreateTime(LocalDateTime.MAX);
        entity.setUpdateTime(LocalDateTime.MAX);

        // Act
        SysPermissionRoleRelationVO vo = SysPermissionRoleRelationDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(Long.MAX_VALUE, vo.getId());
        assertEquals(Long.MAX_VALUE, vo.getPermissionId());
        assertEquals(Long.MAX_VALUE, vo.getRoleId());
        assertEquals(LocalDateTime.MAX, vo.getCreateTime());
        assertEquals(LocalDateTime.MAX, vo.getUpdateTime());
    }

    /**
     * 测试长字符串处理
     */
    @Test
    void testE2v_WithLongStrings() {
        // Arrange
        String longString = "A".repeat(500);
        SysPermissionRoleRelation entity = new SysPermissionRoleRelation();
        entity.setId(1L);
        entity.setPermissionName(longString);
        entity.setPermissionCode(longString);
        entity.setRoleName(longString);
        entity.setRoleCode(longString);
        entity.setStatus(longString);

        // Act
        SysPermissionRoleRelationVO vo = SysPermissionRoleRelationDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(1L, vo.getId());
        assertEquals(longString, vo.getPermissionName());
        assertEquals(longString, vo.getPermissionCode());
        assertEquals(longString, vo.getRoleName());
        assertEquals(longString, vo.getRoleCode());
        assertEquals(longString, vo.getStatus());
    }

    /**
     * 测试权限角色关系的业务场景
     */
    @Test
    void testE2v_BusinessScenario() {
        // Arrange - 模拟管理员角色拥有用户管理权限的场景
        SysPermissionRoleRelation entity = new SysPermissionRoleRelation();
        entity.setId(1L);
        entity.setPermissionId(1001L);
        entity.setRoleId(2001L);
        entity.setPermissionName("用户管理");
        entity.setPermissionCode("USER_MANAGE");
        entity.setRoleName("系统管理员");
        entity.setRoleCode("SYSTEM_ADMIN");
        entity.setStatus("ACTIVE");
        entity.setCreateTime(LocalDateTime.of(2024, 1, 1, 9, 0, 0));
        entity.setUpdateTime(LocalDateTime.of(2024, 1, 1, 9, 0, 0));

        // Act
        SysPermissionRoleRelationVO vo = SysPermissionRoleRelationDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(1L, vo.getId());
        assertEquals(1001L, vo.getPermissionId());
        assertEquals(2001L, vo.getRoleId());
        assertEquals("用户管理", vo.getPermissionName());
        assertEquals("USER_MANAGE", vo.getPermissionCode());
        assertEquals("系统管理员", vo.getRoleName());
        assertEquals("SYSTEM_ADMIN", vo.getRoleCode());
        assertEquals("ACTIVE", vo.getStatus());
        assertEquals(LocalDateTime.of(2024, 1, 1, 9, 0, 0), vo.getCreateTime());
        assertEquals(LocalDateTime.of(2024, 1, 1, 9, 0, 0), vo.getUpdateTime());
    }
}
