package com.dexpo.module.base.app.converter;

import com.dexpo.module.base.app.entity.ExhibitionValuesetOptionRelationCache;
import com.dexpo.module.base.domain.model.agg.ExhibitionValuesetOptionRelation;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

class ExhibitionValueSetOptionDTOConvertTest {

    @Test
    void testToCache() {
        // Arrange
        ExhibitionValuesetOptionRelation relation = new ExhibitionValuesetOptionRelation();
        relation.setId(1L);
        relation.setExhibitionTagCode("tag_code");
        relation.setValuesetCode("vs_code");
        relation.setValuesetOptionCode("opt_code");

        // Act
        ExhibitionValuesetOptionRelationCache cache = ExhibitionValueSetOptionDTOConvert.INSTANCE.toCache(relation);

        // Assert
        assertNotNull(cache);
        assertEquals(1L, cache.getId());
        assertEquals("tag_code", cache.getExhibitionTagCode());
        assertEquals("vs_code", cache.getValuesetCode());
        assertEquals("opt_code", cache.getValuesetOptionCode());
    }

    @Test
    void testToCacheList() {
        // Arrange
        ExhibitionValuesetOptionRelation relation = new ExhibitionValuesetOptionRelation();
        relation.setId(1L);
        relation.setExhibitionTagCode("tag_code");
        relation.setValuesetCode("vs_code");
        relation.setValuesetOptionCode("opt_code");
        List<ExhibitionValuesetOptionRelation> relationList = Collections.singletonList(relation);

        // Act
        List<ExhibitionValuesetOptionRelationCache> cacheList = ExhibitionValueSetOptionDTOConvert.INSTANCE.toCacheList(relationList);

        // Assert
        assertNotNull(cacheList);
        assertEquals(1, cacheList.size());
        ExhibitionValuesetOptionRelationCache cache = cacheList.get(0);
        assertEquals(1L, cache.getId());
        assertEquals("tag_code", cache.getExhibitionTagCode());
        assertEquals("vs_code", cache.getValuesetCode());
        assertEquals("opt_code", cache.getValuesetOptionCode());
    }
    
    @Test
    void testToCacheList_withNull() {
        // Act
        List<ExhibitionValuesetOptionRelationCache> cacheList = ExhibitionValueSetOptionDTOConvert.INSTANCE.toCacheList(null);

        // Assert
        assertNull(cacheList);
    }

    @Test
    void testToCache_withNull() {
        // Act
        ExhibitionValuesetOptionRelationCache cache = ExhibitionValueSetOptionDTOConvert.INSTANCE.toCache(null);

        // Assert
        assertNull(cache);
    }
} 