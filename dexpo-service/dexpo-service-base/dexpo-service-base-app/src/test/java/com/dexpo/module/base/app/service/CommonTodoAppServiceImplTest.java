package com.dexpo.module.base.app.service;

import com.dexpo.framework.cache.redis.entity.sponsor.SponsorProfileCache;
import com.dexpo.framework.cache.redis.operate.member.MemberBaseInfoOpt;
import com.dexpo.framework.common.enums.ValueSetCommonTodoBusinessTypeEnum;
import com.dexpo.framework.common.enums.ValueSetTodoStatusEnum;
import com.dexpo.framework.common.pojo.PageParam;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.framework.security.core.util.SecurityFrameworkUtils;
import com.dexpo.module.base.api.basic.dto.CommenTodoPageQueryDTO;
import com.dexpo.module.base.api.basic.vo.CommonTodoVO;
import com.dexpo.module.base.domain.model.agg.CommonTodo;
import com.dexpo.module.base.domain.service.CommonTodoDomainService;
import com.dexpo.module.base.infrastructure.integration.exhibition.ExhibitionExternalService;
import com.dexpo.module.exhibition.api.dto.ExhibitionQueryDTO;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CommonTodoAppServiceImplTest {
    @Mock
    private CommonTodoDomainService commonTodoDomainService;
    @Mock
    private ExhibitionExternalService exhibitionExternalService;
    @Mock
    private MemberBaseInfoOpt memberBaseInfoOpt;

    @InjectMocks
    private CommonTodoAppServiceImpl service;

    private MockedStatic<SecurityFrameworkUtils> mockedSecurityUtils;

    @BeforeEach
    void setUp() {
        mockedSecurityUtils = mockStatic(SecurityFrameworkUtils.class);
        when(SecurityFrameworkUtils.getLoginUserId()).thenReturn(1L);
    }

    @AfterEach
    void tearDown() {
        mockedSecurityUtils.close();
    }

    @Test
    void getPage_shouldReturnEmpty_whenNoExhibitionIds() {
        CommenTodoPageQueryDTO req = new CommenTodoPageQueryDTO();
        SponsorProfileCache profileCache = new SponsorProfileCache();
        profileCache.setExhibitionTagCode("tag");
        when(memberBaseInfoOpt.sponsorProfile(anyLong())).thenReturn(profileCache);
        when(exhibitionExternalService.getExhibitionIds(any(ExhibitionQueryDTO.class))).thenReturn(Collections.emptyList());
        PageResult<CommonTodoVO> result = service.getPage(req);
        assertNotNull(result);
        assertEquals(0, result.getTotal());
    }

    @Test
    void getPage_shouldSetDefaultStatus_whenReqStatusIsNull() {
        CommenTodoPageQueryDTO req = new CommenTodoPageQueryDTO();
        req.setStatus(null); // Ensure status is null
        SponsorProfileCache profileCache = new SponsorProfileCache();
        profileCache.setExhibitionTagCode("tag");
        when(memberBaseInfoOpt.sponsorProfile(anyLong())).thenReturn(profileCache);
        when(exhibitionExternalService.getExhibitionIds(any(ExhibitionQueryDTO.class))).thenReturn(List.of(1L));
        when(commonTodoDomainService.getPage(any(CommonTodo.class), any(PageParam.class)))
                .thenReturn(new PageResult<>(Collections.emptyList(), 0L));

        service.getPage(req);

        ArgumentCaptor<CommonTodo> captor = ArgumentCaptor.forClass(CommonTodo.class);
        verify(commonTodoDomainService).getPage(captor.capture(), any(PageParam.class));
        assertEquals(ValueSetTodoStatusEnum.TODO.getOptionCode(), captor.getValue().getStatus());
    }

    @Test
    void getPage_shouldReturnPageResult() {
        CommenTodoPageQueryDTO req = new CommenTodoPageQueryDTO();
        req.setPageNo(1);
        req.setPageSize(10);
        req.setStatus(ValueSetTodoStatusEnum.DONE.getOptionCode());
        SponsorProfileCache profileCache = new SponsorProfileCache();
        profileCache.setExhibitionTagCode("tag");
        when(memberBaseInfoOpt.sponsorProfile(anyLong())).thenReturn(profileCache);
        when(exhibitionExternalService.getExhibitionIds(any(ExhibitionQueryDTO.class))).thenReturn(List.of(1L));
        
        CommonTodo todo = new CommonTodo();
        todo.setId(1L);
        PageResult<CommonTodo> pageResult = new PageResult<>(List.of(todo), 1L);

        when(commonTodoDomainService.getPage(any(CommonTodo.class), any(PageParam.class))).thenReturn(pageResult);
        
        PageResult<CommonTodoVO> result = service.getPage(req);
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getList().size());
        assertEquals(1L, result.getList().get(0).getId());
    }

    @Test
    void createCommonTodo_shouldSaveTodo() {
        // Arrange
        CommonTodo commonTodo = new CommonTodo();
        commonTodo.setExhibitionId(1L);
        commonTodo.setBusinessNo("100");
        commonTodo.setBusinessType(ValueSetCommonTodoBusinessTypeEnum.MEDIA_USER_AUDIT.getOptionCode());
        commonTodo.setTodoTitle("Test Todo");

        ArgumentCaptor<CommonTodo> captor = ArgumentCaptor.forClass(CommonTodo.class);

        // Act
        service.createCommonTodo(commonTodo);

        // Assert
        verify(commonTodoDomainService).save(captor.capture());
        CommonTodo savedTodo = captor.getValue();
        assertNotNull(savedTodo);
        assertEquals(1L, savedTodo.getExhibitionId());
        assertEquals("100", savedTodo.getBusinessNo());
        assertEquals(ValueSetCommonTodoBusinessTypeEnum.MEDIA_USER_AUDIT.getOptionCode(), savedTodo.getBusinessType());
    }

    @Test
    void updateCommonTodo_shouldUpdateTodo() {
        // Arrange
        CommonTodo updateTodo = new CommonTodo();
        updateTodo.setBusinessNo("100");
        updateTodo.setExhibitionId(1L);
        updateTodo.setStatus(ValueSetTodoStatusEnum.DONE.getOptionCode());
        
        CommonTodo existingTodo = new CommonTodo();
        existingTodo.setId(1L);
        when(commonTodoDomainService.selectOneByCommonTodo(any(CommonTodo.class))).thenReturn(existingTodo);

        // Act
        service.updateCommonTodo(updateTodo);

        // Assert
        verify(commonTodoDomainService).updateById(any(CommonTodo.class));
    }
    
    @Test
    void updateCommonTodo_shouldNotUpdate_whenTodoNotFound() {
        // Arrange
        CommonTodo updateTodo = new CommonTodo();
        updateTodo.setBusinessNo("100");
        updateTodo.setExhibitionId(1L);
        
        when(commonTodoDomainService.selectOneByCommonTodo(any(CommonTodo.class))).thenReturn(null);

        // Act
        service.updateCommonTodo(updateTodo);

        // Assert
        verify(commonTodoDomainService, never()).updateById(any(CommonTodo.class));
    }

} 