package com.dexpo.module.base.app.converter;

import com.dexpo.module.base.BaseUnitTest;
import com.dexpo.module.base.api.basic.dto.BasicRegionDTO;
import com.dexpo.module.base.api.basic.vo.BasicRegionTreeVO;
import com.dexpo.module.base.api.basic.vo.BasicRegionVO;
import com.dexpo.module.base.domain.model.agg.BasicRegion;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link BasicRegionDTOConvert} 的单元测试类
 * 
 * <p>测试行政区域转换器的所有转换方法，确保100%分支覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class BasicRegionDTOConvertTest extends BaseUnitTest {

    /**
     * 测试实体转VO - 正常情况
     */
    @Test
    void testE2v_Success() {
        // Arrange
        BasicRegion entity = new BasicRegion();
        entity.setId(1L);
        entity.setAdcode("110000");
        entity.setName("北京市");
        entity.setLevel("province");
        entity.setParentAdcode("100000");

        // Act
        BasicRegionVO vo = BasicRegionDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(1L, vo.getId());
        assertEquals("110000", vo.getAdcode());
        assertEquals("北京市", vo.getName());
        assertEquals("province", vo.getLevel());
        assertEquals("100000", vo.getParentAdcode());
    }

    /**
     * 测试实体转VO - null输入
     */
    @Test
    void testE2v_WithNull() {
        // Act
        BasicRegionVO vo = BasicRegionDTOConvert.INSTANCE.e2v(null);

        // Assert
        assertNull(vo);
    }

    /**
     * 测试实体转VO - 空字段
     */
    @Test
    void testE2v_WithEmptyFields() {
        // Arrange
        BasicRegion entity = new BasicRegion();
        entity.setId(null);
        entity.setAdcode("");
        entity.setName("");
        entity.setLevel("");
        entity.setParentAdcode("");

        // Act
        BasicRegionVO vo = BasicRegionDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertNull(vo.getId());
        assertEquals("", vo.getAdcode());
        assertEquals("", vo.getName());
        assertEquals("", vo.getLevel());
        assertEquals("", vo.getParentAdcode());
    }

    /**
     * 测试DTO转实体 - 正常情况
     */
    @Test
    void testD2e_Success() {
        // Arrange
        BasicRegionDTO dto = new BasicRegionDTO();
        dto.setLevel("city");
        dto.setParentAdcode("110000");

        // Act
        BasicRegion entity = BasicRegionDTOConvert.INSTANCE.d2e(dto);

        // Assert
        assertNotNull(entity);
        assertEquals("city", entity.getLevel());
        assertEquals("110000", entity.getParentAdcode());
    }

    /**
     * 测试DTO转实体 - null输入
     */
    @Test
    void testD2e_WithNull() {
        // Act
        BasicRegion entity = BasicRegionDTOConvert.INSTANCE.d2e(null);

        // Assert
        assertNull(entity);
    }

    /**
     * 测试DTO转实体 - 空字段
     */
    @Test
    void testD2e_WithEmptyFields() {
        // Arrange
        BasicRegionDTO dto = new BasicRegionDTO();
        dto.setLevel("");
        dto.setParentAdcode("");

        // Act
        BasicRegion entity = BasicRegionDTOConvert.INSTANCE.d2e(dto);

        // Assert
        assertNotNull(entity);
        assertEquals("", entity.getLevel());
        assertEquals("", entity.getParentAdcode());
    }

    /**
     * 测试DTO转实体 - null字段
     */
    @Test
    void testD2e_WithNullFields() {
        // Arrange
        BasicRegionDTO dto = new BasicRegionDTO();
        dto.setLevel(null);
        dto.setParentAdcode(null);

        // Act
        BasicRegion entity = BasicRegionDTOConvert.INSTANCE.d2e(dto);

        // Assert
        assertNotNull(entity);
        assertNull(entity.getLevel());
        assertNull(entity.getParentAdcode());
    }

    /**
     * 测试实体列表转VO列表 - 正常情况
     */
    @Test
    void testE2vList_Success() {
        // Arrange
        BasicRegion entity1 = new BasicRegion();
        entity1.setId(1L);
        entity1.setAdcode("110000");
        entity1.setName("北京市");
        entity1.setLevel("province");

        BasicRegion entity2 = new BasicRegion();
        entity2.setId(2L);
        entity2.setAdcode("120000");
        entity2.setName("天津市");
        entity2.setLevel("province");

        List<BasicRegion> entityList = Arrays.asList(entity1, entity2);

        // Act
        List<BasicRegionVO> voList = BasicRegionDTOConvert.INSTANCE.e2vList(entityList);

        // Assert
        assertNotNull(voList);
        assertEquals(2, voList.size());
        
        BasicRegionVO vo1 = voList.get(0);
        assertEquals(1L, vo1.getId());
        assertEquals("110000", vo1.getAdcode());
        assertEquals("北京市", vo1.getName());
        assertEquals("province", vo1.getLevel());

        BasicRegionVO vo2 = voList.get(1);
        assertEquals(2L, vo2.getId());
        assertEquals("120000", vo2.getAdcode());
        assertEquals("天津市", vo2.getName());
        assertEquals("province", vo2.getLevel());
    }

    /**
     * 测试实体列表转VO列表 - 空列表
     */
    @Test
    void testE2vList_WithEmptyList() {
        // Act
        List<BasicRegionVO> voList = BasicRegionDTOConvert.INSTANCE.e2vList(Collections.emptyList());

        // Assert
        assertNotNull(voList);
        assertTrue(voList.isEmpty());
    }

    /**
     * 测试实体列表转VO列表 - null输入
     */
    @Test
    void testE2vList_WithNull() {
        // Act
        List<BasicRegionVO> voList = BasicRegionDTOConvert.INSTANCE.e2vList(null);

        // Assert
        assertNull(voList);
    }

    /**
     * 测试实体列表转TreeVO列表 - 正常情况
     */
    @Test
    void testVToTreeList_Success() {
        // Arrange
        BasicRegion entity1 = new BasicRegion();
        entity1.setId(1L);
        entity1.setAdcode("110000");
        entity1.setName("北京市");
        entity1.setLevel("province");
        entity1.setParentAdcode("100000");

        BasicRegion entity2 = new BasicRegion();
        entity2.setId(2L);
        entity2.setAdcode("110100");
        entity2.setName("北京市市辖区");
        entity2.setLevel("city");
        entity2.setParentAdcode("110000");

        List<BasicRegion> entityList = Arrays.asList(entity1, entity2);

        // Act
        List<BasicRegionTreeVO> treeVOList = BasicRegionDTOConvert.INSTANCE.vToTreeList(entityList);

        // Assert
        assertNotNull(treeVOList);
        assertEquals(2, treeVOList.size());
        
        BasicRegionTreeVO treeVO1 = treeVOList.get(0);
        assertEquals(1L, treeVO1.getId());
        assertEquals("110000", treeVO1.getAdcode());
        assertEquals("北京市", treeVO1.getName());
        assertEquals("province", treeVO1.getLevel());
        assertEquals("100000", treeVO1.getParentAdcode());

        BasicRegionTreeVO treeVO2 = treeVOList.get(1);
        assertEquals(2L, treeVO2.getId());
        assertEquals("110100", treeVO2.getAdcode());
        assertEquals("北京市市辖区", treeVO2.getName());
        assertEquals("city", treeVO2.getLevel());
        assertEquals("110000", treeVO2.getParentAdcode());
    }

    /**
     * 测试实体列表转TreeVO列表 - 空列表
     */
    @Test
    void testVToTreeList_WithEmptyList() {
        // Act
        List<BasicRegionTreeVO> treeVOList = BasicRegionDTOConvert.INSTANCE.vToTreeList(Collections.emptyList());

        // Assert
        assertNotNull(treeVOList);
        assertTrue(treeVOList.isEmpty());
    }

    /**
     * 测试实体列表转TreeVO列表 - null输入
     */
    @Test
    void testVToTreeList_WithNull() {
        // Act
        List<BasicRegionTreeVO> treeVOList = BasicRegionDTOConvert.INSTANCE.vToTreeList(null);

        // Assert
        assertNull(treeVOList);
    }

    /**
     * 测试实体列表转TreeVO列表 - 包含null元素
     */
    @Test
    void testVToTreeList_WithNullElements() {
        // Arrange
        BasicRegion entity = new BasicRegion();
        entity.setId(1L);
        entity.setAdcode("110000");
        entity.setName("北京市");

        List<BasicRegion> entityList = Arrays.asList(entity, null);

        // Act
        List<BasicRegionTreeVO> treeVOList = BasicRegionDTOConvert.INSTANCE.vToTreeList(entityList);

        // Assert
        assertNotNull(treeVOList);
        assertEquals(2, treeVOList.size());
        assertNotNull(treeVOList.get(0));
        assertEquals(1L, treeVOList.get(0).getId());
        assertNull(treeVOList.get(1));
    }

    /**
     * 测试DTO列表转实体列表 - 正常情况
     */
    @Test
    void testD2eList_Success() {
        // Arrange
        BasicRegionDTO dto1 = new BasicRegionDTO();
        dto1.setLevel("province");
        dto1.setParentAdcode("100000");

        BasicRegionDTO dto2 = new BasicRegionDTO();
        dto2.setLevel("city");
        dto2.setParentAdcode("110000");

        List<BasicRegionDTO> dtoList = Arrays.asList(dto1, dto2);

        // Act
        List<BasicRegion> entityList = BasicRegionDTOConvert.INSTANCE.d2eList(dtoList);

        // Assert
        assertNotNull(entityList);
        assertEquals(2, entityList.size());
        
        BasicRegion entity1 = entityList.get(0);
        assertEquals("province", entity1.getLevel());
        assertEquals("100000", entity1.getParentAdcode());

        BasicRegion entity2 = entityList.get(1);
        assertEquals("city", entity2.getLevel());
        assertEquals("110000", entity2.getParentAdcode());
    }

    /**
     * 测试DTO列表转实体列表 - 空列表
     */
    @Test
    void testD2eList_WithEmptyList() {
        // Act
        List<BasicRegion> entityList = BasicRegionDTOConvert.INSTANCE.d2eList(Collections.emptyList());

        // Assert
        assertNotNull(entityList);
        assertTrue(entityList.isEmpty());
    }

    /**
     * 测试DTO列表转实体列表 - null输入
     */
    @Test
    void testD2eList_WithNull() {
        // Act
        List<BasicRegion> entityList = BasicRegionDTOConvert.INSTANCE.d2eList(null);

        // Assert
        assertNull(entityList);
    }

    /**
     * 测试转换器实例不为null
     */
    @Test
    void testInstanceNotNull() {
        assertNotNull(BasicRegionDTOConvert.INSTANCE);
    }

    /**
     * 测试特殊行政区域代码
     */
    @Test
    void testE2v_WithSpecialAdcodes() {
        // Arrange
        BasicRegion entity = new BasicRegion();
        entity.setId(999L);
        entity.setAdcode("810000"); // 香港特别行政区
        entity.setName("香港特别行政区");
        entity.setLevel("province");
        entity.setParentAdcode("100000");

        // Act
        BasicRegionVO vo = BasicRegionDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(999L, vo.getId());
        assertEquals("810000", vo.getAdcode());
        assertEquals("香港特别行政区", vo.getName());
        assertEquals("province", vo.getLevel());
        assertEquals("100000", vo.getParentAdcode());
    }

    /**
     * 测试边界值
     */
    @Test
    void testE2v_WithBoundaryValues() {
        // Arrange
        BasicRegion entity = new BasicRegion();
        entity.setId(0L);
        entity.setAdcode("000000");
        entity.setName("根节点");
        entity.setLevel("country");
        entity.setParentAdcode(null);

        // Act
        BasicRegionVO vo = BasicRegionDTOConvert.INSTANCE.e2v(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(0L, vo.getId());
        assertEquals("000000", vo.getAdcode());
        assertEquals("根节点", vo.getName());
        assertEquals("country", vo.getLevel());
        assertNull(vo.getParentAdcode());
    }
}
