package com.dexpo.module.base.app.consumer;

import com.alibaba.fastjson2.JSON;
import com.dexpo.module.base.app.api.CommonTodoAppService;
import com.dexpo.module.base.infrastructure.enums.RegisterStatusEnum;
import com.dexpo.module.member.api.dto.message.MediaRegisterEventDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Objects;
import java.util.function.Consumer;

/**
 * 待办事项消费逻辑
 */
@Slf4j
@Configuration
public class MediaRegisterStatusEventConsumer {

    @Resource
    private CommonTodoAppService commonTodoAppService;

    @Bean("mediaRegisterStatusEventChannel")
    public Consumer<MediaRegisterEventDTO> mediaRegisterStatusEventChannel() {
        return message -> {
            log.info("mediaRegisterEventChannel message: {}", JSON.toJSONString(message));
            if (Objects.equals(message.getRegisterStatus(), RegisterStatusEnum.PENDING_REVIEW.getCode())) {
                commonTodoAppService.createMediaRegisterCommonTodo(message);
            } else {
                commonTodoAppService.updateMediaRegisterCommonTodo(message);
            }
        };
    }
}
