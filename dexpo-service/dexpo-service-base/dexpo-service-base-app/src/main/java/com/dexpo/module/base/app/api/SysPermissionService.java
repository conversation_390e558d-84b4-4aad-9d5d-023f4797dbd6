package com.dexpo.module.base.app.api;

import com.dexpo.module.base.api.permission.dto.SysPermissionDTO;
import com.dexpo.module.base.api.permission.vo.SysPermissionInterfaceRelationVO;
import com.dexpo.module.base.api.permission.vo.SysPermissionVO;

import java.util.List;

public interface SysPermissionService {

    List<SysPermissionVO> permissionByOrganizationList(SysPermissionDTO sysPermissionDTO);

    List<String> permissionByUserCode(SysPermissionDTO sysPermissionDTO);

    List<SysPermissionInterfaceRelationVO> findAllPermissionInterfaceRelation();
}