package com.dexpo.module.base.app.converter;

import com.dexpo.framework.common.pojo.IConvert;
import com.dexpo.module.base.api.basic.dto.BasicLocationDTO;
import com.dexpo.module.base.api.basic.vo.BasicLocationVO;
import com.dexpo.module.base.domain.model.agg.BasicLocation;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface BasicLocationDTOConvert extends IConvert<BasicLocationDTO, BasicLocationVO, BasicLocation> {

    BasicLocationDTOConvert INSTANCE = Mappers.getMapper(BasicLocationDTOConvert.class);
}
