package com.dexpo.module.base.app.api;

import com.dexpo.module.base.api.basic.dto.BasicExhibitionLocationDTO;
import com.dexpo.module.base.api.basic.dto.BasicLocationDTO;
import com.dexpo.module.base.api.basic.vo.BasicLocationVO;

import java.util.List;

/**
 * 业务地域 Service 接口
 */
public interface BasicLocationAppService {


    /**
     * 根据locationTag获取业务地域信息
     * @param locationDTO
     * @return
     */
    List<BasicLocationVO> getLocationList(BasicLocationDTO locationDTO);

    /**
     * 根据exhibitionTagCode获取业务地域信息
     * @param exhibitionLocationDTO
     * @return
     */
    List<BasicLocationVO> getExhibitionLocationList(BasicExhibitionLocationDTO exhibitionLocationDTO);

} 