package com.dexpo.module.base.app.api;

import com.dexpo.module.base.api.basic.dto.BasicRegionDTO;
import com.dexpo.module.base.api.basic.vo.BasicRegionTreeVO;
import com.dexpo.module.base.api.basic.vo.BasicRegionVO;

import java.util.List;
import java.util.Map;

/**
 * 行政区域 Service 接口
 */
public interface BasicRegionAppService {

    /**
     * 根据level和parentAdcode获取区域列表
     * @param regionDTO
     * @return
     */
    List<BasicRegionVO> getRegionList(BasicRegionDTO regionDTO);

    List<BasicRegionTreeVO> getRegionListAll();

    Map<String, Map<String, BasicRegionVO>> getRegionListByLevel();
} 