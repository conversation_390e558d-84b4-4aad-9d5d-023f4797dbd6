package com.dexpo.module.base.app.api;

import com.dexpo.framework.cache.redis.entity.BasicValuesetOptionCache;
import com.dexpo.module.base.api.basic.dto.BasicValuesetOptionDTO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetOptionVO;

import java.util.List;

/**
 * 值集选项 Service 接口
 */
public interface BasicValueSetOptionAppService {


    /**
     * 根据valueset code获取
     *
     * @param valuesetCodes codes
     * @return vo
     */
    List<BasicValuesetInfoVO> listByValuesetCodes(List<String> valuesetCodes);


    /**
     * 根据valuesetOption code获取
     *
     * @param valuesetCodes codes
     * @param optionCodes   option codes
     * @return vo
     */
    List<BasicValuesetInfoVO> listByValuesetOptionCodes(List<String> valuesetCodes, List<String> optionCodes);


    /**
     * 全表信息缓存
     */
    List<BasicValuesetOptionCache> initValueSetOptionCache();


    /**
     * 获取根据值集code父级值集项信息
     * @param request
     * @return
     */
    BasicValuesetOptionVO getValuesetOption(BasicValuesetOptionDTO request);


    /**
     * 根据值获取
     * @param optionCodes optionCodes
     * @return options
     */
    List<BasicValuesetOptionVO> getOptionListByCodes(List<String> optionCodes);

}