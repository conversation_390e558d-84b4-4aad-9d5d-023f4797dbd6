package com.dexpo.module.base.app.converter;

import com.dexpo.framework.common.pojo.IConvert;
import com.dexpo.module.base.api.basic.dto.BasicValuesetInfoDTO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;
import com.dexpo.module.base.domain.model.agg.BasicValueSet;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 值集信息转换接口
 */
@Mapper
public interface BasicValueSetDTOConvert extends IConvert<BasicValuesetInfoDTO, BasicValuesetInfoVO, BasicValueSet> {

    BasicValueSetDTOConvert INSTANCE = Mappers.getMapper(BasicValueSetDTOConvert.class);
} 