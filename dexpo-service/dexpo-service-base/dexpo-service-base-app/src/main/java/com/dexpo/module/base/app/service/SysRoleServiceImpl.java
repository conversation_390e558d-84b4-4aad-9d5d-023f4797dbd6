package com.dexpo.module.base.app.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.dexpo.framework.common.enums.ValueSetUserTypeEnum;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.pojo.PageParam;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.dexpo.framework.security.core.LoginUser;
import com.dexpo.framework.security.core.util.SecurityFrameworkUtils;
import com.dexpo.module.base.api.role.dto.SysRoleDTO;
import com.dexpo.module.base.api.role.vo.SysRoleVO;
import com.dexpo.module.base.app.api.SysRoleService;
import com.dexpo.module.base.app.converter.SysPermissionRoleRelationDTOConvert;
import com.dexpo.module.base.app.converter.SysRoleDTOConvert;
import com.dexpo.module.base.domain.model.valueobject.SysPermissionRoleRelation;
import com.dexpo.module.base.domain.model.valueobject.SysRole;
import com.dexpo.module.base.domain.service.SysPermissionRoleRelationDomainService;
import com.dexpo.module.base.domain.service.SysRoleDomainService;
import com.dexpo.module.base.enums.RoleTypeEnums;
import com.dexpo.module.base.infrastructure.tunnel.database.SysOrganizationMapper;
import com.dexpo.module.base.infrastructure.tunnel.database.SysUserRoleRelationMapper;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.SysOrganizationDO;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.SysUserRoleRelationDO;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class SysRoleServiceImpl implements SysRoleService {

    @Resource
    private SysRoleDomainService sysRoleDomainService;

    @Resource
    private SysPermissionRoleRelationDomainService sysPermissionRoleRelationDomainService;


    @Resource
    private SysUserRoleRelationMapper sysUserRoleRelationMapper;

    @Resource
    private SysOrganizationMapper sysOrganizationMapper;

    @Override
    public PageResult<SysRoleVO> roleList(SysRoleDTO sysRoleDTO) {
        SysRole sysRole = SysRoleDTOConvert.INSTANCE.dte(sysRoleDTO);
        sysRole.setRoleType(RoleTypeEnums.VO_ROLE_TYPE_2.getCode());
        sysRole.setRoleCategory(ValueSetUserTypeEnum.SPONSOR.getOptionCode());
        PageParam pageParam = new PageParam();
        pageParam.setPageNo(sysRoleDTO.getPageNo());
        pageParam.setPageSize(sysRoleDTO.getPageSize());

        // 分页查询
        PageResult<SysRole> pageResult = sysRoleDomainService.getPage(sysRole, pageParam);
        List<SysRoleVO> voList = SysRoleDTOConvert.INSTANCE.e2v(pageResult.getList());
        setOrgName(voList);
        return new PageResult<>(voList, pageResult.getTotal());
    }

    private void setOrgName(List<SysRoleVO> voList) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }
        LambdaQueryWrapperX<SysOrganizationDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(SysOrganizationDO::getDelFlg, false);
        List<SysOrganizationDO> sysOrganizationDOAll = sysOrganizationMapper.selectList(queryWrapperX);
        Map<String, SysOrganizationDO> orgAllMap = sysOrganizationDOAll.stream()
                .collect(Collectors.toMap(SysOrganizationDO::getOrganizationCode, Function.identity()));
        for (SysRoleVO sysRoleVO : voList) {
            sysRoleVO.setOrganizationName(getOrgName(sysRoleVO.getOrganizationCode(), orgAllMap));
        }
    }

    private String getOrgName(String orgCode, Map<String, SysOrganizationDO> organizationMap) {
        SysOrganizationDO currentOrg = organizationMap.get(orgCode);
        if (ObjectUtil.isNull(currentOrg)) {
            return null;
        }
        String orgName = currentOrg.getOrganizationName();
        while (CharSequenceUtil.isNotBlank(currentOrg.getParentOrganizationCode())) {
            String parentCode = currentOrg.getParentOrganizationCode();
            SysOrganizationDO parentOrg = organizationMap.get(parentCode);
            if (parentOrg == null) {
                break; // 父级不存在时结束循环
            }
            orgName = parentOrg.getOrganizationName() + "-" + orgName;
            currentOrg = parentOrg;
        }
        return orgName;
    }

    @Override
    public SysRoleVO add(SysRoleDTO sysRoleDTO) {
        // 对象转换
        SysRole sysRole = new SysRole();
        sysRole.setRoleName(sysRoleDTO.getRoleName());
        sysRole.setOrganizationCode(sysRoleDTO.getOrganizationCode());
        sysRole.setRoleDescription(sysRoleDTO.getRoleDescription());
        List<SysPermissionRoleRelation> sysPermissionRoleRelationList = new ArrayList<>();
        // 管理页面中新增智能新增系统自定的角色
        sysRole.setRoleCode(sysRoleDTO.getOrganizationCode() + new Date().getTime());
        if (CollUtil.isNotEmpty(sysRoleDTO.getPermissionCodeList())) {
            // 生成新的对象
            for (String permissionCode : sysRoleDTO.getPermissionCodeList()) {
                SysPermissionRoleRelation sysPermissionRoleRelation = new SysPermissionRoleRelation();
                sysPermissionRoleRelation.setRoleCode(sysRole.getRoleCode());
                sysPermissionRoleRelation.setPermissionCode(permissionCode);
                sysPermissionRoleRelationList.add(sysPermissionRoleRelation);
            }
        }

        sysRole.setRoleCategory(ValueSetUserTypeEnum.SPONSOR.getOptionCode());
        sysRole.setRoleType(RoleTypeEnums.VO_ROLE_TYPE_2.getCode());
        sysRole.setIsUseAble(true);
        sysRole = sysRoleDomainService.save(sysRole, sysPermissionRoleRelationList);
        return SysRoleDTOConvert.INSTANCE.e2v(sysRole);
    }

    @Override
    public SysRoleVO edit(SysRoleDTO sysRoleDTO) {
        SysRole sysRole = SysRoleDTOConvert.INSTANCE.dte(sysRoleDTO);
        sysRole = sysRoleDomainService.info(sysRole);
        sysRole.setRoleDescription(sysRoleDTO.getRoleDescription());
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        sysRole.setUpdateTime(LocalDateTime.now());
        sysRole.setUpdateUser(loginUser.getId());
        sysRole.setUpdateUserName(loginUser.getUserName());
        List<String> permissionCodeList = sysRoleDTO.getPermissionCodeList();
        // 编辑前的权限列表
        List<SysPermissionRoleRelation> roleRelations =
                sysPermissionRoleRelationDomainService.listByRoleCode(sysRoleDTO.getRoleCode());
        List<String> list = roleRelations.stream().map(SysPermissionRoleRelation::getPermissionCode).toList();
        // 需要删除的权限code
        List<SysPermissionRoleRelation> delectList = roleRelations.stream()
                .filter(item
                        -> !permissionCodeList.contains(item.getPermissionCode()))
                .map(e -> e.setDelFlg(true))
                .toList();

        // 新增的权限
        List<String> addList = permissionCodeList.stream()
                .filter(item -> !list.contains(item))
                .toList();

        List<SysPermissionRoleRelation> addRoleRelationList = new ArrayList<>();
        for (String permissionCode : addList) {
            SysPermissionRoleRelation sysPermissionRoleRelation = new SysPermissionRoleRelation();
            sysPermissionRoleRelation.setRoleCode(sysRole.getRoleCode());
            sysPermissionRoleRelation.setPermissionCode(permissionCode);
            addRoleRelationList.add(sysPermissionRoleRelation);
        }
        sysPermissionRoleRelationDomainService.saveBatch(delectList);
        sysRole = sysRoleDomainService.edit(sysRole, addRoleRelationList);
        return SysRoleDTOConvert.INSTANCE.e2v(sysRole);
    }

    @Override
    public SysRoleVO delete(SysRoleDTO sysRoleDTO) {
        SysRole sysRole = SysRoleDTOConvert.INSTANCE.dte(sysRoleDTO);
        sysRole = sysRoleDomainService.info(sysRole);
        sysRole.setDelFlg(true);
        List<SysPermissionRoleRelation> sysPermissionRoleRelationList
                = sysPermissionRoleRelationDomainService.listByRoleCode(sysRoleDTO.getRoleCode());
        for (SysPermissionRoleRelation roleRelation : sysPermissionRoleRelationList) {
            roleRelation.setDelFlg(true);
        }
        sysRole = sysRoleDomainService.delete(sysRole, sysPermissionRoleRelationList);
        return SysRoleDTOConvert.INSTANCE.e2v(sysRole);
    }

    @Override
    public CommonResult<SysRoleVO> info(SysRoleDTO sysRoleDTO) {
        SysRole sysRole = SysRoleDTOConvert.INSTANCE.dte(sysRoleDTO);
        sysRole = sysRoleDomainService.info(sysRole);
        SysRoleVO vo = SysRoleDTOConvert.INSTANCE.e2v(sysRole);
        List<SysPermissionRoleRelation> roleRelations
                = sysPermissionRoleRelationDomainService.listByRoleCode(vo.getRoleCode());
        List<String> permissionCodeList = roleRelations.stream()
                .map(SysPermissionRoleRelation::getPermissionCode).toList();
        vo.setPermissionList(SysPermissionRoleRelationDTOConvert.INSTANCE.e2vList(roleRelations));
        vo.setPermissionCodeList(permissionCodeList);
        return CommonResult.success(vo);
    }

    @Override
    public SysRoleVO hasUserByRoleCode(SysRoleDTO sysRoleDTO) {
        LambdaQueryWrapperX<SysUserRoleRelationDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(SysUserRoleRelationDO::getRoleCode, sysRoleDTO.getRoleCode());
        queryWrapperX.eq(SysUserRoleRelationDO::getDelFlg, false);
        Long l = sysUserRoleRelationMapper.selectCount(queryWrapperX);
        SysRoleVO vo = new SysRoleVO();
        vo.setHasUser(l > 0);
        return vo;
    }

}
