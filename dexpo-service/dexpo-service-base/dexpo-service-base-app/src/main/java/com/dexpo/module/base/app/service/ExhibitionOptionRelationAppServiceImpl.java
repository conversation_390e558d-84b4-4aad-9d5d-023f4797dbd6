package com.dexpo.module.base.app.service;

import cn.hutool.core.collection.CollUtil;
import com.dexpo.framework.cache.redis.constant.BasicRedisKey;
import com.dexpo.framework.cache.redis.constant.ICacheKey;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.module.base.api.basic.dto.ExhibitionTagValuesetDTO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;
import com.dexpo.module.base.app.api.BasicValueSetOptionAppService;
import com.dexpo.module.base.app.api.ExhibitionOptionRelationAppService;
import com.dexpo.module.base.app.converter.ExhibitionValueSetOptionDTOConvert;
import com.dexpo.module.base.app.entity.ExhibitionValuesetOptionRelationCache;
import com.dexpo.module.base.domain.model.agg.ExhibitionValuesetOptionRelation;
import com.dexpo.module.base.domain.service.ExhibitionOptionRelationDomainService;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ExhibitionOptionRelationAppServiceImpl  implements ExhibitionOptionRelationAppService {


    private final ExhibitionOptionRelationDomainService examinationOptionRelationDomainService;
    private final BasicValueSetOptionAppService basicValueSetOptionAppService;

    private final RedisService redisService;

    @Override
    public List<BasicValuesetInfoVO> getExhibitionValuesetListByCodes(ExhibitionTagValuesetDTO request) {
        // 根据缓存获取数据
        String key = ICacheKey.generateKey(BasicRedisKey.BASIC_EXHIBITION_VALUESET_OPTION_KEY,
                request.getExhibitionTagCode());
        List<ExhibitionValuesetOptionRelationCache> cacheList = redisService.getCacheObject(key);
        if (CollUtil.isEmpty(cacheList)) {
            cacheList = this.initExhibitionValuesetOptionCache().get(request.getExhibitionTagCode());
        }
        if (CollUtil.isEmpty(cacheList)) {
            return List.of();
        }

        List<String> optionCodes = cacheList.stream()
                .map(ExhibitionValuesetOptionRelationCache::getValuesetOptionCode).toList();

        return basicValueSetOptionAppService.listByValuesetOptionCodes(request.getValuesetList(), optionCodes);
    }

    @Override
    public Map<String, List<ExhibitionValuesetOptionRelationCache>> initExhibitionValuesetOptionCache() {
        List<ExhibitionValuesetOptionRelation> relationDOList = examinationOptionRelationDomainService.list();
        Map<String, List<ExhibitionValuesetOptionRelation>> tagMap = relationDOList.stream()
                .collect(Collectors.groupingBy(ExhibitionValuesetOptionRelation::getExhibitionTagCode));
        Map<String, List<ExhibitionValuesetOptionRelationCache>> map = Maps.newHashMapWithExpectedSize(tagMap.size());
        tagMap.forEach((k, v) -> {
            List<ExhibitionValuesetOptionRelationCache> cacheList = ExhibitionValueSetOptionDTOConvert.INSTANCE.toCacheList(v);
            String key = ICacheKey.generateKey(BasicRedisKey.BASIC_EXHIBITION_VALUESET_OPTION_KEY, k);
            redisService.setCacheObject(key, cacheList);
            map.put(k, cacheList);
        });
        return map;
    }
}
