package com.dexpo.module.base.app.service;

import com.dexpo.module.base.api.basic.dto.BasicExhibitionLocationDTO;
import com.dexpo.module.base.api.basic.dto.BasicLocationDTO;
import com.dexpo.module.base.api.basic.vo.BasicLocationVO;
import com.dexpo.module.base.app.api.BasicLocationAppService;
import com.dexpo.module.base.app.converter.BasicLocationDTOConvert;
import com.dexpo.module.base.domain.model.agg.BasicLocation;
import com.dexpo.module.base.domain.service.BasicLocationDomainService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务地域 Service 实现类
 */
@Service
@RequiredArgsConstructor
public class BasicLocationAppServiceImpl implements BasicLocationAppService {
    private final BasicLocationDomainService basicLocationDomainService;

    @Override
    public List<BasicLocationVO> getLocationList(BasicLocationDTO locationDTO) {
        BasicLocation basicLocation = BasicLocationDTOConvert.INSTANCE.d2e(locationDTO);
        List<BasicLocation> baseLocationList =  basicLocationDomainService.getLocationList(basicLocation);
        return BasicLocationDTOConvert.INSTANCE.e2vList(baseLocationList);
    }

    @Override
    public List<BasicLocationVO> getExhibitionLocationList(BasicExhibitionLocationDTO exhibitionLocationDTO) {
        List<BasicLocation> list = basicLocationDomainService.getExhibitionLocationList(exhibitionLocationDTO.getExhibitionTagCode());
        return BasicLocationDTOConvert.INSTANCE.e2vList(list);
    }
}