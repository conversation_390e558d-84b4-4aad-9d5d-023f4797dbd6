package com.dexpo.module.base.app.service;

import cn.hutool.core.text.CharSequenceUtil;
import com.dexpo.module.base.api.permission.dto.SysPermissionDTO;
import com.dexpo.module.base.api.permission.vo.SysPermissionInterfaceRelationVO;
import com.dexpo.module.base.api.permission.vo.SysPermissionVO;
import com.dexpo.module.base.app.api.SysPermissionService;
import com.dexpo.module.base.app.converter.SysPermissionDTOConvert;
import com.dexpo.module.base.app.converter.SysPermissionInterfaceRelationDTOConvert;
import com.dexpo.module.base.domain.model.valueobject.SysPermission;
import com.dexpo.module.base.domain.model.valueobject.SysPermissionInterfaceRelation;
import com.dexpo.module.base.domain.service.SysPermissionDomainService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class SysPermissionServiceImpl implements SysPermissionService {

    @Resource
    private SysPermissionDomainService sysPermissionDomainService;

    @Override
    public List<SysPermissionVO> permissionByOrganizationList(SysPermissionDTO sysPermissionDTO) {
        SysPermission sysPermission = SysPermissionDTOConvert.INSTANCE.dte(sysPermissionDTO);
        List<SysPermission> list = sysPermissionDomainService.permissionByOrganizationList(sysPermission);
        List<SysPermissionVO> sysPermissionVOS = SysPermissionDTOConvert.INSTANCE.e2vList(list);
        // 权限树形图
        Map<String, List<SysPermissionVO>> sysPermissionMap = sysPermissionVOS.stream()
                .collect(Collectors.groupingBy(SysPermissionVO::getParentPermissionCode));
        List<SysPermissionVO> resultList = new ArrayList<>();
        for (SysPermissionVO sysPermissionVO : sysPermissionVOS) {
            sysPermissionVO.setSysPermissionVOList(sysPermissionMap.get(sysPermissionVO.getPermissionCode()));
            if (CharSequenceUtil.isEmpty(sysPermissionVO.getParentPermissionCode())) {
                resultList.add(sysPermissionVO);
            }
        }
        return resultList;
    }

    @Override
    public List<String> permissionByUserCode(SysPermissionDTO sysPermissionDTO) {
        SysPermission sysPermission = SysPermissionDTOConvert.INSTANCE.dte(sysPermissionDTO);
        List<SysPermission> sysPermissions = sysPermissionDomainService.permissionByUserCode(sysPermission);
        return sysPermissions.stream().map(SysPermission::getPermissionCode).distinct().toList();
    }

    @Override
    public List<SysPermissionInterfaceRelationVO> findAllPermissionInterfaceRelation() {
        List<SysPermissionInterfaceRelation> sysPermissions = sysPermissionDomainService.findAllPermissionInterfaceRelation();
        return SysPermissionInterfaceRelationDTOConvert.INSTANCE.e2vList(sysPermissions);
    }

}
