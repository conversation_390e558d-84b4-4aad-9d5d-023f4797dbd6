package com.dexpo.module.base.app.converter;

import com.dexpo.framework.common.pojo.IConvert;
import com.dexpo.module.base.api.attachment.dto.AttachmentInfoDTO;
import com.dexpo.module.base.api.attachment.vo.AttachmentInfoVO;
import com.dexpo.module.base.domain.model.agg.AttachmentInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AttachmentInfoDTOConvert extends IConvert<AttachmentInfoDTO, AttachmentInfoVO, AttachmentInfo> {

    AttachmentInfoDTOConvert INSTANCE = Mappers.getMapper(AttachmentInfoDTOConvert.class);
}
