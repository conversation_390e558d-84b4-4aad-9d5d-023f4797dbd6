package com.dexpo.module.base.app.converter;

import com.dexpo.module.base.api.basic.vo.CommonTodoVO;
import com.dexpo.module.base.domain.model.agg.CommonTodo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CommonTodoDTOConvert {

    CommonTodoDTOConvert INSTANCE = Mappers.getMapper(CommonTodoDTOConvert.class);

    CommonTodoVO e2v(CommonTodo entity);

    List<CommonTodoVO> e2v(List<CommonTodo> entityList);

}
