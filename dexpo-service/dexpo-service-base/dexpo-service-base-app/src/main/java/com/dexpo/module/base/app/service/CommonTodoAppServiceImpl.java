package com.dexpo.module.base.app.service;

import com.alibaba.fastjson2.JSON;
import com.dexpo.framework.cache.redis.entity.sponsor.SponsorProfileCache;
import com.dexpo.framework.cache.redis.operate.member.MemberBaseInfoOpt;
import com.dexpo.framework.common.enums.ValueSetCommonTodoBusinessTypeEnum;
import com.dexpo.framework.common.enums.ValueSetTodoStatusEnum;
import com.dexpo.framework.common.pojo.PageParam;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.framework.security.core.util.SecurityFrameworkUtils;
import com.dexpo.module.base.api.basic.dto.CommenTodoPageQueryDTO;
import com.dexpo.module.base.api.basic.vo.CommonTodoVO;
import com.dexpo.module.base.app.api.CommonTodoAppService;
import com.dexpo.module.base.app.converter.CommonTodoDTOConvert;
import com.dexpo.module.base.domain.model.agg.CommonTodo;
import com.dexpo.module.base.domain.service.CommonTodoDomainService;
import com.dexpo.module.base.infrastructure.integration.exhibition.ExhibitionExternalService;
import com.dexpo.module.exhibition.api.dto.ExhibitionQueryDTO;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class CommonTodoAppServiceImpl implements CommonTodoAppService {

    private final CommonTodoDomainService commonTodoDomainService;
    private final ExhibitionExternalService exhibitionExternalService;


    private final MemberBaseInfoOpt memberBaseInfoOpt;


    @Override
    public PageResult<CommonTodoVO> getPage(CommenTodoPageQueryDTO req) {
        // 需要获取对应展会下面的数据
        ExhibitionQueryDTO queryDTO = new ExhibitionQueryDTO();
        SponsorProfileCache profileCache = memberBaseInfoOpt.sponsorProfile(SecurityFrameworkUtils.getLoginUserId());
        queryDTO.setExhibitionTagCodes(Lists.newArrayList(profileCache.getExhibitionTagCode()));
        List<Long> exhibitionIds = exhibitionExternalService.getExhibitionIds(queryDTO);
        if (CollectionUtils.isEmpty(exhibitionIds)) {
            return PageResult.empty();
        }
        if (req.getStatus() == null) {
            req.setStatus(ValueSetTodoStatusEnum.TODO.getOptionCode());
        }
        CommonTodo commonTodo = new CommonTodo();
        commonTodo.setStatus(req.getStatus());
        PageParam pageParam = new PageParam();
        pageParam.setPageNo(req.getPageNo());
        pageParam.setPageSize(req.getPageSize());
        PageResult<CommonTodo>  pageResult = commonTodoDomainService.getPage(commonTodo,pageParam);
        List<CommonTodoVO> voList = CommonTodoDTOConvert.INSTANCE.e2v(pageResult.getList());
        return new PageResult<>(voList, pageResult.getTotal());
    }

    @Override
    public void createCommonTodo(CommonTodo commonTodo) {
        commonTodoDomainService.save(commonTodo);
    }

    @Override
    public void updateCommonTodo(CommonTodo updateCommonTodo) {
        CommonTodo commonTodo = new CommonTodo();
        commonTodo.setBusinessNo(updateCommonTodo.getBusinessNo());
        commonTodo.setExhibitionId(updateCommonTodo.getExhibitionId());
        commonTodo.setBusinessType(ValueSetCommonTodoBusinessTypeEnum.MEDIA_USER_AUDIT.getOptionCode());
        commonTodo.setStatus(ValueSetTodoStatusEnum.TODO.getOptionCode());
        CommonTodo one = commonTodoDomainService.selectOneByCommonTodo(commonTodo);
        if (one == null) {
            log.warn("updateMediaRegisterCommonTodo 未获取到待办信息 params:{}", JSON.toJSONString(updateCommonTodo));
            return;
        }
        updateCommonTodo.setId(one.getId());
        commonTodoDomainService.updateById(updateCommonTodo);
    }

}
