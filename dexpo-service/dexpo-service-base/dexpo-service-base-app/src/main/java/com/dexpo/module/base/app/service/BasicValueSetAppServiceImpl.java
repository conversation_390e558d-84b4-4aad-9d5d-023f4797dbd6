package com.dexpo.module.base.app.service;

import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;
import com.dexpo.module.base.app.api.BasicValueSetAppService;
import com.dexpo.module.base.app.api.BasicValueSetOptionAppService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 值集 Service 实现类
 */
@Service
@RequiredArgsConstructor
public class BasicValueSetAppServiceImpl implements BasicValueSetAppService {

    private final BasicValueSetOptionAppService basicValueSetOptionAppService;

    public List<BasicValuesetInfoVO> getValuesetListByCodes(List<String> valuesetCodes) {
        return basicValueSetOptionAppService.listByValuesetCodes(valuesetCodes);
    }

} 