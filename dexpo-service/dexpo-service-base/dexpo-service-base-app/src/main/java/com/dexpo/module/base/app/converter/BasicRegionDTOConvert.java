package com.dexpo.module.base.app.converter;

import com.dexpo.framework.common.pojo.IConvert;
import com.dexpo.module.base.api.basic.dto.BasicRegionDTO;
import com.dexpo.module.base.api.basic.vo.BasicRegionTreeVO;
import com.dexpo.module.base.api.basic.vo.BasicRegionVO;
import com.dexpo.module.base.domain.model.agg.BasicRegion;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface BasicRegionDTOConvert extends IConvert<BasicRegionDTO, BasicRegionVO, BasicRegion> {

    BasicRegionDTOConvert INSTANCE = Mappers.getMapper(BasicRegionDTOConvert.class);

    List<BasicRegionTreeVO> vToTreeList(List<BasicRegion> entity);
}
