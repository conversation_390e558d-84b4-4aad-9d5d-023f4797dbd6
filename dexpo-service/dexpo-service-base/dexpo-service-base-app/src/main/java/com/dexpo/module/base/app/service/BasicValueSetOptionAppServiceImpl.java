package com.dexpo.module.base.app.service;

import com.dexpo.framework.cache.redis.constant.BasicRedisKey;
import com.dexpo.framework.cache.redis.entity.BasicValuesetOptionCache;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.module.base.api.basic.dto.BasicValuesetOptionDTO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetInfoVO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetOptionVO;
import com.dexpo.module.base.app.api.BasicValueSetOptionAppService;
import com.dexpo.module.base.app.converter.BasicValueSetOptionDTOConvert;
import com.dexpo.module.base.domain.model.agg.BasicValueSetOption;
import com.dexpo.module.base.domain.service.BasicValueSetOptionDomainService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 值集选项 Service 实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BasicValueSetOptionAppServiceImpl implements BasicValueSetOptionAppService {

    private final RedisService redisService;
    private final BasicValueSetOptionDomainService basicValueSetOptionDomainService;


    @Override
    public List<BasicValuesetInfoVO> listByValuesetCodes(List<String> valuesetCodes) {

        List<BasicValuesetOptionCache> cache = getAllOptionCache();
        if (CollectionUtils.isEmpty(cache)) {
            return List.of();
        }
        List<BasicValuesetOptionCache> filterOptions = cache.stream()
                .filter(e -> valuesetCodes.contains(e.getValuesetCode())).toList();
        return getBasicValuesetInfoVOS(filterOptions);
    }


    @Override
    public List<BasicValuesetInfoVO> listByValuesetOptionCodes(List<String> valuesetCodes, List<String> optionCodes) {
        List<BasicValuesetOptionCache> cache = getAllOptionCache();
        if (CollectionUtils.isEmpty(cache)) {
            return List.of();
        }
        List<BasicValuesetOptionCache> optionList = cache.stream()
                .filter(e -> valuesetCodes.contains(e.getValuesetCode()) && optionCodes.contains(e.getOptionCode()))
                .toList();

        return getBasicValuesetInfoVOS(optionList);
    }

    @Override
    public List<BasicValuesetOptionCache> initValueSetOptionCache() {
        List<BasicValueSetOption> optionDOList = basicValueSetOptionDomainService.list();
        List<BasicValuesetOptionCache> cache = BasicValueSetOptionDTOConvert.INSTANCE
                .toBasicValuesetOptionCache(optionDOList);
        redisService.setCacheObject(BasicRedisKey.BASIC_VALUESET_OPTION, cache);
        return cache;
    }

    @Override
    public BasicValuesetOptionVO getValuesetOption(BasicValuesetOptionDTO request) {
        //缓存没有则从数据库查询
        log.info("从数据库中获取值集选项信息");
        BasicValueSetOption baseValueSetOption = basicValueSetOptionDomainService.selectByOptionCode(request.getOptionCode());
        log.info("数据库中获取的值集选项信息为：{}", baseValueSetOption);
        return BasicValueSetOptionDTOConvert.INSTANCE.e2v(baseValueSetOption);
    }

    @Override
    public List<BasicValuesetOptionVO> getOptionListByCodes(List<String> optionCodes) {
        List<BasicValuesetOptionCache> allOptionCache = getAllOptionCache();
        return allOptionCache.stream().filter(e -> optionCodes.contains(e.getOptionCode()))
                .map(BasicValueSetOptionDTOConvert.INSTANCE::cache2VO).toList();
    }

    /**
     * 基础转换
     *
     * @param optionDOList do
     * @return vo
     */
    private static List<BasicValuesetInfoVO> getBasicValuesetInfoVOS(List<BasicValuesetOptionCache> optionDOList) {
        Map<String, List<BasicValuesetOptionCache>> valuesetMap = optionDOList.stream()
                .collect(Collectors.groupingBy(BasicValuesetOptionCache::getValuesetCode));
        List<BasicValuesetInfoVO> result = Lists.newArrayListWithCapacity(valuesetMap.size());
        valuesetMap.forEach((k, v) -> {
            BasicValuesetInfoVO vo = new BasicValuesetInfoVO();
            vo.setValuesetCode(k);
            vo.setOptions(BasicValueSetOptionDTOConvert.INSTANCE.cache2VO(v));
            result.add(vo);
        });
        return result;
    }

    /**
     * 获取所有的缓存信息 如果不存在 则执行一次初始化
     *
     * @return cache
     */
    private List<BasicValuesetOptionCache> getAllOptionCache() {
        List<BasicValuesetOptionCache> cache = redisService.getCacheObject(BasicRedisKey.BASIC_VALUESET_OPTION);
        if (CollectionUtils.isEmpty(cache)) {
            cache = initValueSetOptionCache();
        }
        return cache;
    }
}