package com.dexpo.module.base.app.api;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.security.core.LoginUser;
import com.dexpo.module.base.api.attachment.dto.AttachmentInfoDTO;
import com.dexpo.module.base.api.attachment.vo.AttachmentInfoVO;

import java.util.List;

/**
 * 值集 Service 接口
 */
public interface AttachmentInfoAppService {

    /**
     * 根据值集代码列表获取值集信息
     *
     * @param id 值集代码列表
     * @return 值集信息列表
     */
    AttachmentInfoVO findById(Long id);

    AttachmentInfoVO createAttachment(AttachmentInfoDTO attachmentInfoDTO, LoginUser loginUser);

    List<AttachmentInfoVO> findByIdList(List<Long> idList);

    CommonResult<AttachmentInfoVO> findFileByBusinessType(String businessType);
}