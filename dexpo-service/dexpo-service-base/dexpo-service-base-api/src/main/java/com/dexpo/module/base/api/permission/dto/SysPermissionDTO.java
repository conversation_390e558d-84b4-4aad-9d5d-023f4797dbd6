package com.dexpo.module.base.api.permission.dto;


import com.dexpo.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 权限信息入参
 */
@Data
public class SysPermissionDTO extends PageParam {

    @Schema(description = "项目组织Code")
    private String organizationCode;

    @Schema(description = "用户Code")
    private String userCode;

    @Schema(description = "用户分类值级VS_ACTION_USER_TYPE")
    private String userType;

}

