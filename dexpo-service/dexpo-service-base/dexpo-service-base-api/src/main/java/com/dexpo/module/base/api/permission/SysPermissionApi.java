package com.dexpo.module.base.api.permission;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.base.api.permission.dto.SysPermissionDTO;
import com.dexpo.module.base.api.permission.vo.SysPermissionInterfaceRelationVO;
import com.dexpo.module.base.api.permission.vo.SysPermissionVO;
import com.dexpo.module.base.enums.ApiConstants;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


@FeignClient(name = ApiConstants.NAME)
public interface SysPermissionApi {

    String PREFIX = "/permission";

    /**
     * 根据组织code查询权限
     *
     * @param sysPermissionDTO codes
     * @return SysPermissionVO
     */
    @PostMapping(PREFIX + "/permissionByOrganizationList")
    CommonResult<List<SysPermissionVO>> permissionByOrganizationList(@Valid @RequestBody SysPermissionDTO sysPermissionDTO);

    /**
     * 根据用户code查询权限
     * 登录时查询用户拥有的 permission code
     *
     * @param sysPermissionDTO codes
     * @return SysPermissionVO
     */
    @PostMapping(PREFIX + "/permissionByUserCode")
    CommonResult<List<String>> permissionByUserCode(@Valid @RequestBody SysPermissionDTO sysPermissionDTO);

    /**
     * 根据现存的权限与url的关系
     * ***内部使用不对BFF层暴露***
     */
    @PostMapping(PREFIX + "/findAllPermissionInterfaceRelation")
    CommonResult<List<SysPermissionInterfaceRelationVO>> findAllPermissionInterfaceRelation();

}