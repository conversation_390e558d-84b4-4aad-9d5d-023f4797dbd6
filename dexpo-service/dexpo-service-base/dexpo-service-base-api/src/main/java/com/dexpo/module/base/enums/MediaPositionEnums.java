package com.dexpo.module.base.enums;

import lombok.Getter;


@Getter
public enum MediaPositionEnums {


    VO_MEDIA_POSITION_1(MediaCategoryEnums.VO_MEDIA_CATEGORY_1, "VO_MEDIA_POSITION_1", "记者", "Journalist"),

    VO_MEDIA_POSITION_2(MediaCategoryEnums.VO_MEDIA_CATEGORY_1, "VO_MEDIA_POSITION_2", "首席记者", "Chief Journalist"),

    VO_MEDIA_POSITION_3(MediaCategoryEnums.VO_MEDIA_CATEGORY_1, "VO_MEDIA_POSITION_3", "主任记者", "Senior Journalist"),

    VO_MEDIA_POSITION_4(MediaCategoryEnums.VO_MEDIA_CATEGORY_1, "VO_MEDIA_POSITION_4", "副站长", "Deputy Station Director"),

    VO_MEDIA_POSITION_5(MediaCategoryEnums.VO_MEDIA_CATEGORY_1, "VO_MEDIA_POSITION_5", "编辑", "Editor"),

    VO_MEDIA_POSITION_6(MediaCategoryEnums.VO_MEDIA_CATEGORY_1, "VO_MEDIA_POSITION_6", "主笔", "Lead Writer"),

    VO_MEDIA_POSITION_7(MediaCategoryEnums.VO_MEDIA_CATEGORY_1, "VO_MEDIA_POSITION_7", "特邀撰稿人", "Contributing Writer"),

    VO_MEDIA_POSITION_8(MediaCategoryEnums.VO_MEDIA_CATEGORY_2, "VO_MEDIA_POSITION_8", "主播", "Anchor"),

    VO_MEDIA_POSITION_9(MediaCategoryEnums.VO_MEDIA_CATEGORY_2, "VO_MEDIA_POSITION_9", "导播", "Director of Programming"),

    VO_MEDIA_POSITION_10(MediaCategoryEnums.VO_MEDIA_CATEGORY_2, "VO_MEDIA_POSITION_10", "摄影记者", "Photo Journalist"),

    VO_MEDIA_POSITION_11(MediaCategoryEnums.VO_MEDIA_CATEGORY_2, "VO_MEDIA_POSITION_11", "节目制作人", "Producer"),

    VO_MEDIA_POSITION_12(MediaCategoryEnums.VO_MEDIA_CATEGORY_2, "VO_MEDIA_POSITION_12", "执行制作", "Executive Producer"),

    VO_MEDIA_POSITION_13(MediaCategoryEnums.VO_MEDIA_CATEGORY_3, "VO_MEDIA_POSITION_13", "内容运营", "Content Operator"),

    VO_MEDIA_POSITION_14(MediaCategoryEnums.VO_MEDIA_CATEGORY_3, "VO_MEDIA_POSITION_14", "新闻编辑", "News Editor"),

    VO_MEDIA_POSITION_15(MediaCategoryEnums.VO_MEDIA_CATEGORY_4, "VO_MEDIA_POSITION_15", "总编辑", "Editor-in-Chief"),

    VO_MEDIA_POSITION_16(MediaCategoryEnums.VO_MEDIA_CATEGORY_4, "VO_MEDIA_POSITION_16", "采访主任", "Director"),

    VO_MEDIA_POSITION_17(MediaCategoryEnums.VO_MEDIA_CATEGORY_4, "VO_MEDIA_POSITION_17", "发行人", "Publisher"),

    VO_MEDIA_POSITION_18(MediaCategoryEnums.VO_MEDIA_CATEGORY_5, "VO_MEDIA_POSITION_18", "美术编辑", "Art Editor"),

    VO_MEDIA_POSITION_19(MediaCategoryEnums.VO_MEDIA_CATEGORY_5, "VO_MEDIA_POSITION_19", "剪辑师", "Video Editor"),

    VO_MEDIA_POSITION_20(MediaCategoryEnums.VO_MEDIA_CATEGORY_5, "VO_MEDIA_POSITION_20", "工程师", "Engineer"),

    VO_MEDIA_POSITION_OTHER(MediaCategoryEnums.VO_MEDIA_CATEGORY_6, "VO_MEDIA_POSITION_OTHER", "其他", "Other");

    /**
     * 枚举项编码
     */
    private final MediaCategoryEnums mediaCategoryEnums;

    /**
     * 枚举项编码
     */
    private final String code;

    /**
     * 中文描述
     */
    private final String descriptionCN;

    /**
     * 英文描述
     */
    private final String descriptionEN;

    MediaPositionEnums(MediaCategoryEnums mediaCategoryEnums, String code, String descriptionCN, String descriptionEN) {
        this.mediaCategoryEnums = mediaCategoryEnums;
        this.code = code;
        this.descriptionCN = descriptionCN;
        this.descriptionEN = descriptionEN;
    }

    /**
     * 根据枚举编码获取枚举实例
     *
     * @param code 格式的编码
     * @return 匹配的枚举实例，未找到返回null
     */
    public static MediaPositionEnums getByCode(String code) {
        for (MediaPositionEnums type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return MediaPositionEnums.VO_MEDIA_POSITION_OTHER;
    }

    /**
     * 根据枚举编码获取枚举实例
     *
     * @param descriptionCN descriptionCN
     * @return 匹配的枚举实例，未找到返回null
     */
    public static MediaPositionEnums getByDescriptionCN(String descriptionCN) {
        for (MediaPositionEnums type : values()) {
            if (type.descriptionCN.equals(descriptionCN)) {
                return type;
            }
        }
        return MediaPositionEnums.VO_MEDIA_POSITION_OTHER;
    }
}
