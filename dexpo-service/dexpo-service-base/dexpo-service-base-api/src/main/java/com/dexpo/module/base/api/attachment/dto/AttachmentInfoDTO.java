package com.dexpo.module.base.api.attachment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 值集信息数据对象
 */
@Data
@Schema(description = "文件信息")
public class AttachmentInfoDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "主键ID集合")
    private List<Long> idList;
    
    /**
     * 附件名
     */
    @Schema(description = "附件名")
    private String attachmentName;

    /**
     * 附件类型
     */
    @Schema(description = "附件类型")
    private String attachmentType;

    /**
     * 附件大小
     */
    @Schema(description = "附件大小")
    private Long attachmentSize;

    /**
     * 附件存储地址
     * /日期20250521/用户编码/uuid_文件名称.文件类型
     */
    @Schema(description = "附件存储地址")
    private String attachmentPath;

} 