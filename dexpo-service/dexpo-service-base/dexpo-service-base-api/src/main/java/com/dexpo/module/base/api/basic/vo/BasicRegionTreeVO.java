package com.dexpo.module.base.api.basic.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 行政区域信息数据对象
 */
@Data
@Schema(description = "行政区域信息数据对象")
public class BasicRegionTreeVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 上级行政区划编码
     */
    @Schema(description = "上级行政区划编码")
    private String parentAdcode;

    /**
     * 行政区划级别
     */
    @Schema(description = "行政区划级别")
    private String level;

    /**
     * 区域代码
     */
    @Schema(description = "区域代码")
    private String adcode;

    /**
     * 城市编码
     */
    @Schema(description = "城市编码")
    private String citycode;

    /**
     * 行政区名称
     */
    @Schema(description = "行政区名称")
    private String name;

    /**
     * 行政区名称(英文)
     */
    @Schema(description = "行政区名称(英文)")
    private String nameEn;

    /**
     * 中心位置
     */
    @Schema(description = "中心位置")
    private String center;

    /**
     * 行政区边界坐标点
     */
    @Schema(description = "行政区边界坐标点")
    private String polyline;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;


    @Schema(description = "子集")
    private List<BasicRegionTreeVO> childrenList;

} 