package com.dexpo.module.base.api.basic.dto;

import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class ExhibitionTagValuesetDTOTest {

    @Test
    void testGetterSetter() {
        ExhibitionTagValuesetDTO dto = new ExhibitionTagValuesetDTO();
        dto.setExhibitionTagCode("tag_code");
        List<String> valuesetList = Collections.singletonList("valueset_item");
        dto.setValuesetList(valuesetList);

        assertNotNull(dto);
        assertEquals("tag_code", dto.getExhibitionTagCode());
        assertEquals(valuesetList, dto.getValuesetList());
    }
} 