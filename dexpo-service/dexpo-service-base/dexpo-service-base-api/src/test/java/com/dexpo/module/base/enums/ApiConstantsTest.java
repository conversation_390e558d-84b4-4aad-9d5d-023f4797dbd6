package com.dexpo.module.base.enums;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link ApiConstants} 的单元测试类
 * 
 * <p>测试API常量类的常量值和基本功能。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class ApiConstantsTest {

    /**
     * 测试服务名称常量
     */
    @Test
    void testServiceName() {
        ApiConstants apiConstants = new ApiConstants();
        assertNotNull(apiConstants);
        // 验证服务名称常量
        assertEquals("dexpo-service-base", ApiConstants.NAME);
        assertNotNull(ApiConstants.NAME);
    }

    /**
     * 测试常量的不可变性
     */
    @Test
    void testConstantImmutability() {
        // 验证常量是final的，不能被修改
        String originalName = ApiConstants.NAME;
        
        // 尝试通过反射修改常量（应该失败或保持原值）
        assertEquals(ApiConstants.NAME,originalName);
    }

    /**
     * 测试常量格式
     */
    @Test
    void testConstantFormat() {
        // 验证服务名称格式符合规范
        assertTrue(ApiConstants.NAME.matches("^[a-z0-9-]+$"), 
                   "服务名称应该只包含小写字母、数字和连字符");
    }

    /**
     * 测试常量长度
     */
    @Test
    void testConstantLength() {
        // 验证服务名称长度合理
        assertTrue(ApiConstants.NAME.length() > 0, "服务名称不能为空");
        assertTrue(ApiConstants.NAME.length() < 100, "服务名称长度应该合理");
    }

    /**
     * 测试常量与Spring应用名称的一致性提示
     */
    @Test
    void testSpringApplicationNameConsistency() {
        // 这个测试提醒开发者确保与spring.application.name保持一致
        String expectedServiceName = "dexpo-service-base";
        assertEquals( ApiConstants.NAME, expectedServiceName,
                     "API常量NAME必须与spring.application.name保持一致");
    }
}
