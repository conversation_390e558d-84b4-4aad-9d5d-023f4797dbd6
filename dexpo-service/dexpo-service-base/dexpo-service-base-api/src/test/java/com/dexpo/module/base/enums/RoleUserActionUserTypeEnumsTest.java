package com.dexpo.module.base.enums;

import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class RoleUserActionUserTypeEnumsTest {

    @Test
    void testEnumValuesInitializedCorrectly() {
        RoleUserActionUserTypeEnums[] values = RoleUserActionUserTypeEnums.values();
        assertThat(values).hasSize(3).contains(
            RoleUserActionUserTypeEnums.VO_ACTION_USER_TYPE_1,
            RoleUserActionUserTypeEnums.VO_ACTION_USER_TYPE_2,
            RoleUserActionUserTypeEnums.VO_ACTION_USER_TYPE_3
        );
    }

    @Test
    void testGettersReturnCorrectValues() {
        assertThat(RoleUserActionUserTypeEnums.VO_ACTION_USER_TYPE_1)
            .extracting(
                RoleUserActionUserTypeEnums::getCode,
                RoleUserActionUserTypeEnums::getDescriptionCN,
                RoleUserActionUserTypeEnums::getDescriptionEN
            )
            .containsExactly("VO_ACTION_USER_TYPE_1", "主办方用户", "Sponsor User");

        assertThat(RoleUserActionUserTypeEnums.VO_ACTION_USER_TYPE_2)
            .extracting(
                RoleUserActionUserTypeEnums::getCode,
                RoleUserActionUserTypeEnums::getDescriptionCN,
                RoleUserActionUserTypeEnums::getDescriptionEN
            )
            .containsExactly("VO_ACTION_USER_TYPE_2", "媒体用户", "Media User");

        assertThat(RoleUserActionUserTypeEnums.VO_ACTION_USER_TYPE_3)
            .extracting(
                RoleUserActionUserTypeEnums::getCode,
                RoleUserActionUserTypeEnums::getDescriptionCN,
                RoleUserActionUserTypeEnums::getDescriptionEN
            )
            .containsExactly("VO_ACTION_USER_TYPE_3", "观众用户", "Audience User");
    }

    @Test
    void testGetByCode_ReturnsCorrectEnum() {
        assertThat(RoleUserActionUserTypeEnums.getByCode("VO_ACTION_USER_TYPE_1"))
            .isEqualTo(RoleUserActionUserTypeEnums.VO_ACTION_USER_TYPE_1);

        assertThat(RoleUserActionUserTypeEnums.getByCode("VO_ACTION_USER_TYPE_2"))
            .isEqualTo(RoleUserActionUserTypeEnums.VO_ACTION_USER_TYPE_2);

        assertThat(RoleUserActionUserTypeEnums.getByCode("VO_ACTION_USER_TYPE_3"))
            .isEqualTo(RoleUserActionUserTypeEnums.VO_ACTION_USER_TYPE_3);
    }

    @Test
    void testGetByCode_ReturnsNullWhenNotFound() {
        assertThat(RoleUserActionUserTypeEnums.getByCode("UNKNOWN_CODE")).isNull();
    }

    @Test
    void testGetByDescriptionCN_ReturnsCorrectEnum() {
        assertThat(RoleUserActionUserTypeEnums.getByDescriptionCN("主办方用户"))
            .isEqualTo(RoleUserActionUserTypeEnums.VO_ACTION_USER_TYPE_1);

        assertThat(RoleUserActionUserTypeEnums.getByDescriptionCN("媒体用户"))
            .isEqualTo(RoleUserActionUserTypeEnums.VO_ACTION_USER_TYPE_2);

        assertThat(RoleUserActionUserTypeEnums.getByDescriptionCN("观众用户"))
            .isEqualTo(RoleUserActionUserTypeEnums.VO_ACTION_USER_TYPE_3);
    }

    @Test
    void testGetByDescriptionCN_ReturnsNullWhenNotFound() {
        assertThat(RoleUserActionUserTypeEnums.getByDescriptionCN("未知描述")).isNull();
    }
}
