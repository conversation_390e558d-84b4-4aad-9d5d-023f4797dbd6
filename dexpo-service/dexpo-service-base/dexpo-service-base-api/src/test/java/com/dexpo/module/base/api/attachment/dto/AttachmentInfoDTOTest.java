package com.dexpo.module.base.api.attachment.dto;

import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class AttachmentInfoDTOTest {

    @Test
    void testGetterSetter() {
        AttachmentInfoDTO dto = new AttachmentInfoDTO();
        List<Long> idList = Collections.singletonList(2L);

        dto.setId(1L);
        dto.setIdList(idList);
        dto.setAttachmentName("test.txt");
        dto.setAttachmentType("text/plain");
        dto.setAttachmentSize(1024L);
        dto.setAttachmentPath("/path/to/file");

        assertNotNull(dto);
        assertEquals(1L, dto.getId());
        assertEquals(idList, dto.getIdList());
        assertEquals("test.txt", dto.getAttachmentName());
        assertEquals("text/plain", dto.getAttachmentType());
        assertEquals(1024L, dto.getAttachmentSize());
        assertEquals("/path/to/file", dto.getAttachmentPath());
    }
} 