package com.dexpo.module.base.enums;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link BasicRegionLevelEnums} 的单元测试类
 * 
 * <p>测试行政区域级别枚举的所有功能，确保100%代码覆盖率。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class BasicRegionLevelEnumsTest {

    /**
     * 测试所有枚举值的基本属性
     */
    @Test
    void testAllEnumValues() {
        BasicRegionLevelEnums[] values = BasicRegionLevelEnums.values();
        
        // 验证枚举数量
        assertEquals(5, values.length);
        
        // 验证每个枚举值都有完整的属性
        for (BasicRegionLevelEnums level : values) {
            assertNotNull(level.getCode(), "枚举编码不能为空");
            assertNotNull(level.getDescriptionCN(), "中文描述不能为空");
            assertNotNull(level.getDescriptionEN(), "英文描述不能为空");
            assertFalse(level.getCode().isEmpty(), "枚举编码不能为空字符串");
            assertFalse(level.getDescriptionCN().isEmpty(), "中文描述不能为空字符串");
            assertFalse(level.getDescriptionEN().isEmpty(), "英文描述不能为空字符串");
        }
    }

    /**
     * 测试特定枚举值的属性
     */
    @Test
    void testSpecificEnumValues() {
        // 测试国家级别
        assertEquals("country", BasicRegionLevelEnums.LEVEL_COUNTRY.getCode());
        assertEquals("国家", BasicRegionLevelEnums.LEVEL_COUNTRY.getDescriptionCN());
        assertEquals("country", BasicRegionLevelEnums.LEVEL_COUNTRY.getDescriptionEN());
        
        // 测试省级别
        assertEquals("province", BasicRegionLevelEnums.LEVEL_PROVINCE.getCode());
        assertEquals("省", BasicRegionLevelEnums.LEVEL_PROVINCE.getDescriptionCN());
        assertEquals("province", BasicRegionLevelEnums.LEVEL_PROVINCE.getDescriptionEN());
        
        // 测试市级别
        assertEquals("city", BasicRegionLevelEnums.LEVEL_CITY.getCode());
        assertEquals("市", BasicRegionLevelEnums.LEVEL_CITY.getDescriptionCN());
        assertEquals("city", BasicRegionLevelEnums.LEVEL_CITY.getDescriptionEN());
        
        // 测试区级别
        assertEquals("district", BasicRegionLevelEnums.LEVEL_DISTRICT.getCode());
        assertEquals("区", BasicRegionLevelEnums.LEVEL_DISTRICT.getDescriptionCN());
        assertEquals("district", BasicRegionLevelEnums.LEVEL_DISTRICT.getDescriptionEN());
        
        // 测试街道级别
        assertEquals("street", BasicRegionLevelEnums.LEVEL_STREET.getCode());
        assertEquals("街道", BasicRegionLevelEnums.LEVEL_STREET.getDescriptionCN());
        assertEquals("street", BasicRegionLevelEnums.LEVEL_STREET.getDescriptionEN());
    }

    /**
     * 提供测试数据：根据编码获取枚举的测试用例
     */
    static Stream<Arguments> provideGetByCodeTestData() {
        return Stream.of(
            Arguments.of("country", BasicRegionLevelEnums.LEVEL_COUNTRY),
            Arguments.of("province", BasicRegionLevelEnums.LEVEL_PROVINCE),
            Arguments.of("city", BasicRegionLevelEnums.LEVEL_CITY),
            Arguments.of("district", BasicRegionLevelEnums.LEVEL_DISTRICT),
            Arguments.of("street", BasicRegionLevelEnums.LEVEL_STREET),
            Arguments.of("invalid", null),
            Arguments.of("", null),
            Arguments.of(null, null)
        );
    }

    /**
     * 参数化测试：根据编码获取枚举
     */
    @ParameterizedTest
    @MethodSource("provideGetByCodeTestData")
    void testGetByCode(String code, BasicRegionLevelEnums expected) {
        assertEquals(expected, BasicRegionLevelEnums.getByCode(code));
    }

    /**
     * 提供测试数据：根据中文描述获取枚举的测试用例
     */
    static Stream<Arguments> provideGetByDescriptionCNTestData() {
        return Stream.of(
            Arguments.of("国家", BasicRegionLevelEnums.LEVEL_COUNTRY),
            Arguments.of("省", BasicRegionLevelEnums.LEVEL_PROVINCE),
            Arguments.of("市", BasicRegionLevelEnums.LEVEL_CITY),
            Arguments.of("区", BasicRegionLevelEnums.LEVEL_DISTRICT),
            Arguments.of("街道", BasicRegionLevelEnums.LEVEL_STREET),
            Arguments.of("无效描述", null),
            Arguments.of("", null),
            Arguments.of(null, null)
        );
    }

    /**
     * 参数化测试：根据中文描述获取枚举
     */
    @ParameterizedTest
    @MethodSource("provideGetByDescriptionCNTestData")
    void testGetByDescriptionCN(String descriptionCN, BasicRegionLevelEnums expected) {
        assertEquals(expected, BasicRegionLevelEnums.getByDescriptionCN(descriptionCN));
    }

    /**
     * 测试枚举的唯一性
     */
    @Test
    void testEnumUniqueness() {
        BasicRegionLevelEnums[] values = BasicRegionLevelEnums.values();
        
        // 测试编码唯一性
        long uniqueCodeCount = Stream.of(values)
                .map(BasicRegionLevelEnums::getCode)
                .distinct()
                .count();
        assertEquals(values.length, uniqueCodeCount, "所有枚举编码应该是唯一的");
        
        // 测试中文描述唯一性
        long uniqueDescriptionCNCount = Stream.of(values)
                .map(BasicRegionLevelEnums::getDescriptionCN)
                .distinct()
                .count();
        assertEquals(values.length, uniqueDescriptionCNCount, "所有中文描述应该是唯一的");
        
        // 测试英文描述唯一性
        long uniqueDescriptionENCount = Stream.of(values)
                .map(BasicRegionLevelEnums::getDescriptionEN)
                .distinct()
                .count();
        assertEquals(values.length, uniqueDescriptionENCount, "所有英文描述应该是唯一的");
    }

    /**
     * 测试枚举的toString方法
     */
    @Test
    void testToString() {
        for (BasicRegionLevelEnums level : BasicRegionLevelEnums.values()) {
            String toString = level.toString();
            assertNotNull(toString);
            assertFalse(toString.isEmpty());
            assertTrue(toString.startsWith("LEVEL_"));
        }
    }

    /**
     * 测试枚举的name方法
     */
    @Test
    void testName() {
        assertEquals("LEVEL_COUNTRY", BasicRegionLevelEnums.LEVEL_COUNTRY.name());
        assertEquals("LEVEL_PROVINCE", BasicRegionLevelEnums.LEVEL_PROVINCE.name());
        assertEquals("LEVEL_CITY", BasicRegionLevelEnums.LEVEL_CITY.name());
        assertEquals("LEVEL_DISTRICT", BasicRegionLevelEnums.LEVEL_DISTRICT.name());
        assertEquals("LEVEL_STREET", BasicRegionLevelEnums.LEVEL_STREET.name());
    }

    /**
     * 测试枚举的ordinal方法
     */
    @Test
    void testOrdinal() {
        assertEquals(0, BasicRegionLevelEnums.LEVEL_COUNTRY.ordinal());
        assertEquals(1, BasicRegionLevelEnums.LEVEL_PROVINCE.ordinal());
        assertEquals(2, BasicRegionLevelEnums.LEVEL_CITY.ordinal());
        assertEquals(3, BasicRegionLevelEnums.LEVEL_DISTRICT.ordinal());
        assertEquals(4, BasicRegionLevelEnums.LEVEL_STREET.ordinal());
    }

    /**
     * 测试valueOf方法
     */
    @Test
    void testValueOf() {
        assertEquals(BasicRegionLevelEnums.LEVEL_COUNTRY, 
                     BasicRegionLevelEnums.valueOf("LEVEL_COUNTRY"));
        assertEquals(BasicRegionLevelEnums.LEVEL_PROVINCE, 
                     BasicRegionLevelEnums.valueOf("LEVEL_PROVINCE"));
        assertEquals(BasicRegionLevelEnums.LEVEL_CITY, 
                     BasicRegionLevelEnums.valueOf("LEVEL_CITY"));
        assertEquals(BasicRegionLevelEnums.LEVEL_DISTRICT, 
                     BasicRegionLevelEnums.valueOf("LEVEL_DISTRICT"));
        assertEquals(BasicRegionLevelEnums.LEVEL_STREET, 
                     BasicRegionLevelEnums.valueOf("LEVEL_STREET"));
        
        // 测试无效值
        assertThrows(IllegalArgumentException.class, 
                     () -> BasicRegionLevelEnums.valueOf("INVALID"));
    }

    /**
     * 测试构造函数覆盖
     */
    @Test
    void testConstructor() {
        // 通过反射验证构造函数的正确性
        BasicRegionLevelEnums level = BasicRegionLevelEnums.LEVEL_COUNTRY;
        assertNotNull(level);
        assertEquals("country", level.getCode());
        assertEquals("国家", level.getDescriptionCN());
        assertEquals("country", level.getDescriptionEN());
    }
}
