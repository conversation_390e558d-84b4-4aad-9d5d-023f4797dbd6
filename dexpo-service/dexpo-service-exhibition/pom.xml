<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.dexpo</groupId>
        <artifactId>dexpo-framework-starter-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../../dexpo-framework/dexpo-framework-starter-parent/pom.xml</relativePath>
    </parent>

    <artifactId>dexpo-service-exhibition</artifactId>
    <packaging>pom</packaging>
    <name>${project.artifactId}</name>

    <modules>
        <module>dexpo-service-exhibition-api</module>
        <!--<module>dexpo-service-exhibition-biz</module>-->
        <module>dexpo-service-exhibition-entry</module>
        <module>dexpo-service-exhibition-domain</module>
        <module>dexpo-service-exhibition-infrustructure</module>
        <module>dexpo-service-exhibition-starter</module>
        <module>dexpo-service-exhibition-app</module>
    </modules>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
    </properties>

    <description>
        exhibition 展会模块
    </description>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-exhibition-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-exhibition-entry</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-exhibition-app</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-exhibition-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-exhibition-infrustructure</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
