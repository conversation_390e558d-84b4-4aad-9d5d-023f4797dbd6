/*
package com.dexpo.module.exhibition.service.impl;

import com.dexpo.framework.cache.redis.constant.ExhibitionRedisKey;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.module.exhibition.dal.dataobject.ExhibitionRegisterScheduleDO;
import com.dexpo.module.exhibition.dal.mysql.ExhibitionRegisterScheduleMapper;
import com.dexpo.framework.cache.redis.entity.exhibition.ExhibitionRegisterScheduleCache;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

*/
/**
 * 展会注册时间安排 Service 实现类单元测试
 *//*

@ExtendWith(MockitoExtension.class)
class ExhibitionRegisterScheduleServiceImplTest {

    @Mock
    private RedisService redisService;

    @Mock
    private ExhibitionRegisterScheduleMapper exhibitionRegisterScheduleMapper;

    @Spy
    @InjectMocks
    private ExhibitionRegisterScheduleServiceImpl exhibitionRegisterScheduleService;

    private static final String TEST_TAG_CODE = "TEST_TAG";
    private static final String TEST_MEMBER_TYPE = "MEDIA";
    private static final String TEST_EXHIBITION_CODE = "TEST_EXHIBITION";

    @BeforeEach
    void setUp() {
        // 设置 baseMapper
        ReflectionTestUtils.setField(exhibitionRegisterScheduleService, "baseMapper", exhibitionRegisterScheduleMapper);
    }

    @Test
    @DisplayName("测试获取展会编码 - 正常场景：从缓存中获取")
    void testGetScheduleTimeFromCache() {
        // 准备测试数据
        List<ExhibitionRegisterScheduleCache> cacheList = new ArrayList<>();
        ExhibitionRegisterScheduleCache cache = new ExhibitionRegisterScheduleCache();
        cache.setExhibitionTagCode(TEST_TAG_CODE);
        cache.setMemberType(TEST_MEMBER_TYPE);
        cache.setExhibitionCode(TEST_EXHIBITION_CODE);
        cache.setRegisterBeginTime(LocalDateTime.now().minusDays(1));
        cache.setRegisterEndTime(LocalDateTime.now().plusDays(1));
        cacheList.add(cache);

        // 模拟缓存数据
        when(redisService.getCacheObject(ExhibitionRedisKey.EXHIBITION_INFO_SCHEDULE_BY_CODE))
                .thenReturn(cacheList);

        // 执行测试
        String result = exhibitionRegisterScheduleService.getScheduleTime(TEST_TAG_CODE, TEST_MEMBER_TYPE);

        // 验证结果
        assertEquals(TEST_EXHIBITION_CODE, result);

        // 验证调用
        verify(redisService).getCacheObject(ExhibitionRedisKey.EXHIBITION_INFO_SCHEDULE_BY_CODE);
        verify(exhibitionRegisterScheduleMapper, never()).selectList(any());
    }

    @Test
    @DisplayName("测试获取展会编码 - 正常场景：缓存为空，从数据库获取")
    void testGetScheduleTimeFromDatabase() {
        // 准备测试数据
        List<ExhibitionRegisterScheduleDO> scheduleList = new ArrayList<>();
        ExhibitionRegisterScheduleDO schedule = new ExhibitionRegisterScheduleDO();
        schedule.setExhibitionTagCode(TEST_TAG_CODE);
        schedule.setMemberType(TEST_MEMBER_TYPE);
        schedule.setExhibitionCode(TEST_EXHIBITION_CODE);
        schedule.setRegisterBeginTime(LocalDateTime.now().minusDays(1));
        schedule.setRegisterEndTime(LocalDateTime.now().plusDays(1));
        scheduleList.add(schedule);

        List<ExhibitionRegisterScheduleCache> cacheList = new ArrayList<>();
        ExhibitionRegisterScheduleCache cache = new ExhibitionRegisterScheduleCache();
        cache.setExhibitionTagCode(TEST_TAG_CODE);
        cache.setMemberType(TEST_MEMBER_TYPE);
        cache.setExhibitionCode(TEST_EXHIBITION_CODE);
        cache.setRegisterBeginTime(LocalDateTime.now().minusDays(1));
        cache.setRegisterEndTime(LocalDateTime.now().plusDays(1));
        cacheList.add(cache);

        // 模拟缓存为空
        when(redisService.getCacheObject(ExhibitionRedisKey.EXHIBITION_INFO_SCHEDULE_BY_CODE))
                .thenReturn(null);

        // 模拟数据库查询
        when(exhibitionRegisterScheduleMapper.selectList(any())).thenReturn(scheduleList);

        // 执行测试
        String result = exhibitionRegisterScheduleService.getScheduleTime(TEST_TAG_CODE, TEST_MEMBER_TYPE);

        // 验证结果
        assertEquals(TEST_EXHIBITION_CODE, result);

        // 验证调用
        verify(redisService).getCacheObject(ExhibitionRedisKey.EXHIBITION_INFO_SCHEDULE_BY_CODE);
        verify(exhibitionRegisterScheduleMapper).selectList(any());
        verify(redisService).setCacheObject(eq(ExhibitionRedisKey.EXHIBITION_INFO_SCHEDULE_BY_CODE), any());
    }

    @Test
    @DisplayName("测试获取展会编码 - 异常场景：注册时间已过期")
    void testGetScheduleTimeWithExpiredTime() {
        // 准备测试数据
        List<ExhibitionRegisterScheduleCache> cacheList = new ArrayList<>();
        ExhibitionRegisterScheduleCache cache = new ExhibitionRegisterScheduleCache();
        cache.setExhibitionTagCode(TEST_TAG_CODE);
        cache.setMemberType(TEST_MEMBER_TYPE);
        cache.setExhibitionCode(TEST_EXHIBITION_CODE);
        cache.setRegisterBeginTime(LocalDateTime.now().minusDays(2));
        cache.setRegisterEndTime(LocalDateTime.now().minusDays(1));
        cacheList.add(cache);

        // 模拟缓存数据
        when(redisService.getCacheObject(ExhibitionRedisKey.EXHIBITION_INFO_SCHEDULE_BY_CODE))
                .thenReturn(cacheList);

        // 执行测试
        String result = exhibitionRegisterScheduleService.getScheduleTime(TEST_TAG_CODE, TEST_MEMBER_TYPE);

        // 验证结果
        assertNull(result);

        // 验证调用
        verify(redisService).getCacheObject(ExhibitionRedisKey.EXHIBITION_INFO_SCHEDULE_BY_CODE);
    }

    @Test
    @DisplayName("测试获取展会编码 - 异常场景：注册时间未开始")
    void testGetScheduleTimeWithNotStartedTime() {
        // 准备测试数据
        List<ExhibitionRegisterScheduleCache> cacheList = new ArrayList<>();
        ExhibitionRegisterScheduleCache cache = new ExhibitionRegisterScheduleCache();
        cache.setExhibitionTagCode(TEST_TAG_CODE);
        cache.setMemberType(TEST_MEMBER_TYPE);
        cache.setExhibitionCode(TEST_EXHIBITION_CODE);
        cache.setRegisterBeginTime(LocalDateTime.now().plusDays(1));
        cache.setRegisterEndTime(LocalDateTime.now().plusDays(2));
        cacheList.add(cache);

        // 模拟缓存数据
        when(redisService.getCacheObject(ExhibitionRedisKey.EXHIBITION_INFO_SCHEDULE_BY_CODE))
                .thenReturn(cacheList);

        // 执行测试
        String result = exhibitionRegisterScheduleService.getScheduleTime(TEST_TAG_CODE, TEST_MEMBER_TYPE);

        // 验证结果
        assertNull(result);

        // 验证调用
        verify(redisService).getCacheObject(ExhibitionRedisKey.EXHIBITION_INFO_SCHEDULE_BY_CODE);
    }

    @Test
    @DisplayName("测试获取展会编码 - 异常场景：缓存服务异常")
    void testGetScheduleTimeWithRedisException() {
        // 模拟缓存服务抛出异常
        when(redisService.getCacheObject(ExhibitionRedisKey.EXHIBITION_INFO_SCHEDULE_BY_CODE))
                .thenThrow(new RuntimeException("Redis error"));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () ->
            exhibitionRegisterScheduleService.getScheduleTime(TEST_TAG_CODE, TEST_MEMBER_TYPE)
        );

        // 验证调用
        verify(redisService).getCacheObject(ExhibitionRedisKey.EXHIBITION_INFO_SCHEDULE_BY_CODE);
    }

    @Test
    @DisplayName("测试初始化展会注册时间缓存 - 正常场景")
    void testInitExhibitionRegisterScheduleCacheSuccess() {
        // 准备测试数据
        List<ExhibitionRegisterScheduleDO> scheduleList = new ArrayList<>();
        ExhibitionRegisterScheduleDO schedule = new ExhibitionRegisterScheduleDO();
        schedule.setExhibitionTagCode(TEST_TAG_CODE);
        schedule.setMemberType(TEST_MEMBER_TYPE);
        schedule.setExhibitionCode(TEST_EXHIBITION_CODE);
        schedule.setRegisterBeginTime(LocalDateTime.now().minusDays(1));
        schedule.setRegisterEndTime(LocalDateTime.now().plusDays(1));
        scheduleList.add(schedule);

        // 模拟数据库查询
        when(exhibitionRegisterScheduleMapper.selectList(any())).thenReturn(scheduleList);

        // 执行测试
        List<ExhibitionRegisterScheduleCache> result = exhibitionRegisterScheduleService.initExhibitionRegisterScheduleCache();

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(TEST_EXHIBITION_CODE, result.get(0).getExhibitionCode());

        // 验证调用
        verify(exhibitionRegisterScheduleMapper).selectList(any());
        verify(redisService).setCacheObject(eq(ExhibitionRedisKey.EXHIBITION_INFO_SCHEDULE_BY_CODE), any());
    }

    @Test
    @DisplayName("测试初始化展会注册时间缓存 - 异常场景：数据库为空")
    void testInitExhibitionRegisterScheduleCacheWithEmptyDatabase() {
        // 模拟数据库查询返回空列表
        when(exhibitionRegisterScheduleMapper.selectList(any())).thenReturn(Collections.emptyList());

        // 执行测试
        List<ExhibitionRegisterScheduleCache> result = exhibitionRegisterScheduleService.initExhibitionRegisterScheduleCache();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证调用
        verify(exhibitionRegisterScheduleMapper).selectList(any());
        verify(redisService).setCacheObject(eq(ExhibitionRedisKey.EXHIBITION_INFO_SCHEDULE_BY_CODE), eq(Collections.emptyList()));
    }

    @Test
    @DisplayName("测试初始化展会注册时间缓存 - 异常场景：数据库异常")
    void testInitExhibitionRegisterScheduleCacheWithDatabaseException() {
        // 模拟数据库查询抛出异常
        when(exhibitionRegisterScheduleMapper.selectList(any()))
                .thenThrow(new RuntimeException("Database error"));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () ->
            exhibitionRegisterScheduleService.initExhibitionRegisterScheduleCache()
        );

        // 验证调用
        verify(exhibitionRegisterScheduleMapper).selectList(any());
        verify(redisService, never()).setCacheObject(any(), any());
    }

    @Test
    @DisplayName("测试初始化展会注册时间缓存 - 异常场景：Redis异常")
    void testInitExhibitionRegisterScheduleCacheWithRedisException() {
        // 准备测试数据
        List<ExhibitionRegisterScheduleDO> scheduleList = new ArrayList<>();
        ExhibitionRegisterScheduleDO schedule = new ExhibitionRegisterScheduleDO();
        schedule.setExhibitionTagCode(TEST_TAG_CODE);
        schedule.setMemberType(TEST_MEMBER_TYPE);
        schedule.setExhibitionCode(TEST_EXHIBITION_CODE);
        scheduleList.add(schedule);

        // 模拟数据库查询
        when(exhibitionRegisterScheduleMapper.selectList(any())).thenReturn(scheduleList);

        // 模拟Redis服务抛出异常
        doThrow(new RuntimeException("Redis error"))
                .when(redisService).setCacheObject(any(), any());

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () ->
            exhibitionRegisterScheduleService.initExhibitionRegisterScheduleCache()
        );

        // 验证调用
        verify(exhibitionRegisterScheduleMapper).selectList(any());
        verify(redisService).setCacheObject(eq(ExhibitionRedisKey.EXHIBITION_INFO_SCHEDULE_BY_CODE), any());
    }
} */
