spring:
  application:
    name: dexpo-service-exhibition
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Feign 等会存在重复定义的服务
  profiles:
    active: ${ENV:local}
  cloud:
    kubernetes:
      discovery:
        # 让所有命名空间服务都可以发现服务
        namespaces:
          - ${ENV}
        # 发现未标记为“就绪”的服务端地址
        include-not-ready-addresses: true
        # ExternalName类型服务的列表  DiscoveryClient::getInstances 返回该列表 ServiceInstance::getMetadata
        include-external-name-services: true
        enabled: true
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER # 解决 SpringFox 与 SpringBoot 2.6.x 不兼容的问题，参见 SpringFoxHandlerProviderBeanPostProcessor 类

  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 LocalDateTime 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 **********.401，而是直接 **********401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean

  # Cache 配置项
  cache:
    type: REDIS
    redis:
      time-to-live: 1h # 设置过期时间为 1 小时

  neo4j:
    pool:
      metrics-enabled: false

--- #################### 接口文档配置 ####################

springdoc:
  api-docs:
    enabled: true # 1. 是否开启 Swagger 接文档的元数据
    path: /v3/api-docs
  swagger-ui:
    enabled: true # 2.1 是否开启 Swagger 文档的官方 UI 界面
    path: /swagger-ui.html
  default-flat-param-object: true # 参见 https://doc.xiaominfo.com/docs/faq/v4/knife4j-parameterobject-flat-param 文档

knife4j:
  enable: true # 2.2 是否开启 Swagger 文档的 Knife4j UI 界面
  setting:
    language: zh_cn

# MyBatis Plus 的配置项
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # 开启SQL日志打印
  global-config:
    db-config:
      # 重要说明：如果将配置放到 Nacos 时，请注意将 id-type 设置为对应 DB 的类型，否则会报错；详细见 https://gitee.com/zhijiantianya/dtt-cloud/issues/I5W2N0 讨论
      id-type: NONE # “智能”模式，基于 IdTypeEnvironmentPostProcessor + 数据源的类型，自动适配成 AUTO、INPUT 模式。
#      id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库
#      id-type: INPUT # 用户输入 ID，适合 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库
#      id-type: ASSIGN_ID # 分配 ID，默认使用雪花算法。注意，Oracle、PostgreSQL、Kingbase、DB2、H2 数据库时，需要去除实体类上的 @KeySequence 注解
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
  type-aliases-package: ${dexpo.info.base-package}.dal.dataobject

--- #################### RR低代码相关配置 ####################

dexpo:
  info:
    version: 1.0.0
    base-package: com.dexpo.module.exhibition
  web:
    admin-ui:
      url: http://dashboard.roselife.com # Admin 管理后台 UI 的地址
  swagger:
    title: exhibition-service
    description: exhibition api
    version: ${dexpo.info.version}
  codegen:
    base-package: com.dexpo
    db-schemas: ${spring.datasource.dynamic.datasource.master.name}
    front-type: 10 # 前端模版的类型，参见 CodegenFrontTypeEnum 枚举类
  error-code: # 错误码相关配置项
    constants-class-list:
      - com.dexpo.module.infra.enums.ErrorCodeConstants
  tenant: # 多租户相关配置项
    enable: true
    ignore-urls:
      - /admin-api/infra/file/*/get/** # 获取图片，和租户无关
    ignore-tables:
      - infra_codegen_column
      - infra_codegen_table
      - infra_test_demo
      - infra_config
      - infra_file_config
      - infra_file
      - infra_file_content
      - infra_job
      - infra_job_log
      - infra_job_log
      - infra_data_source_config
  metrics:
    enable: false
debug:

