spring:
  application:
    name: dexpo-service-exhibition
  cloud:
    kubernetes:
      config:
        name: ${spring.application.name}
        namespace: ${ENV:local}
        includeProfileSpecificSources: false
        useNameAsPrefix: false
        sources:
          - name: ${spring.application.name}
      reload:
        enabled: true
        mode: polling
        period: 5000
        strategy: REFRESH
server:
  port: 48084