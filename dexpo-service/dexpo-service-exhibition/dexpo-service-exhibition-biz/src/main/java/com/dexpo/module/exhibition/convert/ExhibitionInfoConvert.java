package com.dexpo.module.exhibition.convert;

import com.dexpo.framework.common.pojo.IConvert;
import com.dexpo.module.exhibition.api.vo.ExhibitionSessionVO;
import com.dexpo.module.exhibition.api.vo.ExhibitionVO;
import com.dexpo.module.exhibition.dal.dataobject.ExhibitionInfoDO;
import com.dexpo.framework.cache.redis.entity.exhibition.ExhibitionInfoCache;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 展会信息 Convert
 */
@Mapper
public interface ExhibitionInfoConvert extends IConvert<String, ExhibitionVO, ExhibitionInfoDO> {

    ExhibitionInfoConvert INSTANCE = Mappers.getMapper(ExhibitionInfoConvert.class);

    ExhibitionSessionVO toExhibitionSession(ExhibitionInfoCache infoDO);


    List<ExhibitionSessionVO> toExhibitionSession(List<ExhibitionInfoCache> infoDOS);

    ExhibitionInfoCache toCache(ExhibitionInfoDO infoDO);

    List<ExhibitionInfoCache> toCacheList(List<ExhibitionInfoDO> list);


    ExhibitionVO cache2vo(ExhibitionInfoCache cache);


} 