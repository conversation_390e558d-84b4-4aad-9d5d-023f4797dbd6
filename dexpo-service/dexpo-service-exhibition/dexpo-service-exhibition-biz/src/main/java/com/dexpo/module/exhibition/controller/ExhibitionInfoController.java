package com.dexpo.module.exhibition.controller;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.exhibition.api.ExhibitionApi;
import com.dexpo.module.exhibition.api.dto.ExhibitionQueryDTO;
import com.dexpo.module.exhibition.api.dto.ExhibitionTagCodeQueryDTO;
import com.dexpo.module.exhibition.api.vo.ExhibitionVO;
import com.dexpo.framework.common.enums.ValueSetUserTypeEnum;
import com.dexpo.module.exhibition.service.ExhibitionInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 展会信息 Controller
 */
@Tag(name = "展会信息")
@RestController
@RequiredArgsConstructor
public class ExhibitionInfoController implements ExhibitionApi {

    private final ExhibitionInfoService exhibitionInfoService;


    @Override
    @Operation(summary = "媒体入口获取展会信息")
    public CommonResult<ExhibitionVO> getExhibitionByMedia(@Valid @RequestBody ExhibitionTagCodeQueryDTO req) {
        ExhibitionVO exhibitionVO = exhibitionInfoService.getExhibitionInfo(req.getExhibitionTagCode(), ValueSetUserTypeEnum.MEDIA);
        return CommonResult.success(exhibitionVO);
    }


    @Override
    public CommonResult<List<ExhibitionVO>> getExhibitionList(@Valid @RequestBody ExhibitionQueryDTO req) {
        List<ExhibitionVO> result = exhibitionInfoService.getExhibitionList(req);
        return CommonResult.success(result);
    }


    @Override
    public CommonResult<ExhibitionVO> getExhibitionById(@RequestParam("exhibitionId") Long exhibitionId) {
        ExhibitionVO exhibitionVO = exhibitionInfoService.getExhibitionById(exhibitionId);
        return CommonResult.success(exhibitionVO);
    }
}