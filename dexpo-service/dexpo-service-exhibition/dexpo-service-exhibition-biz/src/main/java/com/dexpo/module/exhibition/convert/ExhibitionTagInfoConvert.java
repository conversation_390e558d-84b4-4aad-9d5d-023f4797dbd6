package com.dexpo.module.exhibition.convert;

import com.dexpo.framework.cache.redis.entity.exhibition.ExhibitionTagInfoCache;
import com.dexpo.framework.common.pojo.IConvert;
import com.dexpo.module.exhibition.api.vo.ExhibitionTagVO;
import com.dexpo.module.exhibition.dal.dataobject.ExhibitionTagInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 展会tag信息 Convert
 */
@Mapper
public interface ExhibitionTagInfoConvert extends IConvert<String, ExhibitionTagVO, ExhibitionTagInfoDO> {

    ExhibitionTagInfoConvert INSTANCE = Mappers.getMapper(ExhibitionTagInfoConvert.class);

    /**
     * 转换为缓存对象
     *
     * @param dos
     * @return
     */
    ExhibitionTagInfoCache e2Cache(ExhibitionTagInfoDO dos);

    /**
     * 转换为缓存对象列表
     *
     * @param dos
     * @return
     */
    List<ExhibitionTagInfoCache> e2Cache(List<ExhibitionTagInfoDO> dos);


    /**
     * 缓存转换为vo
     *
     * @param cache
     * @return
     */
    ExhibitionTagVO cache2VO(ExhibitionTagInfoCache cache);

    /**
     * 缓存转换为vo
     *
     * @param caches
     * @return
     */
    List<ExhibitionTagVO> cache2VO(List<ExhibitionTagInfoCache> caches);

}