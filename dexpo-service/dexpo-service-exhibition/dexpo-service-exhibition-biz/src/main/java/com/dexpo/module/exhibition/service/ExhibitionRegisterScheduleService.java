package com.dexpo.module.exhibition.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dexpo.module.exhibition.dal.dataobject.ExhibitionRegisterScheduleDO;
import com.dexpo.framework.cache.redis.entity.exhibition.ExhibitionRegisterScheduleCache;

import java.util.List;
import java.util.Map;

/**
 * 展会注册时间安排 Service 接口
 */
public interface ExhibitionRegisterScheduleService extends IService<ExhibitionRegisterScheduleDO> {

    /**
     * 根据 tagCode 获取会展code
     * 暂时没有使用
     *
     * @param tagCode    tag code
     * @param memberType 用户类型
     * @return 会展code
     */
    ExhibitionRegisterScheduleCache getScheduleTime(String tagCode, String memberType);


    /**
     * 初始化展会注册时间安排缓存
     * initExhibitionRegisterScheduleCache
     *
     * @return 缓存列表
     */
    Map<String, List<ExhibitionRegisterScheduleCache>> initExhibitionRegisterScheduleCache();
}