package com.dexpo.module.exhibition.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dexpo.framework.cache.redis.constant.ExhibitionRedisKey;
import com.dexpo.framework.cache.redis.constant.ICacheKey;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.framework.common.enums.LanguageEnum;
import com.dexpo.module.exhibition.api.dto.AgreementQueryDTO;
import com.dexpo.module.exhibition.api.vo.AgreementSimpleVO;
import com.dexpo.module.exhibition.convert.ExhibitionAgreementConvert;
import com.dexpo.module.exhibition.dal.dataobject.ExhibitionAgreementInfoDO;
import com.dexpo.module.exhibition.dal.mysql.ExhibitionAgreementInfoMapper;
import com.dexpo.module.exhibition.enums.EffectiveEnum;
import com.dexpo.framework.common.enums.ValueSetUserTypeEnum;
import com.dexpo.module.exhibition.service.ExhibitionAgreementInfoService;
import com.dexpo.framework.cache.redis.entity.exhibition.ExhibitionAgreementInfoCache;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 展会协议信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ExhibitionAgreementInfoServiceImpl extends
        ServiceImpl<ExhibitionAgreementInfoMapper, ExhibitionAgreementInfoDO>
        implements ExhibitionAgreementInfoService {

    private final RedisService redisService;

    @Override
    public List<AgreementSimpleVO> getAgreementByExhibitionCode(String exhibitionCode, ValueSetUserTypeEnum valuesetUserTypeEnum) {
        // 根据id做缓存 根据id取
        List<ExhibitionAgreementInfoCache> cacheList = redisService
                .getCacheObject(ExhibitionRedisKey.EXHIBITION_AGREEMENT_ALL);
        if (CollectionUtils.isEmpty(cacheList)) {
            cacheList = initExhibitionAgreementInfoCache();
        }
        if (cacheList.isEmpty()) {
            return List.of();
        }
        return cacheList.stream().filter(e -> Objects.equals(e.getExhibitionCode(), exhibitionCode)
                        && Objects.equals(e.getMemberType(), valuesetUserTypeEnum.getOptionCode()))
                .map(ExhibitionAgreementConvert.INSTANCE::cache2Vo)
                .toList();
    }

    @Override
    public String getAgreementContext(AgreementQueryDTO queryDTO) {
        String key = ICacheKey.generateKey(
                ExhibitionRedisKey.EXHIBITION_AGREEMENT_CONTENT_CN, String.valueOf(queryDTO.getId()));
        if (Objects.equals(queryDTO.getLanguage(), LanguageEnum.EN.getCode())) {
            key = ICacheKey.generateKey(
                    ExhibitionRedisKey.EXHIBITION_AGREEMENT_CONTENT_EN, String.valueOf(queryDTO.getId()));
        }
        String context = redisService.getCacheObject(key);
        if (context == null) {
            Map<String, String> cacheMap = initExhibitionAgreementContextCache();
            context = cacheMap.get(key);
        }
        return context;
    }

    @Override
    public List<ExhibitionAgreementInfoCache> initExhibitionAgreementInfoCache() {
        // 查询所有生效的展会协议信息
        List<ExhibitionAgreementInfoDO> list = getEffectiveAgreements();

        // 转换为缓存对象
        List<ExhibitionAgreementInfoCache> cacheList = ExhibitionAgreementConvert.INSTANCE.convertList(list);
        if (!CollectionUtils.isEmpty(cacheList)) {
            redisService.setCacheObject(ExhibitionRedisKey.EXHIBITION_AGREEMENT_ALL, cacheList);
        }
        return cacheList;
    }


    @Override
    public Map<String, String> initExhibitionAgreementContextCache() {
        // 查询所有生效的展会协议信息
        List<ExhibitionAgreementInfoDO> list = getEffectiveAgreements();
        return contextCache(list);
    }


    /**
     * 设置缓存
     *
     * @param list
     */
    Map<String, String> contextCache(List<ExhibitionAgreementInfoDO> list) {
        Map<String, String> cacheMap = Maps.newHashMapWithExpectedSize(list.size() * 2);
        list.forEach(agreement -> {
            String redisKeyCn = ICacheKey.generateKey(ExhibitionRedisKey.EXHIBITION_AGREEMENT_CONTENT_CN,
                    String.valueOf(agreement.getId()));
            String redisKeyEn = ICacheKey.generateKey(ExhibitionRedisKey.EXHIBITION_AGREEMENT_CONTENT_EN,
                    String.valueOf(agreement.getId()));
            redisService.setCacheObject(redisKeyCn, agreement.getAgreementContentCn());
            redisService.setCacheObject(redisKeyEn, agreement.getAgreementContentEn());
            cacheMap.put(redisKeyCn, agreement.getAgreementContentCn());
            cacheMap.put(redisKeyEn, agreement.getAgreementContentEn());
        });
        return cacheMap;
    }

    /**
     * 获取所有生效协议
     *
     * @return do
     */
    private List<ExhibitionAgreementInfoDO> getEffectiveAgreements() {
        return lambdaQuery()
                .eq(ExhibitionAgreementInfoDO::getAgreementStatus, EffectiveEnum.EFFECTIVE.getOptionCode())
                .list();
    }

}