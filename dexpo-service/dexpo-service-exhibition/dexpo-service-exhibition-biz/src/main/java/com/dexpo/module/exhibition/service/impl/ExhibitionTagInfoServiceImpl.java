package com.dexpo.module.exhibition.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dexpo.framework.cache.redis.constant.ExhibitionRedisKey;
import com.dexpo.framework.cache.redis.entity.exhibition.ExhibitionTagInfoCache;
import com.dexpo.framework.cache.redis.entity.sponsor.SponsorProfileCache;
import com.dexpo.framework.cache.redis.operate.member.MemberBaseInfoOpt;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.framework.security.core.util.SecurityFrameworkUtils;
import com.dexpo.module.exhibition.api.vo.ExhibitionTagVO;
import com.dexpo.module.exhibition.convert.ExhibitionTagInfoConvert;
import com.dexpo.module.exhibition.dal.dataobject.ExhibitionTagInfoDO;
import com.dexpo.module.exhibition.dal.mysql.ExhibitionTagInfoMapper;
import com.dexpo.module.exhibition.service.ExhibitionTagInfoService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ExhibitionTagInfoServiceImpl extends ServiceImpl<ExhibitionTagInfoMapper, ExhibitionTagInfoDO>
        implements ExhibitionTagInfoService {

    private final MemberBaseInfoOpt memberBaseInfoOpt;

    private final RedisService redisServer;

    @Override
    public List<ExhibitionTagVO> listAll() {
        List<ExhibitionTagInfoCache> all = getAll();
        return ExhibitionTagInfoConvert.INSTANCE.cache2VO(all);
    }


    @Override
    public List<ExhibitionTagInfoCache> initExhibitionTagCache() {
        List<ExhibitionTagInfoDO> dos = list();
        List<ExhibitionTagInfoCache> caches = ExhibitionTagInfoConvert.INSTANCE.e2Cache(dos);
        redisServer.setCacheObject(ExhibitionRedisKey.EXHIBITION_TAG_INFO_ALL, caches);
        return caches;
    }

    @Override
    public String isLastLevelExhibition() {
        SponsorProfileCache profileCache = memberBaseInfoOpt.sponsorProfile(SecurityFrameworkUtils.getLoginUserId());
        List<ExhibitionTagInfoCache> caches = getAll();
        Optional<ExhibitionTagInfoCache> any = caches.stream()
                .filter(e -> Objects.equals(e.getParentExhibitionTagCode(), profileCache.getExhibitionTagCode()))
                .findAny();
        return any.isPresent() ? null : profileCache.getExhibitionTagCode();

    }

    @Override
    public List<ExhibitionTagVO> listChildTagCode(List<String> tagCodes) {
        if (CollectionUtils.isEmpty(tagCodes)) {
            return List.of();
        }
        List<ExhibitionTagInfoCache> caches = getAll();
        List<ExhibitionTagInfoCache> tagInfoList = caches.stream()
                .filter(e -> tagCodes.contains(e.getExhibitionTagCode()))
                .toList();
        if (CollectionUtils.isEmpty(tagInfoList)) {
            return List.of();
        }

        List<ExhibitionTagVO> result = Lists.newArrayList();
        result.addAll(ExhibitionTagInfoConvert.INSTANCE.cache2VO(tagInfoList));
        Map<String, List<ExhibitionTagInfoCache>> tagParentMap = caches.stream()
                .filter(e -> e.getParentExhibitionTagCode() != null)
                .collect(Collectors.groupingBy(ExhibitionTagInfoCache::getParentExhibitionTagCode));
        // 获取下级的信息
        List<ExhibitionTagInfoCache> children = Lists.newArrayList();
        for (String tagCode : tagCodes) {
            List<ExhibitionTagInfoCache> infoCaches = tagParentMap.get(tagCode);
            if (!CollectionUtils.isEmpty(infoCaches)) {
                children.addAll(infoCaches);
            }
        }
        if (!CollectionUtils.isEmpty(children)) {
            result.addAll(ExhibitionTagInfoConvert.INSTANCE.cache2VO(children));
            // 获取孙子级 最多三级 多了不考虑 所以不使用递归
            List<ExhibitionTagInfoCache> list = children.stream()
                    .map(e -> tagParentMap.get(e.getExhibitionTagCode()))
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .toList();
            if (!CollectionUtils.isEmpty(list)) {
                result.addAll(ExhibitionTagInfoConvert.INSTANCE.cache2VO(list));
            }
        }
        return result;

    }

    /**
     * 获取全部数据
     *
     * @return cache
     */
    private List<ExhibitionTagInfoCache> getAll() {
        List<ExhibitionTagInfoCache> caches = redisServer.getCacheObject(ExhibitionRedisKey.EXHIBITION_TAG_INFO_ALL);
        if (CollectionUtils.isEmpty(caches)) {
            return initExhibitionTagCache();
        }
        return caches;
    }
}