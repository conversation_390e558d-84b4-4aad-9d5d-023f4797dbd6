package com.dexpo.module.exhibition.controller;

import java.util.List;

import com.dexpo.module.exhibition.api.vo.ExhibitionSessionVO;
import com.dexpo.module.exhibition.api.vo.ExhibitionVO;
import org.springframework.web.bind.annotation.RestController;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.exhibition.api.ExhibitionTagApi;
import com.dexpo.module.exhibition.api.dto.ExhibitionTagCodeBatchQueryDTO;
import com.dexpo.module.exhibition.api.vo.ExhibitionTagVO;
import com.dexpo.module.exhibition.service.ExhibitionInfoService;
import com.dexpo.module.exhibition.service.ExhibitionTagInfoService;

import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class ExhibitionTagInfoController implements ExhibitionTagApi {

    private final ExhibitionTagInfoService exhibitionTagInfoService;

    private final ExhibitionInfoService exhibitionInfoService;

    @Override
    public CommonResult<List<ExhibitionTagVO>> getAllExhibitionTag() {
        List<ExhibitionTagVO> voList = exhibitionTagInfoService.listAll();
        return CommonResult.success(voList);
    }

    @Override
    public CommonResult<List<ExhibitionSessionVO>> getExhibitionSession(ExhibitionTagCodeBatchQueryDTO queryDTO) {
        List<ExhibitionSessionVO> sessionList = exhibitionInfoService.getExhibitionSession(queryDTO);
        return CommonResult.success(sessionList);
    }

    @Override
    public CommonResult<ExhibitionVO> isLastLevelExhibition() {
        ExhibitionVO exhibitionVO = exhibitionInfoService.getLastLevelExhibition();
        return CommonResult.success(exhibitionVO);
    }
}