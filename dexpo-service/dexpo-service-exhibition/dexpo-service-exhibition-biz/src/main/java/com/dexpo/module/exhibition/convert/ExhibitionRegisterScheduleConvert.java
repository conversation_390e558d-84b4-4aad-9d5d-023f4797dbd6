package com.dexpo.module.exhibition.convert;

import com.dexpo.module.exhibition.dal.dataobject.ExhibitionRegisterScheduleDO;
import com.dexpo.framework.cache.redis.entity.exhibition.ExhibitionRegisterScheduleCache;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 展会注册时间安排对象转换接口
 */
@Mapper
public interface ExhibitionRegisterScheduleConvert {

    ExhibitionRegisterScheduleConvert INSTANCE = Mappers.getMapper(ExhibitionRegisterScheduleConvert.class);

    /**
     * 将 DO 转换为缓存对象
     * @param schedule DO
     * @return 缓存对象
     */
    ExhibitionRegisterScheduleCache toCache(ExhibitionRegisterScheduleDO schedule);

    /**
     * 将 DO 列表转换为缓存对象列表
     * @param scheduleList DO 列表
     * @return 缓存对象列表
     */
    List<ExhibitionRegisterScheduleCache> toCacheList(List<ExhibitionRegisterScheduleDO> scheduleList);
} 