package com.dexpo.module.exhibition.domain.model.agg;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 展会注册时间安排
 *
 * <AUTHOR> Xiaohua 19/06/2025 17:15
 **/
@Data
public class ExhibitionRegisterSchedule {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 展会CODE
     */
    private String exhibitionCode;

    /**
     * 展会标签：标签表
     */
    private String exhibitionTagCode;

    /**
     * 用户类型：值集VS_ACTION_USER_TYPE
     */
    private String memberType;

    /**
     * 注册开始时间
     */
    private LocalDateTime registerBeginTime;

    /**
     * 注册结束时间
     */
    private LocalDateTime registerEndTime;

    /**
     * 删除标志
     */
    private Boolean delFlg;

    /**
     * 创建人ID
     */
    private Long createUser;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateUser;

    /**
     * 修改人姓名
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;



}
