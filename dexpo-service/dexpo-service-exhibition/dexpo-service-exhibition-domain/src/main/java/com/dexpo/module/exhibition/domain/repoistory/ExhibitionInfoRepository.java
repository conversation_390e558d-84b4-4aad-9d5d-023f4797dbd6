package com.dexpo.module.exhibition.domain.repoistory;

import com.dexpo.module.exhibition.domain.model.agg.ExhibitionInfo;

import java.util.List;

/**
 * 展会信息仓储
 *
 * <AUTHOR> Xiaohua 19/06/2025 16:47
 **/
public interface ExhibitionInfoRepository {

    /**
     * 通过tag 和user Type查询
     *
     * @param tagCode  tagCode
     * @return 展会信息
     */
    ExhibitionInfo getExhibitionInfoByTag(String tagCode);

    /**
     * 通过id查询
     *
     * @param id id
     * @return ExhibitionInfo
     */
    ExhibitionInfo getExhibitionInfoById(Long id);

    /**
     * 查询所有 展会信息
     *
     * @return 展会信息列表
     */
    List<ExhibitionInfo> listAllExhibitionInfo();


    /**
     * 初始化展会列表缓存
     *
     * @return 展会列表
     */
    void initExhibitionCache();

}
