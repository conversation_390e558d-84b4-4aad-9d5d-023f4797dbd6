package com.dexpo.module.exhibition.domain.service;

import com.dexpo.module.exhibition.domain.model.agg.ExhibitionInfo;
import com.dexpo.module.exhibition.domain.repoistory.ExhibitionInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 展会信息领域服务
 *
 * <AUTHOR> Xiaohua 19/06/2025 15:09
 **/
@Service
@RequiredArgsConstructor
public class ExhibitionInfoDomainService {

    private final ExhibitionInfoRepository exhibitionInfoRepository;

    /**
     * 通过tag 和user Type查询
     *
     * @param tagCode  tagCode
     * @return 展会信息
     */
    public ExhibitionInfo getExhibitionInfoByTag(String tagCode) {
        return exhibitionInfoRepository.getExhibitionInfoByTag(tagCode);
    }

    /**
     * 通过id查询
     *
     * @param id id
     * @return ExhibitionInfo
     */
    public ExhibitionInfo getExhibitionInfoById(Long id) {
        return exhibitionInfoRepository.getExhibitionInfoById(id);
    }

    /**
     * 查询所有 展会信息
     *
     * @return 展会信息列表
     */
    public List<ExhibitionInfo> listAllExhibitionInfo() {
        return exhibitionInfoRepository.listAllExhibitionInfo();
    }

    /**
     * 初始化展会列表缓存
     */
    public void initExhibitionCache() {
        exhibitionInfoRepository.initExhibitionCache();
    }
}
