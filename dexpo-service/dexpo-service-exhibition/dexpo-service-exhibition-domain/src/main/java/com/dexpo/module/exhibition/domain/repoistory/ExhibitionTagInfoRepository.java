package com.dexpo.module.exhibition.domain.repoistory;

import com.dexpo.module.exhibition.domain.model.agg.ExhibitionTagInfo;

import java.util.List;

/**
 * 展会标签仓储
 *
 * <AUTHOR> Xiaohua 19/06/2025 21:20
 **/
public interface ExhibitionTagInfoRepository {

    /**
     * 查询所有tag
     *
     * @return tag列表
     */
    List<ExhibitionTagInfo> listAllExhibitionTagInfoList();


    /**
     * 叶子结点
     * @return
     */
    List<ExhibitionTagInfo> getAllExhibitionLeafTag();

}
