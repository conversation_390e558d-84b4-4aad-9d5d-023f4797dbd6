package com.dexpo.module.exhibition.domain.service;

import com.dexpo.module.exhibition.domain.model.agg.ExhibitionTagInfo;
import com.dexpo.module.exhibition.domain.repoistory.ExhibitionTagInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 展会tag领域服务
 *
 * <AUTHOR> Xiaohua 19/06/2025 15:10
 **/
@Service
@RequiredArgsConstructor
public class ExhibitionTagInfoDomainService {

    private final ExhibitionTagInfoRepository exhibitionTagInfoRepository;

    /**
     * 查询所有标签
     *
     * @return 标签列表
     */
    public List<ExhibitionTagInfo> listAllExhibitionTagInfoList() {
        return exhibitionTagInfoRepository.listAllExhibitionTagInfoList();
    }


    /**
     * 查询所有标签
     *
     * @return 标签列表
     */
    public List<ExhibitionTagInfo> getAllExhibitionLeafTag() {
        return exhibitionTagInfoRepository.getAllExhibitionLeafTag();
    }



}
