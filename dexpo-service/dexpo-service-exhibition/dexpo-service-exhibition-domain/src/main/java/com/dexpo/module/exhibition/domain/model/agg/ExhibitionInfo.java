package com.dexpo.module.exhibition.domain.model.agg;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 展会信息聚合根
 *
 * <AUTHOR> Xiaohua 19/06/2025 15:03
 **/
@Data
public class ExhibitionInfo {

    /**
     * id
     */
    private Long id;

    /**
     * 展会CODE
     */
    private String exhibitionCode;

    /**
     * 展会简介
     */
    private String exhibitionAbbreviation;

    /**
     * 展会名称-中文
     */
    private String exhibitionNameCn;

    /**
     * 展会名称-英文
     */
    private String exhibitionNameEn;

    /**
     * 年份：如 2025
     */
    private Integer exhibitionYear;

    /**
     * 展会届数：如 1
     */
    private Integer exhibitionSession;

    /**
     * 展会年份和届数组成的唯一key，如2025_1
     */
    private String exhibitionSessionKey;

    /**
     * 展会标签
     */
    private String exhibitionTagCode;

    /**
     * 展会状态
     */
    private String exhibitionStatus;

    /**
     * 展会LOGO
     */
    private String exhibitionLogo;

    /**
     * 媒体注册状态
     */
    private String mediaScheduleRegisterStatus;

    /**
     * 用户注册状态
     */
    private String audienceScheduleRegisterStatus;

    /**
     * 展会开始时间
     */
    private LocalDateTime exhibitionBeginTime;

    /**
     * 展会结束时间
     */
    private LocalDateTime exhibitionEndTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建者，目前使用 SysUser 的 id 编号
     * <p>
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String createUserName;

    private Long createUser;
    /**
     * 更新者，目前使用 SysUser 的 id 编号
     * <p>
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String updateUserName;

    private Long updateUser;
    /**
     * 是否删除
     */
    private Boolean delFlg;


}
