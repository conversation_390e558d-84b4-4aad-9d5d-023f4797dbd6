package com.dexpo.module.exhibition.app.api;

import com.dexpo.module.exhibition.api.dto.ExhibitionQueryDTO;
import com.dexpo.module.exhibition.api.dto.ExhibitionTagCodeBatchQueryDTO;
import com.dexpo.module.exhibition.api.vo.ExhibitionSessionVO;
import com.dexpo.module.exhibition.api.vo.ExhibitionVO;

import java.util.List;

/**
 * <AUTHOR> Xiaohua 19/06/2025 13:58
 **/
public interface ExhibitionInfoAppService {

    /**
     * 获取展会信息
     * 判断是否在展会期间
     * @param tagCode  tag code
     */
    ExhibitionVO getExhibitionInfo(String tagCode);

    /**
     * 获取展会tag 下对应展会的届数
     *
     * @param queryDTO 展会tag code
     * @return 展会届数
     */
    List<ExhibitionSessionVO> getExhibitionSession(ExhibitionTagCodeBatchQueryDTO queryDTO);

    /**
     * 获取展会信息
     *
     * @param queryDTO 展会tag code
     * @return 展会信息列表
     */
    List<ExhibitionVO> getExhibitionList(ExhibitionQueryDTO queryDTO);

    /**
     * 根据id获取会展信息
     * 先从缓存中获取，如果缓存中没有，则从数据库中获取
     * @param exhibitionId
     * @return
     */
    ExhibitionVO getExhibitionById(Long exhibitionId);

    /**
     * 根据code 获取最新的展会信息
     *
     * @param tagCode tagCode
     * @return ExhibitionVO
     */
    ExhibitionVO getExhibitionByTagCode(String tagCode);

    /**
     * 根据code 获取最新的展会信息
     *
     * @return ExhibitionVO
     */
    ExhibitionVO getCurrentExhibition();

    /**
     * 初始化展会缓存
     * 包含 1. 全部展会、 2. 根据id缓存，3. 根据展会code缓存
     */
    void initExhibitionCache();
}
