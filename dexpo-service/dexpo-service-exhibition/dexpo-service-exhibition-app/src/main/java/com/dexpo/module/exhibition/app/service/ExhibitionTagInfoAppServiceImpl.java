package com.dexpo.module.exhibition.app.service;

import cn.hutool.core.collection.CollUtil;
import com.dexpo.framework.cache.redis.entity.sponsor.SponsorProfileCache;
import com.dexpo.framework.cache.redis.operate.member.MemberBaseInfoOpt;
import com.dexpo.framework.security.core.util.SecurityFrameworkUtils;
import com.dexpo.module.exhibition.api.vo.ExhibitionTagVO;
import com.dexpo.module.exhibition.app.api.ExhibitionTagInfoAppService;
import com.dexpo.module.exhibition.app.converter.ExhibitionTagInfoDTOConvert;
import com.dexpo.module.exhibition.domain.model.agg.ExhibitionTagInfo;
import com.dexpo.module.exhibition.domain.service.ExhibitionTagInfoDomainService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 会展tag 应用层服务
 *
 * <AUTHOR> Xiaohua 19/06/2025 14:55
 **/
@Service
@RequiredArgsConstructor
public class ExhibitionTagInfoAppServiceImpl implements ExhibitionTagInfoAppService {

    private final ExhibitionTagInfoDomainService exhibitionTagInfoDomainService;
    private final MemberBaseInfoOpt memberBaseInfoOpt;

    @Override
    public List<ExhibitionTagVO> listAll() {

        List<ExhibitionTagInfo> exhibitionTagInfos = exhibitionTagInfoDomainService.listAllExhibitionTagInfoList();
        if (CollUtil.isEmpty(exhibitionTagInfos)) {
            return List.of();
        }

        return ExhibitionTagInfoDTOConvert.INSTANCE.entityToVO(exhibitionTagInfos);
    }

    @Override
    public List<ExhibitionTagVO> getAllExhibitionLeafTag() {
        List<ExhibitionTagInfo> tagInfos = exhibitionTagInfoDomainService.getAllExhibitionLeafTag();
        return ExhibitionTagInfoDTOConvert.INSTANCE.entityToVO(tagInfos);
    }

    @Override
    public String getCurrentLastLevelExhibition() {

        SponsorProfileCache profileCache = memberBaseInfoOpt.sponsorProfile(SecurityFrameworkUtils.getLoginUserId());

        if (profileCache == null) {
            return StringUtils.EMPTY;
        }

        List<ExhibitionTagInfo> exhibitionTagInfoList = exhibitionTagInfoDomainService.listAllExhibitionTagInfoList();

        Optional<ExhibitionTagInfo> any = exhibitionTagInfoList.stream()
                .filter(e -> Objects.equals(e.getParentExhibitionTagCode(), profileCache.getExhibitionTagCode()))
                .findAny();

        return any.isPresent() ? null : profileCache.getExhibitionTagCode();
    }

    @Override
    public List<ExhibitionTagVO> listChildTagCode(List<String> tagCodes) {

        if (CollectionUtils.isEmpty(tagCodes)) {
            return List.of();
        }

        List<ExhibitionTagInfo> exhibitionTagInfoList = exhibitionTagInfoDomainService.listAllExhibitionTagInfoList();
        List<ExhibitionTagInfo> filterTagCodes = exhibitionTagInfoList.stream()
                .filter(e -> tagCodes.contains(e.getExhibitionTagCode()))
                .toList();
        if (CollectionUtils.isEmpty(filterTagCodes)) {
            return List.of();
        }

        List<ExhibitionTagVO> result = Lists.newArrayList();
        result.addAll(ExhibitionTagInfoDTOConvert.INSTANCE.entityToVO(filterTagCodes));
        Map<String, List<ExhibitionTagInfo>> tagParentMap = exhibitionTagInfoList.stream()
                .filter(e -> e.getParentExhibitionTagCode() != null)
                .collect(Collectors.groupingBy(ExhibitionTagInfo::getParentExhibitionTagCode));
        // 获取下级的信息
        List<ExhibitionTagInfo> children = Lists.newArrayList();
        for (String tagCode : tagCodes) {
            List<ExhibitionTagInfo> tagInfo = tagParentMap.get(tagCode);
            if (!CollectionUtils.isEmpty(tagInfo)) {
                children.addAll(tagInfo);
            }
        }
        if (!CollectionUtils.isEmpty(children)) {
            result.addAll(ExhibitionTagInfoDTOConvert.INSTANCE.entityToVO(children));
            // 获取孙子级 最多三级 多了不考虑 所以不使用递归
            List<ExhibitionTagInfo> list = children.stream()
                    .map(e -> tagParentMap.get(e.getExhibitionTagCode()))
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .toList();
            if (!CollectionUtils.isEmpty(list)) {
                result.addAll(ExhibitionTagInfoDTOConvert.INSTANCE.entityToVO(list));
            }
        }
        return result;
    }

    @Override
    public void initExhibitionTagCache() {
        exhibitionTagInfoDomainService.initExhibitionTagCache();
    }


}
