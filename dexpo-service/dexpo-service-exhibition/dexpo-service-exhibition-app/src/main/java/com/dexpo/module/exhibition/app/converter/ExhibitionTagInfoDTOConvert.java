package com.dexpo.module.exhibition.app.converter;

import com.dexpo.module.exhibition.api.vo.ExhibitionTagVO;
import com.dexpo.module.exhibition.domain.model.agg.ExhibitionTagInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 展会标签转换器
 *
 * <AUTHOR> Xiaohua 19/06/2025 21:25
 **/
@Mapper
public interface ExhibitionTagInfoDTOConvert {

    ExhibitionTagInfoDTOConvert INSTANCE = Mappers.getMapper(ExhibitionTagInfoDTOConvert.class);

    /**
     * 展会标签实体转vo
     *
     * @param exhibitionTagInfoList 实体列表
     * @return vo列表
     */
    List<ExhibitionTagVO> entityToVO(List<ExhibitionTagInfo> exhibitionTagInfoList);

}
