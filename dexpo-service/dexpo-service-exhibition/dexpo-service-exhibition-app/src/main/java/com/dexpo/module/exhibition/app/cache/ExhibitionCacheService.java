package com.dexpo.module.exhibition.app.cache;

import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSON;
import com.dexpo.framework.cache.redis.constant.ExhibitionRedisKey;
import com.dexpo.framework.cache.redis.constant.ICacheKey;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.module.exhibition.api.vo.ExhibitionVO;
import com.dexpo.module.exhibition.app.api.ExhibitionInfoAppService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * exhibition 展会缓存处理
 * 如：展会信息缓存
 * 热点数据加载
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ExhibitionCacheService {

    private final RedisService redisService;

    private final ExhibitionInfoAppService exhibitionInfoAppService;


    /**
     * 后续删除 通过定时任务或者是热点数据加载进行处理
     */
    @PostConstruct
    public void initCache(){
        loadExhibitionVOCacheByTag("CIIF");
        initExhibitionCache();
    }

    /**
     * 根据展会标签码加载展会信息
     * 
     * @param exhibitionTagCode
     */
    public void loadExhibitionVOCacheByTag(String exhibitionTagCode) {
        log.info("loadExhibitionCache exhibitionTagCode: {}", exhibitionTagCode);
        String key = ICacheKey.generateKey(ExhibitionRedisKey.EXHIBITION_INFO_VO_BY_TAGCODE, exhibitionTagCode);

        ExhibitionVO exhibitionVO = exhibitionInfoAppService.getExhibitionInfo(exhibitionTagCode);

        redisService.setCacheObject(key, exhibitionVO);
        log.info("loadExhibitionCache exhibitionTagCode: {} cache:{} success", exhibitionTagCode,
                JSON.toJSONString(exhibitionVO));
    }


    /**
     * 初始化展会全部缓存信息
     */
    public void initExhibitionCache() {
        exhibitionInfoAppService.initExhibitionCache();
    }

}
