package com.dexpo.module.exhibition.app.service;

import cn.hutool.core.collection.CollUtil;
import com.dexpo.framework.cache.redis.constant.ExhibitionRedisKey;
import com.dexpo.framework.cache.redis.constant.ICacheKey;
import com.dexpo.framework.cache.redis.entity.sponsor.SponsorProfileCache;
import com.dexpo.framework.cache.redis.operate.member.MemberBaseInfoOpt;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.framework.security.core.util.SecurityFrameworkUtils;
import com.dexpo.module.exhibition.api.dto.ExhibitionQueryDTO;
import com.dexpo.module.exhibition.api.dto.ExhibitionTagCodeBatchQueryDTO;
import com.dexpo.module.exhibition.api.vo.ExhibitionSessionVO;
import com.dexpo.module.exhibition.api.vo.ExhibitionTagVO;
import com.dexpo.module.exhibition.api.vo.ExhibitionVO;
import com.dexpo.module.exhibition.app.api.ExhibitionInfoAppService;
import com.dexpo.module.exhibition.app.api.ExhibitionTagInfoAppService;
import com.dexpo.module.exhibition.app.converter.ExhibitionInfoDTOConvert;
import com.dexpo.module.exhibition.domain.model.agg.ExhibitionInfo;
import com.dexpo.module.exhibition.domain.service.ExhibitionInfoDomainService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * 展会信息
 *
 * <AUTHOR> Xiaohua 19/06/2025 14:46
 **/
@Service
@RequiredArgsConstructor
public class ExhibitionInfoAppServiceImpl implements ExhibitionInfoAppService {

    private final ExhibitionInfoDomainService exhibitionInfoDomainService;
    private final MemberBaseInfoOpt memberBaseInfoOpt;
    private final ExhibitionTagInfoAppService exhibitionTagInfoAppService;
    private final RedisService redisService;

    @Override
    public ExhibitionVO getExhibitionInfo(String tagCode) {

        ExhibitionInfo exhibitionInfo = exhibitionInfoDomainService.getExhibitionInfoByTag(tagCode);

        return ExhibitionInfoDTOConvert.INSTANCE.entityToVO(exhibitionInfo);
    }

    @Override
    public List<ExhibitionSessionVO> getExhibitionSession(ExhibitionTagCodeBatchQueryDTO queryDTO) {

        List<ExhibitionInfo> exhibitionInfoList = exhibitionInfoDomainService.listAllExhibitionInfo();
        if (CollUtil.isEmpty(exhibitionInfoList)) {
            return List.of();
        }
        List<ExhibitionTagVO> tagList = exhibitionTagInfoAppService.listChildTagCode(queryDTO.getExhibitionTagCodes());
        List<String> tagCodes = tagList.stream().map(ExhibitionTagVO::getExhibitionTagCode).toList();
        List<ExhibitionInfo> filterCaches = exhibitionInfoList.stream().filter(e -> queryDTO.getExhibitionTagCodes()
                .stream().anyMatch(tagCodes::contains)).toList();

        return ExhibitionInfoDTOConvert.INSTANCE.entityToSessionVO(filterCaches).stream().distinct().toList();

    }

    @Override
    public List<ExhibitionVO> getExhibitionList(ExhibitionQueryDTO queryDTO) {

        List<ExhibitionInfo> exhibitionInfoList = exhibitionInfoDomainService.listAllExhibitionInfo();
        // 要关注一个展会tagCode 有多个下级展会的问题
        List<ExhibitionInfo> dos = filterExhibition(queryDTO, exhibitionInfoList);

        if (Objects.equals(true, queryDTO.getPermission())) {
            SponsorProfileCache profileCache = memberBaseInfoOpt
                    .sponsorProfile(SecurityFrameworkUtils.getLoginUserId());
            dos = dos.stream().filter(e -> e.getExhibitionCode().contains(profileCache.getExhibitionTagCode()))
                    .toList();
        }

        return dos.stream().map(ExhibitionInfoDTOConvert.INSTANCE::entityToVO).toList();
    }

    private List<ExhibitionInfo> filterExhibition(ExhibitionQueryDTO queryDTO,
                                                       List<ExhibitionInfo> exhibitionInfoList) {
        if (!CollectionUtils.isEmpty(queryDTO.getExhibitionTagCodes())
                || !CollectionUtils.isEmpty(queryDTO.getExhibitionSessionKeys())) {
            List<ExhibitionTagVO> voList = exhibitionTagInfoAppService.listChildTagCode(queryDTO.getExhibitionTagCodes());
            List<String> exhibitionTagCodes = voList.stream().map(ExhibitionTagVO::getExhibitionTagCode).toList();

            return exhibitionInfoList.stream().filter(e -> {
                if (!CollectionUtils.isEmpty(exhibitionTagCodes)
                        && !CollectionUtils.isEmpty(queryDTO.getExhibitionSessionKeys())) {
                    return exhibitionTagCodes
                            .stream()
                            .anyMatch(e1 -> Objects.equals(e1, e.getExhibitionTagCode()) &&
                                    queryDTO.getExhibitionSessionKeys().contains(e.getExhibitionSessionKey()));
                }
                if (!CollectionUtils.isEmpty(exhibitionTagCodes)) {
                    return exhibitionTagCodes
                            .stream()
                            .anyMatch(e1 -> e.getExhibitionTagCode().contains(e1));
                }
                if (!CollectionUtils.isEmpty(queryDTO.getExhibitionSessionKeys())) {
                    return queryDTO.getExhibitionSessionKeys().contains(e.getExhibitionSessionKey());
                }
                return false;
            }).toList();
        }
        return exhibitionInfoList;
    }

    @Override
    public ExhibitionVO getExhibitionById(Long exhibitionId) {
        // 防止缓存穿透
        if (exhibitionId == null || exhibitionId <= 0) {
            return null;
        }
        String key = ICacheKey.generateKey(ExhibitionRedisKey.EXHIBITION_INFO_VO_BY_ID, String.valueOf(exhibitionId));

        ExhibitionVO cache = redisService.getCacheObject(key);
        if (cache == null) {
            ExhibitionInfo exhibitionInfo = exhibitionInfoDomainService.getExhibitionInfoById(exhibitionId);
            ExhibitionVO exhibitionVO = ExhibitionInfoDTOConvert.INSTANCE.entityToVO(exhibitionInfo);
            exhibitionTagInfoAppService.listAll().stream()
                    .filter(e -> Objects.equals(e.getExhibitionTagCode(), exhibitionInfo.getExhibitionTagCode()))
                    .findAny().ifPresent(e -> exhibitionVO.setExhibitionTagName(e.getExhibitionTagName()));
            cache = exhibitionVO;
            redisService.setCacheObject(key, cache);
        }
        return cache;
    }

    @Override
    public ExhibitionVO getExhibitionByTagCode(String tagCode) {

        List<ExhibitionInfo> exhibitionInfoList = exhibitionInfoDomainService.listAllExhibitionInfo();
        if (CollUtil.isEmpty(exhibitionInfoList)) {
            return null;
        }

        ExhibitionInfo exhibitionInfo = exhibitionInfoList.stream()
                .filter(e -> Objects.equals(tagCode, e.getExhibitionTagCode()))
                .max(Comparator.comparing(ExhibitionInfo::getExhibitionBeginTime))
                .orElse(null);

        return ExhibitionInfoDTOConvert.INSTANCE.entityToVO(exhibitionInfo);
    }

    @Override
    public ExhibitionVO getCurrentExhibition() {
        String tagCode = exhibitionTagInfoAppService.getCurrentLastLevelExhibition();
        return getExhibitionByTagCode(tagCode);
    }

    @Override
    public void initExhibitionCache() {
        exhibitionInfoDomainService.initExhibitionCache();
        exhibitionTagInfoAppService.initExhibitionTagCache();
    }
}
