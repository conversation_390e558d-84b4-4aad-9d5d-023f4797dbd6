package com.dexpo.module.exhibition.app.converter;

import com.dexpo.framework.common.pojo.IConvert;
import com.dexpo.module.exhibition.api.vo.AgreementSimpleVO;
import com.dexpo.module.exhibition.domain.model.agg.ExhibitionAgreementInfo;
import com.dexpo.module.exhibition.infrastructure.tunnel.dataobject.ExhibitionAgreementInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * ExhibitionAgreementInfoDTO 转换
 *
 * <AUTHOR> Xiaohua 19/06/2025 15:17
 **/
@Mapper
public interface ExhibitionAgreementInfoDTOConvert extends IConvert<String, AgreementSimpleVO, ExhibitionAgreementInfoDO> {

    ExhibitionAgreementInfoDTOConvert INSTANCE = Mappers.getMapper(ExhibitionAgreementInfoDTOConvert.class);

    /**
     * 聚合根转vo
     *
     * @param exhibitionAgreementInfo 聚合根
     * @return vo
     */
    AgreementSimpleVO entityToVO(ExhibitionAgreementInfo exhibitionAgreementInfo);

}
