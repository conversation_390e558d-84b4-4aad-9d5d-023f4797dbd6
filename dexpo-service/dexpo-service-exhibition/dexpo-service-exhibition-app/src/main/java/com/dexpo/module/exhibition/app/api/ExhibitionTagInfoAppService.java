package com.dexpo.module.exhibition.app.api;

import com.dexpo.module.exhibition.api.vo.ExhibitionTagVO;

import java.util.List;

/**
 * 展会tag 应用服务
 *
 * <AUTHOR> Xiaohua 19/06/2025 14:01
 **/
public interface ExhibitionTagInfoAppService {

    /**
     * 获取所有的tag
     *
     * @return vo
     */
    List<ExhibitionTagVO> listAll();

    /**
     * 获取叶子节点的会展
     *
     * @return
     */
    List<ExhibitionTagVO> getAllExhibitionLeafTag();

    /**
     * 判断展会是否为最后一个层级
     *
     * @return 是否为最后一个层级 如果是 这返回对应的code
     */
    String getCurrentLastLevelExhibition();

    /**
     * 获取 tagCode和其下级
     * @param tagCodes
     * @return
     */
    List<ExhibitionTagVO> listChildTagCode(List<String> tagCodes);

    /**
     * 初始化展会tag缓存
     */
    void initExhibitionTagCache();
}
