package com.dexpo.module.exhibition.app.service;

import com.dexpo.module.exhibition.api.vo.ExhibitionVO;
import com.dexpo.module.exhibition.app.api.ExhibitionInfoAppService;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.framework.cache.redis.constant.ICacheKey;
import com.dexpo.framework.cache.redis.constant.ExhibitionRedisKey;

import com.dexpo.module.exhibition.app.cache.ExhibitionCacheService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

class ExhibitionCacheServiceTest {

    @Mock
    private RedisService redisService;

    @Mock
    private ExhibitionInfoAppService exhibitionInfoAppService;

    @InjectMocks
    private ExhibitionCacheService exhibitionCacheService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testLoadExhibitionCache_WithValidData_ShouldSetVOCacheByTag() {
        // Arrange
        String tagCode = "CIIF";
        ExhibitionVO mockVO = new ExhibitionVO();

        when(exhibitionInfoAppService.getExhibitionInfo(tagCode)).thenReturn(mockVO);

        // Act
        exhibitionCacheService.loadExhibitionVOCacheByTag(tagCode);

        // Assert
        verify(exhibitionInfoAppService, times(1)).getExhibitionInfo(tagCode);
        verify(redisService, times(1))
            .setCacheObject(
                ICacheKey.generateKey(ExhibitionRedisKey.EXHIBITION_INFO_VO_BY_TAGCODE, tagCode),
                mockVO
            );
    }

}
