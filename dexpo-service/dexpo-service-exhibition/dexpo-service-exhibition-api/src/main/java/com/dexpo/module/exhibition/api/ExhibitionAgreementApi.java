package com.dexpo.module.exhibition.api;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.exhibition.api.dto.AgreementQueryDTO;
import com.dexpo.module.exhibition.api.dto.ExhibitionCodeQueryDTO;
import com.dexpo.module.exhibition.api.vo.AgreementSimpleVO;
import com.dexpo.module.exhibition.enums.ApiConstants;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 *
 */
@FeignClient(name = ApiConstants.NAME)
public interface ExhibitionAgreementApi {

    String API_PREFIX = "/exhibition/agreement";

    /**
     * 媒体入口获取展会信息
     *
     * @param req req
     * @return 展会信息 如果不存在说明当前没有开始
     */
    @PostMapping(API_PREFIX + "/getAgreementInfo")
    CommonResult<List<AgreementSimpleVO>> geAgreementInfo(@Valid ExhibitionCodeQueryDTO req);

    /**
     * 媒体入口获取展会信息
     *
     * @param req req
     * @return 展会信息 如果不存在说明当前没有开始
     */
    @PostMapping(API_PREFIX + "/getAgreementContent")
    CommonResult<String> getAgreementContext(@Valid @RequestBody AgreementQueryDTO req);

}
