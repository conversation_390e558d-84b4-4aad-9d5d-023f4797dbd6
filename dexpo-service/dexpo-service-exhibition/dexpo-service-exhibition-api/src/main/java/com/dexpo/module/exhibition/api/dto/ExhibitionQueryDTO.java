package com.dexpo.module.exhibition.api.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExhibitionQueryDTO {

    /**
     * 展会tag code
     */
    private List<String> exhibitionTagCodes;

    /**
     * 展会届数
     */
    private List<String> exhibitionSessionKeys;

    /**
     * 附带权限
     */
    private Boolean permission = true;
}