package com.dexpo.module.exhibition.api.vo;

import lombok.Data;

/**
 * ExhibitionVO
 */
@Data
public class ExhibitionVO {

    /**
     * id
     */
    private Long id;

    /**
     * 展会CODE
     */
    private String exhibitionCode;

    /**
     * 展会简介
     */
    private String exhibitionAbbreviation;

    /**
     * 展会名称-中文
     */
    private String exhibitionNameCn;

    /**
     * 展会名称-英文
     */
    private String exhibitionNameEn;

    /**
     * 年份：如 2025
     */
    private Integer exhibitionYear;

    /**
     * 展会届数：如 1
     */
    private Integer exhibitionSession;

    /**
     * 展会年份和届数组成的唯一key，如2025_1
     */
    private String exhibitionSessionKey;

    /**
     * 展会标签
     */
    private String exhibitionTagCode;

    /**
     * 展会标签名称
     */
    private String exhibitionTagName;


    /**
     * 展会状态
     */
    private String exhibitionStatus;

    /**
     * 展会LOGO
     */
    private String exhibitionLogo;

    /**
     * 媒体注册状态
     */
    private String mediaScheduleRegisterStatus;

    /**
     * 用户注册状态
     */
    private String audienceScheduleRegisterStatus;
}
