package com.dexpo.module.exhibition.api;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.exhibition.api.dto.ExhibitionQueryDTO;
import com.dexpo.module.exhibition.api.dto.ExhibitionTagCodeQueryDTO;
import com.dexpo.module.exhibition.api.vo.ExhibitionVO;
import com.dexpo.module.exhibition.enums.ApiConstants;

import jakarta.validation.Valid;

/**
 *
 */
@FeignClient(name = ApiConstants.NAME)
public interface ExhibitionApi {

    String API_PREFIX = "/exhibition/info";

    /**
     * 媒体入口获取展会信息
     *
     * @param req req
     * @return 展会信息 如果不存在说明当前没有开始
     */
    @PostMapping(API_PREFIX + "/getExhibition")
    CommonResult<ExhibitionVO> getExhibition(@Valid @RequestBody ExhibitionTagCodeQueryDTO req);

    /**
     * 根据展会tag code/届数 获取所有的展会信息
     * 报表/媒体列表查询
     *
     * @param req req
     * @return 展会信息
     */
    @PostMapping(API_PREFIX + "/getExhibitionByTagCode")
    CommonResult<List<ExhibitionVO>> getExhibitionList(@Valid @RequestBody ExhibitionQueryDTO req);

    /**
     * 根据id获取会展信息
     * @param exhibitionId
     * @return
     */
    @GetMapping(API_PREFIX + "/getExhibitionById")
    CommonResult<ExhibitionVO> getExhibitionById(@RequestParam("exhibitionId") Long exhibitionId);
}
