package com.dexpo.module.exhibition.cache.convert;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.dexpo.framework.cache.redis.entity.exhibition.ExhibitionInfoCache;
import com.dexpo.module.exhibition.api.vo.ExhibitionVO;

@Mapper
public interface ExhibitionCacheConvert {

    ExhibitionCacheConvert INSTANCE = Mappers.getMapper(ExhibitionCacheConvert.class);

    ExhibitionVO cache2vo(ExhibitionInfoCache exhibitionInfoCache);

}