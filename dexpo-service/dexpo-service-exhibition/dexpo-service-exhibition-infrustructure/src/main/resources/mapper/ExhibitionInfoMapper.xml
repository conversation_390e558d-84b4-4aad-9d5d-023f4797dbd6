<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dexpo.module.exhibition.infrastructure.tunnel.database.ExhibitionInfoMapper">

    <sql id="Base_Column_List">
        id, exhibition_code, exhibition_name_cn, exhibition_name_en, exhibition_session,
        exhibition_tag_code, exhibition_status, exhibition_logo, exhibition_begin_time, exhibition_end_time,
        del_flg, create_user, create_user_name, create_time,
        update_user, update_user_name, update_time
    </sql>

    <resultMap id="BaseResultMap" type="com.dexpo.module.exhibition.infrastructure.tunnel.dataobject.ExhibitionInfoDO">
        <id column="id" property="id"/>
        <result column="exhibition_code" property="exhibitionCode"/>
        <result column="exhibition_name_cn" property="exhibitionNameCn"/>
        <result column="exhibition_name_en" property="exhibitionNameEn"/>
        <result column="exhibition_session" property="exhibitionSession"/>
        <result column="exhibition_tag_code" property="exhibitionTagCode"/>
        <result column="exhibition_status" property="exhibitionStatus"/>
        <result column="exhibition_logo" property="exhibitionLogo"/>
        <result column="exhibition_begin_time" property="exhibitionBeginTime"/>
        <result column="exhibition_end_time" property="exhibitionEndTime"/>
        <result column="del_flg" property="delFlg"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_name" property="updateUserName"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 根据展会编码查询展会信息 -->
    <select id="selectByExhibitionCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM exhibition_info
        WHERE exhibition_code = #{exhibitionCode}
        AND del_flg = 0
    </select>

    <!-- 根据展会标签查询展会列表 -->
    <select id="selectByExhibitionTagCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM exhibition_info
        WHERE exhibition_tag_code = #{exhibitionTagCode}
        AND del_flg = 0
    </select>

    <!-- 根据展会状态查询展会列表 -->
    <select id="selectByExhibitionStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM exhibition_info
        WHERE exhibition_status = #{exhibitionStatus}
        AND del_flg = 0
    </select>

    <!-- 根据展会届数查询展会列表 -->
    <select id="selectByExhibitionSession" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM exhibition_info
        WHERE exhibition_session = #{exhibitionSession}
        AND del_flg = 0
    </select>

    <!-- 根据展会名称（中文）模糊查询展会列表 -->
    <select id="selectByExhibitionNameCnLike" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM exhibition_info
        WHERE exhibition_name_cn LIKE CONCAT('%', #{exhibitionNameCn}, '%')
        AND del_flg = 0
    </select>

    <!-- 根据展会名称（英文）模糊查询展会列表 -->
    <select id="selectByExhibitionNameEnLike" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM exhibition_info
        WHERE exhibition_name_en LIKE CONCAT('%', #{exhibitionNameEn}, '%')
        AND del_flg = 0
    </select>

</mapper> 