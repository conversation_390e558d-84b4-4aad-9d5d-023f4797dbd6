package com.dexpo.module.exhibition.infrastructure.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dexpo.framework.cache.redis.constant.ExhibitionRedisKey;
import com.dexpo.framework.cache.redis.entity.exhibition.ExhibitionTagInfoCache;
import com.dexpo.module.exhibition.domain.model.agg.ExhibitionTagInfo;
import com.dexpo.module.exhibition.domain.repoistory.ExhibitionTagInfoRepository;
import com.dexpo.module.exhibition.infrastructure.converter.ExhibitionTagInfoDOConvert;
import com.dexpo.module.exhibition.infrastructure.tunnel.cache.CacheService;
import com.dexpo.module.exhibition.infrastructure.tunnel.database.ExhibitionTagInfoMapper;
import com.dexpo.module.exhibition.infrastructure.tunnel.dataobject.ExhibitionTagInfoDO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Xiaohua 20/06/2025 10:14
 **/
@Service
@RequiredArgsConstructor
public class ExhibitionTagInfoRepositoryImpl extends ServiceImpl<ExhibitionTagInfoMapper, ExhibitionTagInfoDO> implements ExhibitionTagInfoRepository {

    private final CacheService cacheService;

    @Override
    public List<ExhibitionTagInfo> listAllExhibitionTagInfoList() {

        List<ExhibitionTagInfoCache> caches = getByCache();

        return ExhibitionTagInfoDOConvert.INSTANCE.cacheToEntity(caches);
    }

    @Override
    public List<ExhibitionTagInfo> getAllExhibitionLeafTag() {
        List<ExhibitionTagInfoCache> caches = getByCache();
        Map<String, List<ExhibitionTagInfoCache>> parentMap = caches.stream()
                .filter(e -> e.getParentExhibitionTagCode() != null)
                .collect(Collectors.groupingBy(ExhibitionTagInfoCache::getParentExhibitionTagCode));
        List<ExhibitionTagInfoCache> leaf = caches.stream()
                .filter(e -> parentMap.get(e.getExhibitionTagCode()) == null)
                .toList();
        return ExhibitionTagInfoDOConvert.INSTANCE.cacheToEntity(leaf);
    }

    private List<ExhibitionTagInfoCache> initExhibitionTagCache() {
        List<ExhibitionTagInfoDO> dos = list();
        List<ExhibitionTagInfoCache> caches = ExhibitionTagInfoDOConvert.INSTANCE.doToCache(dos);
        cacheService.save(ExhibitionRedisKey.EXHIBITION_TAG_INFO_ALL, caches);
        return caches;
    }

    public List<ExhibitionTagInfoCache> getByCache(){
        List<ExhibitionTagInfoCache> caches = cacheService.get(ExhibitionRedisKey.EXHIBITION_TAG_INFO_ALL);
        if (CollectionUtil.isEmpty(caches)) {
            return initExhibitionTagCache();
        }
        return caches;
    }
}
