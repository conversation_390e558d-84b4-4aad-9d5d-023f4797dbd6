package com.dexpo.module.exhibition.infrastructure.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dexpo.framework.cache.redis.constant.ExhibitionRedisKey;
import com.dexpo.framework.cache.redis.constant.ICacheKey;
import com.dexpo.framework.cache.redis.entity.exhibition.ExhibitionInfoCache;
import com.dexpo.framework.common.enums.ValueSetUserTypeEnum;
import com.dexpo.module.exhibition.domain.model.agg.ExhibitionInfo;
import com.dexpo.module.exhibition.domain.model.agg.ExhibitionRegisterSchedule;
import com.dexpo.module.exhibition.domain.repoistory.ExhibitionInfoRepository;
import com.dexpo.module.exhibition.domain.repoistory.ExhibitionRegisterScheduleRepository;
import com.dexpo.module.exhibition.enums.ExhibitionScheduleRegisterStatus;
import com.dexpo.module.exhibition.infrastructure.converter.ExhibitionDOConvert;
import com.dexpo.module.exhibition.infrastructure.tunnel.cache.CacheService;
import com.dexpo.module.exhibition.infrastructure.tunnel.database.ExhibitionInfoMapper;
import com.dexpo.module.exhibition.infrastructure.tunnel.dataobject.ExhibitionInfoDO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 占会信息仓储
 *
 * <AUTHOR> Xiaohua 19/06/2025 16:53
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class ExhibitionInfoRepositoryImpl extends ServiceImpl<ExhibitionInfoMapper, ExhibitionInfoDO> implements ExhibitionInfoRepository {

    private final CacheService cacheService;
    private final ExhibitionRegisterScheduleRepository exhibitionRegisterScheduleRepository;

    @Override
    public ExhibitionInfo getExhibitionInfoByTag(String tagCode) {
        // tag对应的最新的展会信息
        List<ExhibitionInfoCache> cacheList = getAllExhibitionInfo();
        Optional<ExhibitionInfoCache> newest = cacheList.stream()
                .filter(e -> Objects.equals(e.getExhibitionTagCode(), tagCode))
                .max(Comparator.comparing(ExhibitionInfoCache::getExhibitionBeginTime));
        if (newest.isEmpty()) {
            log.warn("ExhibitionInfoServiceImpl.getExhibitionInfo 展会信息不存在，tagCode: {}", tagCode);
            return null;
        }
        ExhibitionInfoCache exhibition = newest.get();
        List<ExhibitionRegisterSchedule> registerSchedules = exhibitionRegisterScheduleRepository
                .getScheduleByExhibitionCode(exhibition.getExhibitionCode());
        if (CollectionUtils.isEmpty(registerSchedules)) {
            log.error("ExhibitionInfoServiceImpl.getExhibitionInfo 展会注册时间不存在，exhibitionCode: {}",
                    exhibition.getExhibitionCode());
            return null;
        }

        // 根据时间，判断状态
        ExhibitionInfo exhibitionInfo = ExhibitionDOConvert.INSTANCE.cacheToEntity(exhibition);

        LocalDateTime now = LocalDateTime.now();
        for (ExhibitionRegisterSchedule registerSchedule : registerSchedules) {
            String status;
            if (now.isBefore(registerSchedule.getRegisterBeginTime())) {
                status = ExhibitionScheduleRegisterStatus.NOT_STARTED.getCode();
            } else if (now.isAfter(registerSchedule.getRegisterEndTime())) {
                status = ExhibitionScheduleRegisterStatus.ENDED.getCode();
            } else {
                status = ExhibitionScheduleRegisterStatus.IN_PROGRESS.getCode();
            }
            if (Objects.equals(ValueSetUserTypeEnum.MEDIA.getOptionCode(), registerSchedule.getMemberType())) {
                exhibitionInfo.setMediaScheduleRegisterStatus(status);
            }
            if (Objects.equals(ValueSetUserTypeEnum.AUDIENCE.getOptionCode(), registerSchedule.getMemberType())) {
                exhibitionInfo.setAudienceScheduleRegisterStatus(status);
            }
        }

        return exhibitionInfo;

    }

    @Override
    public ExhibitionInfo getExhibitionInfoById(Long id) {

        String key = ICacheKey.generateKey(ExhibitionRedisKey.EXHIBITION_INFO_BY_ID, String.valueOf(id));

        ExhibitionInfoCache exhibitionInfoCache = cacheService.computeIfAbsent(key, k -> {
            ExhibitionInfoDO exhibitionInfoDO = getById(id);
            return ExhibitionDOConvert.INSTANCE.toCache(exhibitionInfoDO);
        });

        return ExhibitionDOConvert.INSTANCE.cacheToEntity(exhibitionInfoCache);
    }

    @Override
    public List<ExhibitionInfo> listAllExhibitionInfo() {
        List<ExhibitionInfoCache> exhibitionInfoCacheList = getAllExhibitionInfo();
        return ExhibitionDOConvert.INSTANCE.cacheToEntity(exhibitionInfoCacheList);
    }

    private List<ExhibitionInfoCache> getAllExhibitionInfo() {
        List<ExhibitionInfoCache> cacheList = cacheService.get(ExhibitionRedisKey.EXHIBITION_INFO_ALL);
        if (cacheList == null) {
            cacheList = initExhibitionListCacheHaveReturn();
        }
        return cacheList;
    }

    public void initExhibitionCache() {
        List<ExhibitionInfoCache> infoCaches = initExhibitionListCacheHaveReturn();
        infoCaches.forEach(exhibition -> {
            String idKey = ICacheKey.generateKey(ExhibitionRedisKey.EXHIBITION_INFO_BY_ID,
                    String.valueOf(exhibition.getId()));
            cacheService.save(idKey, exhibition);
            String codeKey = ICacheKey.generateKey(ExhibitionRedisKey.EXHIBITION_INFO_BY_ID,
                    exhibition.getExhibitionCode());
            cacheService.save(codeKey, exhibition);
        });
    }

    private List<ExhibitionInfoCache> initExhibitionListCacheHaveReturn() {
        List<ExhibitionInfoDO> list = this.list();
        List<ExhibitionInfoCache> cacheList = ExhibitionDOConvert.INSTANCE.toCacheList(list);
        cacheService.save(ExhibitionRedisKey.EXHIBITION_INFO_ALL, cacheList);
        return cacheList;
    }

}
