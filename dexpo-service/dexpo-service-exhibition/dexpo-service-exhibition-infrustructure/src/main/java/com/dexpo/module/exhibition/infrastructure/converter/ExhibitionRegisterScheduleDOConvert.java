package com.dexpo.module.exhibition.infrastructure.converter;

import com.dexpo.framework.cache.redis.entity.exhibition.ExhibitionRegisterScheduleCache;
import com.dexpo.module.exhibition.domain.model.agg.ExhibitionRegisterSchedule;
import com.dexpo.module.exhibition.infrastructure.tunnel.dataobject.ExhibitionRegisterScheduleDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 展会时间安排
 *
 * <AUTHOR> Xiaohua 19/06/2025 20:08
 **/
@Mapper
public interface ExhibitionRegisterScheduleDOConvert {

    ExhibitionRegisterScheduleDOConvert INSTANCE = Mappers.getMapper(ExhibitionRegisterScheduleDOConvert.class);

    /**
     * 转换成cache列表
     *
     * @param exhibitionRegisterScheduleDOList list
     * @return list
     */
    List<ExhibitionRegisterScheduleCache> toCacheList(List<ExhibitionRegisterScheduleDO> exhibitionRegisterScheduleDOList);

    /**
     * 缓存对象转聚合根
     *
     * @param exhibitionRegisterScheduleCache 缓存对象
     * @return 聚合根
     */
    ExhibitionRegisterSchedule cacheToEntity(ExhibitionRegisterScheduleCache exhibitionRegisterScheduleCache);

}
