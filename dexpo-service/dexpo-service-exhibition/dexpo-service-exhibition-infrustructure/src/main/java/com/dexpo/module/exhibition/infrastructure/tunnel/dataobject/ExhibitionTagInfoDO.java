package com.dexpo.module.exhibition.infrastructure.tunnel.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("exhibition_tag_info")
public class ExhibitionTagInfoDO extends BaseDO {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("exhibition_tag_name")
    private String exhibitionTagName;
    
    @TableField("exhibition_tag_code")
    private String exhibitionTagCode;
    
    @TableField("exhibition_tag_logo")
    private String exhibitionTagLogo;
    
    @TableField("exhibition_tag_level")
    private Integer exhibitionTagLevel;
    
    @TableField("parent_exhibition_tag_code")
    private String parentExhibitionTagCode;
} 