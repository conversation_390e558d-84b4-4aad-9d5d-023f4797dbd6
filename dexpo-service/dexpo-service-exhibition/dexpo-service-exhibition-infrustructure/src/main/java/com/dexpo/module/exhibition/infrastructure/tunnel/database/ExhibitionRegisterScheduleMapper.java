package com.dexpo.module.exhibition.infrastructure.tunnel.database;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dexpo.module.exhibition.infrastructure.tunnel.dataobject.ExhibitionRegisterScheduleDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 展会注册时间安排 Mapper 接口
 */
@Mapper
public interface ExhibitionRegisterScheduleMapper extends BaseMapper<ExhibitionRegisterScheduleDO> {
} 