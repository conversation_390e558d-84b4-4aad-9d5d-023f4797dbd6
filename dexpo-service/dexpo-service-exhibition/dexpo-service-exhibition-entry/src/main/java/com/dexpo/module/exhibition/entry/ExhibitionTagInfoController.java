package com.dexpo.module.exhibition.entry;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.exhibition.api.ExhibitionTagApi;
import com.dexpo.module.exhibition.api.dto.ExhibitionTagCodeBatchQueryDTO;
import com.dexpo.module.exhibition.api.vo.ExhibitionSessionVO;
import com.dexpo.module.exhibition.api.vo.ExhibitionTagVO;
import com.dexpo.module.exhibition.api.vo.ExhibitionVO;
import com.dexpo.module.exhibition.app.api.ExhibitionInfoAppService;
import com.dexpo.module.exhibition.app.api.ExhibitionTagInfoAppService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
public class ExhibitionTagInfoController implements ExhibitionTagApi {

    private final ExhibitionTagInfoAppService exhibitionTagInfoService;

    private final ExhibitionInfoAppService exhibitionInfoService;

    @Override
    public CommonResult<List<ExhibitionTagVO>> getAllExhibitionTag() {
        List<ExhibitionTagVO> voList = exhibitionTagInfoService.listAll();
        return CommonResult.success(voList);
    }

    @Override
    public CommonResult<List<ExhibitionTagVO>> getAllExhibitionLeafTag() {
        List<ExhibitionTagVO> leafTag = exhibitionTagInfoService.getAllExhibitionLeafTag();
        return CommonResult.success(leafTag);
    }

    @Override
    public CommonResult<List<ExhibitionSessionVO>> getExhibitionSession(ExhibitionTagCodeBatchQueryDTO queryDTO) {
        List<ExhibitionSessionVO> sessionList = exhibitionInfoService.getExhibitionSession(queryDTO);
        return CommonResult.success(sessionList);
    }

    @Override
    public CommonResult<ExhibitionVO> getCurrentExhibition() {
        ExhibitionVO exhibitionVO = exhibitionInfoService.getCurrentExhibition();
        return CommonResult.success(exhibitionVO);
    }
}