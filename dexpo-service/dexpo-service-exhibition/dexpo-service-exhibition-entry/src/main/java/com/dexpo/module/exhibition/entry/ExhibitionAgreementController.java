package com.dexpo.module.exhibition.entry;

import com.alibaba.fastjson2.JSON;
import com.dexpo.framework.common.enums.ValueSetUserTypeEnum;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.exhibition.api.ExhibitionAgreementApi;
import com.dexpo.module.exhibition.api.dto.AgreementQueryDTO;
import com.dexpo.module.exhibition.api.dto.ExhibitionCodeQueryDTO;
import com.dexpo.module.exhibition.api.vo.AgreementSimpleVO;
import com.dexpo.module.exhibition.app.api.ExhibitionAgreementInfoAppService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 展会信息 Controller
 */
@Tag(name = "展会信息")
@RestController
@RequiredArgsConstructor
@Validated
@Slf4j
public class ExhibitionAgreementController implements ExhibitionAgreementApi {

    private final ExhibitionAgreementInfoAppService exhibitionAgreementInfoAppService;

    @Override
    public CommonResult<List<AgreementSimpleVO>> geAgreementInfo(@Valid @RequestBody ExhibitionCodeQueryDTO req) {
        String memberType = req.getMemberType();
        ValueSetUserTypeEnum userTypeEnum = ValueSetUserTypeEnum.getByCode(memberType);
        if (userTypeEnum == null) {
            log.warn("geAgreementInfo 未获取到对应的用户类型 params:{}", JSON.toJSONString(req));
            return CommonResult.success(List.of());
        }
        List<AgreementSimpleVO> agreement = exhibitionAgreementInfoAppService
                .getAgreementByExhibitionCode(req.getExhibitionCode(), userTypeEnum);
        return CommonResult.success(agreement);
    }

    @Override
    public CommonResult<String> getAgreementContext(@Valid @RequestBody AgreementQueryDTO req) {
        String agreementContext = exhibitionAgreementInfoAppService.getAgreementContext(req);
        return CommonResult.success(agreementContext);
    }
}