<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dexpo</groupId>
        <artifactId>dexpo-service-order</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>dexpo-service-order-app</artifactId>
    <packaging>jar</packaging>

    <name>dexpo-service-order-app</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-order-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-order-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-order-infrustructure</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-security</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

    </dependencies>
</project>
