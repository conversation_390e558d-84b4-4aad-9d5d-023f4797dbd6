<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dexpo</groupId>
        <artifactId>dexpo-service-order</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>dexpo-service-order-infrustructure</artifactId>
    <packaging>jar</packaging>

    <name>dexpo-service-order-infrustructure</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-order-domain</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-order-api</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-cache</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId> <!-- 代码生成器，使用它解析表结构 -->
        </dependency>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-cache</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-rpc</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId> <!-- 实现代码生成 -->
        </dependency>

        <dependency>
            <groupId>cn.smallbun.screw</groupId>
            <artifactId>screw-core</artifactId> <!-- 实现数据库文档 -->
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

    </dependencies>
</project>
