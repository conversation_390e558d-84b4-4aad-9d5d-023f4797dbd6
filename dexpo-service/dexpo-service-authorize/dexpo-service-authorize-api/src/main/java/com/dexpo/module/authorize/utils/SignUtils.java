package com.dexpo.module.authorize.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.dexpo.framework.common.exception.enums.AuthorizeServiceErrorCodeEnum;
import com.dexpo.module.authorize.api.dxposign.dto.SignDexpoMsgDTO;
import com.dexpo.module.authorize.enums.SignCategoryEnums;
import jakarta.servlet.http.HttpServletRequest;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

public class SignUtils {
    private SignUtils(){
        throw new IllegalStateException("Utility class");
    }

    /**
     * 组装请求认证服务的报文
     *
     * @param request
     * @return
     */
    public static SignDexpoMsgDTO getSignParams(HttpServletRequest request) {
        Map<String, String> signParams = new HashMap<>();
        SignDexpoMsgDTO dto = new SignDexpoMsgDTO();
        Enumeration<String> headerNames = request.getHeaderNames();
        if (CollUtil.isEmpty(headerNames)) {
            throw AuthorizeServiceErrorCodeEnum.SIGN_NUM_NOT_MATCH.getServiceException();
        }

        // 获取请求头
        while (headerNames.hasMoreElements()) {
            String header = request.getHeader(headerNames.nextElement());
            if (SignCategoryEnums.DEXPO_SIGN.getHeadNameParams().contains(header)) {
                signParams.put(header, request.getHeader(header));
            }
            if (SignCategoryEnums.DEXPO_SIGN.getSignName().equals(header)) {
                dto.setSign(request.getHeader(header));
            }
            if (SignCategoryEnums.DEXPO_SIGN.getDataSecret().equals(header)) {
                dto.setDataSecret(request.getHeader(header));
                dto.setBody(getRequestBody(request));
            }
        }

        // 校验请求头的数量
        if (SignCategoryEnums.DEXPO_SIGN.getHeadNameParams().size() != signParams.size()) {
            throw AuthorizeServiceErrorCodeEnum.SIGN_NOT_HAS_HEAD.getServiceException();
        }

        if (ObjectUtil.isEmpty(dto.getSign())) {
            throw AuthorizeServiceErrorCodeEnum.SIGN_NOT_HAS_SIGN.getServiceException();
        }
        return dto;
    }

    private static String getRequestBody(HttpServletRequest request) {
        StringBuilder requestBody = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                requestBody.append(line);
            }
        } catch (IOException e) {
            throw AuthorizeServiceErrorCodeEnum.SIGN_READ_BODY_ERROR.getServiceException();
        }
        return requestBody.toString();
    }
}
