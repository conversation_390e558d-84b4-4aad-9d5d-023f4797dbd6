package com.dexpo.module.authorize.enums;

import lombok.Getter;

import java.util.Set;

@Getter
public enum SignCategoryEnums {

    /**
     * 会展中台验签
     */
    DEXPO_SIGN(
            "DEXPO_SIGN",
            Set.of("timestamp", "nonce", "appKey"),
            "sign",
            "dataSecret");

    /**
     * 场景code
     */
    private final String signCategoryCode;

    /**
     * 参与签名的请求头的名字
     */
    private final Set<String> headNameParams;

    /**
     * 参与签名的请求头的名字
     */
    private final String signName;

    /**
     * 是否对消息体加密
     */
    private final String dataSecret;


    SignCategoryEnums(String signCategoryCode, Set<String> headNameParams, String signName, String dataSecret) {
        this.signCategoryCode = signCategoryCode;
        this.headNameParams = headNameParams;
        this.signName = signName;
        this.dataSecret = dataSecret;
    }

}
