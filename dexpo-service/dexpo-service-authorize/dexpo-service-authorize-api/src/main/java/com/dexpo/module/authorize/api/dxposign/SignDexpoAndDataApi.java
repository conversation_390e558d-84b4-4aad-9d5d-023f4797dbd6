package com.dexpo.module.authorize.api.dxposign;

import com.dexpo.module.authorize.api.dxposign.dto.SignDexpoMsgDTO;
import com.dexpo.module.authorize.api.dxposign.vo.SignDexpoMsgVO;
import com.dexpo.module.authorize.enums.ApiConstants;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 数仓创建和获取刷新token")
public interface SignDexpoAndDataApi {

    /**
     * 报文验签和数据解密
     *
     * @param dto SignDexpoMsgDTO
     * @return SignDexpoMsgVO
     */
    SignDexpoMsgVO checkSignAndDecryptData(SignDexpoMsgDTO dto);
}
