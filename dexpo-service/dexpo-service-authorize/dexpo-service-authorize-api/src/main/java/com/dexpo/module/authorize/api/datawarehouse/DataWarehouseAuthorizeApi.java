package com.dexpo.module.authorize.api.datawarehouse;

import com.dexpo.module.authorize.api.datawarehouse.vo.DataWarehouseAuthorizeVO;
import com.dexpo.module.authorize.enums.ApiConstants;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 数仓创建和获取刷新token")
public interface DataWarehouseAuthorizeApi {

    /**
     * 创建token
     *
     * @return DataWarehouseAuthorizeVO
     */
    @PostMapping("/dataWarehouseAuthorize/generateBearerToken")
    DataWarehouseAuthorizeVO generateBearerToken();

}
