spring:
  application:
    name: dexpo-service-authorize
  cloud:
    kubernetes:
      config:
        name: ${spring.application.name}
        namespace: ${ENV:local}
        includeProfileSpecificSources: false
        useNameAsPrefix: false
        sources:
          - name: ${spring.application.name}
      reload:
        enabled: true
        mode: polling
        period: 5000
        strategy: REFRESH
server:
  port: 48093
#
## 日志文件配置。注意，如果 logging.file.name 不放在 bootstrap.yaml 配置文件，而是放在 application.yaml 中，会导致出现 LOG_FILE_IS_UNDEFINED 文件
#logging:
#  file:
#    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径
#