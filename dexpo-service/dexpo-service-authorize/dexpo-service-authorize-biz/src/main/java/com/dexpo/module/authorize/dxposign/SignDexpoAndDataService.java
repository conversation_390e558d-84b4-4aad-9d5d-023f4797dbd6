package com.dexpo.module.authorize.dxposign;

import cn.hutool.core.text.CharSequenceUtil;
import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.exception.enums.AuthorizeServiceErrorCodeEnum;
import com.dexpo.module.authorize.api.dxposign.dto.SignDexpoMsgDTO;
import com.dexpo.module.authorize.api.dxposign.vo.SignDexpoMsgVO;
import com.dexpo.module.authorize.utils.outSign.RSAUtil;
import com.dexpo.module.authorize.utils.outSign.SignatureUtil;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 对报文进行验签和解密
 *
 * <AUTHOR>
 */
@Component
public class SignDexpoAndDataService {

    //@Value("${dexpo.sign}")
    private List<Map<String, String>> signMapList;

    public SignDexpoMsgVO checkSignAndDecryptData(SignDexpoMsgDTO dto) {
        SignDexpoMsgVO vo = new SignDexpoMsgVO();
        // 验签
        Map<String, String> signMap = new HashMap<>();
        for (Map<String, String> map : signMapList) {
            String appKey = map.get("appKey");
            if (CharSequenceUtil.equals(appKey, dto.getSignParams().get("appKey"))) {
                signMap = map;
            }
        }

        try {
            String signature = SignatureUtil.generateSignature(dto.getSignParams(), signMap.get("appSecret"));
            vo.setSignResults(CharSequenceUtil.equals(signature, dto.getSign()));
        } catch (Exception e) {
            throw new ServiceException(AuthorizeServiceErrorCodeEnum.SIGN_HAS_ERROR.getCode(), e.getMessage());
        }

        // 报文解密
        if (CharSequenceUtil.isNotEmpty(dto.getDataSecret())) {
            try {
                String body = RSAUtil.decrypt(signMap.get("privateKey"), dto.getBody());
                vo.setBody(body);
            } catch (Exception e) {
                throw new ServiceException(AuthorizeServiceErrorCodeEnum.SIGN_HAS_ERROR.getCode(), e.getMessage());
            }
        }
        return vo;
    }
}