package com.dexpo.module.authorize.controller;

import com.dexpo.module.authorize.api.datawarehouse.DataWarehouseAuthorizeApi;
import com.dexpo.module.authorize.api.datawarehouse.vo.DataWarehouseAuthorizeVO;
import com.dexpo.module.authorize.datawarehouse.DataWarehouseAuthorizeService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * 展会信息 Controller
 */
@Tag(name = "会展中台获取数仓token")
@RestController
@RequiredArgsConstructor
public class DataWarehouseAuthorizeController implements DataWarehouseAuthorizeApi {

    @Resource
    private DataWarehouseAuthorizeService service;

    @Override
    public DataWarehouseAuthorizeVO generateBearerToken() {
        return service.generateBearerToken();
    }
}