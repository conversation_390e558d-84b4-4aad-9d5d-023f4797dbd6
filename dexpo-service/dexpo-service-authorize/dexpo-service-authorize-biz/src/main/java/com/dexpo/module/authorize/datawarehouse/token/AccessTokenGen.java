package com.dexpo.module.authorize.datawarehouse.token;

import cn.hutool.core.util.ObjectUtil;
import com.dexpo.framework.cache.redis.constant.IntegrationRedisKey;
import com.dexpo.framework.cache.redis.service.RedisService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.*;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * token工具类
 */
@Slf4j
@Component
public class AccessTokenGen {

    @Resource
    private DataWareHouseApiConfig dataWareHouseApiConfig;

    @Resource
    private RedisService redisService;

    /**
     * 创建信任所有证书的RestTemplate
     */
    @SuppressWarnings({"squid:S4830", "squid:S5527"})
    private RestTemplate createTrustAllRestTemplate() {
        try {
            // 创建信任所有证书的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        @Override
                        @SuppressWarnings("squid:S4830")
                        public X509Certificate[] getAcceptedIssuers() {
                            return null;
                        }

                        @Override
                        @SuppressWarnings("squid:S4830")
                        public void checkClientTrusted(X509Certificate[] certs, String authType) {
                            // 在开发/测试环境中，我们选择信任所有证书
                            // 在生产环境中，应该实现proper的证书验证
                        }

                        @Override
                        @SuppressWarnings("squid:S4830")
                        public void checkServerTrusted(X509Certificate[] certs, String authType) {
                            // 在开发/测试环境中，我们选择信任所有证书
                            // 在生产环境中，应该实现proper的证书验证
                        }
                    }
            };

            // 创建SSLContext
            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());

            // 创建HttpsURLConnection的HostnameVerifier
            @SuppressWarnings("squid:S5527")
            HostnameVerifier allHostsValid = (hostname, session) -> true;

            // 设置默认的SSLSocketFactory和HostnameVerifier
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);

            // 创建RestTemplate
            SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory() {
                @Override
                protected void prepareConnection(HttpURLConnection connection, String httpMethod) throws IOException {
                    if (connection instanceof HttpsURLConnection) {
                        ((HttpsURLConnection) connection).setSSLSocketFactory(sc.getSocketFactory());
                        ((HttpsURLConnection) connection).setHostnameVerifier(allHostsValid);
                    }
                    super.prepareConnection(connection, httpMethod);
                }
            };
            requestFactory.setConnectTimeout(5000);
            requestFactory.setReadTimeout(5000);

            return new RestTemplateBuilder()
                    .requestFactory(() -> requestFactory)
                    .build();
        } catch (Exception e) {
            log.error("创建RestTemplate异常", e);
            throw new SSLConfigurationException("创建RestTemplate异常", e);
        }
    }

    /**
     * 生成token
     *
     * @return
     */
    public ClientTokenResponseVO generateBearerToken() {
        ClientTokenResponseVO clientTokenResponseVo = redisService.getCacheObject(IntegrationRedisKey.INTEGRATION_DATA_WAREHOUSE_TOKEN_KEY);
        if (ObjectUtil.isEmpty(clientTokenResponseVo)) {
            RestTemplate restTemplate = createTrustAllRestTemplate();
            try {
                // 构建请求头
                HttpHeaders headers = new HttpHeaders();
                headers.set("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
                headers.set("Accept", "*/*");
                headers.set("Host", "iedw.ciif-expo.com");
                headers.set("Connection", "keep-alive");

                // 使用 URLEncoder 进行编码
                String encodedClientSecret = URLEncoder.encode(dataWareHouseApiConfig.getClientSecret(), StandardCharsets.UTF_8);

                // 构建完整的 URL 字符串
                String urlString = String.format("%s?grant_type=%s&client_id=%s&client_secret=%s",
                        dataWareHouseApiConfig.getTokenUrl(),
                        dataWareHouseApiConfig.getGrantType(),
                        dataWareHouseApiConfig.getClientId(),
                        encodedClientSecret);

                // 创建 URI 对象
                URI uri = new URI(urlString);

                log.info("开始获取token，请求URL：{}", uri);

                // 使用HttpEntity发送请求
                HttpEntity<String> entity = new HttpEntity<>(headers);
                ResultResponseVO resultResponseVO = restTemplate.exchange(
                        uri,
                        HttpMethod.GET,
                        entity,
                        ResultResponseVO.class
                ).getBody();

                if (null == resultResponseVO) {
                    log.error("获取token失败: resultResponseVO is null");
                    return null;
                }

                if (ObjectUtil.isEmpty(resultResponseVO.getData())) {
                    log.error("获取token失败: resultResponseVO.getData() is null");
                    return null;
                }

                if (!Objects.equals(200L, resultResponseVO.getCode())
                        || StringUtils.isBlank(resultResponseVO.getData().getClientToken())) {
                    log.error("获取token失败: {}", resultResponseVO);
                    return null;
                }
                clientTokenResponseVo = resultResponseVO.getData();
                log.info("获取token成功：{}", clientTokenResponseVo.getClientToken());
                //缓存token
                redisService.setCacheObject(IntegrationRedisKey.INTEGRATION_DATA_WAREHOUSE_TOKEN_KEY,
                        clientTokenResponseVo,
                        Long.valueOf(clientTokenResponseVo.getExpiresIn() - Long.valueOf(1L)),
                        TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("获取token异常", e);
                return null;
            }
        }

        return clientTokenResponseVo;
    }

    public static class SSLConfigurationException extends RuntimeException {
        public SSLConfigurationException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
