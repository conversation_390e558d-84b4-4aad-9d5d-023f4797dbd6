package com.dexpo.module.authorize.controller;

import com.dexpo.module.authorize.api.echao.SignEchaoApi;
import com.dexpo.module.authorize.api.echao.dto.SignEchaoDTO;
import com.dexpo.module.authorize.api.echao.vo.SignEchaoVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * 毅朝
 */
@Tag(name = "毅朝-签名")
@RestController
@RequiredArgsConstructor
public class SignEchaoController implements SignEchaoApi {


    @Override
    public SignEchaoVO createSign(SignEchaoDTO dto) {
        return null;
    }
}