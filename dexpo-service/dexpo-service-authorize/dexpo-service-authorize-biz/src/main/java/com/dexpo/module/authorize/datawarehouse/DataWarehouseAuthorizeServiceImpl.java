package com.dexpo.module.authorize.datawarehouse;

import com.dexpo.module.authorize.api.datawarehouse.vo.DataWarehouseAuthorizeVO;
import com.dexpo.module.authorize.datawarehouse.token.AccessTokenGen;
import com.dexpo.module.authorize.datawarehouse.token.ClientTokenResponseVO;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 操作日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class DataWarehouseAuthorizeServiceImpl implements DataWarehouseAuthorizeService {

    @Resource
    private AccessTokenGen accessTokenGen;

    @Override
    public DataWarehouseAuthorizeVO generateBearerToken() {
        ClientTokenResponseVO clientTokenResponseVO = accessTokenGen.generateBearerToken();
        DataWarehouseAuthorizeVO vo = new DataWarehouseAuthorizeVO();
        vo.setClientToken(clientTokenResponseVO.getClientToken());
        return vo;
    }


}