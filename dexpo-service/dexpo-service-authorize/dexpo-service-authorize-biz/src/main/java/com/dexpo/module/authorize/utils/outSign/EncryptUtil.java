package com.dexpo.module.authorize.utils.outSign;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

@Slf4j
public class EncryptUtil {

    private static final String ALGORITHM = "RSA/ECB/OAEPWithSHA-256AndMGF1Padding";


    public static void main(String[] args) throws Exception {
        // 生成 RSA 密钥对
        String publicKeyPEM = "publicKeyPEM";

        // 获取公钥和私钥字符串
        log.info("【公钥】");
        log.info(publicKeyPEM);

        String plainText = "{\n" +
                "    \"code\": 0,\n" +
                "    \"data\": [\n" +
                "    ],\n" +
                "    \"msg\": \"\",\n" +
                "    \"msgEn\": \"\"\n" +
                "}";

        // 发送方用公钥加密
        String cipherText = RSAUtil.encrypt(publicKeyPEM, plainText);
        log.info("加密结果: " + cipherText);
    }

    /**
     * 使用公钥加密数据
     */
    public static String encrypt(String publicKeyPEM, String plainText) throws Exception {
        RSAPublicKey publicKey = readPublicKeyFromPEM(publicKeyPEM);
        Cipher cipher = Cipher.getInstance(ALGORITHM, "BC");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes());
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    /**
     * 公钥
     */
    public static RSAPublicKey readPublicKeyFromPEM(String publicKeyPEM) throws Exception {
        byte[] pkcsBytes = Base64.getDecoder().decode(publicKeyPEM);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(pkcsBytes);
        KeyFactory kf = KeyFactory.getInstance("RSA");
        return (RSAPublicKey) kf.generatePublic(keySpec);
    }
}
