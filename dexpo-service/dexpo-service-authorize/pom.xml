<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dexpo</groupId>
        <artifactId>dexpo-framework-starter-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../../dexpo-framework/dexpo-framework-starter-parent/pom.xml</relativePath>
    </parent>

    <artifactId>dexpo-service-authorize</artifactId>
    <packaging>pom</packaging>

    <name>dexpo-service-authorize</name>
    <modules>
        <module>dexpo-service-authorize-api</module>
        <module>dexpo-service-authorize-biz</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!--            <dependency>-->
            <!--                <groupId>com.dexpo</groupId>-->
            <!--                <artifactId>dexpo-service-authorize-api</artifactId>-->
            <!--                <version>1.0.0-SNAPSHOT</version>-->
            <!--            </dependency>-->
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

</project>
