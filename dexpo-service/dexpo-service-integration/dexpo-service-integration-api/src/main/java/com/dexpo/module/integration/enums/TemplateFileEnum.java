package com.dexpo.module.integration.enums;

import lombok.Getter;

/**
 * 验证码的场景
 */
@Getter
@Deprecated
public enum TemplateFileEnum {

    /**
     * 安全责任书单位
     */
    SAFETY_RESPONSIBILITY_LETTER_ENTERPRISE(
            "static/template/28907c6b-e74c-4a3e-a09a-0b8ec61a91a0_【单位】第25届中国工博会直播安全责任书.docx",
            "SAFETY_RESPONSIBILITY_LETTER_ENTERPRISE",
            "安全责任书单位"),

    /**
     * 安全责任书个人
     */
    SAFETY_RESPONSIBILITY_LETTER_INDIVIDUAL(
            "static/template/9590173f-ba58-4cda-8b7e-b33899aa3dd5_【个人】第25届中国工博会直播安全责任书.docx",
            "SAFETY_RESPONSIBILITY_LETTER_INDIVIDUAL",
            "安全责任书个人"),

    /**
     * 媒体批量代注册
     */
    MEDIA_REGISTRATION_PROXY(
            "static/template/02b99139-461e-4a23-9d6c-f5a7995f1f16_会展集团服务中台建设与推广一期项目_工博会_媒体批量代注册模板_V1.1.xlsx",
            "MEDIA_REGISTRATION_PROXY",
            "媒体批量代注册");

    /**
     * 场景code
     */
    private String url;

    /**
     * 模板编码
     */
    private String templateCode;

    /**
     * 描述
     */
    private String desc;

    TemplateFileEnum(String url, String templateCode, String desc) {
        this.url = url;
        this.templateCode = templateCode;
        this.desc = desc;
    }

    public static String getUrl(String templateCode) {

        for (TemplateFileEnum type : values()) {
            if (type.templateCode.equals(templateCode)) {
                return type.url;
            }
        }
        return null;
    }
}
