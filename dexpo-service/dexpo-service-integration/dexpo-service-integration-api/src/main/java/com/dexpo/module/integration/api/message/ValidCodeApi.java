package com.dexpo.module.integration.api.message;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.integration.api.message.dto.ValidCodeDTO;
import com.dexpo.module.integration.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = ApiConstants.NAME)
public interface ValidCodeApi {

    String PREFIX = "/common/valid";

    /**
     * 根据值集代码列表获取值集信息
     *
     * @param codeDTO 手机号码or email
     * @return true - success
     */
    @PostMapping(PREFIX + "/getCode")
    CommonResult<Boolean> getValidCode(@Validated @RequestBody ValidCodeDTO codeDTO);
} 