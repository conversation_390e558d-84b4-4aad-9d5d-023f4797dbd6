package com.dexpo.module.integration.api.datawarehouse.message;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 数仓 -> 服务中台
 * 数仓将毅朝通道注册的观众用户数据实时同步至服务中台，购票激活成功一个阶段
 */
@Data
@Schema(description = "SC_DW_003 观众注册信息同步接口")
public class DataWarehouseAudienceSyncDexpoDTO {
    
    @Schema(description = "基础信息-姓名", example = "唐鹏")
    @NotBlank(message = "姓名不能为空")
    @Size(max = 200, message = "姓名长度不能超过200个字符")
    private String memberName;

    @Schema(description = "基础信息-名", example = "鹏")
    @Size(max = 100, message = "名长度不能超过100个字符")
    private String memberFirstName;

    @Schema(description = "基础信息-姓", example = "唐")
    @Size(max = 100, message = "姓长度不能超过100个字符")
    private String memberLastName;

    @Schema(description = "基础信息-性别", example = "VO_GENDER_1")
    @Size(max = 64, message = "性别长度不能超过64个字符")
    private String memberGender;

    @Schema(description = "基础信息-手机号码", example = "13896110065")
    @Size(max = 32, message = "手机号码长度不能超过32个字符")
    private String memberMobile;

    @Schema(description = "基础信息-邮箱", example = "<EMAIL>")
    @Size(max = 64, message = "邮箱长度不能超过64个字符")
    private String memberEmail;

    @Schema(description = "基础信息-证件类型", example = "VO_ID_CATEGORY_1")
    @NotBlank(message = "证件类型不能为空")
    @Size(max = 64, message = "证件类型长度不能超过64个字符")
    private String idCategory;

    @Schema(description = "基础信息-证件号码", example = "500666199909090318")
    @Size(max = 32, message = "证件号码长度不能超过32个字符")
    private String idNumber;

    @Schema(description = "基础信息-居住地所在省名称", example = "重庆市")
    @Size(max = 64, message = "省名称长度不能超过64个字符")
    private String currentHomeProvinceName;

    @Schema(description = "基础信息-居住地所在市名称", example = "重庆市区")
    @Size(max = 64, message = "市名称长度不能超过64个字符")
    private String currentHomeCityName;

    @Schema(description = "基础信息-居住地所在区名称", example = "渝中区")
    @Size(max = 64, message = "区名称长度不能超过64个字符")
    private String currentHomeDistrictName;

    @Schema(description = "观众信息-企业名称", example = "德勤管理咨询（上海）有限公司")
    @NotBlank(message = "企业名称不能为空")
    @Size(max = 64, message = "企业名称长度不能超过64个字符")
    private String enterpriseName;

    @Schema(description = "观众信息-部门名称", example = "CONSULTING")
    @Size(max = 64, message = "部门名称长度不能超过64个字符")
    private String departmentName;

    @Schema(description = "观众信息-职位名称", example = "经理")
    @NotBlank(message = "职位名称不能为空")
    @Size(max = 64, message = "职位名称长度不能超过64个字符")
    private String positionName;

    @Schema(description = "参展信息-用户类型", example = "VO_ACTION_USER_TYPE_3")
    @NotBlank(message = "用户类型不能为空")
    @Size(max = 64, message = "用户类型长度不能超过64个字符")
    private String memberType;

    @Schema(description = "参展信息-展会编码", example = "CIIF_2025_25")
    @NotBlank(message = "展会编码不能为空")
    @Size(max = 64, message = "展会编码长度不能超过64个字符")
    private String exhibitionCode;

    @Schema(description = "参展信息-展会中文名称", example = "中国国际工业博览会")
    @NotBlank(message = "展会中文名称不能为空")
    @Size(max = 64, message = "展会中文名称长度不能超过64个字符")
    private String exhibitionNameCn;

    @Schema(description = "参展信息-展会届数", example = "2025_25")
    @NotBlank(message = "展会届数不能为空")
    @Size(max = 10, message = "展会届数长度不能超过10个字符")
    private String exhibitionSession;

    @Schema(description = "参展信息-展会标签", example = "CIIF")
    @NotBlank(message = "展会标签不能为空")
    @Size(max = 64, message = "展会标签长度不能超过64个字符")
    private String exhibitionTagCode;

    @Schema(description = "参展信息-注册时间", example = "2025-08-04 09:30:45")
    @NotBlank(message = "注册时间不能为空")
    @Size(max = 20, message = "注册时间长度不能超过20个字符")
    private String registerTime;

    @Schema(description = "参展信息-观众参展状态", example = "VO_AUDIENCE_PARTICIPATE_STATUS_3")
    @NotBlank(message = "观众参展状态不能为空")
    @Size(max = 64, message = "观众参展状态长度不能超过64个字符")
    private String participateStatus;

    @Schema(description = "参展信息-注册系统", example = "VO_REGISTER_SYSTEM_2")
    @NotBlank(message = "注册系统不能为空")
    @Size(max = 64, message = "注册系统长度不能超过64个字符")
    private String registerSystem;

    @Schema(description = "参展信息-注册方式", example = "VO_REGISTER_METHOD_1")
    @NotBlank(message = "注册方式不能为空")
    @Size(max = 64, message = "注册方式长度不能超过64个字符")
    private String registerMethod;

    @Schema(description = "参展信息-注册来源", example = "VO_REGISTER_SOURCE_1")
    @NotBlank(message = "注册来源不能为空")
    @Size(max = 64, message = "注册来源长度不能超过64个字符")
    private String registerSource;

    @Schema(description = "参展信息-二维码")
    @NotBlank(message = "二维码不能为空")
    private String barcode;

    @Schema(description = "购票信息-支付方式", example = "VO_PAY_METHOD_1")
    @NotBlank(message = "支付方式不能为空")
    @Size(max = 64, message = "支付方式长度不能超过64个字符")
    private String payMethod;

    @Schema(description = "购票信息-支付渠道", example = "VO_PAY_CHANNEL_1")
    @NotBlank(message = "支付渠道不能为空")
    @Size(max = 64, message = "支付渠道长度不能超过64个字符")
    private String payChannel;

    @Schema(description = "购票信息-是否使用优惠券", example = "0")
    @NotBlank(message = "是否使用优惠券不能为空")
    @Size(max = 10, message = "是否使用优惠券长度不能超过10个字符")
    private String isUseCoupon;

    @Schema(description = "问卷信息-问卷结果", example = "(1-5),(1-7),(2-9),(2-11),(3-20)")
    private String questionnaireResult;
}
