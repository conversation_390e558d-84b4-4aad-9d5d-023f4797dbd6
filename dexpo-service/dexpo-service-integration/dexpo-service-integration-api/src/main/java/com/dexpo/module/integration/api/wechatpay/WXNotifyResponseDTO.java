package com.dexpo.module.integration.api.wechatpay;

import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Description TODO
 * @Date 20/09/2018 14:00
 */
@XmlRootElement(name = "xml")
@Data
public class WXNotifyResponseDTO {

    /**
     * 返回状态码
     */
    private String return_code;

    /**
     * 返回信息
     */
    private String return_msg;

    /**
     * 返回状态码
     */
    private String code;

    /**
     * 返回信息
     */
    private String message;

}
