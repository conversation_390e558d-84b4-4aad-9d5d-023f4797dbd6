package com.dexpo.module.integration.utils;

import com.dexpo.framework.common.util.date.DateTimeUtils;
import com.dexpo.framework.common.util.date.LocalDateTimesUtils;

/**
 * 文件相关工具类
 */

public class FileUtils {

    /**
     * 获取文件上传路径
     * /当前日期/用户编码
     *
     * @param memberCode memberCode
     * @return path /20250521/memberCode
     */
    public static String getFilePath(String memberCode) {
        String formatNow = LocalDateTimesUtils.formatNow(DateTimeUtils.FORMAT_DATE2);
        return formatNow + "/" + memberCode;
    }
}
