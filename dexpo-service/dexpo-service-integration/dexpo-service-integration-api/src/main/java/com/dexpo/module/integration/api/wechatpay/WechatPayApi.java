package com.dexpo.module.integration.api.wechatpay;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.integration.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.Map;


@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 文件上传 下载 删除")
public interface WechatPayApi {

    @PostMapping(value = "/wechat/payCallBack")
    @Operation(summary = "微信支付回调")
    CommonResult<WXNotifyResponseDTO> payCallBack(@RequestBody WXNotifyMsgDTO wxNotifyMsgDTO) throws GeneralSecurityException, IOException;

    @PostMapping(value = "/wechat/create/order")
    @Operation(summary = "微信支付创建订单")
    CommonResult<Map<String, String>> handlePay(@RequestBody WechatPayDTO wechatPayDTO);


    @PostMapping(value = "/wechat/getOpenId")
    @Operation(summary = "用户通过code获取openid")
    CommonResult<WechatOpenidDTO> getOpenId(@Valid @RequestBody WechatOpenidDTO wechatOpenidDTO);
}
