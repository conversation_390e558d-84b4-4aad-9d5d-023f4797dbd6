package com.dexpo.module.integration.api.wechatpay;

import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Data;

@XmlRootElement(name = "xml")
@Data
public class WXNotifyMsgDTO {
    private String id;
    private String create_time;
    private String resource_type;
    private String event_type;
    private String summary;
    private resource resource;

    @Data
    public class resource {
        private String original_type;
        private String algorithm;
        private String ciphertext;
        private String associated_data;
        private String nonce;
    }

}
