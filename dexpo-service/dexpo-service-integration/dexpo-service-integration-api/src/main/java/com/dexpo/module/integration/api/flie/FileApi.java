package com.dexpo.module.integration.api.flie;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.integration.api.flie.vo.FileVo;
import com.dexpo.module.integration.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;



@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 文件上传 下载 删除")
public interface FileApi {

    @PostMapping(value = "/file/upload", consumes = "multipart/form-data")
    @Operation(summary = "文件上传")
    CommonResult<String> uploadFile(@RequestParam("path") String path, @RequestPart("file") MultipartFile file);

    @PostMapping("/file/uploadFileByBytes")
    @Operation(summary = "文件上传")
    CommonResult<String> uploadFileByBytes(@RequestParam("originalFilename") String originalFilename,
                                           @RequestParam("path") String path,
                                           @RequestBody byte[] content);

    @GetMapping("/file/get/file")
    @Operation(summary = "文件下载")
    CommonResult<FileVo> getFileContent(@RequestParam("path") String path);
}
