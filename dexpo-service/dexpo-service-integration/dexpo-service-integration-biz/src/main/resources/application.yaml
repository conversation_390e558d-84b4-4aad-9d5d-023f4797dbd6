spring:
  application:
    name: dexpo-service-integration
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Feign 等会存在重复定义的服务
  profiles:
    active: ${ENV:local}
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER # 解决 SpringFox 与 SpringBoot 2.6.x 不兼容的问题，参见 SpringFoxHandlerProviderBeanPostProcessor 类

  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 LocalDateTime 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 **********.401，而是直接 **********401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean

  # Cache 配置项
  cache:
    type: REDIS
    redis:
      time-to-live: 1h # 设置过期时间为 1 小时

  neo4j:
    pool:
      metrics-enabled: false
  cloud:
    kubernetes:
      discovery:
        # 让所有命名空间服务都可以发现服务
        namespaces:
          - ${ENV}
        # 发现未标记为“就绪”的服务端地址
        include-not-ready-addresses: true
        # ExternalName类型服务的列表  DiscoveryClient::getInstances 返回该列表 ServiceInstance::getMetadata
        include-external-name-services: true
        enabled: true
    function:
      # 消费者Bean方法命 mailChannel-in-0 中 mailChannel一致
      definition: mailChannel;smsChannel;dataWarehouseChannel;dataWarehouseAudienceSyncChannel
    stream:
      rocketmq:
        binder:
          name-server: rmq-cn-btz49fcqh07.cn-shanghai.rmq.aliyuncs.com:8080
          access-key: ${ACCESS_KEY}
          secret-key: ${SECRET_KEY}
      bindings:
        # 消费者
        mailChannel-in-0:
          binder: rocketmq
          destination: MAIL-CHANNEL
          group: dev-message-mail

        # 消费者
        smsChannel-in-0:
          binder: rocketmq
          destination: SMS-CHANNEL
          group: dev-message-sms
        # 消费者
        dataWarehouseChannel-in-0:
          binder: rocketmq
          destination: MEDIA-REGISTER-DATAWAREHOUSE-CHANNEL
          group: dev-consumer-group
        # 观众用户注册、生成订单、激活同步数仓
        dataWarehouseAudienceSyncChannel-in-0:
          binder: rocketmq
          destination: AUDIENCE-SYNC-DATAWAREHOUSE-CHANNEL
          group: dev-audience-datawarehouse-group

--- #################### RR低代码相关配置 ####################
springdoc:
  api-docs:
    enabled: false # 1. 是否开启 Swagger 接文档的元数据

dexpo:
  info:
    version: 1.0.0
    base-package: com.dexpo.module.integration
  web:
    admin-ui:
      url: http://dashboard.roselife.com # Admin 管理后台 UI 的地址
  swagger:
    title: integration
    description: integration api
    version: ${dexpo.info.version}
    base-package: ${dexpo.info.base-package}
  codegen:
    base-package: com.dexpo
    db-schemas: ${spring.datasource.dynamic.datasource.master.name}
    front-type: 10 # 前端模版的类型，参见 CodegenFrontTypeEnum 枚举类
  error-code: # 错误码相关配置项
    constants-class-list:
      - com.dexpo.module.infra.enums.ErrorCodeConstants
  tenant: # 多租户相关配置项
    enable: true
    ignore-urls:
      - /admin-api/infra/file/*/get/** # 获取图片，和租户无关
    ignore-tables:
      - infra_codegen_column
      - infra_codegen_table
      - infra_test_demo
      - infra_config
      - infra_file_config
      - infra_file
      - infra_file_content
      - infra_job
      - infra_job_log
      - infra_job_log
      - infra_data_source_config
  metrics:
    enable: false
debug:

# 发送认证消息mock
integration:
  code:
    mock: true