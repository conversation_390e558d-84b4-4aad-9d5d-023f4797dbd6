--- #################### 数据库相关配置 ####################
spring:
  data:
    redis:
      host: ${REDIS_HOST}
      port: 6379
      ssl:
        enabled: false
      password: ${REDIS_PASSWORD}
      database: 0

# 日志文件配置
logging:
  level:
    # 配置自己写的 MyBatis Mapper 打印日志
    com.dexpo.module.integration.dal.mysql: debug
aliyun:
  endpoint: http://oss-cn-shanghai.aliyuncs.com
  accessKey: ${ALIYUN_ACCESSKEY}
  accessSecret: ${ALIYUN_ACCESSSECRET}
  oss:
    domain: https://dexpo-sc-dev.oss-cn-shanghai.aliyuncs.com
    region: cn-shanghai
    bucket: dexpo-sc-dev
  cloudauth:
    endpoint: cloudauth-dualstack.cn-shanghai.aliyuncs.com
    paramType: md5
wx:
  app-id: ${WX_APP_ID}
  app-secret: ${WX_APP_SECRET}
  mch-id: ${WX_MCH_ID}
  mch-key: ${WX_MCH_KEY}
  notify-url: https://apidev.dlg-expo.com/user/wechat/pay/payNotify
  pre-payment-url: https://api.mch.weixin.qq.com
  refund-notify-url: https://apidev.dlg-expo.com/user/wechat/pay/refundNotify
  refund-url: https://api.mch.weixin.qq.com/secapi/pay/refund
  api-file-url: ${WX_PRIVATE_KEY}
  open-url: ${WX_OPEN_URL}
  api-v3-key: ${WX_API_V3_KEY}
--- #################### RR低代码相关配置 ####################

# RR低代码配置项，设置当前项目所有自定义的配置
dexpo:
  info:
    base-package: com.dexpo # 基础包路径
  env: # 多环境的配置项
    tag: ${HOSTNAME}
  security:
    mock-enable: true
  web:
    admin-ui:
      url: http://localhost:48080 # Admin UI 地址
      title: DEXPO Admin # Admin UI 标题
  xss:
    enable: false
    exclude-urls: # 如下两个 url，仅仅是为了演示，去掉配置也没关系
      - ${spring.boot.admin.context-path}/** # 不处理 Spring Boot Admin 的请求
      - ${management.endpoints.web.base-path}/** # 不处理 Actuator 的请求
  access-log: # 访问日志的配置项
    enable: false
  error-code: # 错误码相关配置项
    enable: false
  demo: false # 关闭演示模式
  sign:
    - appKey:
      appSecret:
      privateKey:
      publicKey:
#################### 接口文档配置 ####################

springdoc:
  api-docs:
    enabled: true # 1. 是否开启 Swagger 接文档的元数据
    path: /v3/api-docs
  swagger-ui:
    enabled: true # 2.1 是否开启 Swagger 文档的官方 UI 界面
    path: /swagger-ui.html


####数仓同步数据接口相关信息####
data_warehouse:
  grant_type:  client_credentials
  client_id:  service-center
  client_secret: ${DATA_WAREHOUSE_CLIENT_SECRET}
  token_url:  https://iedw.ciif-expo.com/oauth2/client_token
  hostname:  https://iedw.ciif-expo.com
  environment:  dev
# 发送认证消息mock
integration:
  code:
    mock: true