package com.dexpo.module.integration.client;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.authorize.api.datawarehouse.DataWarehouseAuthorizeApi;
import com.dexpo.module.authorize.api.datawarehouse.vo.DataWarehouseAuthorizeVO;
import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseSyncMessage;
import com.dexpo.module.integration.config.DataWareHouseApiConfig;
import com.dexpo.module.integration.token.AccessTokenGen;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 数仓接口客户端
 */
@Component
@Slf4j
public class DataWarehouseApiClient {


    @Resource
    private AccessTokenGen accessTokenGen;

    @Resource
    private DataWareHouseApiConfig dataWarehouseApiConfig;

    @Resource
    private DataWarehouseAuthorizeApi dataWarehouseAuthorizeApi;

    public String syncData(String jsonObject, String requestUrl) {
        log.info("开始同步数据到数仓，同步对象：{}", jsonObject);
        try {
            // 获取访问令牌
            DataWarehouseAuthorizeVO vo = dataWarehouseAuthorizeApi.generateBearerToken();
            String token = vo.getClientToken();
            log.info("获取数仓访问令牌成功，token：{}", token);

            // 构建请求URL
            String url = dataWarehouseApiConfig.getHostname() + requestUrl;
            log.info("数仓同步接口地址：{}", url);

            // 发送POST请求
            HttpResponse response = HttpRequest.post(url)
                    .header("Authorization", "Bearer " + token)
                    .body(jsonObject)
                    .execute();

            // 解析响应
            String responseBody = response.body();
            log.info("数仓同步接口响应：{}", responseBody);

            CommonResult<String> result = JSONUtil.toBean(responseBody, CommonResult.class);
            if (!result.isSuccess()) {
                log.error("同步数据到数仓失败，错误信息：{}", result.getMsg());
                throw new ServiceException(result.getCode(), result.getMsg());
            }

            String requestId = result.getData();
            log.info("同步数据到数仓成功，数仓请求ID：{}", requestId);
            return requestId;
        } catch (Exception e) {
            log.error("同步数据到数仓异常", e);
            throw new ServiceException(500, "同步数据到数仓异常：" + e.getMessage());
        }
    }
}
