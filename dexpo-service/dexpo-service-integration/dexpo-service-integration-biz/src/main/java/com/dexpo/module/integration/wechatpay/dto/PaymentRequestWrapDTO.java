package com.dexpo.module.integration.wechatpay.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

@ApiModel("支付请求封装DTO")
@Data
public class PaymentRequestWrapDTO {

    @ApiModelProperty("支付单DTO")
    private PaymentBillDTO paymentBillDTO;

    /**
     * 支付成功或失败后的跳转地址
     */
    @ApiModelProperty("支付成功或失败后的跳转地址")
    private String returnUrl;

    @ApiModelProperty("客户端类型")
    private String clientType;

    /**
     * 是否二次进入
     */
    @ApiModelProperty("是否二次进入")
    private Boolean replicate;

    /**
     * 是否新生成的支付单
     */
    @ApiModelProperty("是否新生成的支付单")
    private Boolean newPaymentBill;

    /**
     * 之前的支付单状态
     */
    @ApiModelProperty("之前的支付单状态")
    private String oldStatus;

    /**
     * 之前的支付单的支付渠道
     */
    @ApiModelProperty("之前的支付单的支付渠道")
    private String oldChannelCode;

    /**
     * 退款金额
     */
    @ApiModelProperty("退款金额")
    private BigDecimal refundAmount;

    /**
     * 请求参数map(各个渠道参数可能不想同)
     */
    @ApiModelProperty("请求参数map")
    private Map<String, String> paramMap;

    /**
     * clientIP(微信支付会用到)
     */
    @ApiModelProperty("客户端ip")
    private String clientIP;

    /**
     * 微信支付时显示给客户的信息
     */
    @ApiModelProperty("微信支付时显示给客户的信息")
    private String body;

    /**
     * 微信小程序支付时，用此code获取openId
     */
    @ApiModelProperty("编号")
    private String code;

    /**
     * 微信小程序支付时，用此code获取openId
     */
    @ApiModelProperty("openid")
    private String openid;

    /**
     * APP来源端
     * seller:卖家
     * buyer:买家
     * driver:司机
     */
    @ApiModelProperty("app类型")
    private String appType;

    /**
     * 总支付金额
     */
    @ApiModelProperty("总支付金额")
    private BigDecimal totalPayAmount;

    @ApiModelProperty("业务id")
    private String bizId;

    @ApiModelProperty("是否为月结客户 1：是、0：否")
    private Integer isMonthly;

    @ApiModelProperty("是否校验物流费 1：是、0：否")
    private BigDecimal subtractAmount = BigDecimal.ZERO;
}
