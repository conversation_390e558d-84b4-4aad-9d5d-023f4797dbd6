package com.dexpo.module.integration.wechatpay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dexpo.framework.common.exception.ServerException;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.integration.api.wechatpay.WXNotifyMsgDTO;
import com.dexpo.module.integration.api.wechatpay.WXNotifyResponseDTO;
import com.dexpo.module.integration.wechatpay.config.WxProperties;
import com.dexpo.module.integration.wechatpay.dto.PaymentCallbackRequestDTO;
import com.dexpo.module.integration.wechatpay.dto.PaymentCallbackResponseDTO;
import com.dexpo.module.integration.wechatpay.dto.PaymentRequestWrapDTO;
import com.dexpo.module.integration.wechatpay.utils.AesUtil;
import com.dexpo.module.integration.wechatpay.utils.OkHttpUtil;
import com.dexpo.module.integration.wechatpay.utils.WXPayUtil;
import com.google.common.collect.Maps;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.payments.jsapi.JsapiServiceExtension;
import com.wechat.pay.java.service.payments.jsapi.model.Amount;
import com.wechat.pay.java.service.payments.jsapi.model.Payer;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayWithRequestPaymentResponse;
import jakarta.servlet.ServletException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.HashMap;
import java.util.Map;

@Service("weixin")
@Slf4j
public class WeiXinAdapter {

    private static final String RETURN_MSG = "return_msg";
    private static final String RETURN_CODE = "return_code";
    @Autowired
    private WxProperties wxProperties;

    /**
     * 创建支付订单
     *
     * @param paymentRequest paymentRequest
     * @return
     */

    public Map<String, String> createPrePaymentOrder(PaymentRequestWrapDTO paymentRequest) throws ServletException {
        String openId = paymentRequest.getOpenid();
        paymentRequest.setClientType("JSAPI");
        try {
            return buildPayRequestV3(paymentRequest, openId);
        } catch (Exception e) {
            log.error(e.toString());
            throw new ServletException(e.getMessage());
        }
    }

    private Map<String, String> buildPayRequestV3(PaymentRequestWrapDTO paymentRequest, String openId) throws Exception {
        // 使用自动更新平台证书的RSA配置
        // 建议将 config 作为单例或全局静态对象，避免重复的下载浪费系统资源
        Config config =
                new RSAAutoCertificateConfig.Builder()
                        .merchantId(wxProperties.getMchId())
                        .merchantSerialNumber(wxProperties.getMchKey())
                        .privateKey(wxProperties.getApiFileUrl())
                        .apiV3Key(wxProperties.getApiV3Key())
                        .build();

        // 构建service
        JsapiServiceExtension service = new JsapiServiceExtension.Builder().config(config).build();
        PrepayRequest request = new PrepayRequest();
        request.setAppid(wxProperties.getAppId());
        request.setMchid(wxProperties.getMchId());
        request.setDescription(paymentRequest.getBody());
        request.setOutTradeNo(paymentRequest.getPaymentBillDTO().getPaymentBillNo());
        request.setNotifyUrl(wxProperties.getNotifyUrl());
        Amount amount = new Amount();
        amount.setTotal(paymentRequest.getPaymentBillDTO().getPayAmount().intValue());
        request.setAmount(amount);
        Payer payer = new Payer();
        payer.setOpenid(openId);
        request.setPayer(payer);
        // 调用下单方法，得到应答
        PrepayWithRequestPaymentResponse prepay = service.prepayWithRequestPayment(request);
        log.info("PrepayResponse= {}", prepay.toString());

        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("appId", prepay.getAppId());
        paramMap.put("timeStamp", prepay.getTimeStamp());
        paramMap.put("nonceStr", prepay.getNonceStr());
        paramMap.put("package", prepay.getPackageVal());
        paramMap.put("signType", prepay.getSignType());
        paramMap.put("signMsg", prepay.getPaySign());
        log.info("paramMap={}", paramMap);
        return paramMap;
    }

    public PaymentCallbackResponseDTO handlePayCallback(PaymentCallbackRequestDTO paymentRequest) {
        log.info(" 微信支付 handlePayCallback {} ", JSON.toJSONString(paymentRequest));
        PaymentCallbackResponseDTO responseDTO = new PaymentCallbackResponseDTO();
        Map<String, String> wxNotifyResponse = new HashMap<>();
        wxNotifyResponse.put(RETURN_CODE, "FAIL");

        if (paymentRequest == null) {
            log.error("微信支付回调为空:" + paymentRequest);
            wxNotifyResponse.put(RETURN_MSG, "微信支付回调为空");
            responseDTO.setContent(wxNotifyResponse);
            return responseDTO;
        }

        if (StringUtils.isEmpty(paymentRequest.getPaymentBillId())) {
            log.error("支付单ID为空:" + paymentRequest.getPaymentBillId());
            wxNotifyResponse.put(RETURN_MSG, "支付单ID为空");
            responseDTO.setContent(wxNotifyResponse);
            return responseDTO;
        }

        Map<String, Object> map = paymentRequest.getParms();
        Map<String, String> wxNotifyRequest = Maps.newHashMap();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            wxNotifyRequest.put(entry.getKey(), (String) entry.getValue());
        }

        log.info("开始验签----");
        boolean valid = false;
        try {
            valid = WXPayUtil.isSignatureValid(wxNotifyRequest, wxProperties.getMchKey());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        log.info("验签结果----{}", valid);
        if (!valid) {
            log.info("微信验签失败");
            throw new ServerException(1, "PayCode.WECHAT_SIGN_ERROR");
        }


        String result_code = wxNotifyRequest.get("result_code");
        String transaction_id = wxNotifyRequest.get("transaction_id");
        if ("SUCCESS".equals(result_code)) {
            log.info("支付成功！");
        }
        responseDTO.setContent(wxNotifyResponse);
        return responseDTO;
    }

    public String onLogin(String code) {
        Map<String, Object> result = new HashMap<>();
        String url = wxProperties.getOpenUrl();
        Map<String, String> map = new HashMap<>();
        map.put("appid", wxProperties.getAppId());
        map.put("secret", wxProperties.getAppSecret());
        map.put("code", code);
        map.put("grant_type", "authorization_code");
        log.info("用code换取openId请求的数据：url:" + url + ",map:" + map.toString());
        String rspJson = wxGetRequest(url, map);
        log.info("用code换取openId请求返回的数据：" + rspJson);
        JSONObject rspJsonObj = JSON.parseObject(rspJson);
        String openId = "";
        if (StringUtils.isEmpty(rspJsonObj.getString("errcode"))) {
            openId = rspJsonObj.getString("openid");
        } else {
            log.error("获取用户openId失败");
        }
        return openId;
    }

    public String wxGetRequest(String url, Map<String, String> map) {
        log.info("进入get方法,url" + url + ";map:" + map.toString());
        if (StringUtils.isBlank(url)) {
            log.info("发送get请求参数为空");
            //throw new BizException(BasicCode.PARAM_NULL);
        }
        return OkHttpUtil.get(url, map);
    }

    public CommonResult<WXNotifyResponseDTO> payNotify(WXNotifyMsgDTO msg) throws GeneralSecurityException, IOException {
        AesUtil aesUtil = new AesUtil(wxProperties.getApiV3Key().getBytes());
        String s = aesUtil.decryptToString(msg.getResource().getAssociated_data().getBytes(),
                msg.getResource().getNonce().getBytes(), msg.getResource().getCiphertext());
        log.info("payNotify {}", s);
        return null;
    }


}
