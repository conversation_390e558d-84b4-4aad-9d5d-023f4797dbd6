package com.dexpo.module.integration.file;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.text.CharSequenceUtil;
import com.aliyun.oss.*;
import com.aliyun.oss.common.auth.Credentials;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.common.auth.DefaultCredentials;
import com.aliyun.oss.common.comm.SignVersion;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectRequest;
import com.dexpo.framework.common.exception.enums.ErrorCodeEnums;
import com.dexpo.framework.common.util.io.FileUtils;
import com.dexpo.module.integration.api.flie.vo.FileVo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.List;

/**
 * 文件 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FileServiceImpl implements FileService {

    @Value("${aliyun.oss.domain}")
    private String domain;
    @Value("${aliyun.oss.region}")
    private String region;
    @Value("${aliyun.oss.bucket}")
    private String bucket;

    @Value("${aliyun.endpoint}")
    private List<String> endpointList;
    @Value("${aliyun.accessKey}")
    private String accessKey;
    @Value("${aliyun.accessSecret}")
    private String accessSecret;

    private OSS ossClient;

    private OSS getFileClient(){
        if (null != ossClient) {
            return ossClient;
        }
        Credentials credentials = new DefaultCredentials(accessKey,accessSecret);
        CredentialsProvider credentialsProvider = new DefaultCredentialProvider(credentials);
        // 创建OSSClient实例。
        // 当OSSClient实例不再使用时，调用shutdown方法以释放资源。
        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);
        ossClient = OSSClientBuilder.create()
                .endpoint(endpointList.get(0))
                .credentialsProvider(credentialsProvider)
                .clientConfiguration(clientBuilderConfiguration)
                .region(region)
                .build();
        return ossClient;
    }

    @Override
    @SneakyThrows
    public String createFile(String name, String path, byte[] content) {
        name = UUID.fastUUID() + "_" + name;
        // 计算默认的 path 名
        if (CharSequenceUtil.isEmpty(path)) {
            path = FileUtils.generatePath(content, name);
        }
        path += "/" + name;
        // 上传文件
        uploadAliOSS(path, content);
        // 返回路径
        return path;
    }

    @Override
    public FileVo getFileContent(String path) {
        return downloadAliOSS(path);
    }


    private void uploadAliOSS(String path, byte[] content) throws Exception {
        try {
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, path, new ByteArrayInputStream(content));

            // 上传文件。
            getFileClient().putObject(putObjectRequest);
            return;
        } catch (OSSException oe) {
            log.error("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            log.error("Error Message :{}", oe.getErrorMessage());
            log.error("Error Code:{}", oe.getErrorCode());
            log.error("Request ID:{}",  oe.getRequestId());
            log.error("Host ID:{}", oe.getHostId());

        } catch (ClientException ce) {
            log.error("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            log.error("Error Message:{}", ce.getMessage());
        }
        throw ErrorCodeEnums.FILE_UPLOAD_FAIL.getServiceException();
    }

    private FileVo downloadAliOSS(String path) {
        try {
            FileVo fileVo = new FileVo();
            fileVo.setFilePath(path);

            // ossObject包含文件所在的存储空间名称、文件名称、文件元数据以及一个输入流。
            OSSObject ossObject = getFileClient().getObject(bucket, path);
            fileVo.setFileName(ossObject.getKey());
            fileVo.setFileType(ossObject.getObjectMetadata().getContentType());
            InputStream inputStream = ossObject.getObjectContent();
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            // 读取文件内容到字节数组。
            byte[] readBuffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(readBuffer)) != -1) {
                byteArrayOutputStream.write(readBuffer, 0, bytesRead);
            }
            // 获取最终的字节数组。
            byte[] fileBytes = byteArrayOutputStream.toByteArray();
            fileVo.setFileSize(fileBytes.length);
            fileVo.setFileBytes(fileBytes);
            // 打印字节数组的长度。
            log.info("Downloaded file size: {} bytes", fileBytes.length);
            // 数据读取完成后，获取的流必须关闭，否则会造成连接泄漏，导致请求无连接可用，程序无法正常工作。
            inputStream.close();
            byteArrayOutputStream.close();
            // ossObject对象使用完毕后必须关闭，否则会造成连接泄漏，导致请求无连接可用，程序无法正常工作。
            ossObject.close();
            return fileVo;
        } catch (OSSException oe) {
            log.error("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            log.error("Error Message :{}", oe.getErrorMessage());
            log.error("Error Code:{}", oe.getErrorCode());
            log.error("Request ID:{}",  oe.getRequestId());
            log.error("Host ID:{}", oe.getHostId());
        } catch (Throwable ce) {
            log.error("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            log.error("Error Message:{}", ce.getMessage());

        }
        throw ErrorCodeEnums.FILE_UPLOAD_FAIL.getServiceException();
    }




}
