package com.dexpo.module.integration.message.impl;

import com.dexpo.framework.cache.redis.constant.BasicRedisKey;
import com.dexpo.framework.cache.redis.constant.ICacheKey;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.framework.common.util.code.CodeGenerator;
import com.dexpo.module.integration.api.message.dto.ValidCodeDTO;
import com.dexpo.module.integration.constant.IntegrationConstant;
import com.dexpo.module.integration.message.EmailService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
public class EmailServiceImpl implements EmailService {

    private final RedisService redisService;

    @Value("${basic.code.mock:true}")
    private Boolean mock;

    @Override
    public void sendEmailValidCode(ValidCodeDTO request) {

        String code = CodeGenerator.generate();
        if (Boolean.TRUE.equals(mock)) {
            code = "123456";
        }
        String key = ICacheKey.generateKey(BasicRedisKey.BASIC_LOGIN_VALID_KEY, request.getText());
        redisService.setCacheObject(key, code, IntegrationConstant.DEFAULT_LOGIN_CODE_INVALID_TIME, TimeUnit.MINUTES);
    }
}
