package com.dexpo.module.integration.mq.sms;

import com.dexpo.module.integration.api.message.dto.ValidCodeDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

/**
 * Mail 邮件相关消息的 Producer
 *
 * <AUTHOR>
 * @since 2021/4/19 13:33
 */
@Slf4j
@Component
public class SmsChannelProducer {

    @Resource
    private StreamBridge streamBridge;

    public String smsChannel(String content) {
        ValidCodeDTO validCodeDTO = new ValidCodeDTO();
        Message<ValidCodeDTO> message = MessageBuilder.withPayload(validCodeDTO).build();
        streamBridge.send("smsChannel-out-0", message);
        log.info("smsChannel-out-0 {},发送成功", content);
        return "success";
    }

}
