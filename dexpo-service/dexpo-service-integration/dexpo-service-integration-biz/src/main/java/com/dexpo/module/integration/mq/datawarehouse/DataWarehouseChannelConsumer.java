package com.dexpo.module.integration.mq.datawarehouse;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseSyncMessage;
import com.dexpo.module.integration.client.DataWarehouseApiClient;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.function.Consumer;

@Component
@Slf4j
public class DataWarehouseChannelConsumer {

    @Resource
    private DataWarehouseApiClient dataWarehouseApiClient;

    private static final String SYNC_URL = "/open-api/sc/media/sync";


    @Bean(name = "dataWarehouseChannel")
    public Consumer<DataWarehouseSyncMessage> dataWarehouseChannel() {
        return dataWarehouseSyncMessage -> {
            log.info("DataWarehouseChannelConsumer.dataWarehouseChannel: {}", dataWarehouseSyncMessage);
            try {
                validCheck(dataWarehouseSyncMessage);
                dataWarehouseApiClient.syncData(JSONUtil.toJsonStr(dataWarehouseSyncMessage),SYNC_URL);
            }catch (Exception e){
                log.error("DataWarehouseChannelConsumer.dataWarehouseChannel error: {}", e.getMessage());
            }
        };
    }

    private void validCheck(DataWarehouseSyncMessage message){
        if(ObjectUtil.isEmpty(message)){
            throw new RuntimeException("DataWarehouseChannelConsumer message is null");
        }
        
        if(StringUtils.isAnyEmpty(
            message.getMemberCode(),
            message.getMemberName(),
            message.getMemberGender(),
            message.getMemberBirthDay(),
            message.getMemberMobile(),
            message.getMemberEmail(),
            message.getIdCategory(),
            message.getIdNumber(),
            message.getCountryCode(),
            message.getCountryName(),
            message.getCertificateCollectionMethod(),
            message.getRecipientAddress(),
            message.getEnterpriseName(),
            message.getMediaNewsmanNo(),
            message.getMediaTypeCode(),
            message.getMediaPositionCategoryCode(),
            message.getMediaPositionCode(),
            message.getIsApplyLiveStream(),
            message.getAttachmentHeadPhoto(),
            message.getMemberType(),
            message.getExhibitionCode(),
            message.getExhibitionNameCn(),
            message.getExhibitionNameEn(),
            message.getExhibitionSessionKey(),
            message.getExhibitionTagCode(),
            message.getRegisterTime(),
            message.getRegisterStatus(),
            message.getRegisterSystem(),
            message.getRegisterMethod(),
            message.getRegisterSource()
        )){
            throw new RuntimeException("DataWarehouseChannelConsumer parameter is invalid: " + JSONUtil.toJsonStr(message));
        }
    }
}
