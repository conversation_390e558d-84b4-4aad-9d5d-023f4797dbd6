package com.dexpo.module.integration.mq.mail;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Component;

/**
 * Mail 邮件相关消息的 Producer
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class MailChannelProducer {

    @Resource
    private StreamBridge streamBridge;

    public String mailChannel(String content) {
        streamBridge.send("mailChannel-out-0", content);
        log.info("mailChannel-out-0 {},发送成功", content);
        return "success";
    }

}
