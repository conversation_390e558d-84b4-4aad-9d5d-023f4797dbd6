package com.dexpo.module.integration.mq.datawarehouse;

import com.alibaba.fastjson2.JSON;
import com.dexpo.framework.common.enums.ValueSetInterfaceEnum;
import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseAudienceSyncMessage;
import com.dexpo.module.integration.client.DataWarehouseApiClient;
import com.dexpo.module.integration.client.DataWarehouseSyncService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.function.Consumer;

@Component
@Slf4j
public class DataWarehouseAudienceSyncChannelConsumer {

    @Resource
    private DataWarehouseSyncService dataWarehouseSyncService;


    @Resource
    private DataWarehouseApiClient dataWarehouseApiClient;

    public static final String SYNC_URL = "/open-api/sc/register/sync";

//    No route info of this topic: dataWarehouseAudienceSyncChannel-out-0
//    See https://rocketmq.apache.org/docs/bestPractice/06FAQ for further details.
    @Bean(name = "dataWarehouseAudienceSyncChannel")
    public Consumer<DataWarehouseAudienceSyncMessage> dataWarehouseAudienceSyncChannel() {

        // 检查参数信息
        return dataWarehouseSyncMessage -> {
            log.info("DataWarehouseAudienceSyncChannelConsumer.dataWarehouseAudienceSyncChannel: {}", dataWarehouseSyncMessage);
            String result = dataWarehouseSyncService.syncData(ValueSetInterfaceEnum.VO_INTERFACE_SC_DW_002, dataWarehouseSyncMessage,
                    (data) ->
                            dataWarehouseApiClient.syncData(JSON.toJSONString(data), SYNC_URL)
            );

            log.info("DataWarehouseAudienceSyncChannelConsumer.dataWarehouseAudienceSyncChannel: {},result:{}", dataWarehouseSyncMessage, result);
        };
    }

}
