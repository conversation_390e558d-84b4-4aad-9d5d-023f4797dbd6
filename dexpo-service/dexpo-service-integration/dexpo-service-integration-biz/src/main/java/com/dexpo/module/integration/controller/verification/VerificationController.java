package com.dexpo.module.integration.controller.verification;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.integration.api.verification.VerificationApi;
import com.dexpo.module.integration.api.verification.dto.VerificationDTO;
import com.dexpo.module.integration.api.verification.vo.VerificationVO;
import com.dexpo.module.integration.verification.VerificationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Validated
@Slf4j
public class VerificationController implements VerificationApi {

    @Resource
    private VerificationService verificationService;

    @Override
    public CommonResult<List<VerificationVO>> Id2MetaVerifyList(List<VerificationDTO> verificationDTO) {
        return CommonResult.success(verificationService.checkMemberNameAndIdNumberList(verificationDTO));
    }

    @Override
    public CommonResult<VerificationVO> Id2MetaVerify(VerificationDTO verificationDTO) {
        return CommonResult.success(verificationService.checkMemberNameAndIdNumber(verificationDTO));
    }
}
