package com.dexpo.module.integration.wechatpay.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: <EMAIL>
 * @created 14:04 02/03/2019
 * @description TODO
 */
@ApiModel("支付单详情DTO")
@Data
public class PaymentBillDetailDTO {


    @ApiModelProperty("支付单详情id")
    private String paymentBillDetailId;

    /**
     * 编号
     */
    @ApiModelProperty("编号")
    private String paymentBillDetailCode;

    /**
     * 支付单编号（流水号）
     */
    @ApiModelProperty("支付单编号（流水号）")
    private String subject;

    /**
     * 支付单编号（流水号）
     */
    @ApiModelProperty("支付单编号（流水号）")
    private String paymentBillNo;

    @ApiModelProperty("状态")
    private String status;

    /**
     * 收款方会员ID
     */
    @ApiModelProperty("收款方会员ID")
    private String payeeMemberId;

    /**
     * 收款方会员名称
     */
    @ApiModelProperty("收款方会员名称")
    private String payeeMemberName;

    /**
     * 交易金额
     */
    @ApiModelProperty("交易金额")
    private BigDecimal payAmount;

    @ApiModelProperty("币种")
    private String currency;

    /**
     * 实际交易金额
     */
    @ApiModelProperty("实际交易金额")
    private BigDecimal actualPayAmount;

    /**
     * 手续费
     */
    @ApiModelProperty("手续费")
    private BigDecimal serviceFee;

    /**
     * 红包、零钱，退款时需要考虑如何处理
     */
    @ApiModelProperty("红包、零钱，退款时需要考虑如何处理")
    private String otherFee;

    /**
     * 支付渠道名称
     */
    @ApiModelProperty("支付渠道名称")
    private String channelName;

    /**
     * 支付渠道code
     */
    @ApiModelProperty("支付渠道code")
    private String channelCode;

    @ApiModelProperty("警告")
    private String warning;

}
