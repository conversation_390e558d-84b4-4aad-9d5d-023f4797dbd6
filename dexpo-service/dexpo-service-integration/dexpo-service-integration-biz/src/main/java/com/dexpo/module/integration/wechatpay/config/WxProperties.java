package com.dexpo.module.integration.wechatpay.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "wx")
public class WxProperties {

    private String appId;

    private String appSecret;

    private String mchId;

    private String mchKey;

    private String notifyUrl;

    private String prePaymentUrl;

    private String refundUrl;

    private String refundNotifyUrl;

    private String apiFileUrl;

    private String openUrl;

    private String apiV3Key;

    /**
     * 支付有效期(秒) 默认90秒
     */
    private Integer effectiveTimeSeconds = 90;

}
