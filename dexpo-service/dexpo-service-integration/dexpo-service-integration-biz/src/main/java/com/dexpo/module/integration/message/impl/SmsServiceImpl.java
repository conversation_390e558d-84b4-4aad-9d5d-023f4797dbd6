package com.dexpo.module.integration.message.impl;

import com.dexpo.framework.cache.redis.constant.BasicRedisKey;
import com.dexpo.framework.cache.redis.constant.ICacheKey;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.exception.enums.IntegrationServiceErrorCodeEnum;
import com.dexpo.framework.common.util.code.CodeGenerator;
import com.dexpo.module.integration.api.message.dto.ValidCodeDTO;
import com.dexpo.module.integration.constant.IntegrationConstant;
import com.dexpo.module.integration.message.SmsService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * sms 服务
 */
@Service
@RequiredArgsConstructor
public class SmsServiceImpl implements SmsService {

    private final RedisService redisService;


    @Value("${basic.code.mock:true}")
    private Boolean mock;

    @Override
    public void sendSms(ValidCodeDTO mobile) {

        String limitKey = ICacheKey.generateKey(BasicRedisKey.BASIC_LOGIN_SMS_LIMIT_KEY, mobile.getText());
        boolean limitCheck = redisService.setNx(limitKey, 1, IntegrationConstant.DEFAULT_LOGIN_CODE_LIMIT_TIME, TimeUnit.SECONDS);
        if (!limitCheck) {
            throw new ServiceException(IntegrationServiceErrorCodeEnum.VALID_CODE_SEND_LIMIT);
        }


        String code = CodeGenerator.generate();
        if (Boolean.TRUE.equals(mock)) {
            code = "123456";
        }
        String key = ICacheKey.generateKey(BasicRedisKey.BASIC_LOGIN_VALID_KEY, mobile.getText());
        redisService.setCacheObject(key, code, IntegrationConstant.DEFAULT_LOGIN_CODE_INVALID_TIME, TimeUnit.MINUTES);

    }

}
