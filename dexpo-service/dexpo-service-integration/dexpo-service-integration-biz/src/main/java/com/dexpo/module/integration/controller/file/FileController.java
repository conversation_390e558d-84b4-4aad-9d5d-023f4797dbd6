package com.dexpo.module.integration.controller.file;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.dexpo.framework.common.exception.enums.ErrorCodeEnums;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.integration.api.flie.FileApi;
import com.dexpo.module.integration.api.flie.vo.FileVo;
import com.dexpo.module.integration.file.FileService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static com.dexpo.framework.common.pojo.CommonResult.success;

@RestController
@Validated
@Slf4j
public class FileController implements FileApi {

    @Resource
    private FileService fileService;

    @Override
    public CommonResult<String> uploadFile(@RequestParam("path") String path, @RequestParam("file") MultipartFile file) {
        byte[] fileBytes = null;
        try {
            fileBytes = IoUtil.readBytes(file.getInputStream());
        } catch (IOException e) {
            throw ErrorCodeEnums.FILE_UPLOAD_FAIL.getServiceException();
        }
        return success(fileService.createFile(file.getOriginalFilename(), path, fileBytes));
    }

    @Override
    public CommonResult<String> uploadFileByBytes(@RequestParam("path") String path,
                                                  @RequestParam("originalFilename") String originalFilename,
                                                  @RequestBody byte[] content) {
        return success(fileService.createFile(originalFilename, path, content));
    }


    @Override
    public CommonResult<FileVo> getFileContent(String path) {
        // 获取请求的路径
        if (CharSequenceUtil.isEmpty(path)) {
            throw new IllegalArgumentException("结尾的 path 路径必须传递");
        }
        // 读取内容
        FileVo content = fileService.getFileContent(path);
        return CommonResult.success(content);
    }

}
