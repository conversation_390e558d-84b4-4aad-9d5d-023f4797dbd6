package com.dexpo.module.integration.controller.wechatpay;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.integration.api.wechatpay.*;
import com.dexpo.module.integration.wechatpay.WechatPayCallBackService;
import com.dexpo.module.integration.wechatpay.WeiXinAdapter;
import com.dexpo.module.integration.wechatpay.WeixinRefundPost;
import io.swagger.annotations.ApiOperation;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.Map;

@RestController
@Validated
@Slf4j
public class WechatPayController implements WechatPayApi {

    @Resource
    private WechatPayCallBackService wechatPayCallBackService;
    @Resource
    private WeixinRefundPost wxRefundRequest;

    @Resource
    private WeiXinAdapter weiXinAdapter;

    @Override
    public CommonResult<WXNotifyResponseDTO> payCallBack(WXNotifyMsgDTO msg) throws GeneralSecurityException, IOException {
        return wechatPayCallBackService.payNotify(msg);
    }


    @ApiOperation("创建微信订单")
    @Override
    public CommonResult<Map<String, String>> handlePay(WechatPayDTO wechatPayDTO) {
        return CommonResult.success(wechatPayCallBackService.handlePay(wechatPayDTO));
    }

    @ApiOperation("换取openid")
    @Override
    public CommonResult<WechatOpenidDTO> getOpenId(WechatOpenidDTO wechatOpenidDTO) {
        String s = weiXinAdapter.onLogin(wechatOpenidDTO.getCode());
        WechatOpenidDTO dto = new WechatOpenidDTO();
        dto.setOpenid(s);
        dto.setCode(wechatOpenidDTO.getCode());
        return CommonResult.success(dto);
    }

}
