package com.dexpo.module.integration.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@ConfigurationProperties(prefix = "dexpo")
@Data
@Component
public class DexpoConfig {
    private List<SignConfig> sign;

    @Data
    public static class SignConfig {
        private String appKey;
        private String appSecret;
        private String privateKey;
        private String publicKey;
    }
}