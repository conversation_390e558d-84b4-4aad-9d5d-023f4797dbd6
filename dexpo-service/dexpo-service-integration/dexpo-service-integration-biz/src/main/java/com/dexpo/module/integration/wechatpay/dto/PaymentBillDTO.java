package com.dexpo.module.integration.wechatpay.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel("支付单DTO")
@Data
public class PaymentBillDTO {

    /**
     * 分账信息
     */
    @ApiModelProperty("支付单详情DTO列表")
    List<PaymentBillDetailDTO> detailDTOList;
    @ApiModelProperty("支付单id")
    private String paymentBillId;
    /**
     * 支付单编号（流水号）
     */
    @ApiModelProperty("支付单编号（流水号）")
    private String paymentBillNo;
    /**
     * 来源业务单号
     */
    @ApiModelProperty("来源业务单号")
    private String srcBizNo;
    /**
     * 第三方支付系统流水号
     */
    @ApiModelProperty("第三方支付系统流水号")
    private String extBizNo;
    /**
     * 订单ID
     */
    @ApiModelProperty("订单ID")
    private String orderId;
    @ApiModelProperty("订单编号")
    private String orderNo;
    /**
     * 第三方系统支付状态
     */
    @ApiModelProperty("第三方系统支付状态")
    private String extStatus;
    /**
     * 1000 - 已创建，未支付; 2000 - 支付中； 2010 - 支付失败（可重试）; 3000 - 已失效; 3010 - 已取消; 9000 - 支付成功;
     */
    @ApiModelProperty("状态")
    private String status;
    /**
     * 支付业务类型编码
     */
    @ApiModelProperty("支付业务类型编码")
    private String paymentBizCode;
    /**
     * 调用方支付业务名称
     */
    @ApiModelProperty("调用方支付业务名称")
    private String bizName;
    /**
     * 具体描述
     */
    @ApiModelProperty("具体描述")
    private String detail;
    /**
     * 付款方会员ID
     */
    @ApiModelProperty("付款方会员ID")
    private String payerMemberId;
    /**
     * 付款方会员名称
     */
    @ApiModelProperty("付款方会员名称")
    private String payerMemberName;
    @ApiModelProperty("付款方会员编号")
    private String payerMemberCode;
    /**
     * 收款方会员ID
     */
    @ApiModelProperty("收款方会员ID")
    private String payeeMemberId;
    /**
     * 初始交易金额
     */
    @ApiModelProperty("初始交易金额")
    private BigDecimal payAmount;
    /**
     * 实际交易金额
     */
    @ApiModelProperty("实际交易金额")
    private BigDecimal actualPayAmount;
    /**
     * 手续费
     */
    @ApiModelProperty("手续费")
    private BigDecimal serviceFee;
    /**
     * 红包、零钱，退款时需要考虑如何处理
     */
    @ApiModelProperty("红包、零钱，退款时需要考虑如何处理")
    private String otherFee;
    /**
     * 以便后面分类采取进一步措施
     */
    @ApiModelProperty("以便后面分类采取进一步措施")
    private String warningCode;
    @ApiModelProperty("币种")
    private String currency;
    /**
     * 支付渠道类型
     */
    @ApiModelProperty("支付渠道类型")
    private String channelType;
    /**
     * 支付渠道名称
     */
    @ApiModelProperty("支付渠道名称")
    private String channelName;
    /**
     * 支付渠道id
     */
    @ApiModelProperty("支付渠道id")
    private String channelId;
    /**
     * 支付渠道code
     */
    @ApiModelProperty("支付渠道code")
    private String channelCode;
    /**
     * 支付在什么时间点超时
     */
    @ApiModelProperty("支付在什么时间点超时")
    private Date expireTime;
    @ApiModelProperty("完成时间")
    private Date completeTime;
    @ApiModelProperty("关闭时间")
    private Date closeTime;
    /**
     * 支付完成通知业务方的状态
     */
    @ApiModelProperty("支付完成通知业务方的状态")
    private String notifyBusStatus;
    /**
     * 支付单备注
     */
    @ApiModelProperty("支付单备注")
    private String remarks;
    /**
     * 支付回调时间
     */
    @ApiModelProperty("支付回调时间")
    private Date notifyTime;
    @ApiModelProperty("app类型")
    private String appType;
    @ApiModelProperty("客户端类型")
    private String clientType;
    @ApiModelProperty("商品名称")
    private String goodsNames;
    @ApiModelProperty("授权支付 pa_member_channel.member_channel_id")
    private String memberChannelId;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("银联支付-商户订单号（电商端生成的流水号）")
    private String mctOrderNo;
    @ApiModelProperty("银联支付-担保支付订单号")
    private String guaranteedOrderNo;
    @ApiModelProperty("是否处理")
    private Boolean deal;
    /**
     * 线下支付可上传0~5个附件 2019.12.17
     */
    @ApiModelProperty("线下支付可上传0~5个附件")
    private List<String> offlineAttachment;

    @ApiModelProperty("erp余额查询的账号名")
    private String memberMDMCode;
    @ApiModelProperty("合同ERP编号")
    private String contractCode;
    @ApiModelProperty("销售区域对应erp的编码Code")
    private String saleRegion;
    @ApiModelProperty("ERP提货点编码")
    private String warehouse;
    @ApiModelProperty("ERP提货点对应组织编码")
    private String pickupPointOrgId;

    @ApiModelProperty("是否为月结客户 1：是、0：否")
    private Integer isMonthly;

    @ApiModelProperty("是否校验物流费 1：是、0：否")
    private BigDecimal subtractAmount = BigDecimal.ZERO;

    private String qrCodeId;
}
