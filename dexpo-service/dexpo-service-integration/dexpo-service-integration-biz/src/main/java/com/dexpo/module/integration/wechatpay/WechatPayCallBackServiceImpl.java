package com.dexpo.module.integration.wechatpay;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.integration.api.wechatpay.WXNotifyMsgDTO;
import com.dexpo.module.integration.api.wechatpay.WXNotifyResponseDTO;
import com.dexpo.module.integration.api.wechatpay.WechatPayDTO;
import com.dexpo.module.integration.wechatpay.dto.PaymentBillDTO;
import com.dexpo.module.integration.wechatpay.dto.PaymentRequestWrapDTO;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.security.GeneralSecurityException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

@Service
@Slf4j
public class WechatPayCallBackServiceImpl implements WechatPayCallBackService {

    public static String PATTERN = "yyyyMMdd";
    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(PATTERN);
    @Resource
    private WeiXinAdapter weiXinAdapter;

    private String generateRandomNumber() {
        return String.format("%05d", 100000);
    }
    
    @Override
    public Map<String, String> handlePay(WechatPayDTO wechatPayDTO) {
        PaymentRequestWrapDTO paymentRequest = new PaymentRequestWrapDTO();

        PaymentBillDTO paymentBillDTO = new PaymentBillDTO();
        String paymentBillNo = "DEXPOTEST" + LocalDateTime.now().format(DATE_TIME_FORMATTER) + generateRandomNumber();
        paymentBillDTO.setPaymentBillNo(paymentBillNo);
        paymentBillDTO.setPayAmount(new BigDecimal(wechatPayDTO.getOrderAmount()));

        paymentRequest.setPaymentBillDTO(paymentBillDTO);
        paymentRequest.setBody("DEXPO");
        paymentRequest.setOpenid(wechatPayDTO.getOpenId());

        try {
            return weiXinAdapter.createPrePaymentOrder(paymentRequest);
        } catch (ServletException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public CommonResult<WXNotifyResponseDTO> payNotify(WXNotifyMsgDTO msg) throws GeneralSecurityException, IOException {

        return weiXinAdapter.payNotify(msg);
    }



}
