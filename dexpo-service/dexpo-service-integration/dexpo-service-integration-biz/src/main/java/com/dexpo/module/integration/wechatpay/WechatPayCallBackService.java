package com.dexpo.module.integration.wechatpay;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.integration.api.wechatpay.WXNotifyMsgDTO;
import com.dexpo.module.integration.api.wechatpay.WXNotifyResponseDTO;
import com.dexpo.module.integration.api.wechatpay.WechatPayDTO;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.Map;

public interface WechatPayCallBackService {

    // 统一支付
    Map<String, String> handlePay(WechatPayDTO wechatPayDTO);

    CommonResult<WXNotifyResponseDTO> payNotify(WXNotifyMsgDTO msg) throws GeneralSecurityException, IOException;


    // 申请退款
    // 退款回调
}
