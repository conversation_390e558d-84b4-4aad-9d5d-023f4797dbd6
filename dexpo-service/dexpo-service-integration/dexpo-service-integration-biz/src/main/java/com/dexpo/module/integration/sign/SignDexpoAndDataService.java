package com.dexpo.module.integration.sign;

import cn.hutool.core.text.CharSequenceUtil;
import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.exception.enums.AuthorizeServiceErrorCodeEnum;
import com.dexpo.framework.encrypt.core.util.outsign.RSAUtil;
import com.dexpo.framework.encrypt.core.util.outsign.SignatureUtil;
import com.dexpo.module.authorize.api.dxposign.dto.SignDexpoMsgDTO;
import com.dexpo.module.authorize.api.dxposign.vo.SignDexpoMsgVO;
import com.dexpo.module.integration.config.DexpoConfig;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 对报文进行验签和解密
 *
 * <AUTHOR>
 */
@Component
public class SignDexpoAndDataService {


    @Resource
    private DexpoConfig dexpoConfig;


    public SignDexpoMsgVO checkSignAndDecryptData(SignDexpoMsgDTO dto) {
        SignDexpoMsgVO vo = new SignDexpoMsgVO();
        // 验签
        DexpoConfig.SignConfig signConfig = new DexpoConfig.SignConfig();
        for (DexpoConfig.SignConfig config : dexpoConfig.getSign()) {
            String appKey = config.getAppKey();
            if (CharSequenceUtil.equals(appKey, dto.getSignParams().get("appKey"))) {
                signConfig = config;
            }
        }

        try {
            String signature = SignatureUtil.generateSignature(dto.getSignParams(), signConfig.getAppSecret());
            vo.setSignResults(CharSequenceUtil.equals(signature, dto.getSign()));
        } catch (Exception e) {
            throw new ServiceException(AuthorizeServiceErrorCodeEnum.SIGN_HAS_ERROR.getCode(), e.getMessage());
        }

        // 报文解密
        if (CharSequenceUtil.isNotEmpty(dto.getDataSecret())) {
            try {
                String body = RSAUtil.decrypt(signConfig.getPrivateKey(), dto.getBody());
                vo.setBody(body);
            } catch (Exception e) {
                throw new ServiceException(AuthorizeServiceErrorCodeEnum.SIGN_HAS_ERROR.getCode(), e.getMessage());
            }
        }
        return vo;
    }
}