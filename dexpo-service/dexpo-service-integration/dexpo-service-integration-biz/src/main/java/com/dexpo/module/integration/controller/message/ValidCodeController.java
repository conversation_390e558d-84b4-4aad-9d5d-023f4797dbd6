package com.dexpo.module.integration.controller.message;

import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.exception.enums.IntegrationServiceErrorCodeEnum;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.util.validation.ValidationUtils;
import com.dexpo.module.integration.api.message.ValidCodeApi;
import com.dexpo.module.integration.api.message.dto.ValidCodeDTO;
import com.dexpo.module.integration.message.EmailService;
import com.dexpo.module.integration.message.SmsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "验证码发送")
@RestController
@RequiredArgsConstructor
public class Valid<PERSON>odeController implements ValidCodeApi {

    private final SmsService smsService;

    private final EmailService emailService;

    @Override
    public CommonResult<Boolean> getValidCode(@Validated @RequestBody ValidCodeDTO codeDTO) {
        //todo redis 查redis严谨开管 走宽松逻辑还是走严谨逻辑
        if (ValidationUtils.isMobile(codeDTO.getText())) {
            smsService.sendSms(codeDTO);
        } else if (ValidationUtils.isEmail(codeDTO.getText())) {
            emailService.sendEmailValidCode(codeDTO);
        } else {
            throw new ServiceException(IntegrationServiceErrorCodeEnum.EMAIL_OR_MOBILE_CHECK_ERROR);
        }

        return CommonResult.success(true);
    }
}