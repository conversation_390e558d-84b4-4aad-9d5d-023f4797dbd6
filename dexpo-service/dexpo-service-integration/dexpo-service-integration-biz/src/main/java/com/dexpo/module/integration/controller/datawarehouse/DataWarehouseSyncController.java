package com.dexpo.module.integration.controller.datawarehouse;

import java.util.HashMap;
import java.util.Map;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.authorize.api.dxposign.dto.SignDexpoMsgDTO;
import com.dexpo.module.integration.api.datawarehouse.DataWarehouseSyncApi;
import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseAudienceDexpoDTO;
import com.dexpo.module.integration.datawarehouse.DataWarehouseSyncService;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequiredArgsConstructor
@Slf4j
public class DataWarehouseSyncController implements DataWarehouseSyncApi {

    private final DataWarehouseSyncService dataWarehouseSyncService;

    /**
     * 请求头参数 timestamp 时间戳，（秒级）
     */
    private static final String TIMESTAMP = "timestamp";
    /**
     * 请求头参数 nonce 32位随机字符串
     */
    private static final String NONCE = "nonce";
    /**
     * 请求头参数 appKey 应用appKey，用于区分系统
     */
    private static final String APPKEY = "appKey";
    /**
     * 请求头参数 sign 签名
     */
    private static final String SIGN = "sign";


    public CommonResult<Boolean> syncDataWarehouseSyncAudience(@RequestBody DataWarehouseAudienceDexpoDTO dto) {

        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
                
            throw new ServiceException();
        }
        HttpServletRequest request = requestAttributes.getRequest();
        // 获取header 信息 构建map
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(TIMESTAMP, request.getHeader(TIMESTAMP));
        headerMap.put(NONCE, request.getHeader(NONCE));
        headerMap.put(APPKEY, request.getHeader(APPKEY));
        SignDexpoMsgDTO checkDTO = new SignDexpoMsgDTO();
        checkDTO.setBody(dto.getBody());
        checkDTO.setSignParams(headerMap);
        checkDTO.setSign(request.getHeader(SIGN));
        checkDTO.setDataSecret("1");
        dataWarehouseSyncService.syncAudience(checkDTO);

        return CommonResult.success(true);
    }

}
