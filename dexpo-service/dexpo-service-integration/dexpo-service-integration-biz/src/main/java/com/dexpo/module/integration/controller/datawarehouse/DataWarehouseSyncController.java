package com.dexpo.module.integration.controller.datawarehouse;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.integration.api.datawarehouse.DataWarehouseSyncApi;
import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseAudienceEntrySyncDexpoDTO;
import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseAudienceSyncDexpoDTO;
import com.dexpo.module.integration.datawarehouse.DataWarehouseSyncService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequiredArgsConstructor
@Slf4j
public class DataWarehouseSyncController implements DataWarehouseSyncApi {

    private final DataWarehouseSyncService dataWarehouseSyncService;

    public CommonResult<Boolean> syncDataWarehouseSyncAudience(@RequestBody DataWarehouseAudienceSyncDexpoDTO dto) {

        return dataWarehouseSyncService.syncAudience(dto);

    }

    public CommonResult<Boolean> syncDataWarehouseSyncAudienceEntry(
            @RequestBody DataWarehouseAudienceEntrySyncDexpoDTO dto) {
        return dataWarehouseSyncService.syncAudienceEntry(dto);
    }
}
