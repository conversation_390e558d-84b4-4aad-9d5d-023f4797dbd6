package com.dexpo.module.integration.datawarehouse;

import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.module.authorize.api.dxposign.dto.SignDexpoMsgDTO;
import com.dexpo.module.authorize.api.dxposign.vo.SignDexpoMsgVO;
import com.dexpo.module.integration.sign.SignDexpoAndDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class DataWarehouseSyncServiceImpl implements DataWarehouseSyncService {

    private final SignDexpoAndDataService signDexpoAndDataService;


    @Override
    public void syncAudience(SignDexpoMsgDTO checkDTO) {
        SignDexpoMsgVO signDexpoMsgVO = signDexpoAndDataService.checkSignAndDecryptData(checkDTO);
        if (signDexpoMsgVO == null || Boolean.FALSE.equals(signDexpoMsgVO.getSignResults())) {
            log.error("checkSignAndDecryptData check failed");
            throw new ServiceException();
        }
        log.info("checkSignAndDecryptData check success.");

    }
}
