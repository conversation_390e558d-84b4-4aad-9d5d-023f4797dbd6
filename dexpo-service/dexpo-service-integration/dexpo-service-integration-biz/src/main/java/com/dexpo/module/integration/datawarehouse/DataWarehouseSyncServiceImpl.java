package com.dexpo.module.integration.datawarehouse;

import com.alibaba.fastjson2.JSON;
import com.dexpo.framework.common.enums.ValueSetInterfaceEnum;
import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.exception.enums.IntegrationServiceErrorCodeEnum;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.util.validation.ValidationUtils;
import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseAudienceEntrySyncDexpoDTO;
import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseAudienceSyncDexpoDTO;
import com.dexpo.module.integration.api.datawarehouse.message.SignDexpoMsgDTO;
import com.dexpo.module.integration.api.datawarehouse.vo.SignDexpoMsgVO;
import com.dexpo.module.integration.log.SysInterfaceLogTemplate;
import com.dexpo.module.integration.sign.SignDexpoAndDataService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class DataWarehouseSyncServiceImpl implements DataWarehouseSyncService {

    private static final String TIMESTAMP = "timestamp";
    private static final String NONCE = "nonce";
    private static final String APPKEY = "appKey";
    private static final String SIGN = "sign";

    private final SignDexpoAndDataService signDexpoAndDataService;

    private final SysInterfaceLogTemplate sysInterfaceLogTemplate;

    @Override
    public CommonResult<Boolean> syncAudience(DataWarehouseAudienceSyncDexpoDTO dto) {
        log.info("数仓观众数据同步开始, params:{}", JSON.toJSONString(dto));

        return sysInterfaceLogTemplate.handleExternalRequest(ValueSetInterfaceEnum.VO_INTERFACE_SC_DW_003, dto, data -> {

            validateSignature();
            return CommonResult.success();
        });
    }

    @Override
    public CommonResult<Boolean> syncAudienceEntry(DataWarehouseAudienceEntrySyncDexpoDTO dto) {
        log.info("数仓观众入场结果同步开始, params:{}", JSON.toJSONString(dto));
        return sysInterfaceLogTemplate.handleExternalRequest(ValueSetInterfaceEnum.VO_INTERFACE_SC_DW_003, dto, data -> {
            ValidationUtils.validate(data);
            validateSignature();
            return CommonResult.success();
        });
    }

    private void validateSignature() {
        try {
            SignDexpoMsgDTO signDTO = buildSignDTO();
            SignDexpoMsgVO result = signDexpoAndDataService.checkSignAndDecryptData(signDTO);

            if (result == null || !Boolean.TRUE.equals(result.getSignResults())) {
                log.error("签名验证失败");
                throw new ServiceException(IntegrationServiceErrorCodeEnum.DATA_WAREHOUSE_SERVICE_ERROR);
            }

            log.debug("签名验证成功");
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("签名验证异常", e);
            throw new ServiceException(IntegrationServiceErrorCodeEnum.DATA_WAREHOUSE_SERVICE_ERROR);
        }
    }

    private SignDexpoMsgDTO buildSignDTO() {
        HttpServletRequest request = getCurrentRequest();

        Map<String, String> headerMap = Map.of(
                TIMESTAMP, request.getHeader(TIMESTAMP),
                NONCE, request.getHeader(NONCE),
                APPKEY, request.getHeader(APPKEY)
        );

        SignDexpoMsgDTO signDTO = new SignDexpoMsgDTO();
        signDTO.setSignParams(headerMap);
        signDTO.setSign(request.getHeader(SIGN));
        return signDTO;
    }

    private HttpServletRequest getCurrentRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            throw new ServiceException(IntegrationServiceErrorCodeEnum.DATA_WAREHOUSE_SERVICE_ERROR);
        }
        return attributes.getRequest();
    }
}