package com.dexpo.module.integration.verification;

import cn.hutool.crypto.SecureUtil;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.cloudauth20190307.AsyncClient;
import com.aliyun.sdk.service.cloudauth20190307.models.Id2MetaVerifyRequest;
import com.aliyun.sdk.service.cloudauth20190307.models.Id2MetaVerifyResponse;
import com.dexpo.framework.common.util.json.JsonUtils;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class AliVerificationUtils {

    private static final String REGION = "cn-hangzhou";
    private static final String BIZ_CODE_SUCCESS = "1";
    @Value("${aliyun.cloudauth.endpoint}")
    private String endpoint;
    @Value("${aliyun.cloudauth.paramType}")
    private String paramType;
    @Value("${aliyun.accessKey}")
    private String accessKey;
    @Value("${aliyun.accessSecret}")
    private String accessSecret;

    private static String getLowerCase(String s) {
        return SecureUtil.md5(s).toLowerCase();
    }

    public boolean checkMemberNameAndIdNumber(String identify, String name) {
        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(accessKey)
                .accessKeySecret(accessSecret)
                .build());
        AsyncClient client = AsyncClient.builder().region(REGION)
                .credentialsProvider(provider)
                .overrideConfiguration(
                        ClientOverrideConfiguration
                                .create()
                                .setEndpointOverride(endpoint)
                                .setConnectTimeout(Duration.ofSeconds(30))
                )
                .build();

        String identifyNum = identify.substring(0, 6) + getLowerCase(identify.substring(6, 14)) + identify.substring(14);
        String userName = getLowerCase(name.substring(0, 1)) + name.substring(1);

        log.info("identifyNum {}", identifyNum);
        log.info("userName {}", userName);

        Id2MetaVerifyRequest id2MetaVerifyRequest = Id2MetaVerifyRequest.builder()
                .identifyNum(identifyNum)
                .paramType(paramType)
                .userName(userName)
                .build();
        CompletableFuture<Id2MetaVerifyResponse> response = client.id2MetaVerify(id2MetaVerifyRequest);
        boolean result = false;
        try {
            Id2MetaVerifyResponse resp = response.get();
            log.info("resp:{}", JsonUtils.toJsonString(resp));
            Integer statusCode = resp.getStatusCode();
            if (200 == statusCode) {
                String bizCode = resp.getBody().getResultObject().getBizCode();
                if (BIZ_CODE_SUCCESS.equals(bizCode)) {
                    result = true;
                }
            }
        } catch (Exception e) {
            log.error("Error Message:{}", e.getMessage(), e);
            return false;
        } finally {
            client.close();
        }
        return result;
    }

}
