package com.dexpo.module.integration.client;

import com.alibaba.fastjson2.JSON;
import com.dexpo.framework.common.enums.ValueSetInterfaceEnum;
import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.util.validation.ValidationUtils;
import com.dexpo.module.authorize.api.datawarehouse.DataWarehouseAuthorizeApi;
import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseAudienceSyncMessage;
import com.dexpo.module.integration.config.DataWareHouseApiConfig;
import com.dexpo.module.integration.mq.datawarehouse.DataWarehouseFunction;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 往数仓同步数据 基类
 */
@Slf4j
@Component
public class DataWarehouseSyncService {

    @Resource
    private DataWareHouseApiConfig dataWarehouseApiConfig;

    @Resource
    private DataWarehouseAuthorizeApi dataWarehouseAuthorizeApi;

    /**
     * 同步数据到数仓
     * 集成了生成token和调用接口
     *
     * @param interfaceEnum 接口枚举
     * @param data          数据
     * @param function      数据处理函数，需要实现DataWarehouseFunction接口
     * @return 返回值
     */
    public <T> String syncData(ValueSetInterfaceEnum interfaceEnum, T data, DataWarehouseFunction<T> function) {
        // 打印日志，需要知道function实现类
        log.info("同步数据到数仓，function:{}, params:{}", interfaceEnum.getOptionCode(), JSON.toJSONString(data));
        try {
            ValidationUtils.validate(data);

            String result = function.apply(data);
            log.info("同步数据到数仓，function:{}, params:{}, result:{}", interfaceEnum.getOptionCode(),
                    JSON.toJSONString(data), JSON.toJSONString(result));
            return result;
        } catch (ConstraintViolationException e) {
            log.error("同步数据到数仓，function:{},参数校验异常:{}", interfaceEnum.getOptionCode(), e.getMessage());
        } catch (Exception e) {
            log.error("同步数据到数仓异常", e);
            throw new ServiceException(500, "同步数据到数仓异常：" + e.getMessage());
        }
        // 日志记录
        return null;
    }

}
