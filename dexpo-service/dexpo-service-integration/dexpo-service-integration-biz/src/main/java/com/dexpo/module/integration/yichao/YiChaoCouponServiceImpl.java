package com.dexpo.module.integration.yichao;

import com.alibaba.fastjson2.JSON;
import com.dexpo.framework.common.enums.ValueSetInterfaceEnum;
import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.module.integration.api.yichao.dto.YichaoCouponCancellationDTO;
import com.dexpo.module.integration.client.YiChaoApiClient;
import com.dexpo.module.integration.config.YiChaoCiifApiConfig;
import com.dexpo.module.integration.config.YiChaoSignUtil;
import com.dexpo.module.integration.log.SysInterfaceLogTemplate;
import com.dexpo.module.integration.log.SysInterfaceResult;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class YiChaoCouponServiceImpl implements YiChaoCouponService {

    private static final String COUPON_ID = "couponId";

    @Resource
    private YiChaoCiifApiConfig yiChaoCiifApiConfig;

    @Resource
    private SysInterfaceLogTemplate sysInterfaceLogTemplate;

    @Override
    public Boolean yichaoCouponCheck(String couponId) {
        SysInterfaceResult result = sysInterfaceLogTemplate.callExternalApi(ValueSetInterfaceEnum.CIIF_COUPON_CHECK, couponId, data -> {
            Map<String, Object> params = Map.of(COUPON_ID, couponId);
            // header 生成
            Map<String, String> headers = getStringStringMap();
            // 调用接口
            YiChaoApiClient.YiChaoResult<Object> res = YiChaoApiClient.get(yiChaoCiifApiConfig.getHost() + YiChaoCiifApiConfig.COUPON_CHECK, params, headers);
            return new SysInterfaceResult(res.getCode(), JSON.toJSONString(res), res.getMessage(), res.isSuccess());
        });
        if (Boolean.TRUE.equals(result.getSuccess())) {
            return true;
        }
        throw new ServiceException(result.getReturnCode(), result.getErrorMessage());
    }


    @Override
    public Boolean yichaoCouponCancellation(YichaoCouponCancellationDTO dto) {
        SysInterfaceResult result = sysInterfaceLogTemplate.callExternalApi(ValueSetInterfaceEnum.CIIF_COUPON_CANCELLATION, dto, data -> {
            Map<String, String> headers = getStringStringMap();
            YiChaoApiClient.YiChaoResult<Object> res = YiChaoApiClient.post(yiChaoCiifApiConfig.getHost() + YiChaoCiifApiConfig.COUPON_CANCELLATION,
                    JSON.toJSONString(dto), headers);
            return new SysInterfaceResult(res.getCode(), JSON.toJSONString(res), res.getMessage(), res.isSuccess());
        });
        if (Boolean.TRUE.equals(result.getSuccess())) {
            return true;
        }
        throw new ServiceException(result.getReturnCode(), result.getErrorMessage());
    }


    private Map<String, String> getStringStringMap() {
        String nonce = YiChaoSignUtil.getNonce();
        Long timestamp = YiChaoSignUtil.getTimestamp();
        String sign = YiChaoSignUtil.generateApiSign(yiChaoCiifApiConfig.getAppId(), yiChaoCiifApiConfig.getAppSecret(),
                timestamp, nonce);

        return Map.of(
                "Fair-Code", YiChaoCiifApiConfig.CIIF,
                "X-AppId", yiChaoCiifApiConfig.getAppId(),
                "X-Timestamp", String.valueOf(timestamp),
                "X-Nonce", nonce,
                "X-Sign", sign
        );
    }

}