package com.dexpo.module.integration.wechatpay.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

@ApiModel("支付回调响应DTO")
@Data
public class PaymentCallbackResponseDTO {

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("状态")
    private String code;

    @ApiModelProperty("支付渠道")
    private String paymentChannel;

    @ApiModelProperty("支付单DTO")
    private PaymentBillDTO paymentBillDTO;

    @ApiModelProperty("实际金额")
    private BigDecimal actualAmount;

    @ApiModelProperty("是否改变状态")
    private Boolean changeStatus = false;

    @ApiModelProperty("内容")
    private Map<String, String> content;
}
