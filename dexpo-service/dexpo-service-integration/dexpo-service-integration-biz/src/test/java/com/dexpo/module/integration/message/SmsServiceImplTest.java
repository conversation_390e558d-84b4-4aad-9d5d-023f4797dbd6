package com.dexpo.module.integration.message;

import com.dexpo.framework.cache.redis.constant.BasicRedisKey;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.module.integration.api.message.dto.ValidCodeDTO;
import com.dexpo.module.integration.constant.IntegrationConstant;
import com.dexpo.module.integration.message.impl.SmsServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SmsServiceImplTest {

    @InjectMocks
    private SmsServiceImpl smsService;

    @Mock
    private RedisService redisService;

    @BeforeEach
    void setUp() {
        // 设置mock属性
        ReflectionTestUtils.setField(smsService, "mock", true);
    }

    /**
     * 测试场景：正常发送短信验证码（mock模式）
     * 预期结果：成功发送验证码，验证码为123456
     */
    @Test
    void testSendSmsWithMock() {
        // 准备测试数据
        String mobile = "13800138000";
        ValidCodeDTO validCodeDTO = new ValidCodeDTO();
        validCodeDTO.setText(mobile);

        // 模拟Redis操作
        String limitKey = BasicRedisKey.generateKey(BasicRedisKey.BASIC_LOGIN_SMS_LIMIT_KEY, mobile);
        String codeKey = BasicRedisKey.generateKey(BasicRedisKey.BASIC_LOGIN_VALID_KEY, mobile);

        // 设置mock行为
        when(redisService.setNx(eq(limitKey), eq(1), eq(IntegrationConstant.DEFAULT_LOGIN_CODE_LIMIT_TIME), eq(TimeUnit.SECONDS)))
                .thenReturn(true);
        doNothing().when(redisService).setCacheObject(eq(codeKey), eq("123456"), eq(IntegrationConstant.DEFAULT_LOGIN_CODE_INVALID_TIME), eq(TimeUnit.MINUTES));

        // 执行测试
        smsService.sendSms(validCodeDTO);

        // 验证结果
        verify(redisService).setNx(eq(limitKey), eq(1), eq(IntegrationConstant.DEFAULT_LOGIN_CODE_LIMIT_TIME), eq(TimeUnit.SECONDS));
        verify(redisService).setCacheObject(eq(codeKey), eq("123456"), eq(IntegrationConstant.DEFAULT_LOGIN_CODE_INVALID_TIME), eq(TimeUnit.MINUTES));
    }

    /**
     * 测试场景：发送短信验证码时遇到频率限制
     * 预期结果：抛出ServiceException异常
     */
    @Test
    void testSendSmsWithRateLimit() {
        // 准备测试数据
        String mobile = "13800138000";
        ValidCodeDTO validCodeDTO = new ValidCodeDTO();
        validCodeDTO.setText(mobile);

        // 模拟Redis操作 - 设置频率限制
        String limitKey = BasicRedisKey.generateKey(BasicRedisKey.BASIC_LOGIN_SMS_LIMIT_KEY, mobile);
        when(redisService.setNx(eq(limitKey), eq(1), eq(IntegrationConstant.DEFAULT_LOGIN_CODE_LIMIT_TIME), eq(TimeUnit.SECONDS)))
                .thenReturn(false);

        // 执行测试并验证异常
        assertThrows(ServiceException.class, () -> smsService.sendSms(validCodeDTO));

        // 验证Redis调用
        verify(redisService).setNx(eq(limitKey), eq(1), eq(IntegrationConstant.DEFAULT_LOGIN_CODE_LIMIT_TIME), eq(TimeUnit.SECONDS));
        verify(redisService, never()).setCacheObject(anyString(), any(), anyLong(), any(TimeUnit.class));
    }

    /**
     * 测试场景：非mock模式下发送短信验证码
     * 预期结果：生成随机验证码并发送
     */
    @Test
    void testSendSmsWithoutMock() {
        // 准备测试数据
        String mobile = "13800138000";
        ValidCodeDTO validCodeDTO = new ValidCodeDTO();
        validCodeDTO.setText(mobile);

        // 设置非mock模式
        ReflectionTestUtils.setField(smsService, "mock", false);

        // 模拟Redis操作
        String limitKey = BasicRedisKey.generateKey(BasicRedisKey.BASIC_LOGIN_SMS_LIMIT_KEY, mobile);
        String codeKey = BasicRedisKey.generateKey(BasicRedisKey.BASIC_LOGIN_VALID_KEY, mobile);

        when(redisService.setNx(eq(limitKey), eq(1), eq(IntegrationConstant.DEFAULT_LOGIN_CODE_LIMIT_TIME), eq(TimeUnit.SECONDS)))
                .thenReturn(true);
        doNothing().when(redisService).setCacheObject(eq(codeKey), anyString(), eq(IntegrationConstant.DEFAULT_LOGIN_CODE_INVALID_TIME), eq(TimeUnit.MINUTES));

        // 执行测试
        smsService.sendSms(validCodeDTO);

        // 验证结果
        verify(redisService).setNx(eq(limitKey), eq(1), eq(IntegrationConstant.DEFAULT_LOGIN_CODE_LIMIT_TIME), eq(TimeUnit.SECONDS));
        verify(redisService).setCacheObject(eq(codeKey), anyString(), eq(IntegrationConstant.DEFAULT_LOGIN_CODE_INVALID_TIME), eq(TimeUnit.MINUTES));
    }

    /**
     * 测试场景：发送短信验证码时Redis操作失败
     * 预期结果：抛出RuntimeException异常
     */
    @Test
    void testSendSmsWithRedisFailure() {
        // 准备测试数据
        String mobile = "13800138000";
        ValidCodeDTO validCodeDTO = new ValidCodeDTO();
        validCodeDTO.setText(mobile);

        // 模拟Redis操作失败
        String limitKey = BasicRedisKey.generateKey(BasicRedisKey.BASIC_LOGIN_SMS_LIMIT_KEY, mobile);
        when(redisService.setNx(eq(limitKey), eq(1), eq(IntegrationConstant.DEFAULT_LOGIN_CODE_LIMIT_TIME), eq(TimeUnit.SECONDS)))
                .thenReturn(true);
        doThrow(new RuntimeException("Redis operation failed"))
                .when(redisService).setCacheObject(anyString(), any(), anyLong(), any(TimeUnit.class));

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> smsService.sendSms(validCodeDTO));
        assertEquals("Redis operation failed", exception.getMessage());

        // 验证Redis调用
        verify(redisService).setNx(eq(limitKey), eq(1), eq(IntegrationConstant.DEFAULT_LOGIN_CODE_LIMIT_TIME), eq(TimeUnit.SECONDS));
        verify(redisService).setCacheObject(anyString(), any(), anyLong(), any(TimeUnit.class));
    }
} 