package com.dexpo.module.integration.message;

import com.dexpo.framework.cache.redis.constant.BasicRedisKey;
import com.dexpo.framework.cache.redis.service.RedisService;
import com.dexpo.module.integration.api.message.dto.ValidCodeDTO;
import com.dexpo.module.integration.constant.IntegrationConstant;
import com.dexpo.module.integration.message.impl.EmailServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

/**
 * 邮件服务实现类单元测试
 */
@ExtendWith(MockitoExtension.class)
class EmailServiceImplTest {

    @Mock
    private RedisService redisService;

    @InjectMocks
    private EmailServiceImpl emailService;

    private static final String TEST_EMAIL = "<EMAIL>";
    private static final String TEST_CODE = "123456";
    private static final String TEST_KEY = BasicRedisKey.generateKey(BasicRedisKey.BASIC_LOGIN_VALID_KEY, TEST_EMAIL);

    @BeforeEach
    void setUp() {
        // 设置 mock 属性为 true
        ReflectionTestUtils.setField(emailService, "mock", true);
    }

    @Test
    @DisplayName("测试发送邮件验证码 - 模拟模式")
    void testSendEmailValidCodeMockMode() {
        // 准备测试数据
        ValidCodeDTO request = new ValidCodeDTO();
        request.setText(TEST_EMAIL);

        // 执行测试
        emailService.sendEmailValidCode(request);

        // 验证结果
        verify(redisService).setCacheObject(
                eq(TEST_KEY),
                eq(TEST_CODE),
                eq(IntegrationConstant.DEFAULT_LOGIN_CODE_INVALID_TIME),
                eq(TimeUnit.MINUTES)
        );
    }

    @Test
    @DisplayName("测试发送邮件验证码 - 非模拟模式")
    void testSendEmailValidCodeNonMockMode() {
        // 设置 mock 属性为 false
        ReflectionTestUtils.setField(emailService, "mock", false);

        // 准备测试数据
        ValidCodeDTO request = new ValidCodeDTO();
        request.setText(TEST_EMAIL);

        // 执行测试
        emailService.sendEmailValidCode(request);

        // 验证结果
        verify(redisService).setCacheObject(
                eq(TEST_KEY),
                any(String.class), // 非模拟模式下会生成随机验证码
                eq(IntegrationConstant.DEFAULT_LOGIN_CODE_INVALID_TIME),
                eq(TimeUnit.MINUTES)
        );
    }

} 