package com.dexpo.module.integration.yichao;

import com.alibaba.fastjson2.JSON;
import com.dexpo.framework.common.enums.ValueSetInterfaceEnum;
import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.module.integration.api.yichao.dto.YichaoCouponCancellationDTO;
import com.dexpo.module.integration.client.YiChaoApiClient;
import com.dexpo.module.integration.config.YiChaoCiifApiConfig;
import com.dexpo.module.integration.config.YiChaoSignUtil;
import com.dexpo.module.integration.log.SysInterfaceLogTemplate;
import com.dexpo.module.integration.log.SysInterfaceResult;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class YiChaoCouponServiceImplTest {

    @Mock
    private YiChaoCiifApiConfig yiChaoCiifApiConfig;

    @Mock
    private SysInterfaceLogTemplate sysInterfaceLogTemplate;

    @InjectMocks
    private YiChaoCouponServiceImpl yiChaoCouponService;



    @Test
    void testYichaoCouponCheck() {
        // Given
        String couponId = "test-coupon-123";
        SysInterfaceResult successResult = new SysInterfaceResult(200, "{}", null, true);
        when(sysInterfaceLogTemplate.callExternalApi(any(), eq(couponId), any(Function.class)))
            .thenReturn(successResult);
        
        // When
        Boolean result = yiChaoCouponService.yichaoCouponCheck(couponId);
        
        // Then
        assertTrue(result);
        verify(sysInterfaceLogTemplate).callExternalApi(any(), eq(couponId), any(Function.class));
    }

    @Test
    void testYichaoCouponCheckFailure() {
        // Given
        String couponId = "test-coupon-123";
        SysInterfaceResult failResult = new SysInterfaceResult(500, "{}", "error", false);
        when(sysInterfaceLogTemplate.callExternalApi(any(), eq(couponId), any(Function.class)))
            .thenReturn(failResult);
        
        // When & Then
        ServiceException exception = assertThrows(ServiceException.class, 
            () -> yiChaoCouponService.yichaoCouponCheck(couponId));
        assertEquals(500, exception.getCode());
        assertEquals("error", exception.getMessage());
    }

    @Test
    void testYichaoCouponCancellation() {
        // Given
        YichaoCouponCancellationDTO dto = new YichaoCouponCancellationDTO();
        dto.setCouponIds(List.of("coupon1", "coupon2"));
        dto.setOrderNumber("ORDER123");
        SysInterfaceResult successResult = new SysInterfaceResult(200, "{}", null, true);
        when(sysInterfaceLogTemplate.callExternalApi(any(), eq(dto), any(Function.class)))
            .thenReturn(successResult);
        
        // When
        Boolean result = yiChaoCouponService.yichaoCouponCancellation(dto);
        
        // Then
        assertTrue(result);
        verify(sysInterfaceLogTemplate).callExternalApi(any(), eq(dto), any(Function.class));
    }

    @Test
    void testYichaoCouponCancellationFailure() {
        // Given
        YichaoCouponCancellationDTO dto = new YichaoCouponCancellationDTO();
        dto.setCouponIds(List.of("coupon1", "coupon2"));
        dto.setOrderNumber("ORDER123");
        SysInterfaceResult failResult = new SysInterfaceResult(500, "{}", "error", false);
        when(sysInterfaceLogTemplate.callExternalApi(any(), eq(dto), any(Function.class)))
            .thenReturn(failResult);
        
        // When & Then
        ServiceException exception = assertThrows(ServiceException.class, 
            () -> yiChaoCouponService.yichaoCouponCancellation(dto));
        assertEquals(500, exception.getCode());
        assertEquals("error", exception.getMessage());
    }

    @Test
    void testYichaoCouponCheckWithActualApiCall() {
        try (MockedStatic<YiChaoApiClient> mockedClient = mockStatic(YiChaoApiClient.class);
             MockedStatic<YiChaoSignUtil> mockedSignUtil = mockStatic(YiChaoSignUtil.class)) {
            
            // Given
            String couponId = "test-coupon-123";
            when(yiChaoCiifApiConfig.getHost()).thenReturn("https://test.api.com");
            when(yiChaoCiifApiConfig.getAppId()).thenReturn("testAppId");
            when(yiChaoCiifApiConfig.getAppSecret()).thenReturn("testSecret");
            
            mockedSignUtil.when(YiChaoSignUtil::getNonce).thenReturn("testNonce");
            mockedSignUtil.when(YiChaoSignUtil::getTimestamp).thenReturn(1234567890L);
            mockedSignUtil.when(() -> YiChaoSignUtil.generateApiSign(anyString(), anyString(), anyLong(), anyString()))
                .thenReturn("testSign");
            
            YiChaoApiClient.YiChaoResult<Object> apiResult = new YiChaoApiClient.YiChaoResult<>();
            apiResult.setCode(200);
            apiResult.setMessage("success");
            apiResult.setSuccess(true);
            apiResult.setResult(new Object());
            
            mockedClient.when(() -> YiChaoApiClient.get(anyString(), any(Map.class), any(Map.class)))
                .thenReturn(apiResult);
            
            // Mock the actual function execution
            when(sysInterfaceLogTemplate.callExternalApi(eq(ValueSetInterfaceEnum.CIIF_COUPON_CHECK), eq(couponId), any(Function.class)))
                .thenAnswer(invocation -> {
                    Function<String, SysInterfaceResult> function = invocation.getArgument(2);
                    return function.apply(couponId);
                });
            
            // When
            Boolean result = yiChaoCouponService.yichaoCouponCheck(couponId);
            
            // Then
            assertTrue(result);
            mockedClient.verify(() -> YiChaoApiClient.get(
                eq("https://test.api.com" + YiChaoCiifApiConfig.COUPON_CHECK),
                any(Map.class),
                any(Map.class)
            ));
        }
    }

    @Test
    void testYichaoCouponCancellationWithActualApiCall() {
        try (MockedStatic<YiChaoApiClient> mockedClient = mockStatic(YiChaoApiClient.class);
             MockedStatic<YiChaoSignUtil> mockedSignUtil = mockStatic(YiChaoSignUtil.class);
             MockedStatic<JSON> mockedJson = mockStatic(JSON.class)) {
            
            // Given
            YichaoCouponCancellationDTO dto = new YichaoCouponCancellationDTO();
            dto.setCouponIds(List.of("coupon1", "coupon2"));
            dto.setOrderNumber("ORDER123");
            
            when(yiChaoCiifApiConfig.getHost()).thenReturn("https://test.api.com");
            when(yiChaoCiifApiConfig.getAppId()).thenReturn("testAppId");
            when(yiChaoCiifApiConfig.getAppSecret()).thenReturn("testSecret");
            
            mockedSignUtil.when(YiChaoSignUtil::getNonce).thenReturn("testNonce");
            mockedSignUtil.when(YiChaoSignUtil::getTimestamp).thenReturn(1234567890L);
            mockedSignUtil.when(() -> YiChaoSignUtil.generateApiSign(anyString(), anyString(), anyLong(), anyString()))
                .thenReturn("testSign");
            
            mockedJson.when(() -> JSON.toJSONString(dto)).thenReturn("{\"orderNumber\":\"ORDER123\"}");
            
            YiChaoApiClient.YiChaoResult<Object> apiResult = new YiChaoApiClient.YiChaoResult<>();
            apiResult.setCode(200);
            apiResult.setMessage("success");
            apiResult.setSuccess(true);
            apiResult.setResult(new Object());
            
            mockedClient.when(() -> YiChaoApiClient.post(anyString(), anyString(), any(Map.class)))
                .thenReturn(apiResult);
            
            mockedJson.when(() -> JSON.toJSONString(apiResult)).thenReturn("{\"code\":200}");
            
            // Mock the actual function execution
            when(sysInterfaceLogTemplate.callExternalApi(eq(ValueSetInterfaceEnum.CIIF_COUPON_CANCELLATION), eq(dto), any(Function.class)))
                .thenAnswer(invocation -> {
                    Function<YichaoCouponCancellationDTO, SysInterfaceResult> function = invocation.getArgument(2);
                    return function.apply(dto);
                });
            
            // When
            Boolean result = yiChaoCouponService.yichaoCouponCancellation(dto);
            
            // Then
            assertTrue(result);
            mockedClient.verify(() -> YiChaoApiClient.post(
                eq("https://test.api.com" + YiChaoCiifApiConfig.COUPON_CANCELLATION),
                anyString(),
                any(Map.class)
            ));
        }
    }
}