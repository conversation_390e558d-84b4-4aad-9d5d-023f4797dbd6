package com.dexpo.module.integration.controller.yichao;

import com.dexpo.module.integration.api.yichao.dto.YichaoCouponCancellationDTO;
import com.dexpo.module.integration.yichao.YiChaoCouponService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class YiChaoCouponControllerTest {

    private MockMvc mockMvc;

    @Mock
    private YiChaoCouponService yichaoCouponService;

    @InjectMocks
    private YiChaoCouponController yiChaoCouponController;

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(yiChaoCouponController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    void testYichaoCouponCheck() throws Exception {
        // Given
        String couponId = "test-coupon-123";
        when(yichaoCouponService.yichaoCouponCheck(anyString())).thenReturn(true);

        // When & Then
        mockMvc.perform(get("/integration/yichao/coupon/check")
                        .param("couponId", couponId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    void testYichaoCouponCancellation() throws Exception {
        // Given
        YichaoCouponCancellationDTO dto = new YichaoCouponCancellationDTO();
        dto.setCouponIds(List.of("coupon1", "coupon2"));
        dto.setOrderNumber("ORDER123");

        when(yichaoCouponService.yichaoCouponCancellation(any(YichaoCouponCancellationDTO.class)))
                .thenReturn(true);

        // When & Then
        mockMvc.perform(get("/integration/yichao/coupon/cancellation")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    void testYichaoCouponCancellationWithInvalidData() throws Exception {
        // Given - 空的DTO
        YichaoCouponCancellationDTO dto = new YichaoCouponCancellationDTO();

        // When & Then
        mockMvc.perform(get("/integration/yichao/coupon/cancellation")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isBadRequest());
    }
}