spring:
  application:
    name: dexpo-service-payment
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Feign 等会存在重复定义的服务
  profiles:
    active: ${ENV:local}
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER # 解决 SpringFox 与 SpringBoot 2.6.x 不兼容的问题，参见 SpringFoxHandlerProviderBeanPostProcessor 类

  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 LocalDateTime 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 **********.401，而是直接 **********401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean

  # Cache 配置项
  cache:
    type: REDIS
    redis:
      time-to-live: 1h # 设置过期时间为 1 小时

  neo4j:
    pool:
      metrics-enabled: false

  cloud:
    kubernetes:
      discovery:
        # 让所有命名空间服务都可以发现服务
        namespaces:
          - ${ENV}
        # 发现未标记为“就绪”的服务端地址
        include-not-ready-addresses: true
        # ExternalName类型服务的列表  DiscoveryClient::getInstances 返回该列表 ServiceInstance::getMetadata
        include-external-name-services: true
        enabled: true

--- #################### RR低代码相关配置 ####################

dexpo:
  info:
    version: 1.0.0
    base-package: com.dexpo.module.authorize
  web:
    admin-ui:
      url: http://dashboard.roselife.com # Admin 管理后台 UI 的地址
  swagger:
    title: log
    description: log api
    version: ${dexpo.info.version}
  metrics:
    enable: false

