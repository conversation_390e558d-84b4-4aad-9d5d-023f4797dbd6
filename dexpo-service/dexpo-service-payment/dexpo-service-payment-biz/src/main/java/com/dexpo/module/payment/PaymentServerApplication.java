package com.dexpo.module.payment;


import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * 项目的启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {MetricsAutoConfiguration.class, DataSourceAutoConfiguration.class})
@Slf4j
@EnableDiscoveryClient
@ComponentScan({"com.dexpo.framework.cache", "com.dexpo.framework.web", "com.dexpo.module.payment"})
@EnableFeignClients()
public class PaymentServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(PaymentServerApplication.class, args);
        log.info("启动成功！");
    }

}
