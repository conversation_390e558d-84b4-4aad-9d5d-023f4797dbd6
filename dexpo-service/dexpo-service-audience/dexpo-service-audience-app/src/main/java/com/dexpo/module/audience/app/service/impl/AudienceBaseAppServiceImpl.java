package com.dexpo.module.audience.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dexpo.framework.common.enums.RegisterMethodEnum;
import com.dexpo.framework.common.enums.ValueSetParticipateStatusEnum;
import com.dexpo.module.audience.api.dto.audience.AudienceBoardDTO;
import com.dexpo.module.audience.api.dto.audience.AudiencePageQueryDTO;
import com.dexpo.module.audience.api.vo.audience.*;
import com.dexpo.module.audience.app.service.AudienceBaseAppService;
import com.dexpo.module.audience.app.service.AudienceParticipateRecordAppService;
import com.dexpo.module.audience.app.convert.AudienceBaseInfoConvert;
import com.dexpo.module.audience.domain.model.*;
import com.dexpo.module.audience.domain.repository.AudienceBaseInfoRepository;
import com.dexpo.module.audience.domain.repository.AudienceInvestigationInfoRepository;
import com.dexpo.module.audience.domain.repository.AudienceParticipateRecordRepository;
import com.dexpo.module.audience.infrastructure.integration.base.ValuesetExternalService;
import com.dexpo.module.audience.infrastructure.integration.exhibition.ExhibitionExternalService;
import com.dexpo.module.audience.infrastructure.integration.log.BizLogExternalService;
import com.dexpo.module.audience.infrastructure.tunnel.dataobject.*;
import com.dexpo.module.audience.infrastructure.tunnel.mysql.AudienceBaseInfoMapper;
import com.dexpo.module.audience.infrastructure.tunnel.mysql.AudienceInvestigationInfoMapper;
import com.dexpo.module.audience.infrastructure.tunnel.mysql.AudienceParticipateRecordMapper;
import com.dexpo.framework.common.enums.ValueSetDataBizOperateLogBusinessTypeEnum;
import com.dexpo.framework.common.enums.GenderEnum;
import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.exception.enums.AudienceServiceErrorCodeEnum;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.base.api.basic.vo.BasicValuesetOptionVO;
import com.dexpo.module.exhibition.api.dto.ExhibitionQueryDTO;
import com.dexpo.module.exhibition.api.vo.ExhibitionVO;
import com.dexpo.module.log.api.vo.BizOperateLogVO;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class AudienceBaseAppServiceImpl implements AudienceBaseAppService {

    @Resource
    private AudienceBaseInfoMapper baseInfoMapper;

    @Resource
    private AudienceParticipateRecordAppService audienceParticipateRecordAppService;

    @Resource
    private ExhibitionExternalService exhibitionExternalService;

    @Resource
    private AudienceInvestigationInfoMapper audienceInvestigationInfoMapper;

    @Resource
    private AudienceParticipateRecordMapper audienceParticipateRecordMapper;

    @Resource
    private ValuesetExternalService valuesetExternalService;

    @Resource
    private BizLogExternalService bizLogExternalService;

    @Resource
    private AudienceBaseInfoRepository audienceBaseInfoRepository;

    @Resource
    private AudienceParticipateRecordRepository audienceParticipateRecordRepository;

    @Resource
    private AudienceInvestigationInfoRepository audienceInvestigationInfoRepository;

    @Override
    public PageResult<AudiencePageListVO> getAudiencePage(AudiencePageQueryDTO queryDTO) {
        AudiencePageListQuery queryDO = AudienceBaseInfoConvert.INSTANCE.toAudiencePageQueryDO(queryDTO);
        // 不管用户是否选择了展会，都需要一个默认的条件，即当前用户登录对应的展会权限。
        ExhibitionQueryDTO exhibitionQueryDTO = new ExhibitionQueryDTO(queryDTO.getExhibitionTagCodes(),
                queryDTO.getExhibitionSessionKeys(), true);
        Map<Long, ExhibitionVO> exhibitionMap = exhibitionExternalService.getExhibitionMap(exhibitionQueryDTO);
        if (CollectionUtils.isEmpty(exhibitionMap)) {
            return new PageResult<>(List.of(), 0L);
        }
        queryDO.setExhibitionIds(exhibitionMap.keySet().stream().toList());
        PageResult<AudiencePageList> audiencePage = audienceBaseInfoRepository.getAudiencePage(queryDO);

        List<AudiencePageListVO> vos = AudienceBaseInfoConvert.INSTANCE.toAudiencePageListVOList(audiencePage.getList());
        // 回填展会信息
        vos.forEach(v -> {
            v.setExhibitionAbbreviation(exhibitionMap.get(v.getExhibitionId()).getExhibitionAbbreviation());
            v.setExhibitionNameCn(exhibitionMap.get(v.getExhibitionId()).getExhibitionNameCn());
            v.setExhibitionNameEn(exhibitionMap.get(v.getExhibitionId()).getExhibitionNameEn());
            v.setExhibitionCode(exhibitionMap.get(v.getExhibitionId()).getExhibitionCode());
            v.setExhibitionYear(exhibitionMap.get(v.getExhibitionId()).getExhibitionYear());
            v.setExhibitionSession(exhibitionMap.get(v.getExhibitionId()).getExhibitionSession());
        });
        return new PageResult<>(vos, audiencePage.getTotal());

    }

    @Override
    public Boolean activeAudience(List<Long> ids) {
        // 需要支付逻辑校验 数据权限控制 调用订单接口设置为支付
        List<AudienceParticipateRecord> recordDOList = audienceParticipateRecordRepository.selectByIds(ids);

        if (recordDOList.size() != ids.size()) {
            // 如果数据不一致 则说明 可能id存在问题 提示错误
            throw new ServiceException(AudienceServiceErrorCodeEnum.AUDIENCE_PARTICIPATE_STATUS_ERROR);
        }
        if (recordDOList.stream()
                .anyMatch(e -> !Objects.equals(ValueSetParticipateStatusEnum.NOT_ACTIVATED.getOptionCode(),
                        e.getParticipateStatus()))) {
            // 已经存在已激活的数据，不允许再次走激活流程
            throw new ServiceException(AudienceServiceErrorCodeEnum.AUDIENCE_PARTICIPATE_STATUS_ERROR);
        }

        audienceParticipateRecordRepository.activeByIds(ids);
        return true;
    }

    @Override
    public AudienceRegisterExhibitionVO getAudienceRegisterExhibition(Long id) {

        AudienceParticipateRecord participateRecord = audienceParticipateRecordRepository.selectById(id);
        if (participateRecord == null) {
            throw new ServiceException(AudienceServiceErrorCodeEnum.AUDIENCE_NOT_EXIST);
        }
        AudienceBaseInfo audienceBaseInfo = audienceBaseInfoRepository.selectById(participateRecord.getAudienceId());
        if (audienceBaseInfo == null) {
            throw new ServiceException(AudienceServiceErrorCodeEnum.AUDIENCE_NOT_EXIST);
        }

        List<AudienceInvestigationInfo> investigationInfoList = audienceInvestigationInfoRepository
                .selectByExhibitionIdAndAudienceId(participateRecord.getExhibitionId(), participateRecord.getAudienceId());

        Map<String, BasicValuesetOptionVO> optionMap = getOptionValueMap(investigationInfoList);
        AudienceRegisterExhibitionVO exhibitionVO = new AudienceRegisterExhibitionVO();
        List<AudienceOperateLogVO> operateLogs = getOperateLogs(id);
        exhibitionVO.setOperateLogVOs(operateLogs);
        List<AudienceInvestigationInfoVO> investigationInfoVOS = getAudienceInvestigationInfoVOS(investigationInfoList, optionMap);
        AudienceBaseInfoVO baseInfoVO = AudienceBaseInfoConvert.INSTANCE.toAudienceBaseInfoVO(audienceBaseInfo);
        exhibitionVO.setAudienceBaseInfoVO(baseInfoVO);
        exhibitionVO.setParticipateStatus(participateRecord.getParticipateStatus());
        exhibitionVO.setParticipateStatusName(participateRecord.getParticipateStatusName());
        exhibitionVO.setInvestigationInfoVOs(investigationInfoVOS);
        return exhibitionVO;

    }

    private List<AudienceOperateLogVO> getOperateLogs(Long id) {
        // 操作日志
        List<BizOperateLogVO> bizOperateLogVOS = bizLogExternalService.listOperateLog(id,
                ValueSetDataBizOperateLogBusinessTypeEnum.AUDIENCE_PARTICIPATE.getOptionCode());
        return bizOperateLogVOS.stream().map(e -> {
            AudienceOperateLogVO log = new AudienceOperateLogVO();
            log.setOperateTime(e.getCreateTime());
            log.setOperUserName(e.getOperUserName());
            log.setOperBehaviorName(e.getOperBehaviorName());
            return log;
        }).toList();
    }

    /**
     * 获取问卷信息
     *
     * @param investigationInfos
     * @param optionMap
     * @return
     */
    private List<AudienceInvestigationInfoVO> getAudienceInvestigationInfoVOS(List<AudienceInvestigationInfo> investigationInfos,
                                                                              Map<String, BasicValuesetOptionVO> optionMap) {
        return investigationInfos.stream().map(info -> {
            AudienceInvestigationInfoVO infoVO = AudienceBaseInfoConvert.INSTANCE
                    .toAudienceInvestigationInfoVO(info);
            BasicValuesetOptionVO optionVO = optionMap.get(infoVO.getQuestionCode());
            infoVO.setQuestionCodeName(optionVO != null ? optionVO.getOptionDescriptionCn() : "");
            String[] split = infoVO.getAnswerChooseCode().split(",");
            List<String> cn = Arrays.stream(split).map(e -> {
                                BasicValuesetOptionVO answer = optionMap.get(e);
                                return answer != null ? answer.getOptionDescriptionCn() : null;
                            }
                    ).filter(Objects::nonNull)
                    .toList();
            infoVO.setAnswerChooseName(cn);
            return infoVO;
        }).toList();
    }

    /**
     * 获取
     * te
     *
     * @param investigationInfoList
     * @return
     */
    private Map<String, BasicValuesetOptionVO> getOptionValueMap(List<AudienceInvestigationInfo> investigationInfoList) {
        // 将 investigationInfos中的question answer 信息去除 组装查询值集的code
        List<String> questionCodes = investigationInfoList.stream().map(e -> {
                    List<String> codes = Lists.newArrayList(e.getQuestionCode());
                    if (StringUtils.isNotBlank(e.getAnswerChooseCode())) {
                        String[] code = e.getAnswerChooseCode().split(",");
                        codes.addAll(Arrays.stream(code).toList());
                    }
                    return codes;
                })
                .flatMap(List::stream)
                .distinct()
                .toList();

        return valuesetExternalService.getOptionMap(questionCodes);
    }

    @Override
    public AudienceBoardVO getAudienceBoard(AudienceBoardDTO queryDTO) {
        ExhibitionQueryDTO exhibitionQueryDTO = buildExhibitionQueryDTO(queryDTO);
        List<ExhibitionVO> exhibitionList = exhibitionExternalService.getExhibitionList(exhibitionQueryDTO);
        // 区分本届和往届展会ID
        Long thisExhibitionId = null;
        List<Long> previousExhibitionIds = new java.util.ArrayList<>();
        String sessionKey = queryDTO.getExhibitionSessionKey();
        thisExhibitionId =  processSessionKey(sessionKey, exhibitionList, thisExhibitionId, previousExhibitionIds);
        if (thisExhibitionId == null && previousExhibitionIds.isEmpty()) {
            return new AudienceBoardVO();
        }
        // 查询所有相关展会ID
        List<Long> allExhibitionIds = new java.util.ArrayList<>();
        if (thisExhibitionId != null) {
            allExhibitionIds.add(thisExhibitionId);
        }
        allExhibitionIds.addAll(previousExhibitionIds);
        List<AudienceBoardDO> rawList = baseInfoMapper.getAudienceBoardRawList(allExhibitionIds);
        return buildAudienceBoardVO(rawList, thisExhibitionId,previousExhibitionIds,exhibitionList);
    }

    private Long processSessionKey(String sessionKey, List<ExhibitionVO> exhibitionList, Long thisExhibitionId, List<Long> previousExhibitionIds) {
        if (sessionKey == null || sessionKey.isEmpty()) {
            // exhibitionSessionKey为空，默认第一个为thisExhibitionId，其余为previous
            if (!exhibitionList.isEmpty()) {
                thisExhibitionId = exhibitionList.get(0).getId();
                for (int i = 1; i < exhibitionList.size(); i++) {
                    previousExhibitionIds.add(exhibitionList.get(i).getId());
                }
            }
        } else {
            for (ExhibitionVO vo : exhibitionList) {
                if (sessionKey.equals(vo.getExhibitionSessionKey())) {
                    thisExhibitionId = vo.getId();
                } else {
                    previousExhibitionIds.add(vo.getId());
                }
            }
        }
        return thisExhibitionId;
    }

    private AudienceBoardVO buildAudienceBoardVO(List<AudienceBoardDO> rawList, Long thisExhibitionId, List<Long> previousExhibitionIds, List<ExhibitionVO> exhibitionList) {
        long total = rawList.size();
        long domestic = rawList.stream().filter(r -> "1".equals(r.getCountryCode())).count();
        long overseas = total - domestic;
        long male = rawList.stream().filter(r -> GenderEnum.MALE.getCode().equals(r.getAudienceGender())).count();
        long female = rawList.stream().filter(r -> GenderEnum.FEMALE.getCode().equals(r.getAudienceGender())).count();
        final Long finalThisExhibitionId = thisExhibitionId;
        long thisAudience = rawList.stream().filter(r -> r.getExhibitionId().equals(finalThisExhibitionId)).count();
        long previousAudience = rawList.stream().filter(r -> previousExhibitionIds.contains(r.getExhibitionId())).count();
        long online = rawList.stream().filter(r -> RegisterMethodEnum.SELF_REGISTER.getCode().equals(r.getRegisterMethod())).count();
        long offline = rawList.stream().filter(r -> RegisterMethodEnum.AGENT_REGISTER.getCode().equals(r.getRegisterMethod())).count();

        AudienceBoardVO boardVO = new AudienceBoardVO();
        ExhibitionVO firstExhibition = exhibitionList.getFirst();
        boardVO.setExhibitionId(firstExhibition.getId());
        boardVO.setExhibitionSessionKeys(exhibitionList.stream().map(ExhibitionVO::getExhibitionSessionKey).toList());
        boardVO.setTotalNumberRegisteredDomesticVisitors(domestic);
        boardVO.setTotalNumberRegisteredOverseasVisitors(overseas);
        boardVO.setMaleCount(male);
        boardVO.setFemaleCount(female);
        boardVO.setThisAudienceCount(thisAudience);
        boardVO.setPreviousAudienceCount(previousAudience);
        boardVO.setOnlinePaymentCount(online);
        boardVO.setOfflinePaymentCount(offline);
        boardVO.setMalePercent(total > 0 ? round((double) male / total) : 0.0);
        boardVO.setFemalePercent(total > 0 ? round((double) female / total) : 0.0);
        boardVO.setThisAudiencePercent(total > 0 ? round((double) thisAudience / total) : 0.0);
        boardVO.setPreviousAudiencePercent(total > 0 ? round((double) previousAudience / total) : 0.0);
        boardVO.setOnlinePaymentPercent(total > 0 ? round((double) online / total) : 0.0);
        boardVO.setOfflinePaymentPercent(total > 0 ? round((double) offline / total) : 0.0);
        return boardVO;
    }

    /**
     * 构建展会查询DTO
     *
     * @param queryDTO 查询参数
     * @return 展会查询DTO
     */
    private ExhibitionQueryDTO buildExhibitionQueryDTO(AudienceBoardDTO queryDTO) {
        ExhibitionQueryDTO exhibitionQueryDTO = new ExhibitionQueryDTO();

        // 设置展会届次
        if (StringUtils.isNotBlank(queryDTO.getExhibitionSessionKey())) {
            exhibitionQueryDTO.setExhibitionSessionKeys(List.of(queryDTO.getExhibitionSessionKey()));
        }

        // 设置展会标签代码
        if (StringUtils.isNotBlank(queryDTO.getExhibitionTagCode())) {
            exhibitionQueryDTO.setExhibitionTagCodes(List.of(queryDTO.getExhibitionTagCode()));
        }

        return exhibitionQueryDTO;
    }

    /**
     * 四舍五入到指定四位小数
     */
    private double round(double value) {
        double scale = Math.pow(10, 4);
        return Math.round(value * scale) / scale;
    }

}
