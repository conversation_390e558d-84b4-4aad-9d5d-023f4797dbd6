package com.dexpo.module.audience.app.convert;

import com.dexpo.framework.cache.redis.entity.audience.AudienceBaseInfoCache;
import com.dexpo.module.audience.api.dto.audience.AudiencePageQueryDTO;
import com.dexpo.module.audience.api.vo.audience.AudienceBaseInfoVO;
import com.dexpo.module.audience.api.vo.audience.AudienceInvestigationInfoVO;
import com.dexpo.module.audience.api.vo.audience.AudiencePageListVO;
import com.dexpo.module.audience.domain.model.AudienceBaseInfo;
import com.dexpo.module.audience.domain.model.AudienceInvestigationInfo;
import com.dexpo.module.audience.domain.model.AudiencePageListQuery;
import com.dexpo.module.audience.domain.model.AudiencePageList;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Objects;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AudienceBaseInfoConvert {

    AudienceBaseInfoConvert INSTANCE = Mappers.getMapper(AudienceBaseInfoConvert.class);

    /**
     * 转换为观众分页查询DO
     *
     * @param audiencePageQueryDTO
     * @return
     */
    AudiencePageListQuery toAudiencePageQueryDO(AudiencePageQueryDTO audiencePageQueryDTO);

    /**
     * 转换为观众分页列表VO
     *
     * @param audiencePageList
     * @return
     */
    @Mapping(target = "isDomestic", source = "countryCode", qualifiedByName = "setDemoticValue")
    AudiencePageListVO toAudiencePageListVO(AudiencePageList audiencePageList);

    /**
     * 转换为观众分页列表VO列表
     *
     * @param audiencePageListDOList
     * @return
     */
    List<AudiencePageListVO> toAudiencePageListVOList(List<AudiencePageList> audiencePageListDOList);


    /**
     * 转换为观众基础信息VO
     *
     * @param audienceBaseInfo
     * @return
     */
    AudienceBaseInfoVO toAudienceBaseInfoVO(AudienceBaseInfo audienceBaseInfo);

    /**
     * 转换为观众调查信息VO
     *
     * @param audienceInvestigationInfo
     * @return
     */
    AudienceInvestigationInfoVO toAudienceInvestigationInfoVO(AudienceInvestigationInfo audienceInvestigationInfo);

    /**
     * 转换为观众调查信息VO列表
     *
     * @param infos
     * @return
     */
    List<AudienceInvestigationInfoVO> toAudienceInvestigationInfoVOList(List<AudienceInvestigationInfo> infos);


    AudienceBaseInfo cacheToEntity(AudienceBaseInfoCache audienceBaseInfoCache);

    AudienceBaseInfoCache entityToCache(AudienceBaseInfo audienceBaseInfo);

    AudienceBaseInfoVO toVo(AudienceBaseInfo audienceBaseInfo);


    @Named("setDemoticValue")
    default Boolean setDemoticValue(String countryCode) {
        return Objects.equals(countryCode, "1");
    }

}
