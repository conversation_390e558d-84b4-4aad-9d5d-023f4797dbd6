package com.dexpo.module.audience.app.service;

import com.dexpo.module.audience.api.dto.audience.AudienceLoginDTO;
import com.dexpo.module.audience.api.vo.audience.AudienceLoginInfoVO;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Author: <PERSON><PERSON><PERSON>
 * @CreateTime: 2025-06-21
 * @Description:
 */
public interface AudienceMemberAppService{

    AudienceLoginInfoVO audienceLogin( AudienceLoginDTO loginDTO);
}
