package com.dexpo.module.audience.app.convert;

import com.dexpo.framework.cache.redis.entity.exhibition.ExhibitionInfoCache;
import com.dexpo.module.audience.domain.model.AudienceBaseInfo;
import com.dexpo.module.audience.domain.model.AudienceParticipateRecord;
import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseAudienceSyncMessage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

/**
 * @Author: Maojie
 * @CreateTime: 2025-06-24
 * @Description: 观众数据同步数仓对象转换类
 */

@Mapper
public interface DataWarehouseAudienceSyncMessageConvert {

    DataWarehouseAudienceSyncMessageConvert INSTANCE = Mappers.getMapper(DataWarehouseAudienceSyncMessageConvert.class);

    @Mapping(target = "memberCode", source = "baseInfo.audienceCode")
    @Mapping(target = "memberName", source = "baseInfo.audienceName")
    @Mapping(target = "memberFirstName", source = "baseInfo.audienceFirstName")
    @Mapping(target = "memberLastName", source = "baseInfo.audienceLastName")
    @Mapping(target = "memberGender", source = "baseInfo.audienceGender")
    @Mapping(target = "memberMobile", source = "baseInfo.audienceMobile")
    @Mapping(target = "memberEmail", source = "baseInfo.audienceEmail")
    @Mapping(target = "idCategory", source = "baseInfo.idCategory")
    @Mapping(target = "idNumber", source = "baseInfo.idNumber")
    @Mapping(target = "countryCode", source = "baseInfo.countryCode")
    @Mapping(target = "countryName", source = "baseInfo.countryNameCn")
    @Mapping(target = "currentHomeProvinceCode", source = "baseInfo.currentHomeProvinceCode")
    @Mapping(target = "currentHomeProvinceName", source = "baseInfo.currentHomeProvinceName")
    @Mapping(target = "currentHomeCityCode", source = "baseInfo.currentHomeCityCode")
    @Mapping(target = "currentHomeCityName", source = "baseInfo.currentHomeCityName")
    @Mapping(target = "currentHomeDistrictCode", source = "baseInfo.currentHomeDistrictCode")
    @Mapping(target = "currentHomeDistrictName", source = "baseInfo.currentHomeDistrictName")
    @Mapping(target = "enterpriseName", source = "baseInfo.enterpriseName")
    @Mapping(target = "departmentName", source = "baseInfo.departmentName")
    @Mapping(target = "positionName", source = "baseInfo.positionName")
    @Mapping(target = "memberType", source = "audienceParticipateRecord.audienceType")
    @Mapping(target = "barcode", source = "audienceParticipateRecord.participateCode")
    DataWarehouseAudienceSyncMessage convert(AudienceParticipateRecord audienceParticipateRecord, AudienceBaseInfo baseInfo, ExhibitionInfoCache exhibitionInfoCache);

}
