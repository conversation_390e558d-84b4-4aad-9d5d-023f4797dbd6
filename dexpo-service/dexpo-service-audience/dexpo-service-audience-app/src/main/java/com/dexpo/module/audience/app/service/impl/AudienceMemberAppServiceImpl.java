package com.dexpo.module.audience.app.service.impl;

import com.dexpo.framework.cache.redis.entity.audience.AudienceBaseInfoCache;
import com.dexpo.framework.cache.redis.entity.exhibition.ExhibitionInfoCache;
import com.dexpo.framework.common.enums.*;
import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.exception.enums.AudienceServiceErrorCodeEnum;
import com.dexpo.framework.common.exception.enums.MemberServiceErrorCodeEnum;
import com.dexpo.framework.message.enums.ValidScenarioEnum;
import com.dexpo.framework.security.core.LoginUser;
import com.dexpo.framework.security.core.util.SecurityFrameworkUtils;
import com.dexpo.module.audience.api.dto.audience.*;
import com.dexpo.module.audience.api.vo.audience.AudienceDetailInfoVO;
import com.dexpo.module.audience.api.vo.audience.AudienceInvestigationInfoVO;
import com.dexpo.module.audience.api.vo.audience.AudienceLoginInfoVO;
import com.dexpo.module.audience.api.vo.audience.AudienceProxyRecordCheckVO;
import com.dexpo.module.audience.app.convert.AudienceBaseInfoConvert;
import com.dexpo.module.audience.app.convert.AudienceInvestigationInfoConvert;
import com.dexpo.module.audience.app.convert.AudienceParticipateRecordConvert;
import com.dexpo.module.audience.app.convert.DataWarehouseAudienceSyncMessageConvert;
import com.dexpo.module.audience.app.service.AudienceMemberAppService;
import com.dexpo.module.audience.domain.model.AudienceBaseInfo;
import com.dexpo.module.audience.domain.model.AudienceInvestigationInfo;
import com.dexpo.module.audience.domain.model.AudienceParticipateRecord;
import com.dexpo.module.audience.domain.service.AudienceBaseInfoDomainService;
import com.dexpo.module.audience.domain.service.AudienceInvestigationDomainService;
import com.dexpo.module.audience.domain.service.AudienceParticipateRecordDomainService;
import com.dexpo.module.audience.domain.service.AudienceSignRecordDomainService;
import com.dexpo.module.audience.infrastructure.integration.base.CacheOptService;
import com.dexpo.module.audience.infrastructure.integration.integration.VerificationExternalService;
import com.dexpo.module.audience.infrastructure.mq.producer.DataWarehouseChannelProducer;
import com.dexpo.module.audience.infrastructure.tunnel.mysql.AudienceBaseInfoMapper;
import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseAudienceSyncMessage;
import com.dexpo.module.integration.api.verification.dto.VerificationDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: Maojie
 * @CreateTime: 2025-06-21
 * @Description:
 */

@Service
@Slf4j
public class AudienceMemberAppServiceImpl implements AudienceMemberAppService {

    @Resource
    private CacheOptService cacheOptService;

    @Resource
    private AudienceBaseInfoDomainService audienceBaseInfoDomainService;

    @Resource
    private AudienceParticipateRecordDomainService audienceParticipateRecordDomainService;

    @Resource
    private AudienceSignRecordDomainService audienceSignRecordDomainService;

    @Resource
    private DataWarehouseChannelProducer dataWarehouseChannelProducer;

    @Resource
    private AudienceInvestigationDomainService audienceInvestigationDomainService;

    @Resource(name = "audienceCacheExecutor")
    private ThreadPoolTaskExecutor audienceCacheExecutor;

    @Resource
    private VerificationExternalService verificationExternalService;

    @Resource
    private AudienceBaseInfoMapper baseInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT)
    public AudienceLoginInfoVO audienceLogin(AudienceLoginDTO loginDTO) {
        checkValidCode(loginDTO);
        String loginTool = loginDTO.getLoginTool();
        Long exhibitionId = loginDTO.getExhibitionId();
        AudienceLoginInfoVO audienceLoginInfoVO = new AudienceLoginInfoVO();
        AudienceBaseInfo baseInfoDO = AudienceBaseInfo.checkLoginToolAndInitAudienceBaseInfo(loginTool);
        if(!StringUtils.isBlank(baseInfoDO.getAudienceMobile())){
            audienceLoginInfoVO.setLoginType(LoginTypeEnum.PHONE.getCode());
        }else {
            audienceLoginInfoVO.setLoginType(LoginTypeEnum.EMAIL.getCode());
        }
        Long id;
        AudienceBaseInfo oldBaseInfoDO = getOldAudienceBaseInfoDO(loginTool);
        boolean isNewMember;
        if(oldBaseInfoDO == null){//新用户
            /**
             * 这里catch DuplicateKeyException是因为有可能极端情形下会
             * 出现在redis没有而在数据量有数据的状况,这里如果触发了手机号或邮箱的唯一
             * 索引异常则说明该数据已经在数据库存在了 需要查询出这个数据并set到redis中
             */
            try {
                baseInfoDO =audienceBaseInfoDomainService.save(baseInfoDO);
                isNewMember = Boolean.TRUE;
            }catch (DuplicateKeyException e){
                log.info("mediaLogin duplicate key"+e);
                isNewMember = Boolean.FALSE;
                baseInfoDO = audienceBaseInfoDomainService.getAudienceBaseInfoByMobileOrEmail(loginTool);
            }
            id = baseInfoDO.getId();
            //将新用户的用户信息写到cache中
            cacheOptService.setAudienceBaseInfoCache(AudienceBaseInfoConvert.INSTANCE.entityToCache(baseInfoDO),loginTool);
        }else{//老用户
            isNewMember = Boolean.FALSE;
            id = oldBaseInfoDO.getId();
        }
        AudienceBaseInfo baseInfoValue = oldBaseInfoDO == null ? baseInfoDO : oldBaseInfoDO;
        //参会记录
        AudienceParticipateRecord audienceParticipateRecord = audienceParticipateRecordDomainService.saveIfNotExist(id, exhibitionId);
        //协议记录
        audienceSignRecordDomainService.saveIfNotExist(id, loginDTO.getAgreementId());
        // 生成token 缓存token和用户信息
        String token = getToken(id, baseInfoValue);
        audienceLoginInfoVO.setNewMember(isNewMember);
        audienceLoginInfoVO.setToken(token);
        audienceLoginInfoVO.setAudienceBaseInfoVO(AudienceBaseInfoConvert.INSTANCE.toVo(baseInfoValue));
        audienceLoginInfoVO.setLoginTool(loginDTO.getLoginTool());
        if(isNewMember){//新用户需要同步给数仓
            try{
                ExhibitionInfoCache exhibitionInfoCache = cacheOptService.getExhibitionInfoCacheById(exhibitionId);
                syncDataToWarehouse(audienceParticipateRecord,baseInfoValue,exhibitionInfoCache);
            } catch (Exception e) {
                log.error("audienceLogin syncDataToWarehouse error",e);
            }
        }
        return audienceLoginInfoVO;
    }

    @Override
    public AudienceDetailInfoVO getAudienceDetail(AudienceDetailInfoDTO detailDTO) {
        AudienceDetailInfoVO audienceDetailInfoVO = new AudienceDetailInfoVO();
        Long audienceId = SecurityFrameworkUtils.getLoginUserId();
        audienceDetailInfoVO.setAudienceParticipateRecordVO(AudienceParticipateRecordConvert.INSTANCE.entityToVO(audienceParticipateRecordDomainService.getByAudienceIdAndExhibitionId(audienceId,detailDTO.getExhibitionId())));
        audienceDetailInfoVO.setAudienceBaseInfoVO(AudienceBaseInfoConvert.INSTANCE.toAudienceBaseInfoVO(audienceBaseInfoDomainService.getAudienceBaseInfoByIdOrCache(detailDTO.getLoginTool(),audienceId)));
        audienceDetailInfoVO.setAudienceInvestigationInfoVOs(AudienceInvestigationInfoConvert.INSTANCE.entitiesToVos(audienceInvestigationDomainService.selectByAudienceIdAndExhibitionId(audienceId,detailDTO.getExhibitionId())));
        return audienceDetailInfoVO;
    }

    @Override
    public Long submitBaseInfo(AudienceRegisterBaseInfoDTO baseInfoDTO) {
        AudienceBaseInfo baseInfo =  AudienceBaseInfoConvert.INSTANCE.dtoToEntity(baseInfoDTO.getBaseInfo());
        //实名认证
        //identityAuthentication(baseInfo,baseInfoDTO.getRegisterLanguage());
        Long id = audienceBaseInfoDomainService.update(baseInfo,baseInfoDTO.getRegisterLanguage());
        //刷新缓存
        cacheOptService.setAudienceBaseInfoCache(AudienceBaseInfoConvert.INSTANCE.entityToCache(audienceBaseInfoDomainService.getById(id)),baseInfoDTO.getBaseInfo().getAudienceEmail());
        String mobile = baseInfoDTO.getBaseInfo().getAudienceMobile();
        if(StringUtils.isNotBlank( mobile)){
            cacheOptService.setAudienceBaseInfoCache(AudienceBaseInfoConvert.INSTANCE.entityToCache(audienceBaseInfoDomainService.getById(id)),mobile);
        }
        return id;
    }

    private void identityAuthentication(AudienceBaseInfo baseInfo,String registerLanguage){
        //只有身份证类型才走实名认证
        if (!IdCategoryEnum.RESIDENT_ID_CARD.getCode().equals(baseInfo.getIdCategory())) {
            return;
        }
        VerificationDTO verificationDTO = new VerificationDTO();
        verificationDTO.setIndex(0);
        String memberName = baseInfo.getAudienceName();
        if(StringUtils.isBlank(memberName)){
            memberName = buildAudienceName(baseInfo.getAudienceFirstName(), baseInfo.getAudienceLastName(), registerLanguage);
        }
        verificationDTO.setMemberName(memberName);
        verificationDTO.setIdNumber(baseInfo.getIdNumber());
        verificationExternalService.Id2MetaVerify(verificationDTO);
    }


    private String buildAudienceName(String firstName,String lastName, String registerLanguage) {
        String memberName = null;
        if (StringUtils.isNotBlank(lastName)
                && StringUtils.isNotBlank(firstName)) {
            if (RegisterLanguageEnum.CHINESE.getCode().equals(registerLanguage)) {
                memberName = lastName + firstName;
            } else {
                memberName = firstName + " " + lastName;
            }
        }
        return memberName;
    }

    @Override
    public Long updateBaseInfo(AudienceRegisterBaseInfoDTO baseInfoDTO) {
        Long id =  audienceBaseInfoDomainService.update(AudienceBaseInfoConvert.INSTANCE.dtoToEntity(baseInfoDTO.getBaseInfo()),baseInfoDTO.getRegisterLanguage());
        //刷新缓存
        String email = baseInfoDTO.getBaseInfo().getAudienceEmail();
        String mobile = baseInfoDTO.getBaseInfo().getAudienceMobile();
        if (StringUtils.isNotBlank( email)){
            cacheOptService.setAudienceBaseInfoCache(cacheOptService.getAudienceBaseInfoCache(email), email);
        }
        if(StringUtils.isNotBlank( mobile)){
            cacheOptService.setAudienceBaseInfoCache(cacheOptService.getAudienceBaseInfoCache(mobile), mobile);
        }
        return id;
    }

    @Override
    public List<AudienceInvestigationInfoVO>  submitInvestigationInfo(AudienceRegisterInvestigationInfoDTO investigationInfoDTO) {
        List<AudienceInvestigationInfo> investigationInfos = updateInvestigationInfos(investigationInfoDTO);
        return AudienceInvestigationInfoConvert.INSTANCE.entitiesToVos(investigationInfos);
    }

    @Override
    public List<AudienceInvestigationInfoVO> updateInvestigationInfo(AudienceRegisterInvestigationInfoDTO investigationInfoDTO) {
        List<AudienceInvestigationInfo> investigationInfos = updateInvestigationInfos(investigationInfoDTO);
        return AudienceInvestigationInfoConvert.INSTANCE.entitiesToVos(investigationInfos);
    }

    private List<AudienceInvestigationInfo> updateInvestigationInfos(AudienceRegisterInvestigationInfoDTO investigationInfoDTO) {
        List<AudienceInvestigationInfo> investigationInfos = AudienceInvestigationInfoConvert.INSTANCE.dtosToEntities(investigationInfoDTO.getAudienceInvestigationInfos());
        List<Long> willDeleteIds = filterWillDeleteIds(investigationInfos, investigationInfoDTO.getOldIds());
        audienceInvestigationDomainService.deleteByIds(willDeleteIds);
        //刷新缓存
        investigationInfos = audienceInvestigationDomainService.saveOrUpdateBach(investigationInfos);
        cacheOptService.setAudienceInvestigationCache(AudienceInvestigationInfoConvert.INSTANCE.entitiesToCache(investigationInfos), investigationInfoDTO.getAudienceInvestigationInfos().get(0).getAudienceId(), investigationInfoDTO.getAudienceInvestigationInfos().get(0).getExhibitionId());
        return investigationInfos;
    }

    @Override
    public void initAudienceRedisCache() {
        log.info("开始初始化观众基础信息到Redis缓存");
        long startTime = System.currentTimeMillis();
        
        try {
            Integer pageNo = 1;
            Integer pageSize = 1000;
            int totalProcessed = 0;
            int totalFailed = 0;
            
            while (true) {
                // 分页查询数据
                List<AudienceBaseInfo> audienceBaseInfos = audienceBaseInfoDomainService.queryByPage(pageNo, pageSize);
                
                if (audienceBaseInfos.isEmpty()) {
                    log.info("所有数据已处理完成，共处理 {} 条记录，失败 {} 条", totalProcessed, totalFailed);
                    break;
                }
                
                log.info("开始处理第 {} 页数据，共 {} 条记录", pageNo, audienceBaseInfos.size());
                
                // 创建异步任务列表
                List<CompletableFuture<Boolean>> futures = new ArrayList<>();
                
                // 为每条记录创建异步任务
                for (AudienceBaseInfo audienceBaseInfo : audienceBaseInfos) {
                    CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
                        try {
                            // 异步设置Redis缓存
                            cacheOptService.setAudienceBaseInfoCache(
                                AudienceBaseInfoConvert.INSTANCE.entityToCache(audienceBaseInfo), 
                                audienceBaseInfo.getAudienceMobile()
                            );
                            return true; // 成功
                        } catch (Exception e) {
                            log.error("设置观众基础信息缓存失败，观众ID: {}, 手机号: {}, 错误: {}", 
                                audienceBaseInfo.getId(), 
                                audienceBaseInfo.getAudienceMobile(), 
                                e.getMessage(), e);
                            return false; // 失败
                        }
                    }, audienceCacheExecutor);
                    
                    futures.add(future);
                }
                
                // 等待当前批次的所有异步任务完成并统计结果
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
                allFutures.join();
                
                // 统计成功和失败的数量
                for (CompletableFuture<Boolean> future : futures) {
                    try {
                        if (!future.get()) {
                            totalFailed++;
                        }
                    } catch (Exception e) {
                        totalFailed++;
                        log.error("获取异步任务结果失败", e);
                    }
                }
                
                totalProcessed += audienceBaseInfos.size();
                pageNo++;
                
                log.info("第 {} 页数据处理完成，已累计处理 {} 条记录，失败 {} 条", 
                    pageNo - 1, totalProcessed, totalFailed);
            }
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.info("Redis缓存预热完成，总耗时: {} ms，处理记录: {} 条，失败: {} 条，平均处理速度: {} 条/秒", 
                duration, totalProcessed, totalFailed, 
                totalProcessed > 0 ? String.format("%.2f", (double) totalProcessed / duration * 1000) : "0");
            
        } catch (Exception e) {
            log.error("初始化观众基础信息到Redis缓存过程中发生错误", e);
            throw new RuntimeException("Redis缓存预热失败", e);
        }
    }

    @Override
    public Long proxyRegister(AudienceRegisterBaseInfoDTO baseInfoDTO) {
        AudienceBaseInfo baseInfo =  AudienceBaseInfoConvert.INSTANCE.dtoToEntity(baseInfoDTO.getBaseInfo());
        Long exhibitionId = baseInfoDTO.getExhibitionInfo().getId();
        checkIfRegistered(baseInfo,exhibitionId);
        //实名认证
        //identityAuthentication(baseInfo, baseInfoDTO.getRegisterLanguage());
        audienceBaseInfoDomainService.saveOrUpdate(baseInfo,baseInfoDTO.getRegisterLanguage());
        AudienceParticipateRecord audienceParticipateRecord = saveAudienceParticipateRecord(baseInfo, exhibitionId);
        //添加缓存
        cacheOptService.setAudienceBaseInfoCache(AudienceBaseInfoConvert.INSTANCE.entityToCache(baseInfo), baseInfo.getAudienceMobile());
        cacheOptService.setAudienceParticipateRecordCache(audienceParticipateRecord);
        //同步数据到数仓
        ExhibitionInfoCache exhibitionInfoCache = cacheOptService.getExhibitionInfoCacheById(exhibitionId);
        syncDataToWarehouse(audienceParticipateRecord,baseInfo,exhibitionInfoCache);
        //发送通知消息
        return baseInfo.getId();
    }

    private void sendMessage(List<AudienceBaseInfo> audienceBaseInfos,ExhibitionInfoCache exhibitionInfoCache,ValidScenarioEnum scenarioEnum){


    }

    private AudienceParticipateRecord saveAudienceParticipateRecord(AudienceBaseInfo baseInfo, Long exhibitionId) {
        AudienceParticipateRecord audienceParticipateRecord=audienceParticipateRecordDomainService.getByAudienceIdAndExhibitionId(baseInfo.getId(), exhibitionId);
        if(audienceParticipateRecord==null){
            audienceParticipateRecord=AudienceParticipateRecord.initAudienceParticipateRecord(baseInfo.getId(), exhibitionId);
        }
        audienceParticipateRecord.setParticipateStatus(AudienceParticipateStatusEnum.ACTIVATED.getCode());
        audienceParticipateRecord.setParticipateStatusName(AudienceParticipateStatusEnum.ACTIVATED.getDescriptionCN());
        Long audienceParticipateRecordId=audienceParticipateRecordDomainService.save(audienceParticipateRecord);
        audienceParticipateRecord.setAudienceId(audienceParticipateRecordId);
        return audienceParticipateRecord;
    }

    private void checkIfRegistered(AudienceBaseInfo baseInfo,Long exhibitionId){
        String email = baseInfo.getAudienceEmail();
        String mobile = baseInfo.getAudienceMobile();
        AudienceProxyRecordCheckVO  audienceProxyRecordCheckVO = baseInfoMapper.selectRegisterRecordByLoginToolAndExhibitionId(email,mobile,exhibitionId);
        if(ObjectUtils.isNotEmpty(audienceProxyRecordCheckVO)){
             if(AudienceParticipateStatusEnum.isRegistered(audienceProxyRecordCheckVO.getParticipateStatus())){
                 if (StringUtils.isNotBlank( mobile) && mobile.equals(audienceProxyRecordCheckVO.getAudienceMobile())) {
                     throw new ServiceException(MemberServiceErrorCodeEnum.MEMBER_MOBILE_EXIST);
                 } else if (StringUtils.isNotBlank( email) && email.equals(audienceProxyRecordCheckVO.getAudienceEmail())) {
                     throw new ServiceException(MemberServiceErrorCodeEnum.MEMBER_EMAIL_EXIST);
                 }
             }
            baseInfo.setId(audienceProxyRecordCheckVO.getAudienceId());
        }
    }

    /**
     * 筛选出取消选择的问卷ids
     * @return
     */
    private List<Long> filterWillDeleteIds(List<AudienceInvestigationInfo> investigationInfos,List<Long> oldIds){
        if(CollectionUtils.isEmpty(oldIds)){
            return null;
        }
        List<Long> deleteIds = new ArrayList<>();
        //循环investigationInfos匹配oldIds中的id,如果都没有匹配上的就添加到deleteIds中
        for (AudienceInvestigationInfo investigationInfo : investigationInfos) {
            boolean flag = false;
            for (Long oldId : oldIds) {
                if (oldId.equals(investigationInfo.getId())) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
               deleteIds.add(investigationInfo.getId());
            }
        }
        return deleteIds;
    }


    private void syncDataToWarehouse(AudienceParticipateRecord audienceParticipateRecord, AudienceBaseInfo baseInfo, ExhibitionInfoCache exhibitionInfoCache){
        DataWarehouseAudienceSyncMessage syncMessage = DataWarehouseAudienceSyncMessageConvert.INSTANCE.convert(audienceParticipateRecord,baseInfo,exhibitionInfoCache);
        dataWarehouseChannelProducer.dataWarehouseAudienceSyncChannel(syncMessage);
    }


    private String getToken(Long id, AudienceBaseInfo baseInfoDO) {
        LoginUser loginUser = new LoginUser();
        loginUser.setId(id);
        loginUser.setUserType(ValueSetUserTypeEnum.AUDIENCE.getCode());
        loginUser.setMemberCode(baseInfoDO.getAudienceCode());
        loginUser.setUserName(baseInfoDO.getAudienceName());
        /**
         * 因为媒体注册的姓名在审核之前不是有效的(可能会变化),所以这里不放memberName到token中,
         * 防止因为媒体注册的姓名被修改,导致token中的memberName和数据库中的memberName不一致,以及冗余存储的memberName出现错误
         */
        loginUser.setMemberMobile(baseInfoDO.getAudienceMobile());
        loginUser.setMemberEmail(baseInfoDO.getAudienceEmail());
        return cacheOptService.getToken(loginUser);
    }

    private AudienceBaseInfo getOldAudienceBaseInfoDO(String loginTool){
        AudienceBaseInfoCache oldBaseInfoCache = cacheOptService.getAudienceBaseInfoCache(loginTool);
        return AudienceBaseInfoConvert.INSTANCE.cacheToEntity(oldBaseInfoCache);
    }




    private void checkValidCode(AudienceLoginDTO loginDTO) {
        boolean validBoolean = cacheOptService.validCode(loginDTO.getLoginTool(), loginDTO.getValidCode());
        if (!validBoolean) {// 校验失败
            throw new ServiceException(AudienceServiceErrorCodeEnum.AUDIENCE_LOGIN_VALID_ERROR);
        }
    }
}
