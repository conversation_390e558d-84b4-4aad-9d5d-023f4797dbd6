package com.dexpo.module.audience.app.convert;

import com.dexpo.framework.cache.redis.entity.audience.AudienceInvestigationCache;
import com.dexpo.module.audience.api.dto.audience.AudienceInvestigationInfoDTO;
import com.dexpo.module.audience.api.dto.audience.AudienceRegisterInvestigationInfoDTO;
import com.dexpo.module.audience.api.vo.audience.AudienceInvestigationInfoVO;
import com.dexpo.module.audience.domain.model.AudienceInvestigationInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author: Maojie
 * @CreateTime: 2025-06-25
 * @Description:
 */

@Mapper
public interface AudienceInvestigationInfoConvert {
    AudienceInvestigationInfoConvert INSTANCE = Mappers.getMapper(AudienceInvestigationInfoConvert.class);

    List<AudienceInvestigationInfoVO> entitiesToVos(List<AudienceInvestigationInfo> audienceInvestigationInfos);

    List<AudienceInvestigationInfo> dtosToEntities(List<AudienceInvestigationInfoDTO> audienceInvestigationInfoDTOS);

    List<AudienceInvestigationInfo> cacheToEntities(List<AudienceInvestigationCache> cacheList);

    List<AudienceInvestigationCache> entitiesToCache(List<AudienceInvestigationInfo> audienceInvestigationInfos);

}
