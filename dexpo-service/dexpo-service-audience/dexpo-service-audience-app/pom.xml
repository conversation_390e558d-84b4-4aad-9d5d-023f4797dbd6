<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dexpo</groupId>
        <artifactId>dexpo-service-audience</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>dexpo-service-audience-app</artifactId>
    <packaging>jar</packaging>

    <name>dexpo-service-audience-app</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-audience-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-audience-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-audience-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-message</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>


    </dependencies>
</project>
