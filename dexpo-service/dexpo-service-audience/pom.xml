<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dexpo</groupId>
        <artifactId>dexpo-framework-starter-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../../dexpo-framework/dexpo-framework-starter-parent/pom.xml</relativePath>
    </parent>

    <artifactId>dexpo-service-audience</artifactId>
    <packaging>pom</packaging>

    <name>dexpo-service-audience</name>
    <modules>
        <module>dexpo-service-audience-starter</module>
        <module>dexpo-service-audience-api</module>
        <module>dexpo-service-audience-app</module>
        <module>dexpo-service-audience-domain</module>
        <module>dexpo-service-audience-entry</module>
        <module>dexpo-service-audience-infrastructure</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-audience-entry</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-audience-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-audience-domain</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-audience-app</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-audience-infrastructure</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-audience-starter</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-log-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


</project>
