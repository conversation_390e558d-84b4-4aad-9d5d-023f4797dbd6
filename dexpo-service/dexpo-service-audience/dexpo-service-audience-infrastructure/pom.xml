<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dexpo</groupId>
        <artifactId>dexpo-service-audience</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>dexpo-service-audience-infrastructure</artifactId>
    <packaging>jar</packaging>

    <name>dexpo-service-audience-infrastructure</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-audience-domain</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId> <!-- 代码生成器，使用它解析表结构 -->
        </dependency>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-cache</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-base-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-integration-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-exhibition-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-log-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-mq</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-framework-starter-rpc</artifactId>
        </dependency>

    </dependencies>
</project>
