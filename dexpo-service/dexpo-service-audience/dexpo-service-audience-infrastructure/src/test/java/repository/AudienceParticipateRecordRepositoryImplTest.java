package repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dexpo.module.audience.domain.model.AudienceParticipateRecord;
import com.dexpo.module.audience.infrastructure.convert.AudienceParticipateRecordDOConvert;
import com.dexpo.module.audience.infrastructure.integration.base.CacheOptService;
import com.dexpo.module.audience.infrastructure.repository.AudienceParticipateRecordRepositoryImpl;
import com.dexpo.module.audience.infrastructure.tunnel.dataobject.AudienceParticipateRecordDO;
import com.dexpo.module.audience.infrastructure.tunnel.mysql.AudienceParticipateRecordMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("观众参与记录仓储实现类测试")
class AudienceParticipateRecordRepositoryImplTest {

    @Mock
    private AudienceParticipateRecordMapper audienceParticipateRecordMapper;

    @Mock
    private CacheOptService cacheOptService;

    @InjectMocks
    private AudienceParticipateRecordRepositoryImpl audienceParticipateRecordRepository;

    private AudienceParticipateRecord participateRecord;
    private AudienceParticipateRecordDO participateRecordDO;
    private List<AudienceParticipateRecordDO> participateRecordDOList;

    @BeforeEach
    void setUp() {
        participateRecord = new AudienceParticipateRecord();
        participateRecord.setId(1L);
        participateRecord.setAudienceId(1L);
        participateRecord.setExhibitionId(100L);
        participateRecord.setParticipateCode("PART001");
        participateRecord.setParticipateStatus("ACTIVATED");
        participateRecord.setParticipateStatusName("已激活");

        participateRecordDO = new AudienceParticipateRecordDO();
        participateRecordDO.setId(1L);
        participateRecordDO.setAudienceId(1L);
        participateRecordDO.setExhibitionId(100L);
        participateRecordDO.setParticipateCode("PART001");
        participateRecordDO.setParticipateStatus("ACTIVATED");
        participateRecordDO.setParticipateStatusName("已激活");

        participateRecordDOList = Arrays.asList(participateRecordDO);
    }

    @Test
    @DisplayName("保存观众参与记录成功")
    void testSave_Success() {
        when(audienceParticipateRecordMapper.insert(any(AudienceParticipateRecordDO.class))).thenReturn(1);

        Long result = audienceParticipateRecordRepository.save(participateRecord);

        assertNotNull(result);
        assertEquals(1L, result);
        verify(audienceParticipateRecordMapper).insert(any(AudienceParticipateRecordDO.class));
    }

    @Test
    @DisplayName("根据ID列表查询观众参与记录成功")
    void testSelectByIds_Success() {
        when(audienceParticipateRecordMapper.selectByIds(anyList())).thenReturn(participateRecordDOList);

        List<AudienceParticipateRecord> result = audienceParticipateRecordRepository.selectByIds(Arrays.asList(1L, 2L));

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1L, result.get(0).getId());
        verify(audienceParticipateRecordMapper).selectByIds(Arrays.asList(1L, 2L));
    }

    @Test
    @DisplayName("根据ID列表查询观众参与记录 - 空列表")
    void testSelectByIds_EmptyList() {
        when(audienceParticipateRecordMapper.selectByIds(anyList())).thenReturn(Arrays.asList());

        List<AudienceParticipateRecord> result = audienceParticipateRecordRepository.selectByIds(Arrays.asList());

        assertNotNull(result);
        assertEquals(0, result.size());
        verify(audienceParticipateRecordMapper).selectByIds(Arrays.asList());
    }

    @Test
    @DisplayName("根据ID查询观众参与记录成功")
    void testSelectById_Success() {
        when(audienceParticipateRecordMapper.selectById(anyLong())).thenReturn(participateRecordDO);

        AudienceParticipateRecord result = audienceParticipateRecordRepository.selectById(1L);

        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals(1L, result.getAudienceId());
        assertEquals(100L, result.getExhibitionId());
        verify(audienceParticipateRecordMapper).selectById(1L);
    }

    @Test
    @DisplayName("根据ID查询观众参与记录 - 返回null")
    void testSelectById_ReturnsNull() {
        when(audienceParticipateRecordMapper.selectById(anyLong())).thenReturn(null);

        AudienceParticipateRecord result = audienceParticipateRecordRepository.selectById(1L);

        assertNull(result);
        verify(audienceParticipateRecordMapper).selectById(1L);
    }

    @Test
    @DisplayName("批量激活观众参与记录成功")
    void testActiveByIds_Success() {
        List<Long> ids = Arrays.asList(1L, 2L, 3L);

        audienceParticipateRecordRepository.activeByIds(ids);

        verify(audienceParticipateRecordMapper).activeByIds(eq(ids), eq("ACTIVATED"), eq("NOT_ACTIVATED"));
    }

    @Test
    @DisplayName("从缓存获取观众参与记录信息成功")
    void testGetInfoFromCache_Success() {
        when(cacheOptService.getAudienceParticipateRecordCache(anyLong(), anyLong())).thenReturn(participateRecord);

        AudienceParticipateRecord result = audienceParticipateRecordRepository.getInfoFromCache(1L, 100L);

        assertNotNull(result);
        assertEquals(1L, result.getId());
        verify(cacheOptService).getAudienceParticipateRecordCache(1L, 100L);
    }

    @Test
    @DisplayName("从缓存获取观众参与记录信息 - 返回null")
    void testGetInfoFromCache_ReturnsNull() {
        when(cacheOptService.getAudienceParticipateRecordCache(anyLong(), anyLong())).thenReturn(null);

        AudienceParticipateRecord result = audienceParticipateRecordRepository.getInfoFromCache(1L, 100L);

        assertNull(result);
        verify(cacheOptService).getAudienceParticipateRecordCache(1L, 100L);
    }

    @Test
    @DisplayName("设置观众参与记录信息到缓存成功")
    void testSetInfoToCache_Success() {
        audienceParticipateRecordRepository.setInfoToCache(participateRecord);

        verify(cacheOptService).setAudienceParticipateRecordCache(participateRecord);
    }

    @Test
    @DisplayName("根据观众ID和展会ID查询观众参与记录成功")
    void testGetByAudienceIdAndExhibitionId_Success() {
        when(audienceParticipateRecordMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(participateRecordDO);

        AudienceParticipateRecord result = audienceParticipateRecordRepository.getByAudienceIdAndExhibitionId(1L, 100L);

        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals(1L, result.getAudienceId());
        assertEquals(100L, result.getExhibitionId());
        verify(audienceParticipateRecordMapper).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("根据观众ID和展会ID查询观众参与记录 - 返回null")
    void testGetByAudienceIdAndExhibitionId_ReturnsNull() {
        when(audienceParticipateRecordMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        AudienceParticipateRecord result = audienceParticipateRecordRepository.getByAudienceIdAndExhibitionId(1L, 100L);

        assertNull(result);
        verify(audienceParticipateRecordMapper).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("边界测试：ID为0")
    void testSelectById_BoundaryZero() {
        when(audienceParticipateRecordMapper.selectById(0L)).thenReturn(null);

        AudienceParticipateRecord result = audienceParticipateRecordRepository.selectById(0L);

        assertNull(result);
        verify(audienceParticipateRecordMapper).selectById(0L);
    }

    @Test
    @DisplayName("边界测试：大数据量")
    void testSelectByIds_LargeData() {
        List<Long> largeIds = Arrays.asList(999999L, 999998L, 999997L);
        when(audienceParticipateRecordMapper.selectByIds(largeIds)).thenReturn(participateRecordDOList);

        List<AudienceParticipateRecord> result = audienceParticipateRecordRepository.selectByIds(largeIds);

        assertNotNull(result);
        assertEquals(1, result.size());
        verify(audienceParticipateRecordMapper).selectByIds(largeIds);
    }
} 