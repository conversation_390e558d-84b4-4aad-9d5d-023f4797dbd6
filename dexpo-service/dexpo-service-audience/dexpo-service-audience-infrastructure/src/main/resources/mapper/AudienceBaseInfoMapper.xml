<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dexpo.module.audience.infrastructure.tunnel.mysql.AudienceBaseInfoMapper">

    <select id="getAudienceList" resultType="com.dexpo.module.audience.domain.model.AudiencePageList">
        select apr.id audienceParticipateRecordId,
               apr.participate_status,
               apr.participate_status_name,
               apr.exhibition_id,
               apr.register_system,
               apr.register_source,
               apr.register_time,
               apr.audience_id,
               abi.audience_name,
               abi.audience_mobile,
               abi.audience_email,
               abi.country_code,
               apr.audience_type
        from audience_participate_record apr
                 inner join audience_base_info abi on apr.audience_id = abi.id
        <where>
            apr.del_flg = 0
          and abi.del_flg = 0
            <if test="queryDO.exhibitionIds != null and queryDO.exhibitionIds.size() != 0">
                and apr.exhibition_id in
                <foreach collection="queryDO.exhibitionIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="queryDO.participateStatus != null">
                and apr.participate_status = #{queryDO.participateStatus}
            </if>
            <if test="queryDO.registerSystem != null">
                and apr.register_system = #{queryDO.registerSystem}
            </if>
            <if test="queryDO.audienceType != null">
                and apr.audience_type = #{queryDO.audienceType}
            </if>

            <if test="queryDO.isDomestic != null and queryDO.isDomestic == true">
                and abi.country_code = '1'
            </if>
            <if test="queryDO.isDomestic != null and queryDO.isDomestic == false">
                and abi.country_code != '1'
            </if>
            <if test="queryDO.registerTimeStart != null">
                and apr.register_time &gt;= #{queryDO.registerTimeStart}
            </if>
            <if test="queryDO.registerTimeEnd != null">
                and apr.register_time &lt;= #{queryDO.registerTimeEnd}
            </if>

            <if test="queryDO.memberName != null and queryDO.memberName != ''">
                and abi.member_name like concat('%'
                  , #{queryDO.memberName}
                  , '%')
            </if>
            <if test="queryDO.memberMobile != null and queryDO.memberMobile != ''">
                and abi.member_mobile like concat('%'
                  , #{queryDO.memberMobile}
                  , '%')
            </if>
        </where>
        order by
        case when apr.participate_status = #{activeStatus} then 0 else 1 end asc,
        case when apr.participate_status = #{activeStatus} then apr.register_time end ,
        case when apr.participate_status != #{activeStatus} then apr.register_time end desc

    </select>

    <select id="getAudienceBoardRawList" resultType="com.dexpo.module.audience.infrastructure.tunnel.dataobject.AudienceBoardDO">
        SELECT
            apr.id AS participateRecordId,
            apr.audience_id AS audienceId,
            apr.audience_type AS audienceType,
            apr.exhibition_id AS exhibitionId,
            apr.register_method AS registerMethod,
            abi.country_code AS countryCode,
            abi.audience_gender AS audienceGender
        FROM audience_participate_record apr
        INNER JOIN audience_base_info abi ON apr.audience_id = abi.id
        WHERE apr.del_flg = 0
          AND abi.del_flg = 0
          <if test="exhibitionIds != null and exhibitionIds.size() > 0">
            AND apr.exhibition_id IN
            <foreach collection="exhibitionIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
          </if>
    </select>

    <select id="selectRegisterRecordByLoginToolAndExhibitionId" resultType="com.dexpo.module.audience.api.vo.audience.AudienceProxyRecordCheckVO">
        select
            mbi.audience_mobile,
            mbi.audience_email,
            mbi.id as audience_id,
            mpr.exhibition_id,
            mpr.participate_status
        from audience_base_info mbi
                 inner join audience_participate_record mpr on mbi.id = mpr.audience_id and mpr.del_flg =0 and mbi.del_flg =0 and mpr.exhibition_id = #{exhibitionId}
        where ( mbi.audience_mobile =#{audienceMobile}  or mbi.audience_email =#{audienceEmail} ) and mpr.exhibition_id  =#{exhibitionId}
        order by mbi.id desc limit 1
    </select>

</mapper>