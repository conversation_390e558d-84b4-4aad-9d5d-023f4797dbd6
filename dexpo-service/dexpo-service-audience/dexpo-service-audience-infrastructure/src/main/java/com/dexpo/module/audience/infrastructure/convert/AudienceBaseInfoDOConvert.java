package com.dexpo.module.audience.infrastructure.convert;

import com.dexpo.module.audience.domain.model.AudienceBaseInfo;
import com.dexpo.module.audience.infrastructure.tunnel.dataobject.AudienceBaseInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * @Author: <PERSON><PERSON><PERSON>
 * @CreateTime: 2025-06-23
 * @Description:
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AudienceBaseInfoDOConvert {

    AudienceBaseInfoDOConvert INSTANCE = Mappers.getMapper(AudienceBaseInfoDOConvert.class);

    @Mappings({})
     AudienceBaseInfoDO convert(AudienceBaseInfo audienceBaseInfo);

    @Mappings({})
     AudienceBaseInfo convertToEntity(AudienceBaseInfoDO audienceBaseInfoDO);
}
