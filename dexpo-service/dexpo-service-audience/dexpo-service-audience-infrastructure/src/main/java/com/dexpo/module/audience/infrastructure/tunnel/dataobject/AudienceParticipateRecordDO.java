package com.dexpo.module.audience.infrastructure.tunnel.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("audience_participate_record")
public class AudienceParticipateRecordDO  extends BaseDO {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 观众ID
     */
    @TableField("audience_id")
    private Long audienceId;

    /**
     * 观众类型
     */
    @TableField("audience_type")
    private String audienceType;

    /**
     * 展会ID
     */
    @TableField("exhibition_id")
    private Long exhibitionId;


    /**
     * 唯一编码
     */
    @TableField("participate_code")
    private String participateCode;

    /**
     * 注册时间
     */
    @TableField("register_time")
    private LocalDateTime registerTime;

    /**
     * 参展状态 值集VS_AUDIENCE_PARTICIPATE_STATUS
     */
    @TableField("participate_status")
    private String participateStatus;

    /**
     * 参展状态 名称
     */
    @TableField("participate_status_name")
    private String participateStatusName;

    /**
     * 注册系统
     */
    @TableField("register_system")
    private String registerSystem;

    /**
     * 注册方式
     */
    @TableField("register_method")
    private String registerMethod;

    /**
     * 注册来源
     */
    @TableField("register_source")
    private String registerSource;

    /**
     * 注册时语言环境
     */
    @TableField("register_language")
    private String registerLanguage;

}
