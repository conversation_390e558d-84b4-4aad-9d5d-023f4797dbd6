package com.dexpo.module.audience.infrastructure.convert;

import com.dexpo.framework.cache.redis.entity.audience.AudienceParticipateRecordCache;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.dexpo.module.audience.domain.model.AudienceParticipateRecord;
import com.dexpo.module.audience.infrastructure.tunnel.dataobject.AudienceParticipateRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
*@Author: <PERSON><PERSON>e
*@CreateTime: 2025-06-23
*@Description: 
*/

@Mapper
public interface AudienceParticipateRecordDOConvert {
    AudienceParticipateRecordDOConvert INSTANCE = Mappers.getMapper(AudienceParticipateRecordDOConvert.class);

    @Mappings({})
    public AudienceParticipateRecordDO convert(AudienceParticipateRecord audienceParticipateRecord);

    @Mappings({})
    public AudienceParticipateRecord convertToEntity(AudienceParticipateRecordDO audienceParticipateRecordDO);

    @Mappings({})
    AudienceParticipateRecordCache entityToCache(AudienceParticipateRecord audienceParticipateRecord);

    @Mappings({})
    AudienceParticipateRecord cacheToEntity(AudienceParticipateRecordCache audienceParticipateRecordCache);


}
