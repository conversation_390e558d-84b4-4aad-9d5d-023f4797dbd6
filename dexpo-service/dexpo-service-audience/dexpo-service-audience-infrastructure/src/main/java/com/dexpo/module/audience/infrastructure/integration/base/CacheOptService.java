package com.dexpo.module.audience.infrastructure.integration.base;

import com.dexpo.framework.cache.redis.entity.audience.AudienceBaseInfoCache;
import com.dexpo.framework.cache.redis.entity.audience.AudienceInvestigationCache;
import com.dexpo.framework.cache.redis.entity.exhibition.ExhibitionInfoCache;
import com.dexpo.framework.cache.redis.operate.audience.AudienceCacheOpt;
import com.dexpo.framework.cache.redis.operate.base.ValidCodeOpt;
import com.dexpo.framework.cache.redis.operate.exhibition.ExhibitionInfoCacheOpt;
import com.dexpo.framework.cache.redis.operate.member.MemberBaseInfoOpt;
import com.dexpo.framework.security.core.LoginUser;
import com.dexpo.framework.security.core.service.TokenService;
import com.dexpo.module.audience.domain.model.AudienceParticipateRecord;
import com.dexpo.module.audience.infrastructure.convert.AudienceParticipateRecordDOConvert;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: Maojie
 * @CreateTime: 2025-06-23
 * @Description:
 */

@Component
@RequiredArgsConstructor
public class CacheOptService {

    @Resource
    private ValidCodeOpt validCodeOpt;

    @Resource
    private MemberBaseInfoOpt memberBaseInfoOpt;

    @Resource
    private TokenService tokenService;

    @Resource
    private AudienceCacheOpt audienceCacheOpt;

    @Resource
    private ExhibitionInfoCacheOpt exhibitionInfoCacheOpt;

    public boolean validCode(String text, String code) {
        return validCodeOpt.validCode(text, code);
    }

    public AudienceBaseInfoCache getAudienceBaseInfoCache(String loginTool) {
        return memberBaseInfoOpt.getAudienceBaseInfoCache(loginTool);
    }

    public void setAudienceBaseInfoCache(AudienceBaseInfoCache baseInfo, String loginTool) {
        memberBaseInfoOpt.setAudienceBaseInfoCache(baseInfo, loginTool);
    }

    public String getToken(LoginUser loginUser){
        return tokenService.createToken(loginUser);
    }

    public void setAudienceParticipateRecordCache(AudienceParticipateRecord participateRecord){
        audienceCacheOpt.setAudienceParticipateRecordCache(AudienceParticipateRecordDOConvert.INSTANCE.entityToCache(participateRecord));
    }

    public AudienceParticipateRecord getAudienceParticipateRecordCache(Long audienceId, Long exhibitionId){
        return  AudienceParticipateRecordDOConvert.INSTANCE.cacheToEntity(audienceCacheOpt.getAudienceParticipateRecordCache(audienceId, exhibitionId));
    }

    public void setAudienceSignRecordCache( Long audienceId, Long agreementId){
        audienceCacheOpt.setAudienceSignRecordCache(audienceId, agreementId);
    }

    public Long getAudienceSignRecordCache(Long audienceId, Long agreementId){
        return audienceCacheOpt.getAudienceSignRecordCache(audienceId, agreementId);
    }


    public ExhibitionInfoCache getExhibitionInfoCacheById(Long exhibitionId){
        return exhibitionInfoCacheOpt.getExhibitionInfoCacheById(exhibitionId);
    }


    public List<AudienceInvestigationCache> getAudienceInvestigationCache(Long audienceId, Long exhibitionId){
        return audienceCacheOpt.getAudienceInvestigationCache(audienceId, exhibitionId);
    }

    public void setAudienceInvestigationCache(List<AudienceInvestigationCache> cacheList, Long audienceId, Long exhibitionId){
        audienceCacheOpt.setAudienceInvestigationCache(cacheList, audienceId, exhibitionId);
    }


}
