package com.dexpo.module.audience.infrastructure.convert;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.dexpo.module.audience.domain.model.AudienceInvestigationInfo;
import com.dexpo.module.audience.infrastructure.tunnel.dataobject.AudienceInvestigationInfoDO;

@Mapper
public interface AudienceInvestigationInfoDOConvert {

    AudienceInvestigationInfoDOConvert INSTANCE = Mappers.getMapper(AudienceInvestigationInfoDOConvert.class);

    AudienceInvestigationInfo convertToEntity(AudienceInvestigationInfoDO audienceInvestigationInfoDO);

    List<AudienceInvestigationInfo> convertToEntityList(
            List<AudienceInvestigationInfoDO> audienceInvestigationInfoDOList);
}
