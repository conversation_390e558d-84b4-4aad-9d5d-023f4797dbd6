package com.dexpo.module.audience.infrastructure.repository;

import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dexpo.framework.common.enums.ValueSetParticipateStatusEnum;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.audience.domain.model.AudienceBaseInfo;
import com.dexpo.module.audience.domain.model.AudiencePageList;
import com.dexpo.module.audience.domain.model.AudiencePageListQuery;
import com.dexpo.module.audience.domain.repository.AudienceBaseInfoRepository;
import com.dexpo.module.audience.infrastructure.convert.AudienceBaseInfoDOConvert;
import com.dexpo.module.audience.infrastructure.tunnel.dataobject.AudienceBaseInfoDO;
import com.dexpo.module.audience.infrastructure.tunnel.mysql.AudienceBaseInfoMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;

import jakarta.annotation.Resource;

/**
 * @Author: Maojie
 * @CreateTime: 2025-06-23
 * @Description:
 */

@Component
public class AudienceBaseInfoRepositoryImpl implements AudienceBaseInfoRepository {
    @Resource
    private AudienceBaseInfoMapper baseInfoMapper;

    @Override
    public PageResult<AudiencePageList> getAudiencePage(AudiencePageListQuery queryDO) {
        Page<AudiencePageList> objects = PageMethod.startPage(queryDO.getPageNo(), queryDO.getPageSize());
        baseInfoMapper.getAudienceList(queryDO, ValueSetParticipateStatusEnum.ACTIVATED.getOptionCode()
                , ValueSetParticipateStatusEnum.NOT_ACTIVATED.getOptionCode());
        return new PageResult<>(objects, objects.getTotal());
    }

    @Override
    public AudienceBaseInfo selectById(Long id) {
        AudienceBaseInfoDO audienceBaseInfoDO = baseInfoMapper.selectById(id);
        return AudienceBaseInfoDOConvert.INSTANCE.convertToEntity(audienceBaseInfoDO);
    }


    @Override
    public AudienceBaseInfo save(AudienceBaseInfo audienceBaseInfo) {
        AudienceBaseInfoDO audienceBaseInfoDO = AudienceBaseInfoDOConvert.INSTANCE.convert(audienceBaseInfo);
        baseInfoMapper.insert(audienceBaseInfoDO);
        audienceBaseInfo.setId(audienceBaseInfoDO.getId());
        return audienceBaseInfo;
    }

    @Override
    public AudienceBaseInfo getAudienceBaseInfoByMobileOrEmail(String loginTool) {
        LambdaQueryWrapper<AudienceBaseInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(wrapper -> wrapper
                        .eq(AudienceBaseInfoDO::getAudienceMobile, loginTool)
                        .or()
                        .eq(AudienceBaseInfoDO::getAudienceEmail, loginTool)
                )
                .and(wrapper -> wrapper.eq(AudienceBaseInfoDO::getDelFlg, Boolean.FALSE));

        return AudienceBaseInfoDOConvert.INSTANCE.convertToEntity(baseInfoMapper.selectOne(queryWrapper));
    }
}
