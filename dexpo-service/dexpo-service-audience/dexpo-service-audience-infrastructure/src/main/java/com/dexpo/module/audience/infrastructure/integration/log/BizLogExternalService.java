package com.dexpo.module.audience.infrastructure.integration.log;

import com.dexpo.framework.common.core.BaseExternal;
import com.dexpo.module.log.api.BizOperateLogApi;
import com.dexpo.module.log.api.dto.BizOperateLogQueryDTO;
import com.dexpo.module.log.api.vo.BizOperateLogVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class BizLogExternalService extends BaseExternal {

    private final BizOperateLogApi bizOperateLogApi;


    public List<BizOperateLogVO> listOperateLog(Long businessId, String businessType) {
        BizOperateLogQueryDTO queryDTO = new BizOperateLogQueryDTO();
        queryDTO.setBusinessId(businessId);
        queryDTO.setBusinessType(businessType);
        return getResult(bizOperateLogApi.list(queryDTO));
    }
}
