package com.dexpo.module.audience.infrastructure.mq.producer;

import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseAudienceSyncMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class DataWarehouseChannelProducer {

    @Resource
    private StreamBridge streamBridge;

    public void dataWarehouseAudienceSyncChannel(DataWarehouseAudienceSyncMessage content) {
        log.info("dataWarehouseAudienceSyncChannel: {}", content);
        streamBridge.send("dataWarehouseAudienceSyncChannel-out-0", content);
        log.info("success");
    }
} 