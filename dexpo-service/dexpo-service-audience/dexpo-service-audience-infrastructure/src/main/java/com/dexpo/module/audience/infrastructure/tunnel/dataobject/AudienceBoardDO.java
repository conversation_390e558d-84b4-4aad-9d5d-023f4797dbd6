package com.dexpo.module.audience.infrastructure.tunnel.dataobject;


import lombok.Data;

@Data
public class AudienceBoardDO {
    /**
     * 参与记录ID
     */
    private Long participateRecordId;
    /**
     * 观众ID
     */
    private Long audienceId;
    /**
     * 观众类型
     */
    private String audienceType;
    /**
     * 展会ID
     */
    private Long exhibitionId;
    /**
     * 注册方式
     */
    private String registerMethod;
    /**
     * 国家代码
     */
    private String countryCode;
    /**
     * 观众性别
     */
    private String audienceGender;
}
