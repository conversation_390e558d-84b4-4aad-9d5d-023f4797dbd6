package com.dexpo.module.audience.infrastructure.repository;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dexpo.framework.common.Constants;
import com.dexpo.module.audience.infrastructure.integration.base.CacheOptService;
import com.dexpo.module.audience.infrastructure.tunnel.dataobject.AudienceBaseInfoDO;
import org.springframework.stereotype.Component;

import com.dexpo.framework.common.enums.ValueSetParticipateStatusEnum;
import com.dexpo.module.audience.domain.model.AudienceParticipateRecord;
import com.dexpo.module.audience.domain.repository.AudienceParticipateRecordRepository;
import com.dexpo.module.audience.infrastructure.convert.AudienceParticipateRecordDOConvert;
import com.dexpo.module.audience.infrastructure.tunnel.dataobject.AudienceParticipateRecordDO;
import com.dexpo.module.audience.infrastructure.tunnel.mysql.AudienceParticipateRecordMapper;

import jakarta.annotation.Resource;

/**
 * @Author: <PERSON><PERSON>e
 * @CreateTime: 2025-06-23
 * @Description:
 */

@Component
public class AudienceParticipateRecordRepositoryImpl implements AudienceParticipateRecordRepository {

    @Resource
    private AudienceParticipateRecordMapper audienceParticipateRecordMapper;

    @Resource
    private CacheOptService cacheOptService;

    @Override
    public Long save(AudienceParticipateRecord audienceParticipateRecord) {
        AudienceParticipateRecordDO recordDO = AudienceParticipateRecordDOConvert.INSTANCE
                .convert(audienceParticipateRecord);
        audienceParticipateRecordMapper.insertOrUpdate(recordDO);
        audienceParticipateRecord.setId(recordDO.getId());
        return recordDO.getId();
    }

    @Override
    public List<AudienceParticipateRecord> selectByIds(List<Long> ids) {
        List<AudienceParticipateRecordDO> audienceParticipateRecords = audienceParticipateRecordMapper.selectByIds(ids);
        return audienceParticipateRecords.stream().map(AudienceParticipateRecordDOConvert.INSTANCE::convertToEntity)
                .toList();
    }

    @Override
    public AudienceParticipateRecord selectById(Long id) {
        AudienceParticipateRecordDO recordDO = audienceParticipateRecordMapper.selectById(id);
        return AudienceParticipateRecordDOConvert.INSTANCE.convertToEntity(recordDO);
    }

    @Override
    public void activeByIds(List<Long> ids) {
        audienceParticipateRecordMapper.activeByIds(ids, ValueSetParticipateStatusEnum.ACTIVATED.getOptionCode(),
                ValueSetParticipateStatusEnum.NOT_ACTIVATED.getOptionCode());
    }

    @Override
    public AudienceParticipateRecord getInfoFromCache(Long audienceId, Long exhibitionId) {
        return cacheOptService.getAudienceParticipateRecordCache(audienceId, exhibitionId);
    }

    @Override
    public void setInfoToCache(AudienceParticipateRecord participateRecord) {
            cacheOptService.setAudienceParticipateRecordCache(participateRecord);
    }

    @Override
    public AudienceParticipateRecord getByAudienceIdAndExhibitionId(Long audienceId, Long exhibitionId) {
        LambdaQueryWrapper<AudienceParticipateRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AudienceParticipateRecordDO::getAudienceId, audienceId)
                .eq(AudienceParticipateRecordDO::getExhibitionId, exhibitionId)
                .eq(AudienceParticipateRecordDO::getDelFlg, Boolean.FALSE).last(Constants.LIMIT_ONE);
        return AudienceParticipateRecordDOConvert.INSTANCE.convertToEntity(audienceParticipateRecordMapper.selectOne(queryWrapper));
    }
}
