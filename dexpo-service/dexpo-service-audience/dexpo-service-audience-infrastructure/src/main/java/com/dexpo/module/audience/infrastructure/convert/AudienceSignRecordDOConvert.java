package com.dexpo.module.audience.infrastructure.convert;

import com.dexpo.module.audience.domain.model.AudienceSignRecord;
import com.dexpo.module.audience.infrastructure.tunnel.dataobject.AudienceSignRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: <PERSON><PERSON><PERSON>
 * @CreateTime: 2025-06-24
 * @Description:
 */

@Mapper
public interface AudienceSignRecordDOConvert {

    AudienceSignRecordDOConvert INSTANCE = Mappers.getMapper(AudienceSignRecordDOConvert.class);

    AudienceSignRecord convertToEntity(AudienceSignRecordDO audienceSignRecordDO);

    AudienceSignRecordDO convertToDO(AudienceSignRecord audienceSignRecord);
}
