package com.dexpo.module.audience.infrastructure.repository;

import com.dexpo.module.audience.domain.model.AudienceSignRecord;
import com.dexpo.module.audience.domain.repository.AudienceSignRecordRepository;
import com.dexpo.module.audience.infrastructure.convert.AudienceSignRecordDOConvert;
import com.dexpo.module.audience.infrastructure.integration.base.CacheOptService;
import com.dexpo.module.audience.infrastructure.tunnel.dataobject.AudienceSignRecordDO;
import com.dexpo.module.audience.infrastructure.tunnel.mysql.AudienceSignRecordMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @Author: <PERSON><PERSON>e
 * @CreateTime: 2025-06-24
 * @Description:
 */

@Component
public class AudienceSignRecordRepositoryImpl implements AudienceSignRecordRepository {

    @Resource
    private CacheOptService cacheOptService;

    @Resource
    private AudienceSignRecordMapper audienceSignRecordMapper;

    @Override
    public Long getInfoFromCache(Long audienceId, Long exhibitionId) {
        return cacheOptService.getAudienceSignRecordCache(audienceId, exhibitionId);
    }

    @Override
    public void setInfoToCache(Long audienceId, Long exhibitionId) {
        cacheOptService.setAudienceSignRecordCache(audienceId, exhibitionId);
    }

    @Override
    public Long save(AudienceSignRecord audienceSignRecord) {
        AudienceSignRecordDO recordDO = AudienceSignRecordDOConvert.INSTANCE.convertToDO(audienceSignRecord);
        audienceSignRecordMapper.insert(recordDO);
        return recordDO.getId();
    }
}
