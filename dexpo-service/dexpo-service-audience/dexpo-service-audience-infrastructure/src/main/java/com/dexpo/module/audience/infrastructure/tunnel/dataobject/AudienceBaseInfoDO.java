package com.dexpo.module.audience.infrastructure.tunnel.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.dexpo.framework.common.pojo.PageParam;

/**
 * 会员基本信息数据对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("audience_base_info")
public class AudienceBaseInfoDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 观众编码
     */
    @TableField("audience_code")
    private String audienceCode;

    /**
     * 观众姓名
     */
    @TableField("audience_name")
    private String audienceName;

    /**
     * 观众英文名
     */
    @TableField("audience_first_name")
    private String audienceFirstName;

    /**
     * 观众英文姓
     */
    @TableField("audience_last_name")
    private String audienceLastName;

    /**
     * 观众性别
     */
    @TableField("audience_gender")
    private String audienceGender;

    /**
     * 观众手机号
     */
    @TableField("audience_mobile")
    private String audienceMobile;

    /**
     * 观众邮箱
     */
    @TableField("audience_email")
    private String audienceEmail;

    /**
     * 证件类型
     */
    @TableField("id_category")
    private String idCategory;

    /**
     * 证件号码
     */
    @TableField("id_number")
    private String idNumber;

    /**
     * 国家CODE
     */
    @TableField("country_code")
    private String countryCode;

    /**
     * 国家名称-中文
     */
    @TableField("country_name_cn")
    private String countryNameCn;

    /**
     * 国家名称-英文
     */
    @TableField("country_name_en")
    private String countryNameEn;

    /**
     * 居住地所在省CODE
     */
    @TableField("current_home_province_code")
    private String currentHomeProvinceCode;

    /**
     * 居住地所在省名称
     */
    @TableField("current_home_province_name")
    private String currentHomeProvinceName;

    /**
     * 居住地所在市CODE
     */
    @TableField("current_home_city_code")
    private String currentHomeCityCode;

    /**
     * 居住地所在市名称
     */
    @TableField("current_home_city_name")
    private String currentHomeCityName;

    /**
     * 居住地所在区CODE
     */
    @TableField("current_home_district_code")
    private String currentHomeDistrictCode;

    /**
     * 居住地所在区名称
     */
    @TableField("current_home_district_name")
    private String currentHomeDistrictName;

    /**
     * 居住地详细地址
     */
    @TableField("current_home_detail_address")
    private String currentHomeDetailAddress;

    /**
     * 单位名称
     */
    @TableField("enterprise_name")
    private String enterpriseName;

    /**
     * 部门名称
     */
    @TableField("department_name")
    private String departmentName;

    /**
     * 职位名称
     */
    @TableField("position_name")
    private String positionName;
    

   
} 