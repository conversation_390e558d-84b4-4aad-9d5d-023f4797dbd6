package com.dexpo.module.audience.infrastructure.repository;

import java.util.List;

import com.dexpo.module.audience.infrastructure.convert.AudienceInvestigationInfoDOConvert;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dexpo.module.audience.domain.model.AudienceInvestigationInfo;
import com.dexpo.module.audience.domain.repository.AudienceInvestigationInfoRepository;
import com.dexpo.module.audience.infrastructure.tunnel.dataobject.AudienceInvestigationInfoDO;
import com.dexpo.module.audience.infrastructure.tunnel.mysql.AudienceInvestigationInfoMapper;

import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class AudienceInvestigationInfoRepositoryImpl implements AudienceInvestigationInfoRepository {

    private final AudienceInvestigationInfoMapper audienceInvestigationInfoMapper;

    @Override
    public List<AudienceInvestigationInfo> selectByExhibitionIdAndAudienceId(Long exhibitionId, Long audienceId) {

        LambdaQueryWrapper<AudienceInvestigationInfoDO> lambdaQuery = new LambdaQueryWrapper<>();
        lambdaQuery.eq(AudienceInvestigationInfoDO::getAudienceId, audienceId)
                .eq(AudienceInvestigationInfoDO::getExhibitionId, exhibitionId);

        List<AudienceInvestigationInfoDO> investigationInfos = audienceInvestigationInfoMapper.selectList(lambdaQuery);

        return AudienceInvestigationInfoDOConvert.INSTANCE.convertToEntityList(investigationInfos);
    }
}
