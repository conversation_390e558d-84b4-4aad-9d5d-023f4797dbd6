package com.dexpo.module.audience.infrastructure.repository;

import java.util.List;

import com.dexpo.module.audience.infrastructure.convert.AudienceInvestigationInfoDOConvert;
import com.dexpo.module.audience.infrastructure.integration.base.CacheOptService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dexpo.module.audience.domain.model.AudienceInvestigationInfo;
import com.dexpo.module.audience.domain.repository.AudienceInvestigationInfoRepository;
import com.dexpo.module.audience.infrastructure.tunnel.dataobject.AudienceInvestigationInfoDO;
import com.dexpo.module.audience.infrastructure.tunnel.mysql.AudienceInvestigationInfoMapper;

import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class AudienceInvestigationInfoRepositoryImpl implements AudienceInvestigationInfoRepository {

    private final AudienceInvestigationInfoMapper audienceInvestigationInfoMapper;

    @Resource
    private CacheOptService cacheOptService;

    @Override
    public List<AudienceInvestigationInfo> selectByExhibitionIdAndAudienceId(Long exhibitionId, Long audienceId) {

        LambdaQueryWrapper<AudienceInvestigationInfoDO> lambdaQuery = new LambdaQueryWrapper<>();
        lambdaQuery.eq(AudienceInvestigationInfoDO::getAudienceId, audienceId)
                .eq(AudienceInvestigationInfoDO::getExhibitionId, exhibitionId)
                .eq(AudienceInvestigationInfoDO::getDelFlg, Boolean.FALSE);
        List<AudienceInvestigationInfoDO> investigationInfos = audienceInvestigationInfoMapper.selectList(lambdaQuery);
        return AudienceInvestigationInfoDOConvert.INSTANCE.convertToEntityList(investigationInfos);
    }

    @Override
    public void deleteByIds(List<Long> ids) {
        audienceInvestigationInfoMapper.deleteByIds(ids);
    }

    @Override
    public List<AudienceInvestigationInfo> saveOrUpdateBach(List<AudienceInvestigationInfo> audienceInvestigationInfos) {
        List<AudienceInvestigationInfoDO> investigationInfoDOs = AudienceInvestigationInfoDOConvert.INSTANCE.entityListConvertToDOList(audienceInvestigationInfos);
        audienceInvestigationInfoMapper.saveOrUpdateBatch(investigationInfoDOs);
        return AudienceInvestigationInfoDOConvert.INSTANCE.convertToEntityList(investigationInfoDOs);
    }

    @Override
    public List<AudienceInvestigationInfo> getInfoFromCache(Long audienceId, Long exhibitionId) {
        return AudienceInvestigationInfoDOConvert.INSTANCE.cacheListConvertToEntities(cacheOptService.getAudienceInvestigationCache(audienceId,exhibitionId));
    }


}
