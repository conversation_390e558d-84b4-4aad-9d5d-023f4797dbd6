package com.dexpo.module.audience.infrastructure.tunnel.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("audience_investigation_info")
public class AudienceInvestigationInfoDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 观众ID
     */
    @TableField("audience_id")
    private Long audienceId;

    /**
     * 展会ID
     */
    @TableField("exhibition_id")
    private Long exhibitionId;

    /**
     * 问卷ID
     */
    @TableField("investigation_id")
    private Long investigationId;

    /**
     * 题目编码
     */
    @TableField("question_code")
    private String questionCode;

    /**
     * 选择型答案
     */
    @TableField("answer_choose_code")
    private String answerChooseCode;

    /**
     * 输入型答案
     */
    @TableField("answer_input_text")
    private String answerInputText;
}
