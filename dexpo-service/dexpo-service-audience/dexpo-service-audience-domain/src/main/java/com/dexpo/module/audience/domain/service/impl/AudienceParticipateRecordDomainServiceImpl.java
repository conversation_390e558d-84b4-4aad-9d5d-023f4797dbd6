package com.dexpo.module.audience.domain.service.impl;

import com.dexpo.module.audience.domain.model.AudienceParticipateRecord;
import com.dexpo.module.audience.domain.repository.AudienceParticipateRecordRepository;
import com.dexpo.module.audience.domain.service.AudienceParticipateRecordDomainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

/**
 * @Author: Maojie
 * @CreateTime: 2025-06-21
 * @Description:
 */

@Service
@Slf4j
public class AudienceParticipateRecordDomainServiceImpl implements AudienceParticipateRecordDomainService {

    @Resource
    private AudienceParticipateRecordRepository repository;
    @Override
    public Long save(AudienceParticipateRecord participateRecord) {
        return repository.save(participateRecord);
    }

    @Override
    public AudienceParticipateRecord saveIfNotExist(Long audienceId, Long exhibitionId) {
        AudienceParticipateRecord participateRecord = repository.getInfoFromCache(audienceId, exhibitionId);
        if(participateRecord == null){
             participateRecord = AudienceParticipateRecord.initAudienceParticipateRecord(audienceId, exhibitionId);
            //这里要通过audienceId和exhibitionId的联合唯一索引异常来识别redis没有但数据库有数据的情况
            try {
                repository.save(participateRecord);
            }catch (DuplicateKeyException e){
                log.info("AudienceParticipateRecordDomainServiceImpl saveIfNotExist duplicate key"+e);
            }
            repository.setInfoToCache(participateRecord);
        }
        return participateRecord;
    }

    @Override
    public AudienceParticipateRecord getByAudienceIdAndExhibitionId(Long audienceId, Long exhibitionId) {
        return repository.getByAudienceIdAndExhibitionId(audienceId, exhibitionId);
    }
}
