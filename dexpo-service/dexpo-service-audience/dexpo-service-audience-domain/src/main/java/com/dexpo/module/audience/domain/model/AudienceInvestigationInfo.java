package com.dexpo.module.audience.domain.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
public class AudienceInvestigationInfo{

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 观众ID
     */
    private Long audienceId;

    /**
     * 展会ID
     */
    private Long exhibitionId;

    /**
     * 问卷ID
     */
    private Long investigationId;

    /**
     * 题目编码
     */
    private String questionCode;

    /**
     * 选择型答案
     */
    private String answerChooseCode;

    /**
     * 输入型答案
     */
    private String answerInputText;
}
