package com.dexpo.module.audience.domain.model;


import cn.hutool.core.lang.UUID;
import com.dexpo.framework.common.enums.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AudienceParticipateRecord  {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 观众ID
     */
    private Long audienceId;

    /**
     * 观众类型
     */
    private String audienceType;

    /**
     * 展会ID
     */
    private Long exhibitionId;

    /**
     * 唯一编码
     */
    private String participateCode;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 参展状态 值集VS_AUDIENCE_PARTICIPATE_STATUS
     */
    private String participateStatus;

    /**
     * 参展状态 名称
     */
    private String participateStatusName;

    /**
     * 注册系统
     */
    private String registerSystem;

    /**
     * 注册方式
     */
    private String registerMethod;

    /**
     * 注册来源
     */
    private String registerSource;

    /**
     * 注册时语言环境
     */
    private String registerLanguage;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建者，目前使用 SysUser 的 id 编号
     *
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String createUserName;

    private Long createUser;
    /**
     * 更新者，目前使用 SysUser 的 id 编号
     *
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String updateUserName;

    private Long updateUser;
    /**
     * 是否删除
     */
    private Boolean delFlg;

    public static AudienceParticipateRecord initAudienceParticipateRecord(Long audienceId,Long exhibitionId){
        AudienceParticipateRecord participateRecord = new AudienceParticipateRecord();
        participateRecord.setParticipateCode(UUID.randomUUID().toString().replace("-",""));
        participateRecord.setRegisterTime(LocalDateTime.now());
        participateRecord.setRegisterSource(RegisterSourceEnum.NORMAL.getCode());
        participateRecord.setRegisterMethod(RegisterMethodEnum.SELF_REGISTER.getCode());
        participateRecord.setRegisterLanguage(RegisterLanguageEnum.CHINESE.getCode());
        participateRecord.setRegisterSystem(RegisterSystemEnum.CENTRAL_PLATFORM.getCode());
        participateRecord.setAudienceType(AudienceTypeEnum.INDIVIDUAL.getCode());
        participateRecord.setAudienceId(audienceId);
        participateRecord.setExhibitionId(exhibitionId);
        participateRecord.setParticipateStatus(AudienceParticipateStatusEnum.NOT_PURCHASED.getCode());
        participateRecord.setParticipateStatusName(AudienceParticipateStatusEnum.NOT_PURCHASED.getDescriptionCN());
        participateRecord.setCreateUser(audienceId);
        participateRecord.setCreateTime(LocalDateTime.now());
        participateRecord.setUpdateUser(audienceId);
        participateRecord.setUpdateTime(LocalDateTime.now());
        participateRecord.setDelFlg(Boolean.FALSE);
        return participateRecord;




    }

}
