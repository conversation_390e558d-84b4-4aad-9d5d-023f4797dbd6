package com.dexpo.module.audience.domain.repository;

import java.util.List;

import com.dexpo.module.audience.domain.model.AudienceParticipateRecord;

/**
 * @Author: <PERSON><PERSON><PERSON>
 * @CreateTime: 2025-06-23
 * @Description:
 */
public interface AudienceParticipateRecordRepository {

    Long save(AudienceParticipateRecord audienceParticipateRecord);

    /**
     * 根据id列表查询观众参展记录
     * @param ids
     * @return
     */
    List<AudienceParticipateRecord> selectByIds(List<Long> ids);



    /**
     * 根据id查询观众参展记录
     * @param id
     * @return
     */
    AudienceParticipateRecord selectById(Long id); 

    /**
     * 根据id列表更新观众参展记录
     * @param ids
     * @param participateStatus
     */
    void activeByIds(List<Long> ids);

    AudienceParticipateRecord getInfoFromCache(Long audienceId,Long exhibitionId);

    void setInfoToCache(AudienceParticipateRecord participateRecord);

    AudienceParticipateRecord getByAudienceIdAndExhibitionId(Long audienceId,Long exhibitionId);

}
