package com.dexpo.module.audience.domain.service.impl;

import com.dexpo.framework.common.enums.AudienceParticipateStatusEnum;
import com.dexpo.framework.common.enums.AudienceRegisterStatusEnum;
import com.dexpo.module.audience.domain.model.AudienceBaseInfo;
import com.dexpo.module.audience.domain.repository.AudienceBaseInfoRepository;
import com.dexpo.module.audience.domain.service.AudienceBaseInfoDomainService;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: Maojie
 * @CreateTime: 2025-06-21
 * @Description:
 */

@RequiredArgsConstructor
@Service
@Slf4j
public class AudienceBaseInfoDomainServiceImpl implements AudienceBaseInfoDomainService {

    @Resource
    private AudienceBaseInfoRepository audienceBaseInfoRepository;

    @Override
    public AudienceBaseInfo save(AudienceBaseInfo audienceBaseInfo) {
        return audienceBaseInfoRepository.save(audienceBaseInfo);
    }

    @Override
    public AudienceBaseInfo getAudienceBaseInfoByMobileOrEmail(String loginTool) {
        return audienceBaseInfoRepository.getAudienceBaseInfoByMobileOrEmail(loginTool);
    }

    @Override
    public AudienceBaseInfo getById(Long id) {
        return audienceBaseInfoRepository.selectById(id);
    }

    @Override
    public AudienceBaseInfo getAudienceBaseInfoByIdOrCache(String loginTool, Long id) {
        if(StringUtils.isNotBlank(loginTool)){
            AudienceBaseInfo audienceBaseInfo = audienceBaseInfoRepository.getAudienceBaseInfoByMobileOrEmail(loginTool);
            if(audienceBaseInfo != null){
                return audienceBaseInfo;
            }
        }
        return audienceBaseInfoRepository.selectById(id);
    }

    @Override
    public Long update(AudienceBaseInfo audienceBaseInfo, String registerLanguage) {
        audienceBaseInfo.buildAudienceName(registerLanguage);
        return audienceBaseInfoRepository.update(audienceBaseInfo);
    }

    @Override
    public List<AudienceBaseInfo> queryByPage(Integer pageNo, Integer pageSize) {
        return audienceBaseInfoRepository.queryByPage(pageNo, pageSize);
    }

    @Override
    public AudienceBaseInfo saveOrUpdate(AudienceBaseInfo audienceBaseInfo, String registerLanguage) {
        audienceBaseInfo.buildAudienceName(registerLanguage);
        return null;
    }
}
