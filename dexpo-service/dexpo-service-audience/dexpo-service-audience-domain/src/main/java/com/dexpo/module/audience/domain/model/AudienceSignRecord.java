package com.dexpo.module.audience.domain.model;


import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AudienceSignRecord  {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 观众ID
     */
    private Long audienceId;
    
    /**
     * 协议ID
     */
    private Long agreementId;
    
    /**
     * 签署时间
     */
    private LocalDateTime signTime;
    
    /**
     * 是否有效
     */
    private Boolean isEffect;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建者，目前使用 SysUser 的 id 编号
     *
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String createUserName;

    private Long createUser;
    /**
     * 更新者，目前使用 SysUser 的 id 编号
     *
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String updateUserName;

    private Long updateUser;
    /**
     * 是否删除
     */
    private Boolean delFlg;


    public static AudienceSignRecord initAudienceSignRecord(Long audienceId, Long agreementId) {
        AudienceSignRecord record = new AudienceSignRecord();
        record.setAudienceId(audienceId);
        record.setAgreementId(agreementId);
        record.setSignTime(LocalDateTime.now());
        record.setIsEffect(true);
        record.setCreateUser(audienceId);
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateUser(audienceId);
        record.setUpdateTime(LocalDateTime.now());
        record.setDelFlg(Boolean.FALSE);
        return record;
    }
    
    
    
    




}
