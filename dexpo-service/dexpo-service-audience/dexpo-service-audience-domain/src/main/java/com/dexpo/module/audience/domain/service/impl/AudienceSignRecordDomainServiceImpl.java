package com.dexpo.module.audience.domain.service.impl;

import com.dexpo.module.audience.domain.model.AudienceSignRecord;
import com.dexpo.module.audience.domain.repository.AudienceSignRecordRepository;
import com.dexpo.module.audience.domain.service.AudienceSignRecordDomainService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * @Author: <PERSON><PERSON><PERSON>
 * @CreateTime: 2025-06-24
 * @Description:
 */


@Service
public class AudienceSignRecordDomainServiceImpl implements AudienceSignRecordDomainService {

    @Resource
    private AudienceSignRecordRepository audienceSignRecordRepository;

    @Override
    public void saveIfNotExist(Long audienceId, Long exhibitionId) {
        Long cacheId = audienceSignRecordRepository.getInfoFromCache(audienceId, exhibitionId);
        if(cacheId == null){
            AudienceSignRecord signRecord = AudienceSignRecord.initAudienceSignRecord(audienceId, exhibitionId);
            audienceSignRecordRepository.save(signRecord);
            audienceSignRecordRepository.setInfoToCache(audienceId, exhibitionId);
        }
    }
}
