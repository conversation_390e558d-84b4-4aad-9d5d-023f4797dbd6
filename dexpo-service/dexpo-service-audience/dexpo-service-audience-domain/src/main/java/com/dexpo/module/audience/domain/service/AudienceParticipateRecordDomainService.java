package com.dexpo.module.audience.domain.service;

import com.dexpo.module.audience.domain.model.AudienceParticipateRecord;

/**
 * @Author: <PERSON><PERSON><PERSON>
 * @CreateTime: 2025-06-21
 * @Description:
 */


public interface AudienceParticipateRecordDomainService {

    Long save(AudienceParticipateRecord participateRecord);


    AudienceParticipateRecord saveIfNotExist(Long audienceId,Long exhibitionId);

    AudienceParticipateRecord getByAudienceIdAndExhibitionId(Long audienceId,Long exhibitionId);


}
