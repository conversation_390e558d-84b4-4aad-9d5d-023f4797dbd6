package com.dexpo.module.audience.domain.repository;

import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.audience.domain.model.AudienceBaseInfo;
import com.dexpo.module.audience.domain.model.AudiencePageList;
import com.dexpo.module.audience.domain.model.AudiencePageListQuery;
import com.dexpo.module.audience.domain.model.AudienceParticipateRecord;

import java.util.List;

/**
 * @Author: Maojie
 * @CreateTime: 2025-06-23
 * @Description:
 */
public interface AudienceBaseInfoRepository {

    /**
     * 观众用户分页查询
     *
     * @param queryDTO
     * @return
     */
    PageResult<AudiencePageList> getAudiencePage(AudiencePageListQuery queryDTO);
    
    /**
     * 获取基础信息
     * @param id
     * @return
     */
    AudienceBaseInfo selectById(Long id);


    AudienceBaseInfo save(AudienceBaseInfo audienceBaseInfo);

    AudienceBaseInfo getAudienceBaseInfoByMobileOrEmail(String loginTool);

    Long update(AudienceBaseInfo audienceBaseInfo);

    public List<AudienceBaseInfo> queryByPage(Integer pageNo, Integer pageSize);

    AudienceBaseInfo saveOrUpdate(AudienceBaseInfo audienceBaseInfo);


}
