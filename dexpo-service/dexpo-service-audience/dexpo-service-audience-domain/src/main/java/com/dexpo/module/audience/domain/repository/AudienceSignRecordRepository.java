package com.dexpo.module.audience.domain.repository;

import com.dexpo.module.audience.domain.model.AudienceSignRecord;

/**
 * @Author: <PERSON><PERSON><PERSON>
 * @CreateTime: 2025-06-24
 * @Description:
 */
public interface AudienceSignRecordRepository {


    Long getInfoFromCache(Long audienceId,Long exhibitionId);

    void setInfoToCache(Long audienceId,Long exhibitionId);

    Long save(AudienceSignRecord audienceSignRecord);

}
