package com.dexpo.module.audience.api;


import com.dexpo.module.audience.api.dto.audience.AudienceBoardDTO;
import com.dexpo.module.audience.api.dto.audience.AudiencePageQueryDTO;
import com.dexpo.module.audience.api.enums.ApiConstants;
import com.dexpo.module.audience.api.vo.audience.AudienceBoardVO;
import com.dexpo.module.audience.api.vo.audience.AudiencePageListVO;
import com.dexpo.module.audience.api.vo.audience.AudienceRegisterExhibitionVO;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 普通观众后端业务服务")
public interface AudienceBizApi {

    String PREFIX = "/biz/audience";

    /**
     * 分页获取媒体列表
     */
    @PostMapping(PREFIX + "/page")
    @Operation(summary = "分页获取观众分页列表")
    CommonResult<PageResult<AudiencePageListVO>> getAudiencePageList(@Valid @RequestBody AudiencePageQueryDTO queryDTO);


    /**
     * 分页获取媒体列表
     */
    @PostMapping(PREFIX + "/active")
    @Operation(summary = "普通观众用户激活")
    CommonResult<Boolean> activeAudience(@Valid @NotEmpty @RequestBody List<Long> ids);

    @GetMapping(PREFIX + "/registerDetail")
    @Operation(summary = "获取观众注册展会信息")
    CommonResult<AudienceRegisterExhibitionVO> getAudienceDetail(@Valid @NotNull @RequestParam("id") Long id);

    /**
     * 观众看板数据
     */
    @PostMapping(PREFIX+ "/board")
    @Operation(summary = "观众看板数据")
    CommonResult<AudienceBoardVO> getAudienceBoard(@Valid @RequestBody AudienceBoardDTO queryDTO);

}
