package com.dexpo.module.audience.api.vo.audience;

import lombok.Data;

import java.util.List;

/**
 * 服务中台 观众看板VO
 */
@Data
public class AudienceBoardVO {

    /**
     * 展会ID
     */
    private Long exhibitionId;

    /**
     * 展会年份（届）
     */
    private List<String> exhibitionSessionKeys;

    /**
     * 境内观众注册总数
     */
    private Long totalNumberRegisteredDomesticVisitors;

    /**
     * 境外观众注册总数
     */
    private Long totalNumberRegisteredOverseasVisitors;

    /**
     * 男性数量
     */
    private Long maleCount;

    /**
     * 男性比例
     */
    private Double malePercent;

    /**
     * 女性数量
     */
    private Long femaleCount;

    /**
     * 女性比例
     */
    private Double femalePercent;

    /**
     * 本届观众数量
     */
    private Long thisAudienceCount;

    /**
     * 本届比例
     */
    private Double thisAudiencePercent;

    /**
     * 往届观众数量
     */
    private Long previousAudienceCount;

    /**
     * 往届比例
     */
    private Double previousAudiencePercent;

    /**
     * 线上支付数量
     */
    private Long onlinePaymentCount;

    /**
     * 线上支付比例
     */
    private Double onlinePaymentPercent;

    /**
     * 线下支付数量
     */
    private Long offlinePaymentCount;

    /**
     * 线下支付比例
     */
    private Double offlinePaymentPercent;

}
