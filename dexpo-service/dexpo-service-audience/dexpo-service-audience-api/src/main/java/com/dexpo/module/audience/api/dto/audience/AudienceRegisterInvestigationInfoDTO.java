package com.dexpo.module.audience.api.dto.audience;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * @Author: Maojie
 * @CreateTime: 2025-06-26
 * @Description: 观众注册问卷信息DTO
 */

@Data
public class AudienceRegisterInvestigationInfoDTO {

    @Schema(description = "普通观众用户问卷信息DTO")
    @NotNull
    private List<AudienceInvestigationInfoDTO> audienceInvestigationInfos;

    @Schema(description = "旧问卷信息Id集合,如果有需要传进来")
    private List<Long> oldIds;
}
