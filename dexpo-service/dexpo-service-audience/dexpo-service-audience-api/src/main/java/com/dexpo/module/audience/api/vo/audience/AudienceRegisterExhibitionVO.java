package com.dexpo.module.audience.api.vo.audience;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 服务中台 观众报名信息
 */
@Data
public class AudienceRegisterExhibitionVO {


    @Schema(description = "用户基本信息")
    private AudienceBaseInfoVO audienceBaseInfoVO;

    @Schema(description = "用户问卷信息")
    private List<AudienceInvestigationInfoVO> investigationInfoVOs;

    @Schema(description = "日志信息")
    private List<AudienceOperateLogVO> operateLogVOs;

    @Schema(description = "参展状态 值集VS_AUDIENCE_PARTICIPATE_STATUS")
    private String participateStatus;

    @Schema(description = "参展状态 值集VS_AUDIENCE_PARTICIPATE_STATUS")
    private String participateStatusName;

}
