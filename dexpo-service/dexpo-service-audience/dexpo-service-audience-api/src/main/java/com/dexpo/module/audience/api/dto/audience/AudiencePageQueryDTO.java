package com.dexpo.module.audience.api.dto.audience;

import com.dexpo.framework.common.pojo.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 服务中台 观众分页列表查询DTO
 */
/**
 * 服务中台 观众分页列表查询DTO
 */
@Data
public class AudiencePageQueryDTO extends PageParam {


    /**
     * 姓名
     */
    private String memberName;

    /**
     * 手机号
     */
    private String memberMobile;

    /**
     * 类别
     */
    private String audienceType;

    /**
     * 参展状态
     */
    private String participateStatus;

    /**
     * 是否国内，true-国内，false-境外
     */
    private Boolean isDomestic;

    /**
     * 展会tag code -> 当前登录归属展会的下级
     */
    private List<String> exhibitionTagCodes;

    /**
     * 展会年份（届）
     */
    private List<String> exhibitionSessionKeys;

    /**
     * 注册通道
     */
    private String registerSystem;
    /**
     * 注册时间开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime registerTimeStart;

    /**
     * 注册时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime registerTimeEnd;

}
