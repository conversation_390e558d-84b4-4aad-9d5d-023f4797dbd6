package com.dexpo.module.audience.api.dto.audience;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @Author: <PERSON><PERSON>e
 * @CreateTime: 2025-06-26
 * @Description: 观众基本注册信息DTO
 */

@Data
public class AudienceRegisterBaseInfoDTO {

    @Schema(description = "基本信息")
    @NotNull
    private AudienceBaseInfoDTO baseInfo;

    @Schema(description = "参展信息")
    @NotNull
    private ExhibitionInfoDTO exhibitionInfo;

    @Schema(description = "注册语言")
    @NotNull
    private String registerLanguage;



}
