package com.dexpo.module.audience.api;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.audience.api.dto.audience.AudienceLoginDTO;
import com.dexpo.module.audience.api.enums.ApiConstants;
import com.dexpo.module.audience.api.vo.audience.AudienceLoginInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Author: Maojie
 * @CreateTime: 2025-06-21
 * @Description:
 */

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 普通观众前台业务服务")
public interface AudienceApi {

      String PREFIX = "/audience";


     @PostMapping(PREFIX+"/login")
     @Operation(summary = "普通观众用户登录")
     CommonResult<AudienceLoginInfoVO> audienceLogin(@Valid @RequestBody AudienceLoginDTO loginDTO);
}
