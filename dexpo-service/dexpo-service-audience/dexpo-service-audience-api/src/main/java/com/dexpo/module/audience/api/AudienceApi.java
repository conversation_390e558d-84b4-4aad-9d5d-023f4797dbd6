package com.dexpo.module.audience.api;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.audience.api.dto.audience.AudienceDetailInfoDTO;
import com.dexpo.module.audience.api.dto.audience.AudienceLoginDTO;
import com.dexpo.module.audience.api.dto.audience.AudienceRegisterBaseInfoDTO;
import com.dexpo.module.audience.api.dto.audience.AudienceRegisterInvestigationInfoDTO;
import com.dexpo.module.audience.api.enums.ApiConstants;
import com.dexpo.module.audience.api.vo.audience.AudienceDetailInfoVO;
import com.dexpo.module.audience.api.vo.audience.AudienceInvestigationInfoVO;
import com.dexpo.module.audience.api.vo.audience.AudienceLoginInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Author: Maojie
 * @CreateTime: 2025-06-21
 * @Description:
 */

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 普通观众前台业务服务")
public interface AudienceApi {

      String PREFIX = "/audience";


     @PostMapping(PREFIX+"/login")
     @Operation(summary = "普通观众用户登录")
     CommonResult<AudienceLoginInfoVO> audienceLogin(@Valid @RequestBody AudienceLoginDTO loginDTO);


     @PostMapping(PREFIX+"/detail")
     @Operation(summary = "普通观众用户详情")
     CommonResult<AudienceDetailInfoVO> getAudienceDetail(@Valid @RequestBody AudienceDetailInfoDTO detailDTO);
     
     @PostMapping(PREFIX+"/submitBaseInfo")
     @Operation(summary = "普通观众用户提交基本信息")
     CommonResult<Long> submitBaseInfo(@Valid @RequestBody AudienceRegisterBaseInfoDTO baseInfoDTO);
     @PostMapping(PREFIX+"/updateBaseInfo")
     @Operation(summary = "普通观众用户更新草稿基本信息")
     CommonResult<Long> updateBaseInfo(@RequestBody AudienceRegisterBaseInfoDTO baseInfoDTO);
     
     @PostMapping(PREFIX+"/submitInvestigationInfo")
     @Operation(summary = "普通观众用户提交问卷信息")
     CommonResult<List<AudienceInvestigationInfoVO>> submitInvestigationInfo(@Valid @RequestBody AudienceRegisterInvestigationInfoDTO investigationInfoDTO);

     @PostMapping(PREFIX+"/updateInvestigationInfo")
     @Operation(summary = "普通观众用户更新草稿问卷信息")
     CommonResult<List<AudienceInvestigationInfoVO>> updateInvestigationInfo(@RequestBody AudienceRegisterInvestigationInfoDTO investigationInfoDTO);
}
