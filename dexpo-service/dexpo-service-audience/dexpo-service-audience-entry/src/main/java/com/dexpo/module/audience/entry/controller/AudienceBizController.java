package com.dexpo.module.audience.entry.controller;

import com.dexpo.module.audience.api.AudienceBizApi;
import com.dexpo.module.audience.api.dto.audience.AudienceBoardDTO;
import com.dexpo.module.audience.api.dto.audience.AudiencePageQueryDTO;
import com.dexpo.module.audience.api.dto.audience.AudienceRegisterBaseInfoDTO;
import com.dexpo.module.audience.api.vo.audience.AudienceBoardVO;
import com.dexpo.module.audience.api.vo.audience.AudiencePageListVO;
import com.dexpo.module.audience.api.vo.audience.AudienceRegisterExhibitionVO;
import com.dexpo.module.audience.app.service.AudienceBaseAppService;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.audience.app.service.AudienceMemberAppService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Validated
@RequiredArgsConstructor
public class AudienceBizController implements AudienceBizApi {

    private final AudienceBaseAppService audienceBaseAppService;

    @Resource
    private AudienceMemberAppService audienceMemberAppService;

    @Override
    public CommonResult<PageResult<AudiencePageListVO>> getAudiencePageList(@Valid @RequestBody AudiencePageQueryDTO queryDTO) {
        PageResult<AudiencePageListVO> page = audienceBaseAppService.getAudiencePage(queryDTO);
        return CommonResult.success(page);
    }

    @Override
    public CommonResult<Boolean> activeAudience(List<Long> ids) {
        return CommonResult.success(audienceBaseAppService.activeAudience(ids));
    }

    @Override
    public CommonResult<AudienceRegisterExhibitionVO> getAudienceDetail(Long id) {
        return CommonResult.success(audienceBaseAppService.getAudienceRegisterExhibition(id));
    }

    @Override
    public CommonResult<AudienceBoardVO> getAudienceBoard(AudienceBoardDTO queryDTO) {
        return CommonResult.success(audienceBaseAppService.getAudienceBoard(queryDTO));
    }

    @Override
    public CommonResult<Long> proxyRegister(AudienceRegisterBaseInfoDTO baseInfoDTO) {
        return CommonResult.success(audienceMemberAppService.proxyRegister(baseInfoDTO));
    }


}
